package com.tuns.car.channel.dto.apac.carquery.res;

import com.tuns.car.channel.dto.apac.common.AbstractApiXmlResponseHead;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: liuhui
 * @Date: 2024/11/25 15:05
 * @Description: 车辆查询入参
 **/
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "packet")
@Data
public class APACApiCarInfoRes extends AbstractApiXmlResponseHead {

    @XmlElement(name = "body")
    private APACApiCarInfoResBody body;
}
