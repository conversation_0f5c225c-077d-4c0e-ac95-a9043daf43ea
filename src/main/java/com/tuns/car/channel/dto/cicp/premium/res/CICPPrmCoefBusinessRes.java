package com.tuns.car.channel.dto.cicp.premium.res;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-01-09 10:36
 **/
@Data
public class CICPPrmCoefBusinessRes {

    /**
     * 自主核保系数
     */
    private String underwrit_rate;
    /**
     * 渠道系数
     */
    private String chnnel_rate;
    /**
     * 行业NCD不浮动原因代码(商业)
     */
    private String c_no_claim_adjust_reason;
    /**
     * 连续承保年数
     */
    private String c_insure_years;
    /**
     * 连续承保期间出险次数
     */
    private BigDecimal c_claim_times;
    /**
     * NCD系数级别
     */
    private String c_claim_adjust_level;


}
