package com.tuns.car.channel.dto.zcbx.res;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-31 15:24
 **/

@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class ZCBXDeUnderwriteStatusResultDTO {

    /**
     * 投保单号
     */
    @XmlElement(name = "BusinessNo")
    private String businessNo;
    /**
     核保状态
     0  未提交核保
     1  已生成保单
     2  核保不通过，下发修改
     4  拒保
     5  核保通过
     9  待人工核保
     */
    @XmlElement(name = "State")
    private String state;
    /**
     * 参考核保不通过原因列表
     */
    @XmlElementWrapper(name = "ReasonList")
    @XmlElement(name = "Reason")
    private List<ZCBXUnderwriteReasonDTO> reasonList;

    /**
     * 人工核保信息，参考人工核保信息
     */
    @XmlElementWrapper(name = "AuditOpinionDtoList")
    @XmlElement(name = "AuditOpinionDto")
    private List<ZCBXAuditOpinionDTO> auditOpinionDtoList;
}
