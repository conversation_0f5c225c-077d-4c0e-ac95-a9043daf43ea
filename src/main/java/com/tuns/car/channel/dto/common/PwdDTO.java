package com.tuns.car.channel.dto.common;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName PwdDTO
 * @Description 报文加密入参
 * <AUTHOR>
 * @Date 2024/12/13 10:46
 * @Version 1.0
 */
@Data
public class PwdDTO implements Serializable {

    /**
     * 报文内容
     */
    @ApiModelProperty(value = "报文内容", required = true)
    private String content;

    /**
     * key
     */
    @ApiModelProperty(value = "key", required = true)
    private String key;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
