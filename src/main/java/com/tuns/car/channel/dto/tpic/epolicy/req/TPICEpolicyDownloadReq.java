package com.tuns.car.channel.dto.tpic.epolicy.req;


import lombok.Data;
import lombok.experimental.Accessors;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @since 2023-12-13 16:05
 **/
@Data
@Accessors(chain = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "body")
public class TPICEpolicyDownloadReq {

    /**
     * 险种信息
     */
    @XmlElement
    private TPICRisk risk;

    /**
     * 电子单证信息
     */
    @XmlElement
    private TPICElecDocument elecDocument;

    /**
     * 非车信息
     */
    @XmlElement
    private TPICApiFcProductDTO fcProduct;


}
