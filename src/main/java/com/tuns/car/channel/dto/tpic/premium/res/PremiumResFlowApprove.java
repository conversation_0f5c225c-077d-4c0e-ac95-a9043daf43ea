package com.tuns.car.channel.dto.tpic.premium.res;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

/**
 * <AUTHOR>
 * TODO
 * @since 2023/12/11
 */
@Data
@Accessors(chain = true)
@XmlAccessorType(XmlAccessType.FIELD)
public class PremiumResFlowApprove {


    /**
     *
     */
    private String resultType;

    /**
     *
     */
    private String ruleId;

    /**
     *
     */
    private String description;

}
