package com.tuns.car.channel.dto.chac.base.req;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * @project: tuns-business
 * @description: 诚泰webservice请求 头信息封装结构
 * @author: Gk
 * @time: 2022-6-6 10:57
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@Data
public class CHACApiBaseRequest {
    @XmlElement(name = "Head")
    private CHACApiBaseReqHead head;

    public CHACApiBaseReqHead getHead() {
        if (head == null) {
            head = new CHACApiBaseReqHead();
        }
        return head;
    }
}
