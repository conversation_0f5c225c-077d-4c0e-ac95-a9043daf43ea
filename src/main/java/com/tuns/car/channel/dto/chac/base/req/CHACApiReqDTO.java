package com.tuns.car.channel.dto.chac.base.req;

import lombok.Data;

import javax.xml.bind.annotation.*;

/**
 * @project: tuns-business
 * @description: 诚泰webservice请求 最外层结构 包含header与body  数据传输主要在body中
 * @author: Gk
 * @time: 2022-6-6 15:27
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "head",
        "body"
})
@XmlRootElement(name = "Envelope", namespace = "http://schemas.xmlsoap.org/soap/envelope/")
@Data
public class CHACApiReqDTO {
    @XmlElement(name = "Header")
    private CHACApiHeader head;
    @XmlElement(name = "Body")
    private CHACApiBaseReqBody body;

    public CHACApiBaseReqBody getBody() {
        if (body == null) {
            body = new CHACApiBaseReqBody();
        }
        return body;
    }
}
