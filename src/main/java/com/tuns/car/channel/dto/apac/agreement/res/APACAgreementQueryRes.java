package com.tuns.car.channel.dto.apac.agreement.res;

import com.tuns.car.channel.dto.apac.common.AbstractApiXmlResponseHead;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * @Author: liuhui
 * @Date: 2024/11/25 15:05
 * @Description: 车辆查询入参
 **/
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "packet")
@Data
public class APACAgreementQueryRes extends AbstractApiXmlResponseHead {


    @XmlElement(name = "body")
    private Body body;

    @Data
    @Accessors(chain = true)
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class Body {
        @XmlElement(name = "engage")
        @XmlElementWrapper(name = "engages")
        private List<Engage> engages;
    }

    @Data
    @Accessors(chain = true)
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class Engage {
        /**
         * 特约号
         */
        private String clauseNo;
        /**
         * 特约编码
         */
        private String clauseCode;
        /**
         * 特约内容
         */
        private String clauses;
        /**
         * 特约属性(1:交强险 2:商业险 3:交强和商业都有)
         */
        private String clauseAttribute;
    }

}
