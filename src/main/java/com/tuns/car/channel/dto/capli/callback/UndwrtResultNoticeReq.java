package com.tuns.car.channel.dto.capli.callback;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/6/8 11:41
 */
@Data
@Accessors(chain = true)
public class UndwrtResultNoticeReq {
    /**
     * 交强险业务单号核保状态
     */
    private String underwriteFlagCI;
    /**
     * 交强险业务单号核保意见
     */
    private String underwriteOpinionCI;
    /**
     * 商业险业务单号核保状态
     */
    private String underwriteFlagBI;
    /**
     * 商业险业务单号核保意见
     */
    private String underwriteOpinionBI;
    /**
     * 交强险支付号
     */
    private String paymentNoCI;
    /**
     * 商业险支付号
     */
    private String paymentNoBI;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
