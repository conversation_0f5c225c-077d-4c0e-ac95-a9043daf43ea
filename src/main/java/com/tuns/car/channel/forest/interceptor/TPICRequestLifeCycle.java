package com.tuns.car.channel.forest.interceptor;

import com.dtflys.forest.lifecycles.MethodAnnotationLifeCycle;
import com.dtflys.forest.reflection.ForestMethod;
import com.tuns.car.channel.forest.annotation.TPICApi;

/**
 * @project: tuns-business
 * @description: 诚泰财险方法级拦截器
 * @author: Gk
 * @time: 2022-6-7 8:48
 */
public class TPICRequestLifeCycle implements MethodAnnotationLifeCycle<TPICApi, Object> {
    @Override
    public void onMethodInitialized(ForestMethod method, TPICApi annotation) {
        method.setReturnType(String.class);
    }
}
