package com.tuns.car.channel.service.core.zcbx;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tuns.car.channel.constant.BusinessIdTypeEnum;
import com.tuns.car.channel.constant.CallbackEnum;
import com.tuns.car.channel.constants.zcbx.ZCBXErrorCodeEnum;
import com.tuns.car.channel.constants.zcbx.ZCBXRiskCodeEnum;
import com.tuns.car.channel.dto.callback.PayCallBackOwnDataDTO;
import com.tuns.car.channel.dto.zcbx.req.ZCBXOrderQueryDTO;
import com.tuns.car.channel.dto.zcbx.req.ZCBXPolicyQueryDTO;
import com.tuns.car.channel.dto.zcbx.res.*;
import com.tuns.car.channel.rpc.zcbx.ZCBXXmlRpcService;
import com.tuns.car.channel.service.core.AbstractPayCallBackServiceImpl;
import com.tuns.car.channel.util.chac.CHACBusinessUtil;
import com.tuns.car.core.constant.InsEnum;
import com.tuns.car.core.constant.InsuredStatusEnum;
import com.tuns.car.core.constant.InsuredSubStatusEnum;
import com.tuns.core.boot.utils.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * @project: tuns-business
 * @description: 诚泰支付回调实现类
 * @author: Gk
 * @time: 2022-5-31 10:56
 */
@Slf4j
@Service
public class ZCBXPayCallBackServiceImpl extends AbstractPayCallBackServiceImpl<ZCBXPaySignRes> {

    public static final String SUCCESS = "success";

    @Autowired
    private ZCBXXmlRpcService zcbxXmlRpcService;

    @Override
    protected ZCBXPaySignRes checkDataAndTransToInsData(String origin) {
        ZCBXPaySignRes zcbxPaySignRes = CHACBusinessUtil.formConvertBean(origin, ZCBXPaySignRes.class);
        log.info("众诚回调请求参数：{}", JSONUtil.toJsonStr(zcbxPaySignRes));
        return CHACBusinessUtil.formConvertBean(origin, ZCBXPaySignRes.class);
    }

    @Override
    protected PayCallBackOwnDataDTO mapToOwnData(ZCBXPaySignRes zcbxPaySignRes) {
        if (SUCCESS.equals(zcbxPaySignRes.getTradeStatus())) {
            ZCBXOrderQueryDTO orderQueryDTO = new ZCBXOrderQueryDTO();
            orderQueryDTO.setOrderQueryDTO(new ZCBXOrderQueryDTO.OrderQueryDTO(zcbxPaySignRes.getOutTradeNo()));
            //支付查询
            ZCBXOrderQueryBodyRes zcbxOrderQueryBodyRes = zcbxXmlRpcService.payQuery(orderQueryDTO);
            ZCBXOrderQueryResultDTO.OrderQueryResultDTO orderQueryResultDTO = zcbxOrderQueryBodyRes.getBodyDto().getOrderQueryResultDTO();
            return buildResult(orderQueryResultDTO);
        }
        return null;
    }


    @Override
    public InsEnum getSupportIns() {
        return InsEnum.ZCBX;
    }

    @Override
    public CallbackEnum getNotifyType() {
        return CallbackEnum.PAY;
    }

    private PayCallBackOwnDataDTO buildResult(ZCBXOrderQueryResultDTO.OrderQueryResultDTO orderQueryResultDTO) {
        PayCallBackOwnDataDTO result = new PayCallBackOwnDataDTO();
        result.setInsuredStatus(InsuredStatusEnum.PAID);
        result.setInsuredSubStatus(InsuredSubStatusEnum.C19);
        result.setPayTime(getPayTime(orderQueryResultDTO.getValidDate()));
        List<ZCBXItemDTO> policyNoList = orderQueryResultDTO.getItemDtoList();
        if (CollectionUtil.isNotEmpty(policyNoList)) {
            result.setBusinessId(policyNoList.get(0).getBusinessNo());
            result.setBusinessIdType(BusinessIdTypeEnum.PROPOSAL_NUMBER);

            for (ZCBXItemDTO zcbxItemDTO : policyNoList) {
                if (StrUtil.isNotBlank(zcbxItemDTO.getPolicyNo())) {
                    PolicyInf policyInf = queryPolicyDetail(zcbxItemDTO.getPolicyNo());
                    if (policyInf != null) {
                        policyInf.setPolicyNumber(result);
                        result.setInsuredSubStatus(InsuredSubStatusEnum.C2);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 调用保单查询区分保单号类型
     *
     * @param policyNo
     */
    private PolicyInf queryPolicyDetail(String policyNo) {
        ZCBXPolicyQueryDTO zcbxPolicyQueryDTO = new ZCBXPolicyQueryDTO(policyNo);
        //保单查询获取保单类型
        ZCBXPolicyQueryBodyRes zcbxPolicyQueryBodyRes = zcbxXmlRpcService.policyQuery(zcbxPolicyQueryDTO);
        String errorCode = zcbxPolicyQueryBodyRes.getHeadDto().getErrorCode();
        PolicyInf policyInf = new PolicyInf();
        if (ZCBXErrorCodeEnum.SUCCESS.getCode().equals(errorCode)) {
            ZCBXPolicyQueryMainDTO mainDto = zcbxPolicyQueryBodyRes.getBodyDto().getMainDto();
            if (ZCBXRiskCodeEnum.BI.getValue().equals(mainDto.getRiskCode())) {
                policyInf.setPolicyNo(mainDto.getPolicyNo());
                policyInf.setPolicyType(PolicyType.BI);
                return policyInf;
            } else if (ZCBXRiskCodeEnum.CI.getValue().equals(mainDto.getRiskCode())) {
                policyInf.setPolicyNo(mainDto.getPolicyNo());
                policyInf.setPolicyType(PolicyType.CI);
                return policyInf;
            }
        } else {
            policyInf.setPolicyNo(policyNo);
            policyInf.setPolicyType(PolicyType.NOCAR);
            return policyInf;
        }
        return null;
    }

    private static LocalDateTime getPayTime(String validDate) {
        try {
            return DateUtil.parseLocalDateTime(validDate, DatePattern.PURE_DATETIME_PATTERN);
        } catch (Exception e) {
            log.error("众诚支付时间转换异常", e);
        }
        return LocalDateTime.now();
    }

    @Data
    public class PolicyInf {
        private String policyNo;

        private PolicyType policyType;

        public void setPolicyNumber(PayCallBackOwnDataDTO dto) {
            getPolicyType().setPolicyNo(dto, policyNo);
        }
    }


    public enum PolicyType {

        CI(payCallBackOwnDataDTO -> payCallBackOwnDataDTO::setPolicyNumberCi),

        BI(payCallBackOwnDataDTO -> payCallBackOwnDataDTO::setPolicyNumberBi),

        NOCAR(payCallBackOwnDataDTO -> noCarPolicyNo -> payCallBackOwnDataDTO.setNoCarPolicyNo(Collections.singletonList(noCarPolicyNo)));

        private final Function<PayCallBackOwnDataDTO, Consumer<String>> policyNoSetFunc;

        PolicyType(Function<PayCallBackOwnDataDTO, Consumer<String>> setPolicyNoFunc) {
            this.policyNoSetFunc = setPolicyNoFunc;
        }

        public void setPolicyNo(PayCallBackOwnDataDTO dto, String policyNo) {
            policyNoSetFunc.apply(dto).accept(policyNo);
        }
    }
}
