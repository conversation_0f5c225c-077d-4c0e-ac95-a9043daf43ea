package com.tuns.car.channel.service.core;

import cn.hutool.extra.qrcode.BufferedImageLuminanceSource;
import com.google.zxing.*;
import com.google.zxing.common.HybridBinarizer;
import com.tuns.core.boot.utils.Base64Util;
import com.tuns.core.thirdparty.config.OSSManager;
import com.tuns.core.thirdparty.service.IOSSService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * base64格式文件上传oss服务类
 *
 * @Auther tanshun
 * @Date 2022/10/26
 */
@Service
@Slf4j
public class Base64UploadOssService {

    @Autowired
    private IOSSService iossService;

    private static final String PAY_IMAGE = "pay_image";

    /**
     * 上传支付二维码
     *
     * @param base64Str
     * @param policyBatchId
     * @return
     */
    public String uploadPayImage(String base64Str, Long policyBatchId) {
        byte[] decodeByte = Base64Util.decode(base64Str);
        OSSManager.Config.FileConfig fileConfig = iossService.getConfig().getFileConfig().get(PAY_IMAGE);
        String path = fileConfig.getDir() + policyBatchId + "/" + policyBatchId + "_支付二维码";
        iossService.upload(new ByteArrayInputStream(decodeByte), ContentType.APPLICATION_OCTET_STREAM.getMimeType(), path, OSSManager.AccessControl.PublicRead);
        return iossService.downloadUrl(path);
    }

    /**
     * 解析二维码,此方法解析一个路径的二维码图片
     * path:图片路径
     */
    public String deEncodeByPath(String base64Str) {
        byte[] decodeByte = Base64Util.decode(base64Str);
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(decodeByte);
        String content = null;
        BufferedImage image;
        try {
            image = ImageIO.read(byteArrayInputStream);
            LuminanceSource source = new BufferedImageLuminanceSource(image);
            Binarizer binarizer = new HybridBinarizer(source);
            BinaryBitmap binaryBitmap = new BinaryBitmap(binarizer);
            Map<DecodeHintType, Object> hints = new HashMap<>();
            hints.put(DecodeHintType.CHARACTER_SET, "UTF-8");
            Result result = new MultiFormatReader().decode(binaryBitmap, hints);//解码
            System.out.println("图片中内容：  ");
            System.out.println("content： " + result.getText());
            content = result.getText();
        } catch (IOException e) {
            log.error(e.getMessage(),e);
        } catch (NotFoundException notFoundException) {
            //这里判断如果识别不了带LOGO的图片，重新添加上一个属性
            try {
                byte[] decodeByte1 = Base64Util.decode(base64Str);
                ByteArrayInputStream byteArrayInputStream1 = new ByteArrayInputStream(decodeByte1);
                String content1 = null;
                BufferedImage image1;
                image1 = ImageIO.read(byteArrayInputStream1);
                LuminanceSource source = new BufferedImageLuminanceSource(image1);
                Binarizer binarizer = new HybridBinarizer(source);
                BinaryBitmap binaryBitmap = new BinaryBitmap(binarizer);
                Map<DecodeHintType, Object> hints = new HashMap<>();
                //设置编码格式
                hints.put(DecodeHintType.CHARACTER_SET, "UTF-8");
                //设置优化精度
                hints.put(DecodeHintType.TRY_HARDER, Boolean.TRUE);
                //设置复杂模式开启（我使用这种方式就可以识别微信的二维码了）
                hints.put(DecodeHintType.PURE_BARCODE, Boolean.TYPE);
                Result result = new MultiFormatReader().decode(binaryBitmap, hints);//解码
                System.out.println("图片中内容：  ");
                System.out.println("content： " + result.getText());
                content = result.getText();
            } catch (IOException ioException) {
                log.error(ioException.getMessage(),ioException);
            } catch (NotFoundException notFoundException1) {
                log.error(notFoundException1.getMessage(),notFoundException1);

            }
        }
        return content;
    }
}

