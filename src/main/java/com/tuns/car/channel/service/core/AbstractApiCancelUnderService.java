package com.tuns.car.channel.service.core;

import com.alibaba.fastjson.JSON;
import com.tuns.car.channel.dto.cancelunder.ApiCancelUnderDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * api撤销投保流程抽象实现，负责编排整体的撤销投保流程
 * 具体的每家保险公司撤销投保对接，需要子类实现本类方法。
 * {@link Req} 保险公司撤销投保接口 请求数据结构
 * {@link Res} 保险公司撤销投保接口 响应数据结构
 *
 * <AUTHOR>
 * @since 2022/5/27
 */
@Slf4j
public abstract class AbstractApiCancelUnderService<Req, Res> implements ApiCancelUnderwriteService, ApiInvoke<ApiCancelUnderDTO, Boolean, Req, Res> {
    @Override
    public boolean cancelUnderwrite(ApiCancelUnderDTO dto) {
        Req req = mapReq(dto);
        log.info(getSupportIns().getCompanyName() + "===>撤销核保：{}", JSON.toJSONString(req));
        Res res = doInvoke(req, dto);
        log.info(getSupportIns().getCompanyName() + "<===撤销核保：{}", JSON.toJSONString(res));
        return mapRes(res, req, dto);
    }
}
