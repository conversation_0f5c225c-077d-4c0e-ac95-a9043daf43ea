package com.tuns.car.channel.service.core;

import com.tuns.car.channel.constant.CallbackEnum;
import com.tuns.car.channel.service.ApiIntellectCheckCarCodeService;
import com.tuns.car.core.constant.InsEnum;
import com.tuns.core.boot.exception.TunsBusinessException;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * api出单各流程处理类工厂
 *
 * <AUTHOR>
 * @since 2022/5/26
 */
@Component
@AllArgsConstructor(onConstructor_ = {@Autowired})
public class ApiServiceFactory {

    private final List<ApiPremiumCalculateService> apiPremiumCalculateServices;
    private final List<ApiCarInfoService> apiCarInfoServices;
    private final List<APIPayInfoService> apiPayInfoServices;
    private final List<ApiCompStatusInfoService> apiCompStatusInfoServices;
    private final List<ApiCheckCarCodeService> apiCheckCarCodeServices;
    private final List<ApiIntellectCheckCarCodeService> apiIntellectCheckCarCodeServices;
    private final List<ApiCancelUnderwriteService> apiCancelUnderwriteServices;
    private final List<ApiCallBackService> apiCallBackServices;
    private final List<ApiInsureService> apiInsureServices;
    private final List<ApiSendVerifyCodeService> apiSendVerifyCodeServices;
    private final List<ApiDeleteProposalService> apiDeleteProposalServices;
    private final List<ApiNoCarService> apiNoCarServices;
    private final List<AbstractGINoCarApiCommonService> giNoCarApiCommonServices;
    private Map<InsEnum, ApiPremiumCalculateService> apiPremiumCalculateServiceMap;
    private final List<ApiEpolicyDownloadService> apiEpolicyDownloadServices;
    private final List<ApiUploadImageService> apiUploadImageServices;
    private final List<APICarArtificialUnderwriting> carArtificialUnderwritingServices;
    private final List<APIPremiumDetailService> apiPremiumDetailServices;
    private final List<ApiCheckImageBasisService> apiCheckImageBasisServices;

    private final List<ApiPremiumCalculateBeforeService<?, ?>> apiPremiumCalculateBeforeServices;
    private Map<InsEnum, ApiPremiumCalculateBeforeService<?, ?>> apiPremiumCalculateBeforeServicesMap;

    @PostConstruct
    public void init() {
        apiPremiumCalculateServiceMap = apiPremiumCalculateServices.stream()
                .collect(Collectors.toMap(ApiPremiumCalculateService::getSupportIns, Function.identity()));
        apiPremiumCalculateBeforeServicesMap = apiPremiumCalculateBeforeServices.stream()
                .collect(Collectors.toMap(ApiPremiumCalculateBeforeService::getSupportIns, Function.identity()));
    }

    /**
     * 根据保险公司获取 保费试算 服务类
     *
     * @param insEnum
     * @return
     */
    public ApiPremiumCalculateService getPremiumCalculateService(InsEnum insEnum) {
        ApiPremiumCalculateService bean = apiPremiumCalculateServiceMap.get(insEnum);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }

    /**
     * 根据保险公司获取 提交核保 服务类
     *
     * @param name
     * @return
     */
    public ApiInsureService getInsureService(InsEnum name) {
        ApiInsureService bean = apiInsureServices.stream()
                .filter(apiCarInfoServices -> apiCarInfoServices.getSupportIns() == name)
                .findFirst()
                .orElseThrow(() -> new TunsBusinessException("该保司尚未实现api出单流程"));
        return bean;
    }

    /**
     * 车型查询 服务类
     *
     * @param name
     * @return
     */
    public ApiCarInfoService getApiCarInfoService(InsEnum name) {
        ApiCarInfoService bean = apiCarInfoServices.stream()
                .filter(apiCarInfoServices -> apiCarInfoServices.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }

    /**
     * 查询保单详情服务类
     *
     * @param name
     * @return
     */
    public ApiCompStatusInfoService getApiCompStatusInfoService(InsEnum name) {
        ApiCompStatusInfoService bean = apiCompStatusInfoServices.stream()
                .filter(apiCompStatusInfoServices -> apiCompStatusInfoServices.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }

    /**
     * 验车码服务类
     *
     * @param name
     * @return
     */
    public ApiCheckCarCodeService getApiCheckCarCodeService(InsEnum name) {
        ApiCheckCarCodeService bean = apiCheckCarCodeServices.stream()
                .filter(getApiCheckCarCodeService -> getApiCheckCarCodeService.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }

    /**
     * 智能验车 服务类
     *
     * @param name
     * @return
     */
    public ApiIntellectCheckCarCodeService getApiIntellectCheckCarCodeService(InsEnum name) {
        ApiIntellectCheckCarCodeService bean = apiIntellectCheckCarCodeServices.stream()
                .filter(getApiIntellectCheckCarCodeService -> getApiIntellectCheckCarCodeService.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }

    /**
     * 支付流程 服务类
     *
     * @param name
     * @return
     */
    public APIPayInfoService getCorePayInfoService(InsEnum name) {
        APIPayInfoService bean = apiPayInfoServices.stream()
                .filter(apiPayInfoService -> apiPayInfoService.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现集成出单流程");
        }
        return bean;
    }

    /**
     * 撤销核保
     *
     * @param name
     * @return
     */
    public ApiCancelUnderwriteService getApiCancelUnderwriteService(InsEnum name) {
        ApiCancelUnderwriteService bean = apiCancelUnderwriteServices.stream()
                .filter(apiCancelUnderwriteServices -> apiCancelUnderwriteServices.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }

    /**
     * 通知回调
     *
     * @param name
     * @param callbackEnum
     * @return
     */
    public ApiCallBackService getApiCallBackService(InsEnum name, CallbackEnum callbackEnum) {
        ApiCallBackService bean = apiCallBackServices.stream()
                .filter(apiCallBackServices -> apiCallBackServices.getSupportIns() == name && apiCallBackServices.getNotifyType() == callbackEnum)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }

    /**
     * 发送短信验证码
     *
     * @param name
     * @return
     */
    public ApiSendVerifyCodeService getApiSendVerifyCodeService(InsEnum name) {
        ApiSendVerifyCodeService bean = apiSendVerifyCodeServices.stream()
                .filter(apiSendVerifyCodeServices -> apiSendVerifyCodeServices.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }

    /**
     * 投保单删除
     *
     * @param name
     * @return
     */
    public ApiDeleteProposalService getApiDeleteProposalService(InsEnum name) {
        ApiDeleteProposalService bean = apiDeleteProposalServices.stream()
                .filter(apiDeleteProposalServices -> apiDeleteProposalServices.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }

    /**
     * 电子保单下载
     *
     * @param name
     * @return
     */
    public ApiEpolicyDownloadService getApiEpolicyDownloadService(InsEnum name) {
        ApiEpolicyDownloadService bean = apiEpolicyDownloadServices.stream()
                .filter(apiEpolicyDownloadService -> apiEpolicyDownloadService.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }


    /**
     * 根据保险公司获取 保费试算 服务类
     *
     * @param insEnum
     * @return
     */
    public ApiNoCarService getApiNoCarService(InsEnum insEnum) {
        ApiNoCarService bean = apiNoCarServices.stream()
                .filter(apiNoCarServices -> apiNoCarServices.getSupportIns() == insEnum)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }

    /**
     * 校验API非车商品
     *
     * @param insEnum
     * @return
     */
    public ApiNoCarService checkNoCarCommodity(InsEnum insEnum) {
        ApiNoCarService bean = apiNoCarServices.stream()
                .filter(apiNoCarServices -> apiNoCarServices.getSupportIns() == insEnum)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }

    /**
     * 非车服务类
     *
     * @param name
     * @return
     */
    public AbstractGINoCarApiCommonService getGINoCarCommonService(InsEnum name) {
        AbstractGINoCarApiCommonService bean = giNoCarApiCommonServices.stream()
                .filter(apiNoCarServices -> apiNoCarServices.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }

    /**
     * 上传影像服务
     *
     * @param name
     * @return
     */
    public ApiUploadImageService getApiUploadImageService(InsEnum name) {
        ApiUploadImageService bean = apiUploadImageServices.stream()
                .filter(apiUploadImageService -> apiUploadImageService.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api出单流程");
        }
        return bean;
    }


    public APICarArtificialUnderwriting getCarArtificialUnderwriting(InsEnum name) {
        APICarArtificialUnderwriting bean = carArtificialUnderwritingServices.stream()
                .filter(apiService -> apiService.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现集成出单流程");
        }
        return bean;
    }

    /**
     * 报价单详情服务
     *
     * @param name
     * @return
     */
    public APIPremiumDetailService getPremiumDetailService(InsEnum name) {
        APIPremiumDetailService bean = apiPremiumDetailServices.stream()
                .filter(apiPremiumDetailService -> apiPremiumDetailService.getSupportIns() == name)
                .findFirst()
                .orElse(null);
        if (null == bean) {
            throw new TunsBusinessException("该保司尚未实现api报价单详情");
        }
        return bean;
    }

    /**
     * 根据保险公司获取 保费试算 服务类
     *
     * @param insEnum
     * @return
     */
    public ApiPremiumCalculateBeforeService<?, ?> getPremiumCalculateBeforeService(InsEnum insEnum) {
        return apiPremiumCalculateBeforeServicesMap.get(insEnum);
    }

    public ApiCheckImageBasisService getApiCheckImageBasisService(InsEnum insEnum) {
        return apiCheckImageBasisServices.stream()
                .filter(apiService -> apiService.getSupportIns() == insEnum)
                .findFirst()
                .orElse(null);
    }
}
