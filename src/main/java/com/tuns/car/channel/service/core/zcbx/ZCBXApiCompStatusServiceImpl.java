package com.tuns.car.channel.service.core.zcbx;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.tuns.car.channel.constants.zcbx.ZCBXUnderwritingFlagEnum;
import com.tuns.car.channel.dto.APIGetStatusDTO;
import com.tuns.car.channel.dto.zcbx.req.ZCBXDeUnderwriteStatusDTO;
import com.tuns.car.channel.dto.zcbx.req.ZCBXOrderQueryDTO;
import com.tuns.car.channel.dto.zcbx.req.ZCBXPolicyQueryDTO;
import com.tuns.car.channel.dto.zcbx.req.ZCBXUnderwriteStatusReq;
import com.tuns.car.channel.dto.zcbx.res.*;
import com.tuns.car.channel.rpc.zcbx.ZCBXXmlRpcService;
import com.tuns.car.channel.service.core.AbstractApiCompStatusService;
import com.tuns.car.core.constant.InsEnum;
import com.tuns.car.core.constant.InsuredSubStatusEnum;
import com.tuns.car.core.constant.PolicyTypeEnum;
import com.tuns.car.core.constant.RecordTypeEnum;
import com.tuns.car.core.dto.CompStatusInfoDTO;
import com.tuns.car.core.dto.NoCarPolicy;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import com.tuns.car.core.vo.OrderInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-06-04 10:51
 */
@Slf4j
@Service
public class ZCBXApiCompStatusServiceImpl extends AbstractApiCompStatusService<ZCBXUnderwriteStatusReq, ZCBXUnderwriteStatusRes> {
    @Autowired
    private ZCBXXmlRpcService zcbxXmlRpcService;

    @Override
    public InsEnum getSupportIns() {
        return InsEnum.ZCBX;
    }

    @Override
    public ZCBXUnderwriteStatusReq mapReq(APIGetStatusDTO apiGetStatusDTO) {
        ZCBXUnderwriteStatusReq underwriteStatusReq = new ZCBXUnderwriteStatusReq();
        List<ZCBXDeUnderwriteStatusDTO> businessNoDtoList = new ArrayList<>();
        for (ViInsPlcyInfTp viInsPlcyInfTp : apiGetStatusDTO.getViInsPlcyInfTps()) {
            businessNoDtoList.add(new ZCBXDeUnderwriteStatusDTO(viInsPlcyInfTp.getProposalNumber()));
        }
        underwriteStatusReq.setBusinessNoDtoList(businessNoDtoList);
        return underwriteStatusReq;
    }

    @Override
    public ZCBXUnderwriteStatusRes doInvoke(ZCBXUnderwriteStatusReq zcbxUnderwriteStatusReq, APIGetStatusDTO apiGetStatusDTO) {
        ZCBXUnderwriteStatusBodyRes zcbxUnderwriteStatusBodyRes = zcbxXmlRpcService.underwriteStatus(zcbxUnderwriteStatusReq);
        return zcbxUnderwriteStatusBodyRes.getBodyDto();
    }

    @Override
    public CompStatusInfoDTO mapRes(ZCBXUnderwriteStatusRes zcbxUnderwriteStatusRes, ZCBXUnderwriteStatusReq zcbxUnderwriteStatusReq, APIGetStatusDTO apiGetStatusDTO) {
        List<ZCBXDeUnderwriteStatusResultDTO> underwriteResultDtoList = zcbxUnderwriteStatusRes.getUnderwriteResultDtoList();
        CompStatusInfoDTO result = new CompStatusInfoDTO();
        if (CollectionUtil.isNotEmpty(underwriteResultDtoList)) {
            List<ZCBXUnderwritingFlagEnum> zcbxUnderwritingFlagEnums = new ArrayList<>();
            underwriteResultDtoList.forEach(underwriteStatus -> zcbxUnderwritingFlagEnums.add(ZCBXUnderwritingFlagEnum.getInsuredStatus(underwriteStatus.getState())));
            String insuredSubStatus = Optional.ofNullable(ZCBXUnderwritingFlagEnum.mapInsuredStatus(zcbxUnderwritingFlagEnums)).map(InsuredSubStatusEnum::getValue).orElse(null);
            result.setOfferStatus(insuredSubStatus);
            result.setOrderStatus(insuredSubStatus);
            result.setInsuredMsg(getAuditMsg(underwriteResultDtoList));

            queryPolicyWhenInsured(apiGetStatusDTO, result, insuredSubStatus);
        }
        ViInsPlcyInfTp viInsPlcyInfTp = apiGetStatusDTO.getViInsPlcyInfTps().get(0);

        if (notUpdateStatus(result, viInsPlcyInfTp)) {
            return null;
        }

        //判断保单号是否完整，不完整的话就变成支付成功未承保
        Boolean notComplete = policyNumberNotComplete(result, viInsPlcyInfTp);
        if (notComplete) {
            result.setOfferStatus(InsuredSubStatusEnum.C19.getValue());
            result.setOrderStatus(InsuredSubStatusEnum.C19.getValue());
        }
        return result;
    }

    /**
     * 保单号是否完整
     * @param result
     * @param viInsPlcyInfTp
     * @return
     */
    private Boolean policyNumberNotComplete(CompStatusInfoDTO result, ViInsPlcyInfTp viInsPlcyInfTp) {
        boolean notComplete = false;
        if (InsuredSubStatusEnum.C2.getValue().equals(result.getOrderStatus())) {
            if (RecordTypeEnum.BOTH.getValue().equals(viInsPlcyInfTp.getRecordType())) {
                notComplete = StrUtil.isBlank(result.getCommPlyNo()) || StrUtil.isBlank(result.getCompPlyNo());
            }
            if (RecordTypeEnum.CI.getValue().equals(viInsPlcyInfTp.getRecordType())) {
                notComplete =  StrUtil.isBlank(result.getCompPlyNo());
            }
            if (RecordTypeEnum.BI.getValue().equals(viInsPlcyInfTp.getRecordType())) {
                notComplete =  StrUtil.isBlank(result.getCommPlyNo());
            }
            if (StrUtil.isNotBlank(viInsPlcyInfTp.getCarProductName())) {
                if (CollectionUtil.isNotEmpty(result.getNoCarPolicyList())) {
                    notComplete =  StrUtil.isBlank(result.getNoCarPolicyList().get(0).getPolicyNo());
                }
            }
        }
        return notComplete;
    }

    private boolean notUpdateStatus(CompStatusInfoDTO result, ViInsPlcyInfTp viInsPlcyInfTp) {
        //状态不是等待核保，并且支付时间为空就直接返回
        if (!InsuredSubStatusEnum.B1.getValue().equals(viInsPlcyInfTp.getInsuredSubStatus())) {
            if (Objects.isNull(viInsPlcyInfTp.getPayTime())) {
                log.info("众诚状态刷新失败，没有回调记录！");
                return true;
            }
        }
        //这种我们这边等待核保状态然后一直不刷新，然后用户去保司核保通过然后申请支付并支付成功的情况，就不刷新状态
        if (viInsPlcyInfTp.getInsuredSubStatus().equals(InsuredSubStatusEnum.B1.getValue()) && InsuredSubStatusEnum.C2.getValue().equals(result.getOrderStatus())) {
            log.info("{}不允许从等待核保刷新到支付成功：{}", getSupportIns().getCompanyName(), viInsPlcyInfTp.getPolicyBatchId());
            return true;
        }
        return false;
    }


    private void queryPolicyWhenInsured(APIGetStatusDTO apiGetStatusDTO, CompStatusInfoDTO result, String insuredSubStatus) {
        if (InsuredSubStatusEnum.C2.getValue().equals(insuredSubStatus)) {
            //已承保 查询下投保单查询
            setViPolicyNo(apiGetStatusDTO, result);

            setNoCarPolicyNo(apiGetStatusDTO, result);
        }
    }

    private void setViPolicyNo(APIGetStatusDTO apiGetStatusDTO, CompStatusInfoDTO result) {
        for (ViInsPlcyInfTp viInsPlcyInfTp : apiGetStatusDTO.getViInsPlcyInfTps()) {
            ZCBXPolicyQueryDTO zcbxPolicyQueryDTO = new ZCBXPolicyQueryDTO();
            zcbxPolicyQueryDTO.setProposalNo(viInsPlcyInfTp.getProposalNumber());
            ZCBXPolicyQueryBodyRes zcbxPolicyQueryBodyRes = zcbxXmlRpcService.policyQuery(zcbxPolicyQueryDTO);
            if ("0000".equals(zcbxPolicyQueryBodyRes.getHeadDto().getErrorCode())) {
                ZCBXPolicyQueryMainDTO mainDto = zcbxPolicyQueryBodyRes.getBodyDto().getMainDto();
                if (PolicyTypeEnum.CI.getValue().equals(viInsPlcyInfTp.getPolicyType())) {
                    result.setCompPlyNo(mainDto.getPolicyNo());
                } else {
                    result.setCommPlyNo(mainDto.getPolicyNo());
                }
            }
        }
    }

    private void setNoCarPolicyNo(APIGetStatusDTO apiGetStatusDTO, CompStatusInfoDTO result) {
        OrderInfoVO orderInfo = apiGetStatusDTO.getOrderInfo();
        if (Objects.nonNull(orderInfo)) {
            ZCBXOrderQueryDTO orderQueryDTO = new ZCBXOrderQueryDTO();
            orderQueryDTO.setOrderQueryDTO(new ZCBXOrderQueryDTO.OrderQueryDTO(orderInfo.getPaySerialNumber()));
            ZCBXOrderQueryBodyRes payQueryRes = zcbxXmlRpcService.payQuery(orderQueryDTO);
            ZCBXOrderQueryResultDTO.OrderQueryResultDTO orderQueryResultDTO = payQueryRes.getBodyDto().getOrderQueryResultDTO();
            if (Objects.nonNull(orderQueryResultDTO)) {
                String noCarPolicyNo = getNoCarPolicyNo(result, orderQueryResultDTO);
                List<NoCarPolicy> noCarPolicyList = new ArrayList<>();
                NoCarPolicy noCarPolicy = new NoCarPolicy();
                noCarPolicy.setPolicyNo(noCarPolicyNo);
                noCarPolicyList.add(noCarPolicy);
                result.setNoCarPolicyList(noCarPolicyList);
            }
        }
    }

    private String getNoCarPolicyNo(CompStatusInfoDTO result, ZCBXOrderQueryResultDTO.OrderQueryResultDTO orderQueryResultDTO) {
        if (CollectionUtil.isNotEmpty(orderQueryResultDTO.getItemDtoList())) {
            for (ZCBXItemDTO zcbxItemDTO : orderQueryResultDTO.getItemDtoList()) {
                if (!StrUtil.equalsAny(zcbxItemDTO.getPolicyNo(), result.getCompPlyNo(), result.getCommPlyNo())) {
                    return zcbxItemDTO.getPolicyNo();
                }
            }
        }
        return null;
    }


    private String getAuditMsg(List<ZCBXDeUnderwriteStatusResultDTO> underwriteResultDtoList) {
        StringBuilder insuredMsg = new StringBuilder();
        for (ZCBXDeUnderwriteStatusResultDTO statusResultDTO : underwriteResultDtoList) {
            if (CollectionUtil.isNotEmpty(statusResultDTO.getAuditOpinionDtoList())) {
                statusResultDTO.getAuditOpinionDtoList().forEach(audit -> insuredMsg.append(audit.getHandleText()).append("\n"));
            }
        }
        return insuredMsg.toString();
    }

}
