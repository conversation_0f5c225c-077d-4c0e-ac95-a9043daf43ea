package com.tuns.car.channel.service.core.zcbx;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.tuns.car.channel.constants.ApiKeyEnum;
import com.tuns.car.channel.constants.zcbx.ZCBXErrorCodeEnum;
import com.tuns.car.channel.constants.zcbx.ZCBXPayTypeEnum;
import com.tuns.car.channel.dto.ApiCarInsurePaySignDTO;
import com.tuns.car.channel.dto.zcbx.req.ZCBXOrderDTO;
import com.tuns.car.channel.dto.zcbx.req.ZCBXPayApplyReq;
import com.tuns.car.channel.dto.zcbx.req.ZCBXPaySignReq;
import com.tuns.car.channel.dto.zcbx.req.ZCBXPaymentDTO;
import com.tuns.car.channel.dto.zcbx.res.ZCBXBillDTO;
import com.tuns.car.channel.dto.zcbx.res.ZCBXCommonHeadRes;
import com.tuns.car.channel.dto.zcbx.res.ZCBXItemDTO;
import com.tuns.car.channel.dto.zcbx.res.ZCBXPayApplyBodyRes;
import com.tuns.car.channel.entity.ViInsApiPath;
import com.tuns.car.channel.rpc.zcbx.ZCBXXmlRpcService;
import com.tuns.car.channel.service.core.APIPayInfoService;
import com.tuns.car.channel.util.ApiConfContext;
import com.tuns.car.channel.util.UrlUtil;
import com.tuns.car.channel.util.zcbx.ErrorHandleUtil;
import com.tuns.car.channel.util.zcbx.MD5util;
import com.tuns.car.core.constant.InsEnum;
import com.tuns.car.core.constant.PayTypeEnum;
import com.tuns.car.core.dto.pay.CarInsureRefreshPayDTO;
import com.tuns.car.core.dto.pay.CarThirdPaymentDTO;
import com.tuns.car.core.dto.pay.PaySignDTO;
import com.tuns.car.core.dto.pay.PaySignResultDTO;
import com.tuns.car.core.entity.ViInsPlcyCustomInvoice;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import com.tuns.core.boot.constant.YesNoNumberEnum;
import com.tuns.core.boot.exception.TunsBusinessException;
import com.tuns.core.thirdparty.config.OSSManager;
import com.tuns.core.thirdparty.service.IOSSService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022-6-1
 */
@Slf4j
@Service
public class ZCBXApiPayInfoServiceImpl implements APIPayInfoService {
    @Autowired
    private ZCBXXmlRpcService zcbxXmlRpcService;

    private static final String PAY_IMAGE = "pay_image";

    @Autowired
    private IOSSService ossService;

    @Override
    public InsEnum getSupportIns() {
        return InsEnum.ZCBX;
    }

    @Override
    public PaySignResultDTO applyPay(ApiCarInsurePaySignDTO paySignDTO) {
        //支付申请
        ZCBXPayApplyReq payApplyReq = buildPayApplyParam(paySignDTO);
        ZCBXPayApplyBodyRes zcbxPayApplyBodyRes = zcbxXmlRpcService.payApply(payApplyReq);
        ZCBXCommonHeadRes headDto = zcbxPayApplyBodyRes.getHeadDto();
        if (ZCBXErrorCodeEnum.ERROR_CODE_2029.getCode().equals(headDto.getErrorCode())){
            throw new TunsBusinessException(headDto.getTransMessage(),ZCBXErrorCodeEnum.ERROR_CODE_2029.getCode());
        }
        if (!ErrorHandleUtil.SUCCESS.equals(headDto.getErrorCode())) {
            throw new TunsBusinessException(headDto.getTransMessage());
        }
        ZCBXBillDTO billDto = zcbxPayApplyBodyRes.getBodyDto().getBillDto();

        //网银支付对接
        String url = buildPayUrl(billDto);
        log.info("众城刷新支付链接为：{}", url);
        PaySignResultDTO result = new PaySignResultDTO();
        BufferedImage generate = QrCodeUtil.generate(url, new QrConfig());
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            ImageIO.write(generate, "PNG", outputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String payUrl = getPayUrl(Base64.encode(outputStream.toByteArray()), paySignDTO.getInsPlcyInfPub(), paySignDTO.getPaymentType().getValue());
        result.setPaySerialNumber(billDto.getTradeNo());
        result.setImagePayUrlMap(buildPayUrlMap(payUrl));
        return result;
    }


    @Override
    public PaySignDTO refreshTwoDimensionCodeLink(CarInsureRefreshPayDTO carInsureRefreshPayDTO) {
        return null;
    }

    @Override
    public CarThirdPaymentDTO queryPayment(CarThirdPaymentDTO insureDTO) {
        return null;
    }

    private static String buildPayUrl(ZCBXBillDTO billDto) {
        Map<String, ViInsApiPath> pathList = ApiConfContext.getConf().getPathList();
        ZCBXPaySignReq zcbxPaySignReq = new ZCBXPaySignReq();
        zcbxPaySignReq.setOutTradeNo(billDto.getTradeNo());
        zcbxPaySignReq.setTotalFee(billDto.getTotalPremium());
        String account = ApiConfContext.getConf().getCompanyConf().getAccount();
        zcbxPaySignReq.setPartnerId(account);
        zcbxPaySignReq.setTimestamp(String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.ofHours(8)).toEpochMilli()));
        zcbxPaySignReq.setNotifyUrl(pathList.get(ApiKeyEnum.PAY_CALLBACK.name()).getInsApiPath());
        zcbxPaySignReq.setSign(buildSign(zcbxPaySignReq, account));
        ViInsApiPath viInsApiPath = pathList.get(ApiKeyEnum.PAY_SIGN.name());
        String urlParams = UrlUtil.parseUrlParams(zcbxPaySignReq);
        return viInsApiPath.getInsApiPath().concat(urlParams);
    }

    private static Map<PayTypeEnum, PaySignDTO> buildPayUrlMap(String url) {
        PaySignDTO dto = new PaySignDTO();
        dto.setUrl(url);
        dto.setType(PaySignDTO.PaySignTypeEnum.QR_CODE_URL);
        dto.setExpireTime(null);
        return MapUtil.builder(PayTypeEnum.OTHER, dto)
                .build();
    }

    private static String buildSign(ZCBXPaySignReq zcbxPaySignReq, String account) {
        String dataSecretKey = ApiConfContext.getConf().getCompanyConf().getDataSecretKey();
        StringBuilder signStr = new StringBuilder();
        signStr.append("out_trade_no=")
                .append(zcbxPaySignReq.getOutTradeNo())
                .append("&")
                .append("partner_id=")
                .append(account)
                .append("&")
                .append("timestamp=")
                .append(zcbxPaySignReq.getTimestamp())
                .append("&")
                .append("total_fee=")
                .append(zcbxPaySignReq.getTotalFee())
                .append(dataSecretKey);
        return MD5util.md5Encode(signStr.toString(), null);
    }

    private ZCBXPayApplyReq buildPayApplyParam(ApiCarInsurePaySignDTO paySignDTO) {
        ZCBXPayApplyReq zcbxPayApplyReq = new ZCBXPayApplyReq();
        ZCBXOrderDTO zcbxOrderDTO = new ZCBXOrderDTO();
        zcbxOrderDTO.setPoaType(ZCBXPayTypeEnum.getTpValue(paySignDTO.getPaymentType()));
        if (StrUtil.isNotBlank(paySignDTO.getIssueCode())) {
            zcbxOrderDTO.setCheckCode(paySignDTO.getIssueCode());
        }
        if (StrUtil.isNotBlank(paySignDTO.getSendIssueCode()) && YesNoNumberEnum.YES.getValue().equals(paySignDTO.getSendIssueCode())) {
            zcbxOrderDTO.setInputCheckCodeLater("0");
        } else {
            zcbxOrderDTO.setInputCheckCodeLater("1");
        }
        zcbxOrderDTO.setEproposalLinkSmsFlag("0");
        if (PayTypeEnum.CORPORATE_TRANS.equals(paySignDTO.getPaymentType())) {
            ZCBXPaymentDTO paymentDTO = new ZCBXPaymentDTO();
            ViInsPlcyCustomInvoice viInsPlcyCustomInvoice = paySignDTO.getViInsPlcyCustomInvoice();
            paymentDTO.setPayAccountName(paySignDTO.getApp().getPersonName());
            paymentDTO.setPayAccountCode(viInsPlcyCustomInvoice.getBankAccount());
            paymentDTO.setBankName(viInsPlcyCustomInvoice.getBankDeposit());
            zcbxOrderDTO.setPaymentDto(paymentDTO);
        }
        List<ZCBXItemDTO> itemDtoList = new ArrayList<>();
        if (Objects.nonNull(paySignDTO.getInsPlcyInfCi())) {
            itemDtoList.add(new ZCBXItemDTO(paySignDTO.getInsPlcyInfCi().getProposalNumber()));
        }
        if (Objects.nonNull(paySignDTO.getInsPlcyInfBi())) {
            itemDtoList.add(new ZCBXItemDTO(paySignDTO.getInsPlcyInfBi().getProposalNumber()));
        }
        if (CollectionUtil.isNotEmpty(paySignDTO.getNoCarList())) {
            String proposalNo = paySignDTO.getNoCarList().get(0).getProposalNo();
            itemDtoList.add(new ZCBXItemDTO(proposalNo));
        }
        zcbxOrderDTO.setItemDtoList(itemDtoList);
        zcbxPayApplyReq.setOrderDto(zcbxOrderDTO);
        return zcbxPayApplyReq;
    }


    /**
     * 上传到阿里云服务器
     *
     * @param base64      二维码信息
     * @param plcyInfPub  报价信息
     * @param paymentType 支付方式
     * @return 支付链接
     */
    public String getPayUrl(String base64, ViInsPlcyInfTp plcyInfPub, String paymentType) {
        OSSManager.Config.FileConfig fileConfig = ossService.getConfig().getFileConfig().get(PAY_IMAGE);
        String path = fileConfig.getDir() + plcyInfPub.getPolicyBatchId() + "/" + plcyInfPub.getPolicyBatchId() + "_" + paymentType + "_支付二维码";
        byte[] decode = Base64.decode(base64);
        ossService.upload(new ByteArrayInputStream(decode), ContentType.APPLICATION_OCTET_STREAM.getMimeType(), path, OSSManager.AccessControl.PublicRead);
        String url = ossService.downloadUrl(path);
        return url;
    }
}
