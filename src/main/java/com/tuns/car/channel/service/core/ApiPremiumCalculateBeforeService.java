package com.tuns.car.channel.service.core;

import com.tuns.car.channel.dto.APIPremiumCalculateDTO;
import com.tuns.car.core.InsServant;

/**
 * Api保费试算前置处理
 * <Preset:前置处理DTO,Parallel:并行处理DTO>
 *
 * <AUTHOR>
 * @since 2024/12/16
 */
public interface ApiPremiumCalculateBeforeService<Preset, Parallel> extends InsServant {
    /**
     * 保费试算前置处理
     * <p>
     * 调用方:进入非标页面获取非标页配置接口
     * {@see com.tuns.car.core.controller.carprocess.ConfigureController#querySpecialConfig(com.tuns.car.core.vo.QuerySpecialConfigVO)}
     * </P>
     *
     * @param premiumCalculateDTO 保费试算DTO
     * @return 前置处理结果DTO
     */
    default Preset preset(APIPremiumCalculateDTO premiumCalculateDTO) {
        return null;
    }

    /**
     * 保费试算并行处理
     * <p>
     * 调用方:保费试算接口
     * {@see com.tuns.car.core.service.carprocess.premium.impl.PremiumCalculateServiceImpl#premiumCalculate(InsCarFullInfoSaveDTO)}}
     * </P>
     *
     * @param premiumCalculateDTO 保费试算DTO
     * @return 并行处理结果DTO
     */
    default Parallel parallel(APIPremiumCalculateDTO premiumCalculateDTO) {
        return null;
    }
}
