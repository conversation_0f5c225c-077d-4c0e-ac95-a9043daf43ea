package com.tuns.car.channel.service.core;

import com.tuns.car.core.InsServant;
import com.tuns.car.core.dto.carprocess.premium.CarEPolicyDTO;
import com.tuns.car.core.dto.carprocess.premium.EpolicyDTO;

import java.util.List;

/**
 * 电子保单下载
 * <AUTHOR>
 * @since 2023-06-04 14:49
 */
public interface ApiEpolicyDownloadService extends InsServant {

    /**
     * 电子保单下载
     * @param carEpolicyDTO
     * @return 电子保单
     */
    List<EpolicyDTO> epolicyDownload(CarEPolicyDTO carEpolicyDTO);

}
