package com.tuns.car.channel.service.core.guoren;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tuns.car.channel.config.ChannelConfig;
import com.tuns.car.channel.constants.guoren.GuoRenUnitNatureEnum;
import com.tuns.car.channel.constants.guoren.GuorenAppStatusEnum;
import com.tuns.car.channel.constants.guoren.GuorenIdentifyTypeEnum;
import com.tuns.car.channel.constants.guoren.GuorenPersonNatureEnum;
import com.tuns.car.channel.dto.conf.ViInsApiConfDTO;
import com.tuns.car.channel.dto.guoren.*;
import com.tuns.car.channel.exception.InsureFailException;
import com.tuns.car.channel.rpc.guoren.GuorenRpcService;
import com.tuns.car.channel.service.core.AbstractApiInsureService;
import com.tuns.car.channel.util.ApiConfContext;
import com.tuns.car.core.constant.*;
import com.tuns.car.core.dto.car.CarThirdInsureDTO;
import com.tuns.car.core.dto.carprocess.premium.AreaDTO;
import com.tuns.car.core.dto.gi.GiInsurePrdVO;
import com.tuns.car.core.dto.gi.GiOrderInfoVO;
import com.tuns.car.core.dto.gi.GiOrderInsureVO;
import com.tuns.car.core.entity.*;
import com.tuns.car.core.util.StatusConvertUtil;
import com.tuns.car.core.vo.car.CarInsureVO;
import com.tuns.car.spider.util.SpiderDateUtil;
import com.tuns.core.boot.constant.YesNoNumberEnum;
import com.tuns.core.boot.exception.TunsBusinessException;
import com.tuns.core.boot.utils.TunsBeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @project: tuns-business
 * @description: 国任提交核保
 * @author: tanshun
 * @time: 2022-6-9
 */
@Slf4j
@Service
public class GuorenApiInsureConfirmServiceImpl extends AbstractApiInsureService<GuorenInsureComfirmReqDTO, GuorenInsureComfirmResultResDataDTO> {
    @Autowired
    private GuorenRpcService guorenRpcService;
    @Autowired
    private GuorenChannelQueryService guorenChannelQueryService;
    @Autowired
    private GuorenUploadFileService guorenUploadFileService;
    @Autowired
    private ChannelConfig channelConfig;

    @Override
    protected void before(CarThirdInsureDTO inputDTO) {
        // 2.非车处理
        GuorenChannelResDTO guorenChannelResDTO = guorenChannelQueryService.queryChannel();
        GuorenNoCarOrderRes guorenNoCarOrderRes = noCar(inputDTO, guorenChannelResDTO.getPrptmain());
        // 3.投保单保存
        GuorenSavePolicyResDTO saveRes = policyTpSave(inputDTO, guorenNoCarOrderRes);
        // 4.修改投保请求入参
        modifyInputDTO(inputDTO, saveRes);
        // 5.附件上传
        guorenUploadFileService.uploadFile(inputDTO.getPlcyInfList(), inputDTO.getAttaches(), inputDTO.getViCompanyImageMappingMap(), guorenChannelResDTO.getPrptmain().getOperatorCode());
    }


    @Override
    public GuorenInsureComfirmReqDTO mapReq(CarThirdInsureDTO carThirdInsureDTO) {
        GuorenChannelResDTO guorenChannelResDTO = guorenChannelQueryService.queryChannel();
        //构建保司接口请求所需数据,头信息,参数,path
        GuorenInsureComfirmReqDTO reqDTO = new GuorenInsureComfirmReqDTO();
        List<GuorenProposalNoSubDTO> list = new ArrayList<>();
        GuorenProposalNoSubDTO guorenProposalNoSubDTO = new GuorenProposalNoSubDTO();
        for (ViInsPlcyInfTp tp : carThirdInsureDTO.getPlcyInfList()) {
            if (PolicyTypeEnum.CI.getValue().equals(tp.getPolicyType())) {
                guorenProposalNoSubDTO.setProposalNoCI(tp.getProposalNumber());
            }
            if (PolicyTypeEnum.BI.getValue().equals(tp.getPolicyType())) {
                guorenProposalNoSubDTO.setProposalNoBI(tp.getProposalNumber());
            }
            list.add(guorenProposalNoSubDTO);
        }
        reqDTO.setUserCode(guorenChannelResDTO.getPrptmain().getOperatorCode());
        reqDTO.setProposalNoSubList(list);
        return reqDTO;
    }

    @Override
    public GuorenInsureComfirmResultResDataDTO doInvoke(GuorenInsureComfirmReqDTO guorenInsureComfirmReqDTO, CarThirdInsureDTO carThirdInsureDTO) {
        //调用接口,获取保司返回响应,处理保司接口响应的特殊化
        log.debug("国任===>提交核保，{}", JSONUtil.toJsonStr(guorenInsureComfirmReqDTO));
        GuorenInsureComfirmResultResDataDTO guorenInsureComfirmResultResDataDTO = guorenRpcService.insureConfirm(guorenInsureComfirmReqDTO);
        log.debug("国任《===提交核保，{}", JSONUtil.toJsonStr(guorenInsureComfirmResultResDataDTO));
        if ("1".equals(guorenInsureComfirmResultResDataDTO.getResultCode())) {
            if (guorenInsureComfirmResultResDataDTO.getResultMsg().contains("Read timed out")) {
                return flushPolicyStatus(guorenInsureComfirmReqDTO);
            }
            throw new TunsBusinessException(guorenInsureComfirmResultResDataDTO.getResultMsg());
        }
        return guorenInsureComfirmResultResDataDTO;
    }

    @Override
    public CarInsureVO mapRes(GuorenInsureComfirmResultResDataDTO guorenInsureComfirmResultResDTO, GuorenInsureComfirmReqDTO guorenInsureComfirmReqDTO, CarThirdInsureDTO carThirdInsureDTO) {

        CarInsureVO carInsureVO = new CarInsureVO();
        //商业
        String underWriteFlagBI = guorenInsureComfirmResultResDTO.getData().get(0).getUnderWriteFlagBI();
        //交强
        String underWriteFlagCI = guorenInsureComfirmResultResDTO.getData().get(0).getUnderWriteFlagCI();
        //提示
        String message = null;

        //取交强商业投保状态最大值,作为最终状态
        String finalFlag = Stream.of(underWriteFlagBI, underWriteFlagCI).max(Comparator.comparing(s -> s)).orElseThrow(() -> new TunsBusinessException(""));
        GuorenAppStatusEnum appStatusEnum = GuorenAppStatusEnum.getByValue(finalFlag);
        if (StringUtils.isNotEmpty(guorenInsureComfirmResultResDTO.getData().get(0).getMessage())) {
            if (Objects.nonNull(appStatusEnum)) {
                message = guorenInsureComfirmResultResDTO.getData().get(0).getMessage();
            } else {
                message = guorenInsureComfirmResultResDTO.getData().get(0).getMessage();
                throw new InsureFailException(message);
            }
        }

        String subStatus = appStatusEnum.getOwnEnum().getValue();
        carInsureVO.setSubStatus(subStatus);
        String insuredStatus = StatusConvertUtil.insuredSubStatusConvert(subStatus);
        carInsureVO.setInsuredStatus(insuredStatus);
        carInsureVO.setFailedMsg(message);
        carInsureVO.setInsOrderNo(carThirdInsureDTO.getInsOrderNo());
        for (ViInsPlcyInfTp tp : carThirdInsureDTO.getPlcyInfList()) {
            List<String> statusList = Arrays.asList(InsuredSubStatusEnum.B2.getValue(), InsuredSubStatusEnum.C1.getValue());
            if (statusList.contains(subStatus)) {
                tp.setIssueTime(LocalDateTime.now());
            }
            if (StrUtil.equals(PolicyTypeEnum.CI.getValue(), tp.getPolicyType())) {
                tp.setProposalNumber(guorenInsureComfirmReqDTO.getProposalNoSubList().get(0).getProposalNoCI());
            }
            if (StrUtil.equals(PolicyTypeEnum.BI.getValue(), tp.getPolicyType())) {
                tp.setProposalNumber(guorenInsureComfirmReqDTO.getProposalNoSubList().get(0).getProposalNoBI());
            }
        }
        carInsureVO.setPlcyInfList(carThirdInsureDTO.getPlcyInfList());
        return carInsureVO;
    }


    @Override
    public InsEnum getSupportIns() {
        return InsEnum.XDCX;
    }

    private void modifyInputDTO(CarThirdInsureDTO inputDTO, GuorenSavePolicyResDTO saveRes) {
        Map<String, ViInsPlcyInfTp> persInfTpMap = inputDTO.getPlcyInfList().stream().collect(Collectors.toMap(ViInsPlcyInfTp::getPolicyType, Function.identity()));
        ViInsPlcyInfTp viInsPlcyInfTpCi = persInfTpMap.get(PolicyTypeEnum.CI.getValue());
        ViInsPlcyInfTp viInsPlcyInfTpBi = persInfTpMap.get(PolicyTypeEnum.BI.getValue());
        if (StringUtils.isNotEmpty(saveRes.getProposalNoDetailVo().getProposalNoCI())) {
            viInsPlcyInfTpCi.setProposalNumber(saveRes.getProposalNoDetailVo().getProposalNoCI());
        }
        if (StringUtils.isNotEmpty(saveRes.getProposalNoDetailVo().getProposalNoBI())) {
            viInsPlcyInfTpBi.setProposalNumber(saveRes.getProposalNoDetailVo().getProposalNoBI());
        }


    }

    public GuorenNoCarOrderRes noCar(CarThirdInsureDTO inputDTO, GuorenChannel prptmain) {
        if (Objects.nonNull(inputDTO.getGiOrder())) {
            if (!inputDTO.getGiOrder().getInsures().isEmpty() && !inputDTO.getGiOrder().getPrds().isEmpty()) {
                GuorenNoCarOrderReq req = new GuorenNoCarOrderReq();
                req.setChannelCode("TSKJ");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                req.setRequestTime(sdf.format(new Date()));
                String uuid = IdUtil.randomUUID();
                req.setOthBusinessNo(uuid);
                GuorenApiNoCarDTO guorenApiNoCarDTO = new GuorenApiNoCarDTO();
                List<GuorenApiNoCarOrderDTO> list = new ArrayList<>();
                GiOrderInfoVO giOrder = inputDTO.getGiOrder();
                for (GiInsurePrdVO prdVO : giOrder.getPrds()) {
                    GuorenApiNoCarOrderDTO guorenApiNoCarOrderDTO = new GuorenApiNoCarOrderDTO();
                    //被保险人信息
                    insuredList(guorenApiNoCarOrderDTO, inputDTO);
                    //投保人信息
                    orderAppl(guorenApiNoCarOrderDTO, inputDTO);
                    //车辆标的信息
                    objCarList(guorenApiNoCarOrderDTO, inputDTO);
                    //订单信息
                    orderMain(guorenApiNoCarOrderDTO, inputDTO, prdVO);
                    //方案信息
                    planList(guorenApiNoCarOrderDTO, inputDTO);
                    //业务员信息
                    salesmanList(guorenApiNoCarOrderDTO, prptmain);
                    list.add(guorenApiNoCarOrderDTO);
                }

                guorenApiNoCarDTO.setOrderList(list);
                req.setData(guorenApiNoCarDTO);
                log.debug("国任===>创建非车订单，{}", JSONUtil.toJsonStr(req));
                GuorenNoCarOrderRes guorenNoCarOrderRes = guorenRpcService.newNoCarOrder(req);
                if (!"0000".equals(guorenNoCarOrderRes.getCode())) {
                    throw new TunsBusinessException(guorenNoCarOrderRes.getMessage());
                }
                log.debug("国任<===创建非车订单，{}", JSONUtil.toJsonStr(guorenNoCarOrderRes));
                inputDTO.setInsOrderNo(guorenNoCarOrderRes.getData().getOrderCode());
                return guorenNoCarOrderRes;
            }
        }
        return null;
    }

    private void insuredList(GuorenApiNoCarOrderDTO guorenApiNoCarOrderDTO, CarThirdInsureDTO inputDTO) {
        PubPersInfTp insuredInfo = inputDTO.getPubPerList().stream()
                .filter(s -> s.getPersonType().equals(ViPersonTypeEnum.INSURED.getValue())).findFirst().get();

        List<GuorenApiNoCarInsuredDTO> guorenApiNoCarInsuredDTOList = new ArrayList<>();
        GuorenApiNoCarInsuredDTO guorenApiNoCarInsuredDTO = new GuorenApiNoCarInsuredDTO();
        guorenApiNoCarInsuredDTO.setCertfNo(insuredInfo.getIdentifyNumber());
        guorenApiNoCarInsuredDTO.setCertfType(GuorenIdentifyTypeEnum.getTunsCodeEnum(insuredInfo.getIdentifyType()).getCode());
        guorenApiNoCarInsuredDTO.setCountryName(insuredInfo.getNationality());
        guorenApiNoCarInsuredDTO.setInsuredName(insuredInfo.getPersonName());
        guorenApiNoCarInsuredDTO.setInsuredType("1".equals(insuredInfo.getNature()) ? "1" : "2");
        guorenApiNoCarInsuredDTO.setPhoneNo(insuredInfo.getMobilePhone());
        guorenApiNoCarInsuredDTO.setPlanNo("1");
        //2025之后创建的商品都需要省市区
        guorenApiNoCarInsuredDTO.setAddrProvince(insuredInfo.getProvinceNumberName());
        guorenApiNoCarInsuredDTO.setAddrCity(insuredInfo.getCityNumberName());
        guorenApiNoCarInsuredDTO.setAddrCounty(insuredInfo.getCountyNumberName());
        guorenApiNoCarInsuredDTO.setAddrStreet(insuredInfo.getAddressDetail());
        guorenApiNoCarInsuredDTO.setFullAddr(insuredInfo.getAddressComplete());
        guorenApiNoCarInsuredDTOList.add(guorenApiNoCarInsuredDTO);
        guorenApiNoCarOrderDTO.setInsuredList(guorenApiNoCarInsuredDTOList);
    }

    private void orderAppl(GuorenApiNoCarOrderDTO guorenApiNoCarOrderDTO, CarThirdInsureDTO inputDTO) {
        PubPersInfTp holderInfo = inputDTO.getPubPerList().stream()
                .filter(s -> s.getPersonType().equals(ViPersonTypeEnum.HOLDER.getValue())).findFirst().get();
//        List<GuorenApiNoCarOrderApplDTO> guorenApiNoCarOrderApplDTOList = new ArrayList<>();
        GuorenApiNoCarOrderApplDTO dto = new GuorenApiNoCarOrderApplDTO();
        dto.setApplType("1".equals(holderInfo.getNature()) ? "1" : "2");
        dto.setApplName(holderInfo.getPersonName());
        dto.setCertfNo(holderInfo.getIdentifyNumber());
        dto.setCertfType(GuorenIdentifyTypeEnum.getTunsCodeEnum(holderInfo.getIdentifyType()).getCode());
        dto.setPhoneNo(holderInfo.getMobilePhone());
        dto.setContactName(holderInfo.getContactName());
        //2025之后创建的商品都需要省市区
        dto.setAddrProvince(holderInfo.getProvinceNumberName());
        dto.setAddrCity(holderInfo.getCityNumberName());
        dto.setAddrCounty(holderInfo.getCountyNumberName());
        dto.setAddrStreet(holderInfo.getAddressDetail());
        dto.setFullAddr(holderInfo.getAddressComplete());
//        guorenApiNoCarOrderApplDTOList.add(dto);
        guorenApiNoCarOrderDTO.setOrderAppl(dto);
    }

    private void objCarList(GuorenApiNoCarOrderDTO guorenApiNoCarOrderDTO, CarThirdInsureDTO inputDTO) {
        ViInsCarInfTp carInfTp = inputDTO.getCarInfTp();
        CmmVehicleTypeRelation vehicleTypeRelation = inputDTO.getVehicleTypeRelation();
        List<GuorenApiNoCarObjCarDTO> objCarList = new ArrayList<>();
        GuorenApiNoCarObjCarDTO objCarDTO = new GuorenApiNoCarObjCarDTO();
        objCarDTO.setCarModels(carInfTp.getModelName());
        objCarDTO.setCarType(carInfTp.getCarTypeCode());
        objCarDTO.setCarTypeCode(inputDTO.getVehicleTypeRelation().getThirdFieldValue(GuorenApiNoCarObjCarDTO.Fields.carTypeCode));
        objCarDTO.setClnt("1".equals(carInfTp.getNewCarMark()) ? "1" : "2");
        objCarDTO.setFrameNo(carInfTp.getFrameNumber());
        //todo fix_26582 国任bug 新车 提交保司，提示车牌号不存在
//        objCarDTO.setLicenseNo(carInfTp.getPlateNumber());
        objCarDTO.setLicenseNo(NewCarMarkEnum.NEW.getValue().equals(carInfTp.getNewCarMark()) ? "新车" : carInfTp.getPlateNumber());
        objCarDTO.setPlanNo("1");
        objCarDTO.setPurchasePrice(carInfTp.getPurchasePrice().toString());
        objCarDTO.setSeatNum(carInfTp.getSeatCount().toString());
        objCarDTO.setToncount(carInfTp.getCarTonnage().toString());
        objCarDTO.setCarCategory(inputDTO.getVehicleTypeRelation().getThirdFieldValue(GuorenApiNoCarObjCarDTO.Fields.carCategory));
        //2025之后创建的商品都需要省市区
        PubPersInfTp holderInfo = inputDTO.getPubPerList().stream()
                .filter(s -> s.getPersonType().equals(ViPersonTypeEnum.HOLDER.getValue())).findFirst().get();
        objCarDTO.setAddrProvince(holderInfo.getProvinceNumberName());
        objCarDTO.setAddrCity(holderInfo.getCityNumberName());
        objCarDTO.setAddrCounty(holderInfo.getCountyNumberName());
        objCarDTO.setAddrStreet(holderInfo.getAddressDetail());
        objCarDTO.setFullAddr(holderInfo.getAddressComplete());
        objCarList.add(objCarDTO);
        guorenApiNoCarOrderDTO.setObjCarList(objCarList);
    }

    private void orderMain(GuorenApiNoCarOrderDTO guorenApiNoCarOrderDTO, CarThirdInsureDTO inputDTO, GiInsurePrdVO prdVO) {
        //通过我方 购买的非车商品编码 查找保司商品
        GiOrderInsureVO giOrderInsureVO = inputDTO.getGiOrder().getInsures().stream()
                .filter(s -> s.getProdName().equals(prdVO.getProdName())).findFirst().get();
        GuorenApiNoCarCommodityReq guorenApiNoCarCommodityReq = new GuorenApiNoCarCommodityReq();
        GuorenApiNoCarCommodityReqDTO reqDTO = new GuorenApiNoCarCommodityReqDTO();
        reqDTO.setGoodsCode(prdVO.getProdCode());
        guorenApiNoCarCommodityReq.setData(reqDTO);
        guorenApiNoCarCommodityReq.setRequestTime(String.valueOf(System.currentTimeMillis()));
        guorenApiNoCarCommodityReq.setSystemCode("TSKJ");
        log.debug("国任===>查询非车商品信息，{}", JSONUtil.toJsonStr(guorenApiNoCarCommodityReq));
        GuorenApiNoCarCommodityResDTO guorenApiNoCarCommodityRes = guorenRpcService.noCarCommodity(guorenApiNoCarCommodityReq);
        log.debug("国任<===查询非车商品信息，{}", JSONUtil.toJsonStr(guorenApiNoCarCommodityRes));
        if ("1".equals(guorenApiNoCarCommodityRes.getCode())) {
            throw new InsureFailException("国任API查询非车商品接口报错！");
        }
        GuorenApiNoCarOrderMainDTO dto = new GuorenApiNoCarOrderMainDTO();
        //通过查出的商品  循环找出 我方购买的 产品
        ViInsApiConfDTO conf = ApiConfContext.getConf();
        String defaultMark = conf.getChannelConf().getDefaultMark();
        for (GuorenApiProdListDTO prodListDTO : guorenApiNoCarCommodityRes.getData().getProdList()) {
            if (prodListDTO.getProdCode().equals(prdVO.getPlanCode())) {
                BigDecimal amount = BigDecimal.valueOf(Long.parseLong(prodListDTO.getAmount())).multiply(BigDecimal.valueOf(prdVO.getCopies()));
                dto.setAmount(amount.toString());
                dto.setEndDate(giOrderInsureVO.getEndDate());
                dto.setGoodsCode(prdVO.getProdCode());
                dto.setOthOrderNo(inputDTO.getPubInsPlcy().getInsPremiumNumber());
                BigDecimal premium = BigDecimal.valueOf(Long.parseLong(prodListDTO.getPremium())).multiply(BigDecimal.valueOf(prdVO.getCopies()));
                //特殊产品要乘以座位数
                if ("02746400001001".equals(prdVO.getProdCode())){
                    premium = premium.multiply(BigDecimal.valueOf(inputDTO.getCarInfTp().getSeatCount()));
                }
                dto.setPremium(premium.toString());
                dto.setProdCode(prodListDTO.getProdCode());
                //默认新保
                dto.setRenewalInd("0");
                List<String> riskCodeList = new ArrayList<>();
                for (GuorenApiProdRiskListDTO riskListDTO : prodListDTO.getProdRiskList()) {
                    riskCodeList.add(riskListDTO.getRiskCode());
                }
                dto.setRiskCode(riskCodeList);
                //提交核保 有商业取商业时间
                Map<String, ViInsPlcyInfTp> viInsPlcyInfTpMap = inputDTO.getPlcyInfList().stream().collect(Collectors.toMap(ViInsPlcyInfTp::getPolicyType, Function.identity()));
                ViInsPlcyInfTp ci = viInsPlcyInfTpMap.get(PolicyTypeEnum.CI.getValue());
                ViInsPlcyInfTp bi = viInsPlcyInfTpMap.get(PolicyTypeEnum.BI.getValue());
                if (Objects.nonNull(bi)) {
                    //判断是否及时起保
                    timelyTimeHandle(bi, dto);
                } else {
                    timelyTimeHandle(ci, dto);
                }
                dto.setUwCount(prdVO.getCopies().toString());
                dto.setBusinessMode("6");
                dto.setBusinessSource(defaultMark);
            }
        }
        guorenApiNoCarOrderDTO.setOrderMain(dto);

    }

    private static void timelyTimeHandle(ViInsPlcyInfTp insPlcyInfTp, GuorenApiNoCarOrderMainDTO dto) {
        //判断是否及时起保
        String startHour = SpiderDateUtil.splitTime(insPlcyInfTp.getInsBegin(), SpiderDateUtil.SPLIT_HOUR);
        if ("00".equals(startHour)) {
            dto.setStartDate(insPlcyInfTp.getInsBegin());
            dto.setEndDate(insPlcyInfTp.getInsEnd());
        } else {
            //及时起保：加一天，重置时分秒为00
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime dateTime = LocalDateTime.parse(insPlcyInfTp.getInsBegin(), formatter);
            LocalDateTime localDateTime = dateTime.plusDays(1).withHour(0).withMinute(0).withSecond(0);
            String date = LocalDateTimeUtil.formatNormal(localDateTime);
            dto.setStartDate(date);

            //起保时间加一年减一秒
            LocalDateTime endTime = LocalDateTime.parse(date, formatter);
            LocalDateTime endDateTime = endTime.plusYears(1).minusSeconds(1);
            dto.setEndDate(LocalDateTimeUtil.formatNormal(endDateTime));
        }
        log.info("修复前---非车修复时间：起保时间：" + insPlcyInfTp.getInsBegin() + " 终保时间：" + insPlcyInfTp.getInsEnd());
        log.info("修复后---非车修复时间：起保时间：" + dto.getStartDate() + " 终保时间：" + dto.getEndDate());
    }

    private void planList(GuorenApiNoCarOrderDTO guorenApiNoCarOrderDTO, CarThirdInsureDTO inputDTO) {

        List<GuorenApiNoCarPlanDTO> planList = new ArrayList<>();
        GuorenApiNoCarPlanDTO dto = new GuorenApiNoCarPlanDTO();
        dto.setOccupationCode("0500020");
        dto.setOccupationGrade("1");
        dto.setOccupationName("其他");
        dto.setPlanNo("1");
        dto.setQuantity("1");
        dto.setSocialSecInd("01");
        planList.add(dto);
        guorenApiNoCarOrderDTO.setPlanList(planList);
    }

    private void salesmanList(GuorenApiNoCarOrderDTO guorenApiNoCarOrderDTO, GuorenChannel prptmain) {
        List<GuorenApiNoCarSalesmanDTO> salesmanList = new ArrayList<>();
        GuorenApiNoCarSalesmanDTO dto = new GuorenApiNoCarSalesmanDTO();
        dto.setAgentCode(prptmain.getAgentCode());
        dto.setAgentCnrtNo(prptmain.getAgreementNo());

        dto.setCompanyCode(prptmain.getComCode());
        dto.setSalesmanCode(prptmain.getHandlerCode());
        dto.setSalesmanFlag("1");
        dto.setSalesmanName(prptmain.getHandlerName());
        dto.setSerialNo("1");
        dto.setTeamCode("");
        dto.setTeamName("");
        salesmanList.add(dto);
        guorenApiNoCarOrderDTO.setSalesmanList(salesmanList);
    }

    /**
     * 投保单保存
     *
     * @param inputDTO
     */
    public GuorenSavePolicyResDTO policyTpSave(CarThirdInsureDTO inputDTO, GuorenNoCarOrderRes guorenNoCarOrderRes) {
        //投保单保存
        GuorenSavePolicyReqDTO guorenSavePolicyReqDTO = new GuorenSavePolicyReqDTO();
        guorenSavePolicyReqDTO.setUuid(inputDTO.getPubInsPlcy().getInsPremiumNumber());
        if (Objects.nonNull(guorenNoCarOrderRes)) {
            guorenSavePolicyReqDTO.setOrder(guorenNoCarOrderRes.getData().getOrderCode());
        }
        GuorenPrpTinsuredDTO guorenPrpTinsuredDTO = new GuorenPrpTinsuredDTO();
        Map<String, AreaDTO> areaDTO = inputDTO.getAreaDTO();
        Map<String, PubPersInfTp> personMap = inputDTO.getPubPerList().stream().collect(Collectors.toMap(PubPersInfTp::getPersonType, Function.identity()));
        PubPersInfTp holder = personMap.get(ViPersonTypeEnum.HOLDER.getValue());
        PubPersInfTp insured = personMap.get(ViPersonTypeEnum.INSURED.getValue());
        PubPersInfTp owner = personMap.get(ViPersonTypeEnum.OWNER.getValue());
        guorenPrpTinsuredDTO.setAppliUnitNature(GuoRenUnitNatureEnum.getGuorenRelationTypeEnum(holder.getNature()));
        guorenPrpTinsuredDTO.setAppliName(holder.getPersonName());
        guorenPrpTinsuredDTO.setAppliInsuredNature(GuorenPersonNatureEnum.getGuorenRelationTypeEnum(holder.getNature()));
        guorenPrpTinsuredDTO.setAppliIdentifyType(GuorenIdentifyTypeEnum.getTunsCodeEnum(holder.getIdentifyType()).getCode());
        guorenPrpTinsuredDTO.setAppliIdentifyNumber(holder.getIdentifyNumber());
        guorenPrpTinsuredDTO.setAppliMobile(holder.getMobilePhone());
        guorenPrpTinsuredDTO.setAppliAddress(holder.getAddressComplete());
        guorenPrpTinsuredDTO.setAppliPostCode(holder.getPostCode());
        AreaDTO holderArea = areaDTO.get(holder.getPersonType());
        guorenPrpTinsuredDTO.setAppliProvinceName(holderArea.getProvinceName());
        guorenPrpTinsuredDTO.setAppliCityName(holderArea.getCityName());
        guorenPrpTinsuredDTO.setAppliIDValidStartDate(holder.getIdentityValidityStart());
        guorenPrpTinsuredDTO.setAppliEmail(holder.getEmail());
        guorenPrpTinsuredDTO.setAppliIDValidDate(holder.getIdentityValidity());
        String holderLongFlag = holder.getIdentityValidity().contains("2099") ? YesNoNumberEnum.YES.getValue() : YesNoNumberEnum.NO.getValue();
        if (PersonNatureEnum.PERSON.getValue().equals(holder.getNature())) {
            guorenPrpTinsuredDTO.setAppliValidDateLongFlag(holderLongFlag);
        } else {
            guorenPrpTinsuredDTO.setAppliUnitcodeTimes(holder.getIdentityValidity());
            guorenPrpTinsuredDTO.setAppliUnitcodeTimesLongFlag(holderLongFlag);//投保人为个人是必传 0:非长期有效，1：长期有效
        }
        guorenPrpTinsuredDTO.setInsuredUnitNature(GuoRenUnitNatureEnum.getGuorenRelationTypeEnum(insured.getNature()));
        guorenPrpTinsuredDTO.setInsuredName(insured.getPersonName());
        guorenPrpTinsuredDTO.setInsuredInsuredNature(GuorenPersonNatureEnum.getGuorenRelationTypeEnum(insured.getNature()));
        guorenPrpTinsuredDTO.setInsuredMobile(insured.getMobilePhone());
        guorenPrpTinsuredDTO.setInsuredIdentifyType(GuorenIdentifyTypeEnum.getTunsCodeEnum(insured.getIdentifyType()).getCode());
        guorenPrpTinsuredDTO.setInsuredIdentifyNumber(insured.getIdentifyNumber());
        guorenPrpTinsuredDTO.setInsuredAddress(insured.getAddressComplete());
        guorenPrpTinsuredDTO.setInsuredPostCode(insured.getPostCode());
        AreaDTO insuredArea = areaDTO.get(insured.getPersonType());
        guorenPrpTinsuredDTO.setInsuredProvinceName(insuredArea.getProvinceName());
        guorenPrpTinsuredDTO.setInsuredEmail(insured.getEmail());
        guorenPrpTinsuredDTO.setInsuredCityName(insuredArea.getCityName());
        guorenPrpTinsuredDTO.setInsuredIDValidStartDate(insured.getIdentityValidityStart());
        guorenPrpTinsuredDTO.setInsuredIDValidDate(insured.getIdentityValidity());
        //被保人为个人是必传 0:非长期有效，1：长期有效
        String insuredLongFlag = insured.getIdentityValidity().contains("2099") ? YesNoNumberEnum.YES.getValue() : YesNoNumberEnum.NO.getValue();
        if (PersonNatureEnum.PERSON.getValue().equals(insured.getNature())) {
            guorenPrpTinsuredDTO.setInsuredValidDateLongFlag(insuredLongFlag);
        } else {
            guorenPrpTinsuredDTO.setInsuredUnitcodeTimes(insured.getIdentityValidity());
            guorenPrpTinsuredDTO.setInsuredUnitcodeTimesLongFlag(insuredLongFlag);
        }
        guorenPrpTinsuredDTO.setAppliNationality("中国");
        ViInsCarInfTp carInfTp = inputDTO.getCarInfTp();
        guorenPrpTinsuredDTO.setOcrOwnerAddress(carInfTp.getLicenseAddress());
        GuorenChannelResDTO guorenChannelResDTO = guorenChannelQueryService.queryChannel();
        // 封装特别约定
        List<GuorenPrptengage> biAgreement = buildBiAgreement(inputDTO.getSpecialAgreementDtoList(), guorenChannelResDTO.getPrptmain().getComCode());
        if (CollUtil.isNotEmpty(biAgreement)) {
            guorenSavePolicyReqDTO.setBiPrptenages(TunsBeanUtil.copyList(biAgreement, GuorenClauseDTO.class));
        }
        List<GuorenPrptengage> ciAgreement = buildCiAgreement(inputDTO.getSpecialAgreementDtoList(), guorenChannelResDTO.getPrptmain().getComCode());
        if (CollUtil.isNotEmpty(ciAgreement)) {
            guorenSavePolicyReqDTO.setCiPrptenages(TunsBeanUtil.copyList(ciAgreement, GuorenClauseDTO.class));
        }
        guorenSavePolicyReqDTO.setPrpTinsured(guorenPrpTinsuredDTO);
        log.debug("国任===>投保单保存，{}", JSONUtil.toJsonStr(guorenSavePolicyReqDTO));
        GuorenSavePolicyDataResDTO guorenSavePolicyDataResDTO = guorenRpcService.savePolicy(guorenSavePolicyReqDTO);
        log.debug("国任《===投保单保存，{}", JSONUtil.toJsonStr(guorenSavePolicyDataResDTO));
        if ("1".equals(guorenSavePolicyDataResDTO.getResultCode())) {
            throw new TunsBusinessException(guorenSavePolicyDataResDTO.getResultMsg());
        }
        return guorenSavePolicyDataResDTO.getData();
    }

    private List<GuorenPrptengage> buildAgreements(GuorenQueryClauseReq guorenQueryClauseReq, List<ViQuoteSpecialAgreement> agreements) {
        GuorenClauseQueryRes guorenClauseQueryRes = guorenRpcService.queryClause(guorenQueryClauseReq);
        if (Objects.isNull(guorenClauseQueryRes) || CollUtil.isEmpty(guorenClauseQueryRes.getData())) {
            return null;
        }
        List<GuorenClauseDTO> data = guorenClauseQueryRes.getData();
        return agreements.stream().map(x -> {
            Optional<GuorenClauseDTO> first = data.stream().filter(o -> o.getClauseCode().equals(x.getEngageCode())).findFirst();
            return buildPprtengage(x, first);
        }).collect(Collectors.toList());
    }

    private GuorenPrptengage buildPprtengage(ViQuoteSpecialAgreement x, Optional<GuorenClauseDTO> first) {
        if (first.isPresent()) {
            GuorenClauseDTO insClause = first.get();
            GuorenPrptengage engage = new GuorenPrptengage();
            engage.setClausesContext(x.getEngageContent());
            engage.setFlag(insClause.getFlag());
            engage.setClauseCode(insClause.getClauseCode());
            engage.setClauses(insClause.getClauses());
            return engage;
        }
        return null;
    }

    public List<GuorenPrptengage> buildCiAgreement(List<ViQuoteSpecialAgreement> specialAgreementDtoList, String comCode) {
        if (CollUtil.isNotEmpty(specialAgreementDtoList)) {
            GuorenQueryClauseReq guorenQueryClauseReq = new GuorenQueryClauseReq();
            guorenQueryClauseReq.setRiskCode("0507");
            guorenQueryClauseReq.setComCode(comCode);
            List<ViQuoteSpecialAgreement> ciAgreements = specialAgreementDtoList.stream().filter(x -> PolicyTypeEnum.CI == x.getRecordType()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(ciAgreements)) {
                return buildAgreements(guorenQueryClauseReq, ciAgreements);
            }
        }
        return null;
    }

    public List<GuorenPrptengage> buildBiAgreement(List<ViQuoteSpecialAgreement> specialAgreementDtoList, String comCode) {
        if (CollUtil.isNotEmpty(specialAgreementDtoList)) {
            GuorenQueryClauseReq guorenQueryClauseReq = new GuorenQueryClauseReq();
            guorenQueryClauseReq.setRiskCode("0518");
            guorenQueryClauseReq.setComCode(comCode);
            List<ViQuoteSpecialAgreement> biAgreements = specialAgreementDtoList.stream().filter(x -> PolicyTypeEnum.BI == x.getRecordType()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(biAgreements)) {
                return buildAgreements(guorenQueryClauseReq, biAgreements);
            }
        }
        return null;
    }

    private GuorenInsureComfirmResultResDataDTO flushPolicyStatus(GuorenInsureComfirmReqDTO guorenInsureComfirmReqDTO){
        //睡眠2秒中，查询投保单信息
        ThreadUtil.sleep(2, TimeUnit.SECONDS);
        GuorenProposalReq req = new GuorenProposalReq();
        List<String> proposalNoList = new ArrayList<>();
        String proposalNoCI = "";
        String proposalNoBI = "";
        for (GuorenProposalNoSubDTO subDTO : guorenInsureComfirmReqDTO.getProposalNoSubList()) {
            if(StringUtils.isNotBlank(subDTO.getProposalNoCI())){
                proposalNoList.add(subDTO.getProposalNoCI());
                proposalNoCI = subDTO.getProposalNoCI();
            }
            if(StringUtils.isNotBlank(subDTO.getProposalNoBI())){
                proposalNoList.add(subDTO.getProposalNoBI());
                proposalNoBI = subDTO.getProposalNoBI();
            }
        }
        req.setProposalNos(proposalNoList);
        log.debug("国任===>超时查询保单状态，{}", JSONUtil.toJsonStr(req));
        GuorenProposalStatusDataRes guorenProposalStatusDataRes = guorenRpcService.proposalStatus(req);
        log.debug("国任<===超时查询保单状态，{}", JSONUtil.toJsonStr(guorenProposalStatusDataRes));
        if ("1".equals(guorenProposalStatusDataRes.getResultCode())) {
            log.error("国任保险接口调用异常，异常信息:核保失败【状态刷新】");
            throw new TunsBusinessException("国任保险接口调用异常，异常信息:核保失败");
        }
        GuorenInsureComfirmResultResDataDTO resultResDataDTO = new GuorenInsureComfirmResultResDataDTO();
        List<GuorenInsureComfirmResultResDTO> data = new ArrayList<>();
        GuorenInsureComfirmResultResDTO resultResDTO = new GuorenInsureComfirmResultResDTO();
        for(GuorenProposalStatusRes res : guorenProposalStatusDataRes.getData()){
            if(res.getProposalNo().equals(proposalNoCI)){
                resultResDTO.setUnderWriteFlagCI(res.getUnderwriteflag());
            }
            if(res.getProposalNo().equals(proposalNoBI)){
                resultResDTO.setUnderWriteFlagBI(res.getUnderwriteflag());
            }
            resultResDTO.setMessage(guorenProposalStatusDataRes.getResultMsg());
        }
        data.add(resultResDTO);
        resultResDataDTO.setData(data);
        return resultResDataDTO;
    }
}
