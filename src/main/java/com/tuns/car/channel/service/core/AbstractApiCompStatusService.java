package com.tuns.car.channel.service.core;

import com.alibaba.fastjson.JSON;
import com.tuns.car.channel.dto.APIGetStatusDTO;
import com.tuns.car.core.dto.CompStatusInfoDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * api查询保司保单状态详情 流程抽象实现，负责编排整体的保单状态查询流程
 * 具体的每家保险公司查询保司保单详情对接，需要子类实现本类方法。
 * {@link Req} 保险公司保单详情接口 请求数据结构
 * {@link Res} 保险公司保单详情接口 响应数据结构
 *
 * <AUTHOR>
 * @date 2022/6/7 11:15
 */
@Slf4j
public abstract class AbstractApiCompStatusService<Req, Res> implements ApiCompStatusInfoService, ApiInvoke<APIGetStatusDTO, CompStatusInfoDTO, Req, Res> {
    @Override
    public CompStatusInfoDTO queryPolicyStatus(APIGetStatusDTO apiGetStatusDTO) {
        Req req = mapReq(apiGetStatusDTO);
        log.info(getSupportIns().getCompanyName() + "===>状态查询：{}", JSON.toJSONString(req));
        Res res = doInvoke(req, apiGetStatusDTO);
        log.info(getSupportIns().getCompanyName() + "<===状态查询：{}", JSON.toJSONString(res));
        CompStatusInfoDTO result = mapRes(res, req, apiGetStatusDTO);
        return result;
    }
}
