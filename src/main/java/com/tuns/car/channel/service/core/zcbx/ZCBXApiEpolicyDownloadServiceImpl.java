package com.tuns.car.channel.service.core.zcbx;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.plumelog.core.TraceId;
import com.tuns.car.channel.constants.zcbx.ZCBXFileTypeCodeEnum;
import com.tuns.car.channel.dto.conf.ViInsApiConfDTO;
import com.tuns.car.channel.dto.zcbx.req.ZCBXEpolicyDownloadReq;
import com.tuns.car.channel.dto.zcbx.res.ZCBXEpolicyDTO;
import com.tuns.car.channel.dto.zcbx.res.ZCBXEpolicyDownloadBodyRes;
import com.tuns.car.channel.rpc.zcbx.ZCBXXmlRpcService;
import com.tuns.car.channel.service.core.ApiEpolicyDownloadService;
import com.tuns.car.channel.util.ApiConfContext;
import com.tuns.car.core.constant.*;
import com.tuns.car.core.dto.carprocess.premium.CarEPolicyDTO;
import com.tuns.car.core.dto.carprocess.premium.EpolicyDTO;
import com.tuns.car.core.dto.gi.NoCarPolicyDTO;
import com.tuns.car.core.entity.PubPersInf;
import com.tuns.car.core.entity.ViInsPlcyInf;
import com.tuns.core.boot.exception.TunsBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-06-04 14:51
 **/
@Slf4j
@Service
public class ZCBXApiEpolicyDownloadServiceImpl implements ApiEpolicyDownloadService {

    @Autowired
    @Qualifier("ZCBX_EPOLICY_DOWNLOAD")
    private ThreadPoolExecutor executor;

    @Autowired
    private ZCBXXmlRpcService zcbxXmlRpcService;

    @Override
    public List<EpolicyDTO> epolicyDownload(CarEPolicyDTO carEpolicyDTO) {
        try {
            //1.构建下载任务
            List<Callable<ZCBXEpolicyDTO>> callables = buildEpolicyDownloadTask(carEpolicyDTO);
            //2.执行任务
            List<Future<ZCBXEpolicyDTO>> futures = executor.invokeAll(callables);
            //3.获取结果
            return buildResult(carEpolicyDTO, futures);
        } catch (InterruptedException e) {
            log.error("下载电子保单 等待结果时主线程发生阻塞，异常信息：", e);
            Thread.currentThread().interrupt(); // 重新设置中断状态
            throw new TunsBusinessException("系统发生未知异常，请稍后再试");
        } catch (Exception e) {
            log.error("下载电子保单 等待结果时主线程发生阻塞，异常信息：", e);
            throw new TunsBusinessException("系统发生未知异常，请稍后再试");
        }
    }

    private List<EpolicyDTO> buildResult(CarEPolicyDTO carEpolicyDTO, List<Future<ZCBXEpolicyDTO>> futures) throws InterruptedException, ExecutionException {
        List<EpolicyDTO> epolicyList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(futures)) {
            Long policyBatchId = carEpolicyDTO.getViInsPlcyInfs().get(0).getPolicyBatchId();
            for (Future<ZCBXEpolicyDTO> future : futures) {
                if (Objects.nonNull(future.get())) {
                    ZCBXEpolicyDTO zcbxEpolicyDTO = future.get();
                    if (StrUtil.isNotBlank(zcbxEpolicyDTO.getPdf())) {
                        EpolicyDTO epolicyDTO = new EpolicyDTO();
                        epolicyDTO.setCompanyId(carEpolicyDTO.getCompanyId());
                        epolicyDTO.setPolicyBatchId(policyBatchId);
                        epolicyDTO.setChanDetailId(carEpolicyDTO.getChanDetailId());
                        epolicyDTO.setType(zcbxEpolicyDTO.getType());
                        epolicyDTO.setBase64(zcbxEpolicyDTO.getPdf());
                        epolicyList.add(epolicyDTO);
                    }
                }
            }
        }
        return epolicyList;
    }

    private List<Callable<ZCBXEpolicyDTO>> buildEpolicyDownloadTask(CarEPolicyDTO carEpolicyDTO) {
        List<Callable<ZCBXEpolicyDTO>> callables = new ArrayList<>();
        ViInsApiConfDTO conf = ApiConfContext.getConf();
        String traceId = TraceId.logTraceID.get();
        Map<String, PubPersInf> pubPersInfMap = carEpolicyDTO.getPubPersInfs().stream().collect(Collectors.toMap(PubPersInf::getPersonType, Function.identity()));
        PubPersInf holder = pubPersInfMap.get(ViPersonTypeEnum.HOLDER.getValue());
        //是否下载电子投保单
        boolean downloadProposal = !PersonNatureEnum.PERSON.getValue().equals(holder.getNature());
        carEpolicyDTO.getViInsPlcyInfs().forEach(viInsPlcyInf -> {
            callables.add(buildCiOrBiTask(viInsPlcyInf, conf, traceId));
            if(downloadProposal){
                callables.add(buildCiOrBiProposalTask(viInsPlcyInf, conf, traceId));
            }
            if (PolicyTypeEnum.CI.getValue().equals(viInsPlcyInf.getPolicyType())) {
                callables.add(buildCiLabelTask(viInsPlcyInf, conf, traceId));
            }
        });
        //非车
        if (CollectionUtil.isNotEmpty(carEpolicyDTO.getNoCarPolicyList())) {
            callables.add(buildNoCarTask(carEpolicyDTO, conf, traceId));
        }
        return callables;
    }

    /***
     * 交强或商业电子保单下载任务
     * @param viInsPlcyInf
     * @param conf
     * @param traceId
     * @return
     */
    private Callable<ZCBXEpolicyDTO> buildCiOrBiTask(ViInsPlcyInf viInsPlcyInf, ViInsApiConfDTO conf, String traceId) {
        return () -> {
            TraceId.logTraceID.set(traceId);
            ApiConfContext.setConf(conf);
            ZCBXEpolicyDownloadReq.BaseDto baseDto = new ZCBXEpolicyDownloadReq.BaseDto();
            baseDto.setProposalNo(viInsPlcyInf.getProposalNumber());
            baseDto.setPolicyNo(viInsPlcyInf.getPolicyNumber());
            baseDto.setFileType(ZCBXFileTypeCodeEnum.FILETYPE_1.getTpCode());
            ZCBXEpolicyDownloadReq downloadReq = new ZCBXEpolicyDownloadReq();
            downloadReq.setBaseDto(baseDto);
            ZCBXEpolicyDownloadBodyRes epolicyDownload = zcbxXmlRpcService.epolicyDownload(downloadReq);
            String pdf = epolicyDownload.getBodyDto().getFileDto().getPdf();
            if (PolicyTypeEnum.CI.getValue().equals(viInsPlcyInf.getPolicyType())) {
                return new ZCBXEpolicyDTO(ViAttachTypeEnum.CIINSURANCE, pdf);
            } else {
                return new ZCBXEpolicyDTO(ViAttachTypeEnum.BIINSURANCE, pdf);
            }
        };
    }

    /**
     * 交强或商业电子投保单下载任务
     * @param viInsPlcyInf
     * @param conf
     * @param traceId
     * @return
     */
    private Callable<ZCBXEpolicyDTO> buildCiOrBiProposalTask(ViInsPlcyInf viInsPlcyInf, ViInsApiConfDTO conf, String traceId) {
        return () -> {
            TraceId.logTraceID.set(traceId);
            ApiConfContext.setConf(conf);
            ZCBXEpolicyDownloadReq.BaseDto baseDto = new ZCBXEpolicyDownloadReq.BaseDto();
            baseDto.setProposalNo(viInsPlcyInf.getProposalNumber());
            baseDto.setPolicyNo(viInsPlcyInf.getPolicyNumber());
            baseDto.setFileType(ZCBXFileTypeCodeEnum.FILETYPE_6.getTpCode());
            ZCBXEpolicyDownloadReq downloadReq = new ZCBXEpolicyDownloadReq();
            downloadReq.setBaseDto(baseDto);
            ZCBXEpolicyDownloadBodyRes epolicyDownload = zcbxXmlRpcService.epolicyDownload(downloadReq);
            String pdf = epolicyDownload.getBodyDto().getFileDto().getPdf();
            if (PolicyTypeEnum.CI.getValue().equals(viInsPlcyInf.getPolicyType())) {
                return new ZCBXEpolicyDTO(ViAttachTypeEnum.ELECTRONICS_CI, pdf);
            } else {
                return new ZCBXEpolicyDTO(ViAttachTypeEnum.ELECTRONICS_BI, pdf);
            }
        };
    }

    /**
     * 交强险标志下载任务
     * @param viInsPlcyInf
     * @param conf
     * @param traceId
     * @return
     */
    private Callable<ZCBXEpolicyDTO> buildCiLabelTask(ViInsPlcyInf viInsPlcyInf, ViInsApiConfDTO conf, String traceId) {
        return () -> {
            TraceId.logTraceID.set(traceId);
            ApiConfContext.setConf(conf);
            ZCBXEpolicyDownloadReq.BaseDto baseDto = new ZCBXEpolicyDownloadReq.BaseDto();
            baseDto.setProposalNo(viInsPlcyInf.getProposalNumber());
            baseDto.setPolicyNo(viInsPlcyInf.getPolicyNumber());
            baseDto.setFileType(ZCBXFileTypeCodeEnum.FILETYPE_2.getTpCode());
            ZCBXEpolicyDownloadReq downloadReq = new ZCBXEpolicyDownloadReq();
            downloadReq.setBaseDto(baseDto);
            ZCBXEpolicyDownloadBodyRes epolicyDownload = zcbxXmlRpcService.epolicyDownload(downloadReq);
            String pdf = epolicyDownload.getBodyDto().getFileDto().getPdf();
            return new ZCBXEpolicyDTO(ViAttachTypeEnum.CILABEL, pdf);
        };
    }

    /**
     * 非车险电子保单下载任务
     * @param carEpolicyDTO
     * @param conf
     * @param traceId
     * @return
     */
    private Callable<ZCBXEpolicyDTO> buildNoCarTask(CarEPolicyDTO carEpolicyDTO, ViInsApiConfDTO conf, String traceId) {
        return () -> {
            TraceId.logTraceID.set(traceId);
            ApiConfContext.setConf(conf);
            NoCarPolicyDTO noCarPolicyDTO = carEpolicyDTO.getNoCarPolicyList().get(0);
            ZCBXEpolicyDownloadReq.BaseDto baseDto = new ZCBXEpolicyDownloadReq.BaseDto();
            baseDto.setProposalNo(noCarPolicyDTO.getProposalNo());
            baseDto.setFileType(ZCBXFileTypeCodeEnum.FILETYPE_1.getTpCode());
            ZCBXEpolicyDownloadReq downloadReq = new ZCBXEpolicyDownloadReq();
            downloadReq.setBaseDto(baseDto);
            ZCBXEpolicyDownloadBodyRes epolicyDownload = zcbxXmlRpcService.epolicyDownload(downloadReq);
            String pdf = epolicyDownload.getBodyDto().getFileDto().getPdf();
            return new ZCBXEpolicyDTO(ViAttachTypeEnum.NOCAR_E_POLICY, pdf);
        };
    }

    @Override
    public InsEnum getSupportIns() {
        return InsEnum.ZCBX;
    }
}
