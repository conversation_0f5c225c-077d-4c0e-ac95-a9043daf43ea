package com.tuns.car.channel.service.core.zcbx;

import cn.hutool.core.collection.CollectionUtil;
import com.tuns.car.channel.constants.zcbx.ZCBXErrorCodeEnum;
import com.tuns.car.channel.dto.zcbx.req.*;
import com.tuns.car.channel.dto.zcbx.res.ZCBXImageUploadResDTO;
import com.tuns.car.channel.rpc.zcbx.ZCBXJsonRpcService;
import com.tuns.car.channel.service.core.ApiUploadImageService;
import com.tuns.car.channel.util.TunsImageUtil;
import com.tuns.car.core.constant.InsEnum;
import com.tuns.car.core.dto.car.ViCompanyImageMappingDTO;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import com.tuns.car.channel.dto.UploadImageDTO;
import com.tuns.core.boot.exception.TunsBusinessException;
import com.tuns.core.thirdparty.service.IOSSService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-08-23 11:37
 **/

@Slf4j
@Service
public class ZCBXApiUploadImageServiceImpl implements ApiUploadImageService {

    @Autowired
    private ZCBXJsonRpcService zcbxJsonRpcService;

    @Autowired
    private IOSSService iossService;


    @Override
    public void uploadImage(UploadImageDTO uploadImageDTO) {
        try {
            //构造上传任务
            doUpload(uploadImageDTO);
        } catch (Exception e) {
            log.error("上传影像执行过程失败", e);
            throw new TunsBusinessException("上传影像执行过程失败", e);
        }
    }

    /**
     * 上传影像
     */
    public void doUpload(UploadImageDTO uploadImageDTO) {
        //交强商业分开上传影像资料
        for (ViInsPlcyInfTp viInsPlcyInf : uploadImageDTO.getViInsPlcyInfTps()) {
            List<ZCBXFileStreamDTO> zcbxFileStreamList = buildFileStreamList(uploadImageDTO);
            ZCBXImageUploadReqDTO zcbxImageUploadReqDTO = buildImageUploadReq(viInsPlcyInf, zcbxFileStreamList);
            ZCBXImageUploadResDTO zcbxImageUploadResDTO = zcbxJsonRpcService.attachUpload(zcbxImageUploadReqDTO);
            if (!ZCBXErrorCodeEnum.SUCCESS.getCode().equals(zcbxImageUploadResDTO.getErrorCode())) {
                log.error("众诚影像上传失败：{}={}", zcbxImageUploadResDTO.getErrorCode(), zcbxImageUploadResDTO.getErrorMessage());
            }
        }
    }

    private List<ZCBXFileStreamDTO> buildFileStreamList(UploadImageDTO inputDTO) {
        List<ZCBXFileStreamDTO> zcbxFileStreamList = new ArrayList<>();
        Map<String, List<ViCompanyImageMappingDTO>> viCompanyImageMappingMap = inputDTO.getViCompanyImageMappingMap();
        inputDTO.getAttaches().forEach(attach -> {
            ZCBXFileStreamDTO fileStreamDTO;
            List<ViCompanyImageMappingDTO> viCompanyImageMappingList = viCompanyImageMappingMap.get(attach.getType().getValue());
            //如果是其他并且验车情况为已验车需要往验车证明里面传
            if (CollectionUtil.isNotEmpty(viCompanyImageMappingList)) {
                ViCompanyImageMappingDTO viCompanyImageMappingDTO = viCompanyImageMappingList.get(0);
                fileStreamDTO = new ZCBXFileStreamDTO();
                fileStreamDTO.setFileType(viCompanyImageMappingDTO.getInsFieldFirstLevelCode());
                fileStreamDTO.setFileName(attach.getFileName());
                fileStreamDTO.setFileStream(TunsImageUtil.compressToBase64(iossService.download(attach.getKey())));
                zcbxFileStreamList.add(fileStreamDTO);
            }
        });
        return zcbxFileStreamList;
    }

    private static ZCBXImageUploadReqDTO buildImageUploadReq(ViInsPlcyInfTp viInsPlcyInfTp, List<ZCBXFileStreamDTO> zcbxFileStreamDTOS) {
        ZCBXImageUploadReqDTO zcbxImageUploadReqDTO = new ZCBXImageUploadReqDTO();
        ZCBXImageUploadDTO imageUploadDTO = new ZCBXImageUploadDTO();
        ZCBXImageUploadDTO.ActionDTO actionDTO = new ZCBXImageUploadDTO.ActionDTO();
        actionDTO.setBusinessNo(viInsPlcyInfTp.getProposalNumber());
        actionDTO.setFileStreamList(zcbxFileStreamDTOS);
        actionDTO.setCount(zcbxFileStreamDTOS.size());
        imageUploadDTO.setActionDto(actionDTO);
        zcbxImageUploadReqDTO.setData(imageUploadDTO);
        return zcbxImageUploadReqDTO;
    }

    @Override
    public InsEnum getSupportIns() {
        return InsEnum.ZCBX;
    }
}
