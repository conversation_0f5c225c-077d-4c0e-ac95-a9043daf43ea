package com.tuns.car.channel.service.core.zcbx;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tuns.car.channel.constants.zcbx.*;
import com.tuns.car.channel.dto.zcbx.req.*;
import com.tuns.car.channel.dto.zcbx.res.*;
import com.tuns.car.channel.rpc.zcbx.ZCBXJsonRpcService;
import com.tuns.car.channel.rpc.zcbx.ZCBXXmlRpcService;
import com.tuns.car.channel.service.core.AbstractApiInsureService;
import com.tuns.car.channel.util.TunsImageUtil;
import com.tuns.car.channel.util.zcbx.ErrorHandleUtil;
import com.tuns.car.core.constant.*;
import com.tuns.car.core.dto.car.CarThirdInsureDTO;
import com.tuns.car.core.dto.car.ViCompanyImageMappingDTO;
import com.tuns.car.core.dto.car.ViOfferImgDTO;
import com.tuns.car.core.dto.gi.CarSameSaleGiOrderInfoVO;
import com.tuns.car.core.entity.PubPersInfTp;
import com.tuns.car.core.entity.ViInsCarInfTp;
import com.tuns.car.core.entity.ViInsPlcyCustomInvoice;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import com.tuns.car.core.vo.car.CarInsureVO;
import com.tuns.core.boot.constant.InsConstants;
import com.tuns.core.boot.constant.YesNoNumberEnum;
import com.tuns.core.boot.exception.TunsBusinessException;
import com.tuns.core.thirdparty.service.IOSSService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 提交核保流程：
 * 1、投保单保存
 * 2、影像上传
 * 3、提交核保
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Slf4j
@Service
public class ZCBXApiInsureConfirmServiceImpl extends AbstractApiInsureService<ZCBXInsureReq, ZCBXInsureRes> {

    private static final String ZCBX_IMAGE_RESULT_KEY = "ZCBX:IMAGERESULT:KEY:";
    private static final String ZCBX_PROPOSALNO = "ZCBX:PROPOSALNO:";
    private static final int PROPOSAL_CACHE_EXPIRE_DAYS = 3;
    private static final int IMAGE_RESULT_CACHE_EXPIRE_HOURS = 24;

    @Autowired
    private ZCBXXmlRpcService zcbxXmlRpcService;
    @Autowired
    private ZCBXJsonRpcService zcbxJsonRpcService;
    @Autowired
    private IOSSService iossService;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public InsEnum getSupportIns() {
        return InsEnum.ZCBX;
    }

    @Override
    protected void before(CarThirdInsureDTO inputDTO) {
        try {
            ViInsPlcyInfTp pubInsPlcy = inputDTO.getPubInsPlcy();
            String batchNo = getImageUploadResultFromCache(pubInsPlcy.getPolicyBatchId());

            if (StrUtil.isNotBlank(batchNo)) {
                log.info("众诚保险从缓存中获取影像结果查询的批次号：{}", batchNo);
                validateUploadSuccess(inputDTO, batchNo);
                return;
            }

            handleProposalAndUpload(inputDTO);
        } catch (TunsBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("{}提交核保前置处理未知异常", getSupportIns().getCompanyName(), e);
            throw new TunsBusinessException("系统发生了未知异常，请联系系统管理员！");
        }
    }

    @Override
    public ZCBXInsureReq mapReq(CarThirdInsureDTO carThirdInsureDTO) {
        ZCBXInsureReq zcbxInsureReq = new ZCBXInsureReq();
        List<ZCBXInsureUnderwriteDTO> underwriteList = carThirdInsureDTO.getPlcyInfList().stream()
                .map(this::buildUnderwriteDTO)
                .collect(Collectors.toList());
        zcbxInsureReq.setUnderwriteDtoList(underwriteList);
        return zcbxInsureReq;
    }

    @Override
    public ZCBXInsureRes doInvoke(ZCBXInsureReq zcbxInsureReq, CarThirdInsureDTO carThirdInsureDTO) {
        ZCBXInsureBodyRes insure = zcbxXmlRpcService.insure(zcbxInsureReq);
        ErrorHandleUtil.errorHandle(insure.getHeadDto());
        return insure.getBodyDto();
    }

    @Override
    public CarInsureVO mapRes(ZCBXInsureRes zcbxInsureRes, ZCBXInsureReq zcbxInsureReq, CarThirdInsureDTO carThirdInsureDTO) {
        CarInsureVO result = new CarInsureVO();
        processUnderwriteResults(zcbxInsureRes, result);
        result.setPlcyInfList(carThirdInsureDTO.getPlcyInfList());

        carThirdInsureDTO.getPlcyInfList().forEach(x ->
                redisTemplate.delete(ZCBX_PROPOSALNO + x.getPolicyId())
        );

        return result;
    }

    private ZCBXInsureUnderwriteDTO buildUnderwriteDTO(ViInsPlcyInfTp viInsPlcyInfTp) {
        String proposalNumber = Optional.ofNullable(viInsPlcyInfTp.getProposalNumber())
                .orElseGet(() -> {
                    String no = redisTemplate.opsForValue().get(ZCBX_PROPOSALNO + viInsPlcyInfTp.getPolicyId());
                    log.info("众诚保险从缓存中获取投保保单号：{}", no);
                    viInsPlcyInfTp.setProposalNumber(no);
                    return no;
                });
        return new ZCBXInsureUnderwriteDTO(proposalNumber);
    }

    private void processUnderwriteResults(ZCBXInsureRes zcbxInsureRes, CarInsureVO result) {
        StringBuilder failedMsg = new StringBuilder();
        List<ZCBXUnderwriteStateEnum> underwriteState = zcbxInsureRes.getUnderwriteResultDtoList().stream()
                .peek(dto -> failedMsg.append(dto.getBackReason()))
                .map(dto -> ZCBXUnderwriteStateEnum.getByValue(dto.getUnderwriteState()))
                .collect(Collectors.toList());

        InsuredSubStatusEnum insuredSubStatusEnum = ZCBXUnderwriteStateEnum.mapInsuredStatus(underwriteState);
        result.setSubStatus(Optional.ofNullable(insuredSubStatusEnum).map(InsuredSubStatusEnum::getValue).orElse(null));
        result.setInsuredStatus(Optional.ofNullable(insuredSubStatusEnum)
                .map(x -> x.getParentStatus().getValue()).orElse(null));
        result.setFailedMsg(failedMsg.toString());
    }

    private String getImageUploadResultFromCache(Long policyBatchId) {
        return redisTemplate.opsForValue().get(ZCBX_IMAGE_RESULT_KEY + policyBatchId);
    }

    private void handleProposalAndUpload(CarThirdInsureDTO inputDTO) {
        processProposalNumbers(inputDTO);

        if (shouldSaveProposal(inputDTO)) {
            ZCBXInsureProposalReq proposalReq = buildProposalSaveParam(inputDTO);
            proposalSave(proposalReq, inputDTO);
        }

        uploadAttaches(inputDTO);
    }

    private void processProposalNumbers(CarThirdInsureDTO inputDTO) {
        inputDTO.getPlcyInfList().forEach(viInsPlcyInfTp -> {
            String proposalNumber = redisTemplate.opsForValue().get(ZCBX_PROPOSALNO + viInsPlcyInfTp.getPolicyId());
            if (StrUtil.isNotBlank(proposalNumber)) {
                viInsPlcyInfTp.setProposalNumber(proposalNumber);
            }
        });
    }

    private boolean shouldSaveProposal(CarThirdInsureDTO inputDTO) {
        return inputDTO.getPlcyInfList().stream()
                .map(viInsPlcyInfTp -> redisTemplate.opsForValue().get(ZCBX_PROPOSALNO + viInsPlcyInfTp.getPolicyId()))
                .noneMatch(StrUtil::isNotBlank);
    }

    private void validateUploadSuccess(CarThirdInsureDTO inputDTO, String batchNo) {
        ViInsPlcyInfTp pubInsPlcy = inputDTO.getPubInsPlcy();
        ZCBXUploadImageResultRes resultRes = zcbxJsonRpcService.imageResult(buildImageResultReq(batchNo));

        if (!ZCBXErrorCodeEnum.SUCCESS.getCode().equals(resultRes.getErrorCode())) {
            redisTemplate.delete(ZCBX_IMAGE_RESULT_KEY + pubInsPlcy.getPolicyBatchId());
            throw new TunsBusinessException("影像上传异常了，请稍后再次点击提交核保！");
        }

        if (Integer.parseInt(resultRes.getData().getActionDto().getFailCount()) > 0) {
            throw new TunsBusinessException("影像上传中，请稍后再次点击提交核保！");
        }

        redisTemplate.delete(ZCBX_IMAGE_RESULT_KEY + pubInsPlcy.getPolicyBatchId());
    }

    private static Map<String, ViInsPlcyInfTp> getPlcyInfTpMap(CarThirdInsureDTO inputDTO) {
        return inputDTO.getPlcyInfList().stream().collect(Collectors.toMap(ViInsPlcyInfTp::getPolicyType, Function.identity()));
    }

    private void uploadAttaches(CarThirdInsureDTO inputDTO) {
        if (CollectionUtil.isEmpty(inputDTO.getAttaches())) {
            return;
        }

        doUpload(inputDTO);
    }

    private static Map<String, PubPersInfTp> getPersInfTpMap(CarThirdInsureDTO inputDTO) {
        return inputDTO.getPubPerList().stream().collect(Collectors.toMap(PubPersInfTp::getPersonType, Function.identity()));
    }

    private static List<ZCBXInsureNonVehicleProductDTO> buildNoCar(CarThirdInsureDTO inputDTO) {
        List<CarSameSaleGiOrderInfoVO> carSameSaleGiOrderInfoList = inputDTO.getCarSameSaleGiOrderInfoList();
        List<ZCBXInsureNonVehicleProductDTO> nonVehicleProductDtoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(carSameSaleGiOrderInfoList)) {
            CarSameSaleGiOrderInfoVO carSameSaleGiOrderInfoVO = carSameSaleGiOrderInfoList.get(0);
            ZCBXInsureNonVehicleProductDTO zcbxInsureNonVehicleProductDTO = new ZCBXInsureNonVehicleProductDTO();
            zcbxInsureNonVehicleProductDTO.setProposalNo(carSameSaleGiOrderInfoVO.getProposalNo());
            zcbxInsureNonVehicleProductDTO.setSumPremium(carSameSaleGiOrderInfoVO.getPrem().toString());
            zcbxInsureNonVehicleProductDTO.setSumAmount(carSameSaleGiOrderInfoVO.getAmount().toString());
            nonVehicleProductDtoList.add(zcbxInsureNonVehicleProductDTO);
        }
        return nonVehicleProductDtoList;
    }

    private void doUpload(CarThirdInsureDTO inputDTO) {
        List<String> imageBatchNoList = inputDTO.getPlcyInfList().stream()
                .map(viInsPlcyInfTp -> {
                    List<ZCBXFileStreamDTO> fileStreamList = buildFileStreamList(inputDTO);
                    ZCBXImageUploadReqDTO uploadReq = buildImageUploadReq(viInsPlcyInfTp, fileStreamList);
                    ZCBXImageUploadResDTO uploadRes = zcbxJsonRpcService.attachUpload(uploadReq);

                    if (!ZCBXErrorCodeEnum.SUCCESS.getCode().equals(uploadRes.getErrorCode())) {
                        log.error("众诚影像上传失败：{}={}", uploadRes.getErrorCode(), uploadRes.getErrorMessage());
                    }

                    return Optional.ofNullable(uploadRes.getData().getActionDto())
                            .map(ZCBXImageUploadResDTO.ActionDTO::getBatchNo)
                            .orElse(null);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(imageBatchNoList)) {
            return;
        }

        ViInsPlcyInfTp pubInsPlcy = inputDTO.getPubInsPlcy();
        imageBatchNoList.forEach(batchNo -> {
            ZCBXUploadImageResultRes resultRes = zcbxJsonRpcService.imageResult(buildImageResultReq(batchNo));

            if (ZCBXErrorCodeEnum.SUCCESS.getCode().equals(resultRes.getErrorCode()) &&
                    Integer.parseInt(resultRes.getData().getActionDto().getFailCount()) > 0) {
                redisTemplate.opsForValue().set(
                        ZCBX_IMAGE_RESULT_KEY + pubInsPlcy.getPolicyBatchId(),
                        batchNo,
                        IMAGE_RESULT_CACHE_EXPIRE_HOURS,
                        TimeUnit.HOURS
                );
                throw new TunsBusinessException("影像上传中，请稍后再次点击提交核保！");
            }
        });
    }

    private static ZCBXImageUploadReqDTO buildImageUploadReq(ViInsPlcyInfTp viInsPlcyInfTp, List<ZCBXFileStreamDTO> zcbxFileStreamList) {
        ZCBXImageUploadReqDTO zcbxImageUploadReqDTO = new ZCBXImageUploadReqDTO();
        ZCBXImageUploadDTO imageUploadDTO = new ZCBXImageUploadDTO();
        ZCBXImageUploadDTO.ActionDTO actionDTO = new ZCBXImageUploadDTO.ActionDTO();
        actionDTO.setBusinessNo(viInsPlcyInfTp.getProposalNumber());
        actionDTO.setFileStreamList(zcbxFileStreamList);
        actionDTO.setCount(zcbxFileStreamList.size());
        imageUploadDTO.setActionDto(actionDTO);
        zcbxImageUploadReqDTO.setData(imageUploadDTO);
        return zcbxImageUploadReqDTO;
    }

    private List<ZCBXFileStreamDTO> buildFileStreamList(CarThirdInsureDTO inputDTO) {
        List<ZCBXFileStreamDTO> zcbxFileStreamList = new ArrayList<>();
        List<String> imageLogs = new ArrayList<>();
        inputDTO.getAttaches().forEach(attach -> {
            //避免用户上传没有图片后缀的文件
            String[] split = attach.getFileName().split("\\.");
            if (split.length == 1) {
                attach.setFileName(attach.getFileName() + "." + "png");
            }
            if (Objects.nonNull(ViAttachTypeMainEnum.getByValue(attach.getType().getValue()))) {
                ZCBXFileStreamDTO fileStreamDTO = new ZCBXFileStreamDTO();
                String fileName = buildImageLog(attach);
                fileStreamDTO.setFileType(ZCBXFileTypeEnum.getTpCode(attach.getType()));
                fileStreamDTO.setFileName(fileName);
                fileStreamDTO.setFileStream(TunsImageUtil.compressToBase64(iossService.download(attach.getKey())));
                imageLogs.add(fileName);
                zcbxFileStreamList.add(fileStreamDTO);
                if (ZCBXFileTypeEnum.isIdentity(attach.getType())) {
                    String fileName2 = buildImageLog(attach);
                    fileStreamDTO = new ZCBXFileStreamDTO();
                    //身份证明
                    fileStreamDTO.setFileType("U0005");
                    fileStreamDTO.setFileName(fileName2);
                    fileStreamDTO.setFileStream(TunsImageUtil.compressToBase64(iossService.download(attach.getKey())));
                    zcbxFileStreamList.add(fileStreamDTO);
                    imageLogs.add(fileName2);
                }
            } else {
                Map<String, List<ViCompanyImageMappingDTO>> viCompanyImageMappingMap = inputDTO.getViCompanyImageMappingMap();
                List<ViCompanyImageMappingDTO> viCompanyImageMappingList = viCompanyImageMappingMap.get(attach.getType().getValue());
                if (CollectionUtil.isNotEmpty(viCompanyImageMappingList)) {
                    ViCompanyImageMappingDTO viCompanyImageMappingDTO = viCompanyImageMappingList.get(0);
                    ZCBXFileStreamDTO fileStreamDTO = new ZCBXFileStreamDTO();
                    //无映射关系就不传过去
                    if (StrUtil.isNotEmpty(viCompanyImageMappingDTO.getInsFieldFirstLevelCode())) {
                        String fileName = buildImageLog(attach);
                        fileStreamDTO.setFileType(viCompanyImageMappingDTO.getInsFieldFirstLevelCode());
                        fileStreamDTO.setFileName(fileName);
                        fileStreamDTO.setFileStream(TunsImageUtil.compressToBase64(iossService.download(attach.getKey())));
                        imageLogs.add(fileName);
                        zcbxFileStreamList.add(fileStreamDTO);
                    }
                }
            }

        });
        log.info("众诚影像资料上传：{}", JSONUtil.toJsonStr(imageLogs));
        return zcbxFileStreamList;
    }

    private String buildImageLog(ViOfferImgDTO attach) {
        return attach.getType().getValue() + "_" + ZCBXFileTypeEnum.getTpCode(attach.getType()) + "_" + attach.getFileName();
    }

    private void proposalSave(ZCBXInsureProposalReq proposalReq, CarThirdInsureDTO inputDTO) {
        Map<String, ViInsPlcyInfTp> plcyInfTpMap = getPlcyInfTpMap(inputDTO);
        Map<String, PubPersInfTp> pubPersInfTpMap = getPersInfTpMap(inputDTO);
        PubPersInfTp holder = pubPersInfTpMap.get(ViPersonTypeEnum.HOLDER.getValue());

        ViInsPlcyInfTp pubInsPlcy = inputDTO.getPubInsPlcy();
        ZCBXInsureApplicationDTO applicationDTO = new ZCBXInsureApplicationDTO();
        applicationDTO.setQuoteTransCode(pubInsPlcy.getInsPremiumNumber());
        if (PersonNatureEnum.PERSON.getValue().equals(holder.getNature())) {
            applicationDTO.setEProposalFlag(YesNoNumberEnum.YES.getValue());
        }
        proposalReq.setApplicationDto(applicationDTO);
        ZCBXInsureProposalBodyRes zcbxInsureProposalBodyRes = zcbxXmlRpcService.proposalSave(proposalReq);
        ErrorHandleUtil.errorHandle(zcbxInsureProposalBodyRes.getHeadDto());
        if (Objects.nonNull(zcbxInsureProposalBodyRes.getBodyDto().getProposalResultDto())) {
            ZCBXInsureProposalResultDTO.ProposalResult proposalResultDto = zcbxInsureProposalBodyRes.getBodyDto().getProposalResultDto();
            if (StrUtil.isNotBlank(proposalResultDto.getCiProposalNo())) {
                ViInsPlcyInfTp viInsPlcyInfTpCi = plcyInfTpMap.get(PolicyTypeEnum.CI.getValue());
                viInsPlcyInfTpCi.setProposalNumber(proposalResultDto.getCiProposalNo());
                //设置自动销毁时间
                redisTemplate.opsForValue().set(ZCBX_PROPOSALNO + viInsPlcyInfTpCi.getPolicyId(), proposalResultDto.getCiProposalNo(), 3, TimeUnit.DAYS);
            }
            if (StrUtil.isNotBlank(proposalResultDto.getBiProposalNo())) {
                ViInsPlcyInfTp viInsPlcyInfTpBi = plcyInfTpMap.get(PolicyTypeEnum.BI.getValue());
                viInsPlcyInfTpBi.setProposalNumber(proposalResultDto.getBiProposalNo());
                //设置自动销毁时间
                redisTemplate.opsForValue().set(ZCBX_PROPOSALNO + viInsPlcyInfTpBi.getPolicyId(), proposalResultDto.getBiProposalNo(), 3, TimeUnit.DAYS);
            }
        }
    }

    private static ZCBXInsureProposalReq buildProposalSaveParam(CarThirdInsureDTO inputDTO) {
        ZCBXInsureProposalReq proposalReq = new ZCBXInsureProposalReq();
        Map<String, PubPersInfTp> pubPersInfTpMap = getPersInfTpMap(inputDTO);
        //车辆信息
        proposalReq.setVehicleDto(buildVehicle(inputDTO.getCarInfTp()));
        //投保人信息
        proposalReq.setPhDtoList(buildApply(inputDTO, pubPersInfTpMap));
        //被保人信息
        proposalReq.setInsuredDtoList(buildInsured(pubPersInfTpMap));
        //车主信息
        proposalReq.setVehicleOwnerDto(buildOwner(pubPersInfTpMap));
        //验车信息
        proposalReq.setCheckCarInfoDto(buildCarCheck(inputDTO, pubPersInfTpMap));
        //非车信息
        proposalReq.setNonVehicleProductDtoList(buildNoCar(inputDTO));
        return proposalReq;

    }

    private static ZCBXInsureVehicleDTO buildVehicle(ViInsCarInfTp carInfTp) {
        ZCBXInsureVehicleDTO vehicleDTO = new ZCBXInsureVehicleDTO();
        boolean newEnergy = FuelTypeEnum.isNewEnergy(carInfTp.getFuelType());
        if (StrUtil.isNotBlank(carInfTp.getLicenseColor())) {
            vehicleDTO.setLicensePlateColorCode(ZCBXLicenseColorEnum.getTpCode(carInfTp.getLicenseColor()));
        } else {
            vehicleDTO.setLicensePlateColorCode(newEnergy ? "06" : "01");
        }
        vehicleDTO.setLoanStatus(InsConstants.YNFlag.NO);
        return vehicleDTO;
    }

    private static ZCBXInsureCheckCarInfoDTO buildCarCheck(CarThirdInsureDTO inputDTO, Map<String, PubPersInfTp> pubPersInfTpMap) {
        ViInsCarInfTp carInfTp = inputDTO.getCarInfTp();
        if (Objects.nonNull(carInfTp.getCheckCarStatus())) {
            ZCBXInsureCheckCarInfoDTO checkCarInfoDTO = new ZCBXInsureCheckCarInfoDTO();
            checkCarInfoDTO.setCarCheckStatus(ZCBXCarCheckEnum.getTpCode(carInfTp.getCheckCarStatus().getValue()));
            if (CheckCarStatusEnum.EXEMPTION.equals(carInfTp.getCheckCarStatus())) {
                checkCarInfoDTO.setCarCheckReason(ZCBXExemptionReasonEnum.getTpCode(carInfTp.getExemptionReason()));
                checkCarInfoDTO.setOtherCarCheckReason(carInfTp.getCarCheckReasonStr());
            } else if (CheckCarStatusEnum.VERIFIED.equals(carInfTp.getCheckCarStatus())) {
                PubPersInfTp owner = pubPersInfTpMap.get(ViPersonTypeEnum.OWNER.getValue());
                if (StrUtil.isNotBlank(carInfTp.getCarInspector())) {
                    String[] split = carInfTp.getCarInspector().split("-");
                    if (split.length > 1) {
                        checkCarInfoDTO.setCarChecker(split[1]);
                    } else {
                        checkCarInfoDTO.setCarChecker(split[0]);
                    }
                }
                checkCarInfoDTO.setCarCheckTime(carInfTp.getInspectionTime());
                if (PersonNatureEnum.PERSON.getValue().equals(owner.getNature())) {
                    checkCarInfoDTO.setCheckerID(carInfTp.getOwnerIdentify());
                } else {
                    checkCarInfoDTO.setCheckerID("430103198907287507");
                }
                checkCarInfoDTO.setCheckerPhone(owner.getMobilePhone());
                String tpCode = ZCBXCheckRecordEnum.getTpCode(carInfTp.getInspectionResult());
                checkCarInfoDTO.setCarCheckRecord(tpCode);
                if (tpCode.equals(ZCBXCheckRecordEnum.DAMAGE.getTpCode())) {
                    checkCarInfoDTO.setDamageLocation(carInfTp.getDamageLocation());
                }
            }
            return checkCarInfoDTO;
        }
        return null;
    }

    private static ZCBXInsureVehicleOwnerDTO buildOwner(Map<String, PubPersInfTp> pubPersInfTpMap) {
        PubPersInfTp owner = pubPersInfTpMap.get(ViPersonTypeEnum.OWNER.getValue());
        ZCBXInsureVehicleOwnerDTO vehicleOwnerDTO = new ZCBXInsureVehicleOwnerDTO();
        vehicleOwnerDTO.setOwnerType(owner.getNature());
        vehicleOwnerDTO.setCertiType(ZCBXIdentifyTypeEnum.getByOwnEnum(owner.getIdentifyType()).getCode());
        vehicleOwnerDTO.setCertiCode(owner.getIdentifyNumber());
        vehicleOwnerDTO.setPhoneNumber(owner.getMobilePhone());
        vehicleOwnerDTO.setMobileNo(owner.getMobilePhone());
        vehicleOwnerDTO.setAddressProvinceCode(owner.getProvinceNumber());
        vehicleOwnerDTO.setAddressCityCode(owner.getCityNumber());
        vehicleOwnerDTO.setAddressAreaCode(owner.getCountyNumber());
        vehicleOwnerDTO.setAddress(owner.getAddressComplete());
        return vehicleOwnerDTO;
    }

    /**
     * 构建被保人信息
     */
    private static List<ZCBXInsureInsuredDTO> buildInsured(Map<String, PubPersInfTp> pubPersInfTpMap) {
        PubPersInfTp insured = pubPersInfTpMap.get(ViPersonTypeEnum.INSURED.getValue());
        List<ZCBXInsureInsuredDTO> insuredDtoList = new ArrayList<>();
        ZCBXInsureInsuredDTO insuredDTO = new ZCBXInsureInsuredDTO();
        insuredDTO.setCertiType(ZCBXIdentifyTypeEnum.getByOwnEnum(insured.getIdentifyType()).getCode());
        insuredDTO.setCertiCode(insured.getIdentifyNumber());
        insuredDTO.setPhoneNumber(insured.getMobilePhone());
        insuredDTO.setMobileNo(insured.getMobilePhone());
        insuredDTO.setEmail(insured.getEmail());
        insuredDTO.setAddressProvinceCode(insured.getProvinceNumber());
        insuredDTO.setAddressCityCode(insured.getCityNumber());
        insuredDTO.setAddressAreaCode(insured.getCountyNumber());
        insuredDTO.setAddress(insured.getAddressComplete());
        insuredDTO.setIdStartDate(insured.getIdentityValidityStart().replaceAll("-", ""));
        insuredDTO.setIdExpireDate(insured.getIdentityValidity().replaceAll("-", ""));
        insuredDtoList.add(insuredDTO);
        return insuredDtoList;
    }

    /**
     * 构建投保人信息
     */
    private static List<ZCBXInsurePhDTO> buildApply(CarThirdInsureDTO inputDTO, Map<String, PubPersInfTp> pubPersInfTpMap) {
        PubPersInfTp holder = pubPersInfTpMap.get(ViPersonTypeEnum.HOLDER.getValue());
        List<ZCBXInsurePhDTO> applyList = new ArrayList<>();
        ZCBXInsurePhDTO zcbxInsurePhDTO = new ZCBXInsurePhDTO();
        zcbxInsurePhDTO.setPolicyHolder(holder.getPersonName());
        zcbxInsurePhDTO.setCertiType(ZCBXIdentifyTypeEnum.getByOwnEnum(holder.getIdentifyType()).getCode());
        zcbxInsurePhDTO.setCertiCode(holder.getIdentifyNumber());
        if (PersonNatureEnum.PERSON.getValue().equals(holder.getNature())) {
            zcbxInsurePhDTO.setBirthday(IdcardUtil.getBirth(holder.getIdentifyNumber()));
        }
        zcbxInsurePhDTO.setPhoneNumber(holder.getMobilePhone());
        zcbxInsurePhDTO.setMobileNo(holder.getMobilePhone());
        zcbxInsurePhDTO.setEmail(holder.getEmail());
        zcbxInsurePhDTO.setAddress(holder.getAddressComplete());
        zcbxInsurePhDTO.setIdStartDate(holder.getIdentityValidityStart().replaceAll("-", ""));
        zcbxInsurePhDTO.setIdExpireDate(holder.getIdentityValidity().replaceAll("-", ""));
        zcbxInsurePhDTO.setAddressProvinceCode(holder.getProvinceNumber());
        zcbxInsurePhDTO.setAddressCityCode(holder.getCityNumber());
        zcbxInsurePhDTO.setAddressAreaCode(holder.getCountyNumber());
        ViInsPlcyCustomInvoice viInsPlcyCustomInvoice = inputDTO.getViInsPlcyCustomInvoice();
        if (Objects.nonNull(viInsPlcyCustomInvoice)) {
            if (!PersonNatureEnum.PERSON.getValue().equals(holder.getNature())) {
                if (InvoiceTypeEnum.ELECTRONIC.equals(viInsPlcyCustomInvoice.getInvoiceType())) {
                    zcbxInsurePhDTO.setTaxpayersType(ZCBXTaxpayerTypeEnum.FUELCELL.getValue());
                } else {
                    zcbxInsurePhDTO.setTaxpayersType(ZCBXTaxpayerTypeEnum.PUREELECTRIC.getValue());
                }
                zcbxInsurePhDTO.setTaxpayersIdentifier(viInsPlcyCustomInvoice.getIdentifyNumber());
                if (StrUtil.isNotBlank(viInsPlcyCustomInvoice.getAddressComplete())) {
                    zcbxInsurePhDTO.setTaxAddress(viInsPlcyCustomInvoice.getAddressComplete());
                } else {
                    zcbxInsurePhDTO.setTaxAddress(holder.getAddressComplete());
                }
                zcbxInsurePhDTO.setBank(viInsPlcyCustomInvoice.getBankDeposit());
                zcbxInsurePhDTO.setAccount(viInsPlcyCustomInvoice.getBankAccount());
            }
        }
        applyList.add(zcbxInsurePhDTO);
        return applyList;
    }

    private static ZCBXUploadImageResultReq buildImageResultReq(String batchNo) {
        ZCBXUploadImageResultReq zcbxUploadImageResultReq = new ZCBXUploadImageResultReq();
        ZCBXUploadImageResultReq.Data data = new ZCBXUploadImageResultReq.Data();
        ZCBImageActionDTO zcbImageActionDTO = new ZCBImageActionDTO();
        zcbImageActionDTO.setBatchNo(batchNo);
        data.setActionDto(zcbImageActionDTO);
        zcbxUploadImageResultReq.setData(data);
        return zcbxUploadImageResultReq;
    }
}
