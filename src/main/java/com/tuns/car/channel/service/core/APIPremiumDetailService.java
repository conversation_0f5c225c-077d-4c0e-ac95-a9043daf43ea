package com.tuns.car.channel.service.core;

import com.tuns.car.core.InsServant;
import com.tuns.car.core.dto.compare.QueryProposalDetailDTO;

import java.util.Map;

/**
 * @Description: API报价单详情服务
 * @Author: <a href="http://www.loserzhao.com"><PERSON><PERSON><PERSON></a>
 * @Create: 2024-11-20 23:32
 */
public interface APIPremiumDetailService extends InsServant {
    /**
     * 查询保司报价单数据
     *
     * @param proposalDetailDTO
     * @return
     */
    Map<String, Object> queryPremiumDetail(QueryProposalDetailDTO proposalDetailDTO);
}
