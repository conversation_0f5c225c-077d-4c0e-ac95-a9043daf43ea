package com.tuns.car.channel.service.core;

/**
 * api对接，基本流程
 *
 * <AUTHOR>
 * @since 2022/5/27
 */
public interface ApiInvoke<OwnReq, OwnRes, InsReq, InsRes> {

    /**
     * 主要是将我方入参映射为保险公司的请求参数
     *
     * @param ownReq
     * @return
     */
    InsReq mapReq(OwnReq ownReq);

    /**
     * 保险公司接口调用
     *
     * @param insReq 保险公司api需要的数据结构
     * @param ownReq 我方请求数据结构
     * @return 保险公司返回的结果
     */
    InsRes doInvoke(InsReq insReq, OwnReq ownReq);

    /**
     * 后置处理
     * 主要是将 {@link #doInvoke )}方法的响应结果映射为我方系统需要的的数据结构
     *
     * @param res
     * @return
     */
    OwnRes mapRes(InsRes res, InsReq req, OwnReq ownReq);
}
