package com.tuns.car.channel.service.core;

import cn.hutool.core.util.ObjectUtil;
import com.tuns.car.channel.dto.GetCheckImageBasisDTO;
import com.tuns.car.channel.entity.ViInsPlcyPreInsureRes;
import com.tuns.car.channel.service.conf.ViInsPlcyPreInsureResService;
import com.tuns.car.core.vo.car.CheckImageBasisVO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractApiCheckImageBasisService implements ApiCheckImageBasisService {
    @Resource
    private ViInsPlcyPreInsureResService viInsPlcyPreInsureResService;


    @Override
    public List<CheckImageBasisVO> getCheckImageBasis(GetCheckImageBasisDTO dto) {
        // 获取最新一条历史预核保错误报文
        ViInsPlcyPreInsureRes res = viInsPlcyPreInsureResService.getLastResByPolicyBatchId(dto.getPolicyBatchId());
        if (ObjectUtil.isNull(res)) {
            // 不存在预核保报文时直接返回空
            return Collections.emptyList();
        } else {
            // 调用对应保司的转换实现
            return buildCheckImageBasis(res.getPreInsureRes());
        }
    }
}
