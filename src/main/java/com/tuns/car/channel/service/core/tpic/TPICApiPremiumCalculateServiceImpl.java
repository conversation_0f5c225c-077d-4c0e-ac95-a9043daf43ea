package com.tuns.car.channel.service.core.tpic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tuns.car.channel.constants.tpic.*;
import com.tuns.car.channel.dto.APIPremiumCalculateDTO;
import com.tuns.car.channel.dto.tpic.TPICBaseRes;
import com.tuns.car.channel.dto.tpic.carquery.TPICApiCarInfoResultDTO;
import com.tuns.car.channel.dto.tpic.common.TPICSalesDTO;
import com.tuns.car.channel.dto.tpic.insure.res.TPICOrder;
import com.tuns.car.channel.dto.tpic.newcar.NewCarCarOwner;
import com.tuns.car.channel.dto.tpic.newcar.NewCarVehicle;
import com.tuns.car.channel.dto.tpic.newcar.TPICNewCarReqDTO;
import com.tuns.car.channel.dto.tpic.newcar.TPICNewCarResDTO;
import com.tuns.car.channel.dto.tpic.premium.*;
import com.tuns.car.channel.dto.tpic.premium.res.*;
import com.tuns.car.channel.dto.tpic.statusrefresh.req.TPICStatusRefreshReq;
import com.tuns.car.channel.dto.tpic.statusrefresh.res.TPICStatusRefreshRes;
import com.tuns.car.channel.exception.CarTypeErrorException;
import com.tuns.car.channel.rpc.tpic.TPICRpcService;
import com.tuns.car.channel.service.core.AbstractApiPremiumCalculateService;
import com.tuns.car.channel.service.core.ApiPremiumCalculateBeforeService;
import com.tuns.car.channel.util.ApiConfContext;
import com.tuns.car.core.constant.*;
import com.tuns.car.core.dto.car.GiOrderDetailDTO;
import com.tuns.car.core.dto.car.PremiumCalculateResultDTO;
import com.tuns.car.core.dto.carprocess.premium.ServiceTermDTO;
import com.tuns.car.core.dto.carprocess.premium.SpecialInfoDTO;
import com.tuns.car.core.dto.carprocess.premium.request.CarInfo;
import com.tuns.car.core.dto.carprocess.premium.request.ItemKind;
import com.tuns.car.core.dto.carprocess.premium.request.PersonInfo;
import com.tuns.car.core.dto.charging.ChargingPileDTO;
import com.tuns.car.core.entity.CmmVehicleTypeRelation;
import com.tuns.car.core.entity.ViInsKindDetialTp;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import com.tuns.car.core.entity.ViInsuranceInf;
import com.tuns.core.boot.constant.InsConstants;
import com.tuns.core.boot.exception.TunsBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 太平API保费试算
 */
@Slf4j
@Service
public class TPICApiPremiumCalculateServiceImpl extends AbstractApiPremiumCalculateService<TPICPremiumReqDTO, TPICPremiumResDTO, TPICPremiumReqDTO>
        implements ApiPremiumCalculateBeforeService<TPICPremiumReqDTO, Object> {

    @Autowired
    private TPICRpcService tpicRpcService;

    @Autowired
    private TPICPremiumCalculateErrorResponseHandler premiumCalculateErrorResponseHandler;

    @Override
    public InsEnum getSupportIns() {
        return InsEnum.TPIC;
    }

    @Override
    public String preprocessThirdPartyQuotes(APIPremiumCalculateDTO preprocessDTO) {
        // 新车备案
        newCarReg(preprocessDTO.getCarInfo());
        return null;
    }

    @Override
    public TPICPremiumReqDTO preset(APIPremiumCalculateDTO ownReq) {
        // 新车备案
        newCarReg(ownReq.getCarInfo());
        return null;
    }

    @Override
    public Object parallel(APIPremiumCalculateDTO premiumCalculateDTO) {
        return null;
    }

    @Override
    protected TPICPremiumReqDTO mapReq(APIPremiumCalculateDTO ownReq, TPICPremiumReqDTO dTO) {
        Map<String, ViInsPlcyInfTp> viInsPlcyInfTpMap = ownReq.getViInsPlcyInfTpMap();
        ViInsPlcyInfTp viInsPlcyInfTpBi = viInsPlcyInfTpMap.get(PolicyTypeEnum.BI.getValue());
        ViInsPlcyInfTp viInsPlcyInfTpCi = viInsPlcyInfTpMap.get(PolicyTypeEnum.CI.getValue());
        /*
        TPICSalesDTO tpicSalesDTO = new TPICSalesDTO();
        Map<CustomSettingFieldEnum, String> map = ApiConfContext.getConf().getMap();
        String groupNo = map.get(CustomSettingFieldEnum.SETTING_CODE_040);
        if (StrUtil.isBlank(groupNo)) {
            throw new TunsBusinessException("缺少相关自定义配置");
        }
        tpicSalesDTO.setGroupNo(groupNo);
        // 新车备案
        newCarReg(ownReq.getCarInfo(), tpicSalesDTO);
        */
        // 保费试算
        TPICPremiumReqDTO tpicPremiumReqDTO = new TPICPremiumReqDTO();
        List<PremiumRisk> risks = new ArrayList<>();
        tpicPremiumReqDTO.setRisk(risks);
        List<PremiumItemKindBS> itemKindBS = new ArrayList<>();
        tpicPremiumReqDTO.setItemKindBS(itemKindBS);
        PremiumVehicle vehicle = new PremiumVehicle();
        SpecialInfoDTO specialInfo = ownReq.getSpecialInfo();
        if (specialInfo != null) {
            vehicle.setNegotiatedActualValue(specialInfo.getDamageInsAmount() != null ? specialInfo.getDamageInsAmount().setScale(2, RoundingMode.HALF_UP).toString() : null);
        }
        tpicPremiumReqDTO.setVehicle(vehicle);
        List<PremiumNewEnergyDevice> newEnergyDevice = new ArrayList<>();
        tpicPremiumReqDTO.setNewEnergyDevice(newEnergyDevice);
        PremiumApplicant applicant = new PremiumApplicant();
        tpicPremiumReqDTO.setApplicant(applicant);
        PremiumInsured insured = new PremiumInsured();
        tpicPremiumReqDTO.setInsured(insured);
        PremiumExtend extend = new PremiumExtend();
        tpicPremiumReqDTO.setExtend(extend);
        //险种信息
        setRiskKind(ownReq, viInsPlcyInfTpBi, viInsPlcyInfTpCi, risks, itemKindBS);
        // 充电桩信息
        List<ChargingPileDTO> chargingPiles = ownReq.getChargingPiles();
        if (chargingPiles != null) {
            for (ChargingPileDTO chargingPile : chargingPiles) {
                PremiumNewEnergyDevice device = new PremiumNewEnergyDevice();
                device.setModel(chargingPile.getChargingModel());
                device.setCode(chargingPile.getChargingCode());
                device.setLocationAddress(chargingPile.getChargingAddrComplete());
                device.setChargingPileType(TPICChargingTypeEnum.getTpByOwn(chargingPile.getChargingType()).getValue());
                device.setInstallLocation(chargingPile.getChargingInstallAddrType().equals(ChargingAddrTypeEnum.GROUND_PARKING_LOT) ? "1" : "2");
                chargingPile.getChargingKindList().forEach(e -> {
                    if (e.getKindCode().equals(NewKindCodeEnum.NEW_ENERGY_PERSONAL_CHARGER_DAMAGE_COVERAGE.getValue())) {
                        device.setChargeLossSumInsured(e.getAmount().toString());
                    } else if (e.getKindCode().equals(NewKindCodeEnum.NEW_ENERGY_PERSONAL_CHARGER_LIABILITY_COVERAGE.getValue())) {
                        device.setChargeLiabilitySumInsured(e.getAmount().toString());
                    }
                });
                newEnergyDevice.add(device);
            }
        }
        // 投保人信息
        PersonInfo holder = ownReq.getPersonInfoMap().get(ViPersonTypeEnum.HOLDER.getValue());
        applicant.setAppliName(holder.getPersonName());
        applicant.setIdentifyNumber(holder.getIdentifyNumber());
        TPICIdentifyTypeEnum tpByOwn = TPICIdentifyTypeEnum.getTpByOwn(holder.getIdentifyType());
        if (tpByOwn.equals(TPICIdentifyTypeEnum.ID_CARD)) {
            applicant.setBirthDate(DateUtil.formatDate(IdcardUtil.getBirthDate(holder.getIdentifyNumber())));
            applicant.setSex(IdcardUtil.getGenderByIdCard(applicant.getIdentifyNumber()) > 0 ? "1" : "2");
        }
        applicant.setIdentifyType(tpByOwn.getValue());
        applicant.setInsuredType(holder.getNature().equals(PersonNatureEnum.PERSON) ? "1" : "2");
        applicant.setMobilePhone(holder.getMobilePhone());
        applicant.setEmail(holder.getEmail());
        applicant.setAppliAddress(holder.getAddressComplete());
        applicant.setIdentifyStartDate(holder.getIdentityValidityStart());
        applicant.setIdentifyEndDate(holder.getIdentityValidity());
        if (holder.getNature().equals(PersonNatureEnum.COMPANY)) {
            applicant.setBusinessRange("经营范围");
        }
        applicant.setOccupationCode("99");
        // 被保险人信息
        PersonInfo insuredPerson = ownReq.getPersonInfoMap().get(ViPersonTypeEnum.INSURED.getValue());
        insured.setInsuredName(insuredPerson.getPersonName());
        insured.setIdentifyNumber(insuredPerson.getIdentifyNumber());
        TPICIdentifyTypeEnum tpByOwn1 = TPICIdentifyTypeEnum.getTpByOwn(insuredPerson.getIdentifyType());
        insured.setIdentifyType(tpByOwn1.getValue());
        if (tpByOwn1.equals(TPICIdentifyTypeEnum.ID_CARD)) {
            insured.setBirthDate(insuredPerson.getBirthDate());
            insured.setSex(insuredPerson.getPersonSex());
        }
        insured.setInsuredType(insuredPerson.getNature().equals(PersonNatureEnum.PERSON) ? "1" : "2");
        insured.setInsuredMobilePhone(insuredPerson.getMobilePhone());
        insured.setInsuredAddress(insuredPerson.getAddressComplete());
        insured.setEmail(insuredPerson.getEmail());
        insured.setIdentifyStartDate(insuredPerson.getIdentityValidityStart());
        insured.setIdentifyEndDate(insuredPerson.getIdentityValidity());
        if (insuredPerson.getNature().equals(PersonNatureEnum.COMPANY)) {
            insured.setBusinessRange("经营范围");
        }
        insured.setOccupationCode("99");
        extend.setPremiumTrialFlag("0");

        //保存订单
        TPICSaveOrderResDTO tpicSaveOrderResDTO = saveOrder(ownReq);
        tpicPremiumReqDTO.setOrder(tpicSaveOrderResDTO.getOrder().setSuitClause(null));
        return tpicPremiumReqDTO;
    }

    /**
     * 设置险种信息
     *
     * @param ownReq
     * @param viInsPlcyInfTpBi
     * @param viInsPlcyInfTpCi
     * @param risks
     * @param itemKindBS
     */
    private void setRiskKind(APIPremiumCalculateDTO ownReq, ViInsPlcyInfTp viInsPlcyInfTpBi, ViInsPlcyInfTp viInsPlcyInfTpCi, List<PremiumRisk> risks, List<PremiumItemKindBS> itemKindBS) {
        Map<String, ItemKind> buyInsuranceInfMap = ownReq.getBuyInsuranceInfMap();
        Map<String, ViInsuranceInf> tpViInsuranceInfMapSelf = ownReq.getTpViInsuranceInfMapSelf();
        if (viInsPlcyInfTpCi != null) {
            PremiumRisk premiumRisk = new PremiumRisk();
            premiumRisk.setStartDate(viInsPlcyInfTpCi.getInsBegin());
            premiumRisk.setEndDate(viInsPlcyInfTpCi.getInsEnd());
            premiumRisk.setRiskCode("BZ");
            LocalDateTime localDateTime = DateUtil.parseLocalDateTime(viInsPlcyInfTpCi.getInsBegin());
            if (!localDateTime.toLocalDate().equals(LocalDate.now())) {
                //判断是不是当天即时起保
                premiumRisk.setEffectFlag(EffectiveImmediatelyEnum.NO.getCode());
            } else {
                premiumRisk.setEffectFlag(viInsPlcyInfTpCi.getEffectiveImmediately());
            }
            risks.add(premiumRisk);
        }
        if (viInsPlcyInfTpBi != null) {
            PremiumRisk premiumRisk = new PremiumRisk();
            premiumRisk.setStartDate(viInsPlcyInfTpBi.getInsBegin());
            premiumRisk.setEndDate(viInsPlcyInfTpBi.getInsEnd());
            premiumRisk.setRiskCode("BS");
            premiumRisk.setEffectFlag(viInsPlcyInfTpBi.getEffectiveImmediately());
            risks.add(premiumRisk);
        }
        for (ItemKind value : buyInsuranceInfMap.values()) {
            String kindCode = value.getKindCode();
            ViInsuranceInf viInsuranceInf = tpViInsuranceInfMapSelf.get(kindCode);
            if (NewKindCodeEnum.CI_JQ.getValue().equals(kindCode) || NewKindCodeEnum.CI_BT.getValue().equals(kindCode)) {
                continue;//交强险/车船税无需添加
            } else {
                PremiumItemKindBS premiumItemKindBS = new PremiumItemKindBS();
                premiumItemKindBS.setKindCode(viInsuranceInf.getKindCode());
                // 服务条款
                if (value.getBeServiceTerm() != null && value.getBeServiceTerm()) {
                    // 如果是费用补偿险填数量
                    if (NewKindCodeEnum.BI_REPAIR.getValue().equals(value.getKindCode())) {
                        premiumItemKindBS.setQuantity(value.getQuantity().toString());
                    } else {
                        premiumItemKindBS.setServiceTimes(value.getQuantity().toString());
                    }
                } else {
                    // 不填车损险, 附加外部电网故障损失险跟新能源车损险走
                    if (!NewKindCodeEnum.BI_VEHICLE_LOSS.getValue().equals(value.getKindCode()) && !NewKindCodeEnum.NEW_ENERGY_EXTERNAL_POWER_FAULT_COVERAGE.getValue().equals(value.getKindCode())) {
                        // 医保外、车上人员责任险填单位险种保额
                        if (Arrays.asList(NewKindCodeEnum.BI_MEDICAL_DRIVER.getValue(), NewKindCodeEnum.BI_MEDICAL_PASSENGER.getValue(),
                                NewKindCodeEnum.BI_PERSONNEL_DRIVER.getValue(), NewKindCodeEnum.BI_PERSONNEL_PASSENGER.getValue()).contains(value.getKindCode())) {
                            premiumItemKindBS.setInsured(value.getUnitAmount().toString());
                        } else {
                            // 其他填总保额
                            premiumItemKindBS.setInsured(value.getAmount().toString());
                        }
                    } else {
                        if (ownReq.getSpecialInfo().getDamageInsAmount() != null) {
                            premiumItemKindBS.setInsured(ownReq.getSpecialInfo().getDamageInsAmount().setScale(2, RoundingMode.DOWN).toString());
                        }
                    }
                }
                itemKindBS.add(premiumItemKindBS);
            }
        }
    }

    /**
     * 保存订单
     *
     * @param ownReq
     */
    private TPICSaveOrderResDTO saveOrder(APIPremiumCalculateDTO ownReq) {
        TPICSaveOrderReqDTO tpicSaveOrderReqDTO = new TPICSaveOrderReqDTO();
        CarInfo carInfo = ownReq.getCarInfo();
        SpecialInfoDTO specialInfo = ownReq.getSpecialInfo();
        if (specialInfo.getDamageInsAmount() != null && CollectionUtil.isNotEmpty(ownReq.getLastPlcyTpList())) {
            ViInsPlcyInfTp viInsPlcyInfTp = ownReq.getLastPlcyTpList().stream().findFirst().get();
            if (InsEnum.TPIC.getCompanyId().equals(viInsPlcyInfTp.getCompanyId())) {
                TPICOrder tpicOrder = new TPICOrder();
                tpicOrder.setOrderNo(viInsPlcyInfTp.getInsPremiumNumber());
                tpicSaveOrderReqDTO.setOrder(tpicOrder);
            }
        }
        TPICCarOwner carOwner = new TPICCarOwner();
        tpicSaveOrderReqDTO.setCarOwner(carOwner);
        TPICVehicle vehicle = new TPICVehicle();
        tpicSaveOrderReqDTO.setVehicle(vehicle);
        TPICSalesDTO tpicSalesDTO = new TPICSalesDTO();
        Map<CustomSettingFieldEnum, String> map = ApiConfContext.getConf().getMap();
        String groupNo = map.get(CustomSettingFieldEnum.SETTING_CODE_040);
        tpicSalesDTO.setGroupNo(groupNo);
        tpicSaveOrderReqDTO.setSales(tpicSalesDTO);
        carOwner.setName(carInfo.getCarOwner());
        carOwner.setIdentifyNumber(carInfo.getOwnerIdentify());
        TPICIdentifyTypeEnum tpByOwn = TPICIdentifyTypeEnum.getTpByOwn(carInfo.getOwnerIdentifyType());
        if (tpByOwn == TPICIdentifyTypeEnum.ID_CARD) {
            carOwner.setBirthDate(DateUtil.formatDate(IdcardUtil.getBirthDate(carInfo.getOwnerIdentify())));
            carOwner.setSex(IdcardUtil.getGenderByIdCard(carOwner.getIdentifyNumber()) > 0 ? "1" : "2");
        }
        carOwner.setOwnerNature(TPICNatureEnum.getTpByOwn(carInfo.getOwnerNature()).getValue());
        carOwner.setIdentifyType(tpByOwn.getValue());
        vehicle.setLicenseNo(carInfo.getPlateNumber().endsWith("*") ? "暂未上牌" : carInfo.getPlateNumber());
        vehicle.setFrameNo(carInfo.getFrameNumber());
        vehicle.setEngineNo(carInfo.getEngineNumber());
        vehicle.setModelCode(carInfo.getModelCode());
        vehicle.setPrintBrandName(carInfo.getLicenseBrand());
        vehicle.setChgOwnerFlag("1".equals(carInfo.getTransferMark()) ? "01" : "0");
        if ("1".equals(carInfo.getTransferMark())) {
            vehicle.setTransferDate(carInfo.getTransferDate());
            TransferMarkTypeEnum transferMarkType = carInfo.getTransferMarkType();
            if (transferMarkType != null) {
                if (transferMarkType.equals(TransferMarkTypeEnum.ONLY_CI)) {
                    vehicle.setBzchgOwnerFlag("01");
                } else if (transferMarkType.equals(TransferMarkTypeEnum.ONLY_BI)) {
                    vehicle.setBschgOwnerFlag("01");
                } else {
                    vehicle.setBzchgOwnerFlag("01");
                    vehicle.setBschgOwnerFlag("01");
                }
            }
        }
        CmmVehicleTypeRelation vehicleTypeRelation = ownReq.getVehicleTypeRelation();
        vehicle.setSingeinDate(carInfo.getRegDate());
        vehicle.setCertificateDate(carInfo.getCertDate());
        vehicle.setSeatCount(carInfo.getSeatCount().toString());
        vehicle.setLoanVehicleFlag(carInfo.getLoanCarMark());
        vehicle.setAttachNature(TPICNatureEnum.getTpByOwn(carInfo.getOwnershipNature()).getValue());
        vehicle.setUseNatureShow("0".equals(carInfo.getOperationNature()) ? "02" : "01");
        vehicle.setCarUseType(vehicleTypeRelation.getThirdFieldValue(TPICVehicle.Fields.carUseType));
        vehicle.setDriveUseAttribute(vehicleTypeRelation.getThirdFieldValue(TPICVehicle.Fields.driveUseAttribute));
        TPICApiCarInfoResultDTO.Vehicle queryVehicle = JSONUtil.toBean(carInfo.getExtendField(), TPICApiCarInfoResultDTO.Vehicle.class);
        vehicle.setVehicleCategory(queryVehicle.getVehicleCategory());
        vehicle.setTonCount(orStr(carInfo.getCarTonnage().multiply(new BigDecimal("1000")).setScale(4, RoundingMode.HALF_UP), "0"));
        vehicle.setCompleteKerbMass(carInfo.getWholeWeight().toString());
        vehicle.setCarKindCodeShow(vehicleTypeRelation.getThirdFieldValue(TPICVehicle.Fields.carKindCodeShow));
        vehicle.setCarKindCode(vehicleTypeRelation.getThirdFieldValue(TPICVehicle.Fields.carKindCode));
        if (Objects.nonNull(ownReq.getSpecialInfo()) && StrUtil.isNotBlank(ownReq.getSpecialInfo().getLicenseColor())) {
            vehicle.setLicenseColorCode(TPICLicenseColorEnum.getTpCode(ownReq.getSpecialInfo().getLicenseColor()));
        }
        TPICSaveOrderResDTO tpicSaveOrderResDTO = tpicRpcService.saveOrder(tpicSaveOrderReqDTO);
        return tpicSaveOrderResDTO;
    }

    /**
     * 新车备案
     *
     * @param carInfo
     */
    private void newCarReg(CarInfo carInfo) {

        TPICSalesDTO tpicSalesDTO = new TPICSalesDTO();
        Map<CustomSettingFieldEnum, String> map = ApiConfContext.getConf().getMap();
        String groupNo = map.get(CustomSettingFieldEnum.SETTING_CODE_040);
        if (StrUtil.isBlank(groupNo)) {
            throw new TunsBusinessException("缺少相关自定义配置");
        }
        tpicSalesDTO.setGroupNo(groupNo);

        TPICNewCarReqDTO tpicNewCarReqDTO = new TPICNewCarReqDTO();
        NewCarVehicle newCarVehicle = new NewCarVehicle();
        tpicNewCarReqDTO.setVehicle(newCarVehicle);
        NewCarCarOwner carCarOwner = new NewCarCarOwner();
        tpicNewCarReqDTO.setCarOwner(carCarOwner);
        tpicNewCarReqDTO.setSales(tpicSalesDTO);
        // 车辆信息
        newCarVehicle.setEngineNo(carInfo.getEngineNumber());
        newCarVehicle.setFrameNo(carInfo.getFrameNumber());
        newCarVehicle.setModelCode(carInfo.getModelCode());
        newCarVehicle.setVehicleCategory(carInfo.getLicenseVehicleType());
        newCarVehicle.setSeatCount(carInfo.getSeatCount().toString());
        newCarVehicle.setTonCount(orStr(carInfo.getCarTonnage().multiply(new BigDecimal("1000")).setScale(2, RoundingMode.HALF_UP), "0"));
        newCarVehicle.setCompleteKerbMass(orStr(carInfo.getWholeWeight(), "0"));
        newCarVehicle.setExhaustScale(orStr(carInfo.getExhaustScale(), "0", e -> new BigDecimal(e).divide(new BigDecimal("1000")).toString()));
        TPICEnergyTypeEnum tpByOwn = TPICEnergyTypeEnum.getTpByOwn(carInfo.getFuelType());
        newCarVehicle.setFuelType(tpByOwn.getFuelType());
        newCarVehicle.setCertificateType("01");
        /// 未知车辆来历凭证编号
        newCarVehicle.setCertificateNo("**********");
        newCarVehicle.setCertificateDate(DateUtil.formatDate(new Date()));
        // 车主信息
        carCarOwner.setName(carInfo.getCarOwner());
        TPICIdentifyTypeEnum identifyTypeEnum = TPICIdentifyTypeEnum.getTpByOwn(carInfo.getOwnerIdentifyType());
        carCarOwner.setIdentifyType(identifyTypeEnum.getValue());
        carCarOwner.setIdentifyNumber(carInfo.getOwnerIdentify());
        TPICBaseRes<List<TPICNewCarResDTO>> listTPICBaseRes = null;
        try {
            tpicRpcService.newCarReg(tpicNewCarReqDTO);

        } catch (Exception e) {
            log.error("太平新车备案失败，异常：", e);
            log.error("太平新车备案失败，返回结果:{}", listTPICBaseRes);
        }
    }

    @Override
    public TPICPremiumResDTO doInvoke(TPICPremiumReqDTO tpicPremiumReqDTO, APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        // TODO 新增非车
        TPICBaseRes<TPICPremiumResDTO> res = tpicRpcService.premium(tpicPremiumReqDTO);
        if (Objects.nonNull(res.getBody()) && Objects.nonNull(res.getBody().getVehicleDepSD())) {
            throw new CarTypeErrorException(InsEnum.TPIC, "保费试算异常,请选择正确车型", res.getBody().getVehicleDepSD().stream().map(JSONUtil::toJsonStr).collect(Collectors.toList()));
        }
        premiumCalculateErrorResponseHandler.handle(res, apiPremiumCalculateDTO);
        return res.getBody();
    }

    @Override
    public PremiumCalculateResultDTO mapRes(TPICPremiumResDTO tpicPremiumResDTO, TPICPremiumReqDTO tpicPremiumReqDTO, APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        PremiumCalculateResultDTO result = BeanUtil.toBean(apiPremiumCalculateDTO, PremiumCalculateResultDTO.class);
        List<ViInsKindDetialTp> viInsKindDetialTps = new ArrayList<>();
        //保司险种信息
        Map<String, ViInsuranceInf> tpViInsuranceInfMapTP = apiPremiumCalculateDTO.getTpViInsuranceInfMapTP();
        //我方险种信息
        List<PremiumResRisk> risk = tpicPremiumResDTO.getRisk();
        List<PremiumResItemKindBZ> itemKindBZ = tpicPremiumResDTO.getItemKindBZ();
        PremiumResTax tax = tpicPremiumResDTO.getTax();
        PremiumResExtend extend = tpicPremiumResDTO.getExtend();
        PremiumResVehicle vehicle = tpicPremiumResDTO.getVehicle();
        // 查詢訂單信息，獲取自主定價係數
        TPICStatusRefreshReq req = new TPICStatusRefreshReq();
        TPICOrder order = new TPICOrder();
        order.setOrderNo(tpicPremiumReqDTO.getOrder().getOrderNo());
        req.setOrder(order);
        TPICStatusRefreshRes tpicStatusRefreshRes = tpicRpcService.orderDetail(req);
        PremiumResRiskProfit n05 = Optional.ofNullable(tpicStatusRefreshRes.getRiskProfit()).flatMap(e -> e.stream().filter(elm -> "N05".equals(elm.getProfitCode())).findFirst())
                .orElse(null);
        StringBuilder preMsg = new StringBuilder();
        preMsg.append(Optional.ofNullable(tpicPremiumResDTO.getFlowUW()).map(e -> e.stream().map(PremiumResFlowUW::getDescription).collect(Collectors.joining("\n"))).orElse(""));
        preMsg.append(Optional.ofNullable(tpicPremiumResDTO.getFlowApprove()).map(e -> e.stream().map(PremiumResFlowApprove::getDescription).collect(Collectors.joining("\n"))).orElse(""));
        if (preMsg.toString().contains("重复投保")) {
            result.setReviewMsg(preMsg.toString());
        }
        //需求4599 取消太平API智能验车功能
//        if (preMsg.toString().contains("验车")) {
//            result.setAutoCheckCar(YesNoEnum.YES);
//        }
        Map<String, ViInsuranceInf> tpViInsuranceInfMapSelf = apiPremiumCalculateDTO.getTpViInsuranceInfMapSelf();
        PremiumResCommonParam commonParam = tpicPremiumResDTO.getCommonParam();
        for (ViInsPlcyInfTp viInsPlcyInfTp : apiPremiumCalculateDTO.getViInsPlcyInfTps()) {
            // 预核保信息
            viInsPlcyInfTp.setPreUnderwritingMsg(preMsg.toString());
            viInsPlcyInfTp.setInsuredType(TPICInsuredTypeEnum.getOwnEnumValueByValue(extend.getXzxId()));
            viInsPlcyInfTp.setInsPremiumNumber(tpicPremiumReqDTO.getOrder().getOrderNo());
            // 太平分
            Optional.ofNullable(tpicPremiumResDTO.getActuary()).ifPresent(act -> viInsPlcyInfTp.setVehicleRating(act.stream().filter(e -> "TP_Actuary_COREVALUE".equals(e.getActuaryCode())).findFirst()
                    .map(PremiumResActuary::getActuaryValue)
                    .map(BigDecimal::new)
                    .orElse(null)));
            // 自主定价系数
            Optional.ofNullable(n05).map(PremiumResRiskProfit::getRate).map(BigDecimal::new).map(e -> e.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP)).ifPresent(viInsPlcyInfTp::setIndependentPriceRate);
            viInsPlcyInfTp.setClaimAdjustLevel(commonParam.getClaimAdJustLevel());
            viInsPlcyInfTp.setNoClaimLevel(new BigDecimal(orStr(commonParam.getClaimAdJustLevel(), "0")));
            // 无赔优系数
            viInsPlcyInfTp.setNoClaimDiscount(new BigDecimal(orStr(commonParam.getNcdAdjust(), "0")));
            if (PolicyTypeEnum.CI.getValue().equals(viInsPlcyInfTp.getPolicyType())) {
                PremiumResRisk bz = risk.stream().filter(e -> "BZ".equals(e.getRiskCode())).findFirst().get();
                PremiumResItemKindBZ kindBz = itemKindBZ.stream().filter(e -> "BZ".equals(e.getKindCode())).findFirst().get();
                viInsPlcyInfTp.setInsBegin(bz.getStartDate());
                viInsPlcyInfTp.setInsEnd(bz.getEndDate());
                viInsPlcyInfTp.setInsuredPremium(new BigDecimal(kindBz.getPriceTaxTotal()));
                viInsPlcyInfTp.setAttachPremium(new BigDecimal(tax.getSumPayTax()));
                viInsPlcyInfTp.setCarTaxType(TPICTaxRelifFlagEnum.getCarTaxType(tax.getTaxRelifFlag()));
                viInsPlcyInfTp.setDiscount(new BigDecimal(kindBz.getDiscount()).divide(new BigDecimal(100)));
                viInsPlcyInfTp.setStandardPremium(new BigDecimal(bz.getSumUWPremium()));
                viInsPlcyInfTp.setEffectiveImmediately(bz.getEffectFlag());
                if (!RecordTypeEnum.BOTH.getValue().equals(viInsPlcyInfTp.getRecordType())) {
//                    viInsPlcyInfTp.setVehicleRating(new BigDecimal(premiumRes.getZCScoreDto().getZCScore()));
                }
//                viInsPlcyInfTp.setCarTaxType(ZCBXCarTaxTypeEnum.getByValue(applicationDto.getTaxRelifFlag()).getOwnEnum().getValue());
                viInsPlcyInfTp.setTotalPremium(NumberUtil.add(new BigDecimal(kindBz.getPriceTaxTotal()), new BigDecimal(tax.getSumPayTax())));

                ViInsuranceInf viInsuranceInf = tpViInsuranceInfMapTP.get(ViIssueConstants.KindCode.CIBZ);
                setKindDetail(NewKindCodeEnum.CI_JQ, viInsKindDetialTps, null, null, viInsPlcyInfTp, viInsuranceInf);
                setKindDetail(NewKindCodeEnum.CI_BT, viInsKindDetialTps, null, null, viInsPlcyInfTp, tpViInsuranceInfMapSelf.get(NewKindCodeEnum.CI_BT.getValue()));
            } else {
                List<PremiumResItemKindBS> itemKindBS = tpicPremiumResDTO.getItemKindBS();
                String discount = itemKindBS.stream().findFirst().map(PremiumResItemKindBS::getDiscount).orElse("0");
                PremiumResRisk bs = risk.stream().filter(e -> "BS".equals(e.getRiskCode())).findFirst().get();
                viInsPlcyInfTp.setInsuredPremium(new BigDecimal(bs.getSumPriceTaxTotal()));
                viInsPlcyInfTp.setDiscount(new BigDecimal(discount).divide(new BigDecimal(100)));
                viInsPlcyInfTp.setStandardPremium(new BigDecimal(bs.getSumPriceTaxTotal()));
                viInsPlcyInfTp.setTotalPremium(new BigDecimal(bs.getSumPriceTaxTotal()));
                Optional.of(bs).map(PremiumResRisk::getEndDate).filter(StrUtil::isNotBlank).ifPresent(viInsPlcyInfTp::setInsEnd);
                // 商业险是即时生效返回的日期和起保日期（startDate）不一样时，以即时生效日期（immEffectStartDate）为准。
                if (BooleanEnum.YES.getValue().equals(bs.getEffectFlag())) {
                    viInsPlcyInfTp.setInsBegin(bs.getImmEffectStartDate());
                } else {
                    Optional.of(bs).map(PremiumResRisk::getStartDate).filter(StrUtil::isNotBlank).ifPresent(viInsPlcyInfTp::setInsBegin);
                }
                AtomicInteger orderNo = new AtomicInteger(apiPremiumCalculateDTO.getBuyInsuranceInfMap().size());
                for (PremiumResItemKindBS KindBs : itemKindBS) {
                    ViInsuranceInf tpViInsuranceInf = tpViInsuranceInfMapTP.get(KindBs.getKindCode());
                    ItemKind itemKind = apiPremiumCalculateDTO.getBuyInsuranceInfMap().get(tpViInsuranceInf.getInsuranceCode());
                    if (itemKind == null) {
                        log.info("未找到购买的车险：{}", JSONUtil.toJsonStr(KindBs));
                        itemKind = new ItemKind();
                        if (NewKindCodeEnum.BI_ROADSIDE.getValue().equals(tpViInsuranceInf.getInsuranceCode())
                                || NewKindCodeEnum.BI_SAFETY_MONITORING.getValue().equals(tpViInsuranceInf.getInsuranceCode())
                                || NewKindCodeEnum.BI_INSPECT.getValue().equals(tpViInsuranceInf.getInsuranceCode())) {
                            int times = Integer.parseInt(orStr(KindBs.getServiceTimes(), "0"));
                            orderNo.incrementAndGet();
                            itemKind.setKindCode(tpViInsuranceInf.getInsuranceCode());
                            itemKind.setOrderNo(orderNo.get());
                            itemKind.setKindName(tpViInsuranceInf.getKindName());
                            itemKind.setQuantity(times);
                            itemKind.setBeServiceTerm(true);
                            apiPremiumCalculateDTO.getBuyInsuranceInfMap().put(tpViInsuranceInf.getInsuranceCode(), itemKind);
                            ServiceTermDTO serviceTermDTO = new ServiceTermDTO();
                            serviceTermDTO.setKindCode(tpViInsuranceInf.getInsuranceCode());
                            serviceTermDTO.setValue(times);
                            apiPremiumCalculateDTO.getSpecialInfo().getServiceTerms().add(serviceTermDTO);
                        }
                    }
                    setKindDetail(null, viInsKindDetialTps, itemKind, KindBs, viInsPlcyInfTp, tpViInsuranceInf);
                }
            }
            CarInfo carInfo = result.getCarInfo();
            carInfo.setActualPrice(new BigDecimal(vehicle.getActualValue()));
            viInsPlcyInfTp.setRecordStatus(RecordStatusEnum.SUCCEED);
            viInsPlcyInfTp.setInsuredStatus(InsuredStatusEnum.PREMIUMCACULATE.getValue());
        }
        if (result.getAddNoCarDTO() != null && CollectionUtil.isNotEmpty(result.getAddNoCarDTO().getGiOrderResult())) {
            ViInsPlcyInfTp viInsPlcyInfTp = apiPremiumCalculateDTO.getViInsPlcyInfTps()
                    .stream()
                    .filter(e -> e.getPolicyType().equals(PolicyTypeEnum.BI.getValue()))
                    .findFirst().orElseGet(() -> apiPremiumCalculateDTO.getViInsPlcyInfTps().stream().findFirst().get());
            for (GiOrderDetailDTO giOrderDetailDTO : result.getAddNoCarDTO().getGiOrderResult()) {
                giOrderDetailDTO.setStartTime(viInsPlcyInfTp.getInsBegin());
                giOrderDetailDTO.setEndTime(viInsPlcyInfTp.getInsEnd());
                if (!viInsPlcyInfTp.getInsBegin().endsWith("00:00:00")) {
                    Calendar start = Calendar.getInstance();
                    start.setTime(DateUtil.parseDateTime(viInsPlcyInfTp.getInsBegin()));
                    start.add(Calendar.DAY_OF_MONTH, 1);
                    start.set(Calendar.HOUR_OF_DAY, 0);
                    start.set(Calendar.MINUTE, 0);
                    start.set(Calendar.SECOND, 0);

                    Calendar end = Calendar.getInstance();
                    end.setTime(DateUtil.parseDateTime(viInsPlcyInfTp.getInsEnd()));
                    end.add(Calendar.DAY_OF_MONTH, 1);
                    end.set(Calendar.HOUR_OF_DAY, 0);
                    end.set(Calendar.MINUTE, 0);
                    end.set(Calendar.SECOND, 0);
                    giOrderDetailDTO.setStartTime(DateUtil.formatDateTime(start.getTime()));
                    giOrderDetailDTO.setEndTime(DateUtil.formatDateTime(end.getTime()));
                }
            }
        }
        result.setViInsPlcyInfTps(apiPremiumCalculateDTO.getViInsPlcyInfTps());
        result.setViInsKindDetialTps(viInsKindDetialTps);
        result.setExtendJson(JSONUtil.toJsonStr(tpicPremiumResDTO));
        return result;
    }

    private void setKindDetail(NewKindCodeEnum kindCode, List<ViInsKindDetialTp> viInsKindDetialTps, ItemKind itemKind, PremiumResItemKindBS KindBs, ViInsPlcyInfTp viInsPlcyInfTp, ViInsuranceInf insuranceInf) {
        ViInsKindDetialTp detialTp = new ViInsKindDetialTp();
        if (NewKindCodeEnum.CI_JQ.equals(kindCode)) {
            detialTp.setPolicyId(viInsPlcyInfTp.getPolicyId());// 保单记录ID
            detialTp.setPolicyBatchId(viInsPlcyInfTp.getPolicyBatchId());// 保单批次ID
            detialTp.setOrderNo(1);// 序号
            detialTp.setKindCode(insuranceInf.getInsuranceCode());// 险别代码
            detialTp.setKindName(insuranceInf.getKindName());// 险别名称
            detialTp.setQuantity(0);// 数量
            detialTp.setUnitAmount(BigDecimal.ZERO);// 单位保额
            detialTp.setAmount(viInsPlcyInfTp.getInsAmount());// 总保额
            detialTp.setPremium(viInsPlcyInfTp.getInsuredPremium());// 保费
            detialTp.setDiscount(BigDecimal.ZERO);// 折扣
            detialTp.setRate(BigDecimal.ZERO);// 费率
            detialTp.setAddlMark(AddlMarkEnum.NO.getCode());// 是否不计面免赔
            detialTp.setBuyAddl(BuyAddlMarkEnum.NO.getCode());// 是否购买不计免赔险
            detialTp.setBusinessMark(BusinessMarkEnum.NO.getCode());// 是否商业险
            detialTp.setCreUser(viInsPlcyInfTp.getCreUser());// 创建人
            detialTp.setMdfUser(viInsPlcyInfTp.getMdfUser());// 更新人
            detialTp.setDelFlag(InsConstants.DelFlag.NORMAL);// 删除标志
        } else if (NewKindCodeEnum.CI_BT.equals(kindCode)) {
            detialTp.setPolicyId(viInsPlcyInfTp.getPolicyId());// 保单记录ID
            detialTp.setPolicyBatchId(viInsPlcyInfTp.getPolicyBatchId());// 保单批次ID
            detialTp.setOrderNo(2);// 序号
            detialTp.setKindCode(insuranceInf.getInsuranceCode());// 险别代码
            detialTp.setKindName(insuranceInf.getKindName());// 险别名称
            detialTp.setQuantity(0);// 数量
            detialTp.setUnitAmount(BigDecimal.ZERO);// 单位保额
            detialTp.setAmount(BigDecimal.ZERO);// 总保额
            detialTp.setPremium(viInsPlcyInfTp.getAttachPremium());// 保费
            detialTp.setDiscount(BigDecimal.ZERO);// 折扣
            detialTp.setRate(BigDecimal.ZERO);// 费率
            detialTp.setAddlMark(AddlMarkEnum.NO.getCode());// 是否不计面免赔
            detialTp.setBuyAddl(BuyAddlMarkEnum.NO.getCode());// 是否购买不计免赔险
            detialTp.setBusinessMark(BusinessMarkEnum.NO.getCode());// 是否商业险
            detialTp.setCreUser(viInsPlcyInfTp.getCreUser());// 创建人
            detialTp.setMdfUser(viInsPlcyInfTp.getMdfUser());// 更新人
            detialTp.setDelFlag(InsConstants.DelFlag.NORMAL);// 删除标志
        } else {
            detialTp.setOrderNo(itemKind.getOrderNo());// 序号
            detialTp.setBusinessMark(BusinessMarkEnum.YES.getCode());// 是否商业险
            detialTp.setPolicyId(viInsPlcyInfTp.getPolicyId());// 保单记录ID
            detialTp.setPolicyBatchId(viInsPlcyInfTp.getPolicyBatchId());// 保单批次ID
            detialTp.setKindCode(insuranceInf.getInsuranceCode());// 险别代码
            detialTp.setKindName(insuranceInf.getKindName());// 险别名称
            detialTp.setValueType(itemKind.getValueType());
            detialTp.setQuantity(itemKind.getQuantity() == null ? 0 : itemKind.getQuantity());// 数量
            detialTp.setAmount(new BigDecimal(KindBs.getSumInsured()));// 总保额
            detialTp.setPremium(new BigDecimal(KindBs.getPriceTaxTotal()));// 保费
            detialTp.setDiscount(viInsPlcyInfTp.getDiscount().divide(new BigDecimal(100)));// 折扣
            detialTp.setCreUser(viInsPlcyInfTp.getCreUser());// 创建人
            detialTp.setMdfUser(viInsPlcyInfTp.getMdfUser());// 更新人
            detialTp.setDelFlag(InsConstants.DelFlag.NORMAL);// 删除标志
            if (StrUtil.equalsAny(itemKind.getKindCode(), NewKindCodeEnum.BI_PERSONNEL_PASSENGER.getValue(), NewKindCodeEnum.BI_MEDICAL_PASSENGER.getValue())) {
                detialTp.setQuantity(itemKind.getQuantity());// 数量
                detialTp.setAmount(itemKind.getAmount());
                detialTp.setUnitAmount(itemKind.getUnitAmount());
            }
        }
        viInsKindDetialTps.add(detialTp);
    }

    public static String orStr(Object o, String value, Function<String, String>... function) {
        if (Objects.isNull(o)) {
            return value;
        }
        if (function == null || function.length <= 0) {
            return o.toString();
        } else {
            return function[0].apply(o.toString());
        }

    }

}
