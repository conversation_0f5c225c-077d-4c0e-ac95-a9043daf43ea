package com.tuns.car.channel.service.core;

import com.tuns.car.channel.dto.GetCheckImageBasisDTO;
import com.tuns.car.channel.dto.paic.res.PAICInsureRes;
import com.tuns.car.core.InsServant;
import com.tuns.car.core.vo.car.CheckImageBasisVO;

import java.util.List;

/**
 * Api保费试算流程
 *
 * <AUTHOR>
 * @since 2022/5/26
 */
public interface ApiCheckImageBasisService extends InsServant {

    List<CheckImageBasisVO> getCheckImageBasis(GetCheckImageBasisDTO dto);

    /**
     * 根据保司预核保返回报文生产校验数据
     *
     * @param insRes 保司预核保返回报文
     * @return
     */
    List<CheckImageBasisVO> buildCheckImageBasis(String insRes);

    /**
     * 根据保司核保返回报文生产校验数据
     * @param preInsureRes
     * @return
     */
    List<CheckImageBasisVO> buildCheckInsureResImage(PAICInsureRes preInsureRes);
}
