package com.tuns.car.channel.service.core;

import com.alibaba.fastjson.JSON;
import com.tuns.car.channel.exception.InsureFailException;
import com.tuns.car.core.constant.InsuredStatusEnum;
import com.tuns.car.core.constant.InsuredSubStatusEnum;
import com.tuns.car.core.dto.car.CarThirdInsureDTO;
import com.tuns.car.core.vo.car.CarInsureVO;
import com.tuns.car.core.vo.car.InvoiceSubmitDTO;
import com.tuns.core.boot.exception.TunsBusinessException;
import lombok.extern.slf4j.Slf4j;

/**
 * 抽象api提交核保实现，编排提交核保主流程
 *
 * <AUTHOR>
 * @since 2022/5/31
 */
@Slf4j
public abstract class AbstractApiInsureService<Req, Res> implements ApiInsureService, ApiInvoke<CarThirdInsureDTO, CarInsureVO, Req, Res> {

    /**
     * 提交核保之前 每家保险公司不同的处理
     *
     * @param inputDTO
     */
    protected void before(CarThirdInsureDTO inputDTO) {

    }

    @Override
    public CarInsureVO insure(CarThirdInsureDTO inputDTO) {
        try {
            before(inputDTO);
            Req req = mapReq(inputDTO);
            log.info(getSupportIns().getCompanyName() + "===>提交核保：{}", JSON.toJSONString(req));
            Res res = doInvoke(req, inputDTO);
            log.info(getSupportIns().getCompanyName() + "<===提交核保：{}", JSON.toJSONString(res));
            CarInsureVO insureVO = mapRes(res, req, inputDTO);
            after(inputDTO, insureVO);
            return insureVO;
        } catch (InsureFailException e) {
            // 遇到核保失败异常，直接返回失败的响应
            return buildFailRes(e);
        }
    }

    private CarInsureVO buildFailRes(InsureFailException e) {
        CarInsureVO result = new CarInsureVO();
        result.setFailedMsg(e.getReason());
        result.setInsuredStatus(InsuredStatusEnum.UNDERWRITING_FAILURE.getValue());
        result.setSubStatus(InsuredSubStatusEnum.B5.getValue());
        return result;
    }

    /**
     * 提交核保之后 每家保险公司不同的处理
     *
     * @param inputDTO
     */
    protected void after(CarThirdInsureDTO inputDTO, CarInsureVO insureVO) {

    }

    @Override
    public String submitInvoice(InvoiceSubmitDTO invoice) {
        throw new TunsBusinessException("当前保司不支持投保后开具发票！");
    }
}
