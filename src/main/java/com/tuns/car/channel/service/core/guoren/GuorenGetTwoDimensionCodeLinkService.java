package com.tuns.car.channel.service.core.guoren;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import cn.hutool.json.JSONUtil;
import com.tuns.car.channel.dto.ApiCarInsurePaySignDTO;
import com.tuns.car.channel.dto.guoren.*;
import com.tuns.car.channel.rpc.guoren.GuorenRpcService;
import com.tuns.car.channel.util.PayUrlUtil;
import com.tuns.car.core.constant.PayTypeEnum;
import com.tuns.car.core.dto.gi.CarSameSaleGiOrderInfoVO;
import com.tuns.car.core.dto.pay.PaySignDTO;
import com.tuns.car.core.dto.pay.PaySignResultDTO;
import com.tuns.core.boot.exception.TunsBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 国任 -- 获取支付二维码服务类
 *
 * <AUTHOR>
 * @since 2022/6/10
 */
@Slf4j
@Service
public class GuorenGetTwoDimensionCodeLinkService {

    @Autowired
    private GuorenRpcService guoRenRpcService;
    @Autowired
    private PayUrlUtil payUrlUtil;
    @Autowired
    private GuorenChannelQueryService guorenChannelQueryService;
    @Autowired
    private GuorenNoCarProposalQueryService guorenNoCarProposalQueryService;


    /**
     * 获取支付二维码
     *
     * @param paySignDTO
     * @return
     */
    public PaySignResultDTO getPersonalPayUrl(ApiCarInsurePaySignDTO paySignDTO) {
        GuorenObtainAutographReq guorenObtainAutographReq = new GuorenObtainAutographReq();
        if (Objects.nonNull(paySignDTO.getInsPlcyInfBi())) {
            guorenObtainAutographReq.setProposalNoBI(paySignDTO.getInsPlcyInfBi().getProposalNumber());
        }
        if (Objects.nonNull(paySignDTO.getInsPlcyInfCi())) {
            guorenObtainAutographReq.setProposalNoCI(paySignDTO.getInsPlcyInfCi().getProposalNumber());
        }
        log.debug("国任===>通过二维码扫描获取电子投保单签名，{}", JSONUtil.toJsonStr(guorenObtainAutographReq));
        GuorenObtainAutographResDTO guorenObtainAutographDateResDTO = guoRenRpcService.guorenObtainAutograph(guorenObtainAutographReq);
        log.debug("国任<===通过二维码扫描获取电子投保单签名，{}", JSONUtil.toJsonStr(guorenObtainAutographDateResDTO));
        if (StringUtils.isEmpty(guorenObtainAutographDateResDTO.getQrCode())) {
            throw new TunsBusinessException("国任保险接口调用异常，异常信息: 未返回二维码信息！请走线下流程！！！");
        }
        String[] split = guorenObtainAutographDateResDTO.getQrCode().split(",");
        String base64 = split.length > 1 ? split[1] : split[0];
        String url = payUrlUtil.getPayUrl(base64, paySignDTO.getInsPlcyInfPub(), paySignDTO.getPaymentType().getValue());
        //二维码转URL链接
        PaySignResultDTO paySignVO = new PaySignResultDTO();
        PaySignDTO dto = new PaySignDTO();
        dto.setUrl(url);
        dto.setType(PaySignDTO.PaySignTypeEnum.QR_CODE_URL);
        dto.setExpireTime(null);
        Map<PayTypeEnum, PaySignDTO> payImageUrlMap = MapUtil.builder(PayTypeEnum.OTHER, dto)
                .build();
        paySignVO.setImagePayUrlMap(payImageUrlMap);
        return paySignVO;
    }

    /**
     * 获取支付二维码
     *
     * @param paySignDTO
     * @return
     */
    public PaySignResultDTO getNonPersonalPayUrl(ApiCarInsurePaySignDTO paySignDTO) {
        GuorenPayReq guorenPayReq = new GuorenPayReq();
        List<String> proposalNoList = new ArrayList<>();
        if (Objects.nonNull(paySignDTO.getInsPlcyInfBi())) {
            proposalNoList.add(paySignDTO.getInsPlcyInfBi().getProposalNumber());
        }
        if (Objects.nonNull(paySignDTO.getInsPlcyInfCi())) {
            proposalNoList.add(paySignDTO.getInsPlcyInfCi().getProposalNumber());
        }
        guorenPayReq.setProposalNo(proposalNoList);
        guorenPayReq.setChannelFlag("RBT");
        guorenPayReq.setAmount(paySignDTO.getPremium());
        guorenPayReq.setRiskType("Y");
        if (CollectionUtil.isNotEmpty(paySignDTO.getNoCarList())) {
            List<CarSameSaleGiOrderInfoVO> noCarList = paySignDTO.getNoCarList();
            CarSameSaleGiOrderInfoVO carSameSaleGiOrderInfoVO = noCarList.get(0);
            guorenPayReq.setRiskType("P");
            guorenPayReq.setProductCode("CA");
            guorenPayReq.setOrderCode(carSameSaleGiOrderInfoVO.getInsOrderNo());
            String proposalNumber = Objects.nonNull(paySignDTO.getInsPlcyInfCi()) ? paySignDTO.getInsPlcyInfCi().getProposalNumber() : paySignDTO.getInsPlcyInfBi().getProposalNumber();
            GuorenNocarNumberQueryRes guorenNocarNumberQueryRes = guorenNoCarProposalQueryService.queryNoCarProposalNo(proposalNumber);
            if (CollUtil.isNotEmpty(guorenNocarNumberQueryRes.getNoCarProposalNumbers())) {
                guorenPayReq.getProposalNo().addAll(guorenNocarNumberQueryRes.getNoCarProposalNumbers());
            }
        }
        GuorenChannelResDTO guorenChannelResDTO = guorenChannelQueryService.queryChannel();
        guorenPayReq.setComCode(guorenChannelResDTO.getPrptmain().getComCode());

        log.debug("国任===>企业车获取支付二维码，{}", JSONUtil.toJsonStr(guorenPayReq));
        GuorenPayDataRes guorenPayDataRes = guoRenRpcService.paySign(guorenPayReq);
        log.debug("国任<===企业车获取支付二维码，{}", JSONUtil.toJsonStr(guorenPayDataRes));
        if (!"0".equals(guorenPayDataRes.getResultCode())) {
            throw new TunsBusinessException("国任保险接口调用异常，异常信息: 未返回二维码信息！请走线下流程！！！");
        }
        GuorenPayRes data = guorenPayDataRes.getData();
        BufferedImage generate = QrCodeUtil.generate(data.getNewPayUrl(), new QrConfig());
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            ImageIO.write(generate, "PNG", outputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String payUrl = payUrlUtil.getPayUrl(Base64.encode(outputStream.toByteArray()), paySignDTO.getInsPlcyInfPub(), paySignDTO.getPaymentType().getValue());
        //二维码转URL链接
        PaySignResultDTO paySignVO = new PaySignResultDTO();
        PaySignDTO dto = new PaySignDTO();
        dto.setUrl(payUrl);
        dto.setType(PaySignDTO.PaySignTypeEnum.QR_CODE_URL);
        dto.setExpireTime(null);
        Map<PayTypeEnum, PaySignDTO> payImageUrlMap = MapUtil.builder(PayTypeEnum.OTHER, dto)
                .build();
        paySignVO.setImagePayUrlMap(payImageUrlMap);
        return paySignVO;
    }
}
