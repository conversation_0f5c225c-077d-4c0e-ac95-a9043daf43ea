package com.tuns.car.channel.service.core.zcbx;

import com.tuns.car.channel.service.core.ApiDeleteProposalService;
import com.tuns.car.core.constant.InsEnum;
import com.tuns.car.core.dto.offerorder.DeleteOfferOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @time 2023-6-08 14:55
 */
@Slf4j
@Service
public class ZCBXApiDeleteProposalServiceImpl implements ApiDeleteProposalService {
    @Override
    public void deleteProposal(DeleteOfferOrderDTO req) {
    }

    @Override
    public InsEnum getSupportIns() {
        return InsEnum.ZCBX;
    }
}
