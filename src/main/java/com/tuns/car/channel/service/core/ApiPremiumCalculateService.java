package com.tuns.car.channel.service.core;

import com.tuns.car.channel.dto.APIPremiumCalculateDTO;
import com.tuns.car.core.InsServant;
import com.tuns.car.core.dto.car.PremiumCalculateResultDTO;

/**
 * Api保费试算流程
 *
 * <AUTHOR>
 * @since 2022/5/26
 */
public interface ApiPremiumCalculateService extends InsServant {
    /**
     * api保费试算
     *
     * @param premiumCaculateDTO
     * @return
     */
    PremiumCalculateResultDTO doCalculate(APIPremiumCalculateDTO premiumCaculateDTO);

    /**
     * 第三方报价预处理 考虑不一定所有保司都有预处理场景，这里给定一个默认实现
     *
     * @param preprocessDTO
     * @return
     */
    default String preprocessThirdPartyQuotes(APIPremiumCalculateDTO preprocessDTO) {
        return null;
    }

    /**
     * 智能报价
     *
     * @param apiPremiumCalculateDTO
     * @return
     */
    PremiumCalculateResultDTO intelligentQuote(APIPremiumCalculateDTO apiPremiumCalculateDTO);
}
