package com.tuns.car.channel.service.core;

import com.tuns.car.core.InsServant;
import com.tuns.car.core.dto.special.CheckCarCodeDTO;
import com.tuns.car.core.dto.special.CheckCarCodeQueryDTO;

/**
 * 查询验车码
 *
 * <AUTHOR>
 * @since 2022/5/27
 */
public interface ApiCheckCarCodeService extends InsServant {
    /**
     * 查询验车码
     *
     * @param apiCheckCarCodeDTO
     * @return
     */
    CheckCarCodeDTO checkCarCode(CheckCarCodeQueryDTO apiCheckCarCodeDTO);
}
