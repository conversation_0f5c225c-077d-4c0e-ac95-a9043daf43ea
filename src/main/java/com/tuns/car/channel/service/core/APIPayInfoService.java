package com.tuns.car.channel.service.core;

import com.tuns.car.channel.dto.ApiCarInsurePaySignDTO;
import com.tuns.car.core.InsServant;
import com.tuns.car.core.dto.pay.CarInsureRefreshPayDTO;
import com.tuns.car.core.dto.pay.CarThirdPaymentDTO;
import com.tuns.car.core.dto.pay.PaySignDTO;
import com.tuns.car.core.dto.pay.PaySignResultDTO;

/**
 * <AUTHOR>
 * @date 2022/5/27 14:36
 */
public interface APIPayInfoService extends InsServant {

    /**
     * 获取支付二维码
     *
     * @param paySignDTO
     * @return
     */
    PaySignResultDTO applyPay(ApiCarInsurePaySignDTO paySignDTO);

    /**
     * 刷新支付二维码
     *
     * @param carInsureRefreshPayDTO@return
     */
    PaySignDTO refreshTwoDimensionCodeLink(CarInsureRefreshPayDTO carInsureRefreshPayDTO);

    /**
     * 查询支付信息
     *
     * @param insureDTO 投保信息
     * @return 订单状态信息
     */
    CarThirdPaymentDTO queryPayment(CarThirdPaymentDTO insureDTO);
}
