package com.tuns.car.channel.service.core;

import com.tuns.car.core.InsServant;
import com.tuns.car.core.entity.ViInsPlcyInfTp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-31 15:36
 */
public interface APICarArtificialUnderwriting extends InsServant {

    /**
     * 转人工核保
     *
     * @param viInsPlcyInfTps
     * @return
     */
    boolean apiCarArtificialUnderwriting(List<ViInsPlcyInfTp> viInsPlcyInfTps);
}
