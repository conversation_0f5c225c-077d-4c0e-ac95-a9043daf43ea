package com.tuns.car.channel.service.core;

import com.tuns.car.channel.dto.QueryCarActualPriceDTO;
import com.tuns.car.core.InsServant;
import com.tuns.car.core.dto.car.CarInfoDTO;
import com.tuns.car.core.vo.car.CarInfoVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * Api车型查询
 *
 * <AUTHOR>
 * @since 2022/5/27
 */
public interface ApiCarInfoService extends InsServant {
    /**
     * 车型查询
     *
     * @param carInfoDTO
     * @return
     */
    List<CarInfoVO> queryCarInfo(CarInfoDTO carInfoDTO);

    /**
     * 查询-- 车辆实际价值
     *
     * @param req
     * @return 车辆实际价值
     */
    BigDecimal queryCarActualPrice(QueryCarActualPriceDTO req);

    /**
     * 查询使用年限
     */
    Integer queryUserYears(QueryCarActualPriceDTO req);


    /**
     * 查询车辆续保信息
     * @param carInfoDTO
     * @return
     */
    String queryRenewalInfo(CarInfoDTO carInfoDTO);
}
