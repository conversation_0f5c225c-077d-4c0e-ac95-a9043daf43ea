package com.tuns.car.channel.service.core.htic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tuns.car.channel.constants.htic.HTICCarTaxTypeEnum;
import com.tuns.car.channel.constants.htic.HTICInsuredTypeEnum;
import com.tuns.car.channel.constants.htic.HTICRiskCodeEnum;
import com.tuns.car.channel.dto.APIPremiumCalculateDTO;
import com.tuns.car.channel.dto.htic.*;
import com.tuns.car.channel.exception.CarTypeErrorException;
import com.tuns.car.channel.rpc.htic.HTICRpcService;
import com.tuns.car.channel.service.core.AbstractApiPremiumCalculateService;
import com.tuns.car.channel.util.ApiConfContext;
import com.tuns.car.core.constant.*;
import com.tuns.car.core.dto.car.PremiumCalculateResultDTO;
import com.tuns.car.core.dto.carprocess.premium.QuoteBaseDTO;
import com.tuns.car.core.dto.carprocess.premium.SpecialInfoDTO;
import com.tuns.car.core.dto.carprocess.premium.request.CarInfo;
import com.tuns.car.core.dto.carprocess.premium.request.ItemKind;
import com.tuns.car.core.entity.*;
import com.tuns.core.boot.constant.InsConstants;
import com.tuns.core.boot.exception.TunsBusinessException;
import com.tuns.core.boot.utils.TunsBeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 华泰保费计算服务
 *
 * <AUTHOR>
 * @since 2024-10-28 18:07
 */
@Slf4j
@Service
public class HTICApiPremiumCalculateServiceImpl extends AbstractApiPremiumCalculateService<HTICQuotedPriceReq, HTICQuotedPriceRes, Object> {

    @Autowired
    private HTICPremiumCalculateReqMapService reqMapService;
    @Autowired
    private HTICRpcService hticRpcService;
    @Autowired
    private HTICPremiumCalculateErrorResponseHandler handler;
    @Autowired
    private HTICNoCarPremiumCalculateService noCarPremiumCalculateService;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    protected APIPremiumCalculateDTO beforeProcess(APIPremiumCalculateDTO ownReq) {
        //保费试算前置获取数据
        BigDecimal currentValue = new BigDecimal("0");
        if(StringUtils.isNotBlank(ownReq.getPreQuoteResult())){
            currentValue=NumberUtil.toBigDecimal(JSONUtil.parseObj(ownReq.getPreQuoteResult()).getStr("actualPrice"));
        }else{
            throw new TunsBusinessException("华泰API保费试算前置异常,批次号:",ownReq.getPolicyBatchId());
        }
        ownReq.getCarInfo().setActualPrice(currentValue);
        return ownReq;
    }

    @Override
    protected HTICQuotedPriceReq mapReq(APIPremiumCalculateDTO ownReq, Object o) {
        //车险保费计算请求参数封装
        return reqMapService.mapReq(ownReq);
    }

    private HTICActualValueRes calculateCarActualPrice(APIPremiumCalculateDTO ownReq) {
        HTICActualValueReq hticActualValueReq = new HTICActualValueReq();
        Map<CustomSettingFieldEnum, String> map = ApiConfContext.getConf().getMap();
        CarInfo carInfo = ownReq.getCarInfo();
        QuoteBaseDTO baseDTO = ownReq.getBaseDTO();
        CmmVehicleTypeRelation vehicleTypeRelation = ownReq.getVehicleTypeRelation();
        hticActualValueReq.setLoginCom(map.get(CustomSettingFieldEnum.SETTING_CODE_001));
        HTICModelsQueryRes.FullCarData fullCarData = JSONUtil.toBean(carInfo.getExtendField(), new TypeReference<HTICModelsQueryRes.FullCarData>() {
        }, true);
        hticActualValueReq.setLocalModelCode(fullCarData.getLocalModelCode());
        if (RecordTypeEnum.CI.getValue().equals(baseDTO.getRecordType())) {
            hticActualValueReq.setValidDate(baseDTO.getInsBeginCi());
        } else {
            hticActualValueReq.setValidDate(baseDTO.getInsBeginBi());
        }
        hticActualValueReq.setRegisterDate(DateUtil.formatDateTime(DateUtil.parseDate(carInfo.getRegDate())));
        hticActualValueReq.setPurchasePrice(carInfo.getPurchasePrice());
        hticActualValueReq.setApprovedPassengersCapacity(carInfo.getSeatCount());
        hticActualValueReq.setMotorUsageTypeCode(vehicleTypeRelation.getThirdFieldValue(HTICPolicyCar.Fields.motorUsageTypeCode));
        hticActualValueReq.setMotorTypeCode(vehicleTypeRelation.getThirdFieldValue(HTICPolicyCar.Fields.motorTypeCode));
        return hticRpcService.queryActualValue(hticActualValueReq);
    }

    @Override
    public HTICQuotedPriceRes doInvoke(HTICQuotedPriceReq hticQuotedPriceReq, APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        //修改车损险保额
        updateActualValueAndAmount(hticQuotedPriceReq, apiPremiumCalculateDTO);
        HTICQuotedPriceRes res;
        try {
            res = hticRpcService.premiumCalculate(hticQuotedPriceReq);
        } catch (Exception e) {
            //发现一种藏在Head里的交强险重复投保消息 这里特殊处理一下
            if (StrUtil.contains(e.getMessage(), "发生重复投保")) {
                res = new HTICQuotedPriceRes();
                res.setSpecialJqRepeatMsg(e.getMessage());
            } else {
                throw e;
            }
        }
        //转保场景处理
        HTICQuotedPriceRes hticQuotedPriceRes = reinsuranceScene(hticQuotedPriceReq, res);
        //车型修复
        carModelFix(hticQuotedPriceRes);
        handler.handle(hticQuotedPriceRes, apiPremiumCalculateDTO);
        return hticQuotedPriceRes;
    }

    private void carModelFix(HTICQuotedPriceRes hticQuotedPriceRes) {
        if (Objects.isNull(hticQuotedPriceRes.getBusinessData())) {
            return;
        }
        HTICContract biContract = HTICRiskCodeEnum.getBiContract(hticQuotedPriceRes.getBusinessData().getContract());
        HTICContract ciContract = HTICRiskCodeEnum.getCiContract(hticQuotedPriceRes.getBusinessData().getContract());
        if (Objects.nonNull(biContract) && Objects.nonNull(biContract.getPlatFormMessage()) && CollUtil.isNotEmpty(biContract.getPlatFormMessage().getAccurateServiceInfo())) {
            throwCarTypeErrorException(biContract);
        }
        if (Objects.nonNull(ciContract) && Objects.nonNull(ciContract.getPlatFormMessage()) && CollUtil.isNotEmpty(ciContract.getPlatFormMessage().getAccurateServiceInfo())) {
            throwCarTypeErrorException(ciContract);
        }
    }

    private static void throwCarTypeErrorException(HTICContract hticContract) {
        List<String> carModelList = hticContract.getPlatFormMessage().getAccurateServiceInfo().stream().map(HTICAccurateServiceInfo::getModelCode).collect(Collectors.toList());
        String carModelStr = CollUtil.join(carModelList, ",");
        String errorMsg = StrUtil.format("选择车辆的行业车型编码在平台不存在，请选择正确的车型编码,平台车型精准服务信息返回：{}", carModelStr);
        throw new CarTypeErrorException(InsEnum.HTIC, errorMsg, carModelList);
    }

    private HTICQuotedPriceRes reinsuranceScene(HTICQuotedPriceReq hticQuotedPriceReq, HTICQuotedPriceRes res) {
        if (Objects.isNull(res.getBusinessData())) {
            return res;
        }
        //商业险合同
        HTICContract biContract = HTICRiskCodeEnum.getBiContract(res.getBusinessData().getContract());
        //交强险合同
        HTICContract ciContract = HTICRiskCodeEnum.getCiContract(res.getBusinessData().getContract());
        //转保场景
        boolean reinsurance = false;
        if (Objects.nonNull(biContract) && Objects.nonNull(biContract.getPlatFormMessage()) && StrUtil.isNotBlank(biContract.getPlatFormMessage().getQuerySequenceNo())) {
            reinsurance = true;
            //是转保场景 重新给req赋值
            isReinsuranceForResetReq(HTICRiskCodeEnum.getBiContract(hticQuotedPriceReq.getBusinessData().getContract()), biContract);
        }
        if (Objects.nonNull(ciContract) && Objects.nonNull(ciContract.getPlatFormMessage()) && StrUtil.isNotBlank(ciContract.getPlatFormMessage().getQuerySequenceNo())) {
            reinsurance = true;
            isReinsuranceForResetReq(HTICRiskCodeEnum.getCiContract(hticQuotedPriceReq.getBusinessData().getContract()), ciContract);
        }
        //重新赋值后再次调用保费计算
        if (reinsurance) {
            return hticRpcService.premiumCalculate(hticQuotedPriceReq);
        }
        return res;
    }

    private static void isReinsuranceForResetReq(HTICContract hticQuotedPriceReq, HTICContract contractRes) {
        HTICPlatFormMessage platFormMessage = contractRes.getPlatFormMessage();
        HTICPlatFormMessage hticPlatFormMessage = new HTICPlatFormMessage();
        hticPlatFormMessage.setQuerySequenceNo(platFormMessage.getQuerySequenceNo());
        hticPlatFormMessage.setQuerySequenceNoForBiRe(platFormMessage.getQuerySequenceNoForBiRe());
        hticPlatFormMessage.setCheckCodeRe(platFormMessage.getCheckCode());
        hticPlatFormMessage.setCheckCodeReForBiRe(platFormMessage.getCheckCodeForBiRe());
        hticQuotedPriceReq.setPlatFormMessage(hticPlatFormMessage);
    }

    /**
     * 修改车损险
     *
     * @param hticQuotedPriceReq
     * @param apiPremiumCalculateDTO
     */
    private void updateActualValueAndAmount(HTICQuotedPriceReq hticQuotedPriceReq, APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        SpecialInfoDTO specialInfo = apiPremiumCalculateDTO.getSpecialInfo();
        //不含车损修改
        if (Objects.isNull(specialInfo) || Objects.isNull(specialInfo.getDamageInsAmount())) {
            return;
        }
        Map<String, ViInsuranceInf> tpViInsuranceInfMapSelf = apiPremiumCalculateDTO.getTpViInsuranceInfMapSelf();
        ViInsuranceInf biVehicleLoss = tpViInsuranceInfMapSelf.get(NewKindCodeEnum.BI_VEHICLE_LOSS.getValue());
        ViInsuranceInf biRobberyTheft = tpViInsuranceInfMapSelf.get(NewKindCodeEnum.BI_ROBBERY_THEFT.getValue());
        ViInsuranceInf newEnergyExternalPowerFaultCoverage = tpViInsuranceInfMapSelf.get(NewKindCodeEnum.NEW_ENERGY_EXTERNAL_POWER_FAULT_COVERAGE.getValue());
        List<ViInsuranceInf> viInsuranceInfs = Stream.of(biVehicleLoss, newEnergyExternalPowerFaultCoverage, biRobberyTheft).filter(Objects::nonNull).collect(Collectors.toList());
        setAmountByKinds(specialInfo.getDamageInsAmount(), hticQuotedPriceReq, viInsuranceInfs);
    }

    /**
     * 给对应险种的保额 重新赋值
     *
     * @param damageInsAmount
     * @param hticQuotedPriceReq
     * @param list
     */
    private void setAmountByKinds(BigDecimal damageInsAmount, HTICQuotedPriceReq hticQuotedPriceReq, List<ViInsuranceInf> list) {
        HTICContract biContract = HTICRiskCodeEnum.getBiContract(hticQuotedPriceReq.getBusinessData().getContract());
        if (Objects.nonNull(biContract)) {
            List<HTICCoverage> coverage = biContract.getCoverage();
            Map<String, HTICCoverage> hticCoverageMap = coverage.stream().collect(Collectors.toMap(HTICCoverage::getKindCode, Function.identity()));
            for (ViInsuranceInf viInsuranceInf : list) {
                HTICCoverage hticCoverage = hticCoverageMap.get(viInsuranceInf.getKindCode());
                if (Objects.nonNull(hticCoverage)) {
                    hticCoverage.setAmount(damageInsAmount);
                }
            }
        }
    }

    @Override
    public PremiumCalculateResultDTO mapRes(HTICQuotedPriceRes hticQuotedPriceRes, HTICQuotedPriceReq hticQuotedPriceReq, APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        PremiumCalculateResultDTO result = BeanUtil.toBean(apiPremiumCalculateDTO, PremiumCalculateResultDTO.class);
        HTICContract biContract = HTICRiskCodeEnum.getBiContract(hticQuotedPriceRes.getBusinessData().getContract());
        HTICContract ciContract = HTICRiskCodeEnum.getCiContract(hticQuotedPriceRes.getBusinessData().getContract());
        //预核保信息
        setPreUnderwritingMsg(result, hticQuotedPriceRes);
        //矫正纳税类型
        fixCarTaxType(ciContract, result);
        //设置商业险信息
        mapBi(result, biContract);
        //设置交强险信息
        mapCi(result, ciContract);
        //评分系数
        mapScore(result, hticQuotedPriceRes);
        //特别约定
        mapSpecialAgreement(result, hticQuotedPriceRes);
        //险种信息
        List<ViInsKindDetialTp> list = mapKind(biContract, result);
        result.setViInsKindDetialTps(list);
        //非车报价
        noCarPremiumCalculateService.noCarPremiumCalculate(result, apiPremiumCalculateDTO);
        return result;
    }

    private List<ViInsKindDetialTp> mapKind(HTICContract biContract, PremiumCalculateResultDTO calculateResult) {
        Map<String, ViInsPlcyInfTp> viInsPlcyInfTpMap = calculateResult.getViInsPlcyInfTpMap();
        ViInsPlcyInfTp bi = viInsPlcyInfTpMap.get(PolicyTypeEnum.BI.getValue());
        ViInsPlcyInfTp ci = viInsPlcyInfTpMap.get(PolicyTypeEnum.CI.getValue());
        Map<String, ViInsuranceInf> insuranceMap = calculateResult.getTpViInsuranceInfMapTP();
        Map<String, ViInsuranceInf> tpViInsuranceInfMapSelf = calculateResult.getTpViInsuranceInfMapSelf();
        Map<String, ItemKind> buyKindMap = calculateResult.getBuyInsuranceInfMap();
        List<ViInsKindDetialTp> result = new ArrayList<>();
        if (Objects.nonNull(ci)) {
            // 记录交强险
            ViInsuranceInf jqInsuranceInf = tpViInsuranceInfMapSelf.get(NewKindCodeEnum.CI_JQ.getValue());
            ViInsKindDetialTp jq = kindDetailHandle(jqInsuranceInf, ci, null, null);
            result.add(jq);
            // 记录车船税
            ViInsuranceInf btInsuranceInf = tpViInsuranceInfMapSelf.get(NewKindCodeEnum.CI_BT.getValue());
            ViInsKindDetialTp bt = kindDetailHandle(btInsuranceInf, ci, null, null);
            result.add(bt);
        }
        // 商业险
        if (Objects.nonNull(bi)) {
            for (HTICCoverage hticCoverage : biContract.getCoverage()) {
                // 我方险种配置信息
                ViInsuranceInf insuranceInf = insuranceMap.get(hticCoverage.getKindCode());
                if (Objects.isNull(insuranceInf)) {
                    throw new TunsBusinessException("险种映射表配置异常，缺失华泰保险险种{}", hticCoverage.getKindCode());
                }
                // 购买的险种信息
                ItemKind buyKind = buyKindMap.get(insuranceInf.getInsuranceCode());
                if (Objects.isNull(buyKind)) {
                    throw new TunsBusinessException("险种映射表配置异常，缺失华泰保险险种{}", hticCoverage.getKindCode());
                }
                ViInsKindDetialTp viInsKindDetialTp = kindDetailHandle(insuranceInf, bi, buyKind, hticCoverage);
                result.add(viInsKindDetialTp);
            }
        }
        return result;
    }

    private void mapSpecialAgreement(PremiumCalculateResultDTO result, HTICQuotedPriceRes hticQuotedPriceRes) {
        ViInsPlcyInfTp viInsPlcyInfTp = result.getViInsPlcyInfTps().get(0);
        List<ViQuoteSpecialAgreement> specialAgreementDtoList = result.getSpecialAgreementDtoList();
        HTICContract biContract = HTICRiskCodeEnum.getBiContract(hticQuotedPriceRes.getBusinessData().getContract());
        HTICContract ciContract = HTICRiskCodeEnum.getCiContract(hticQuotedPriceRes.getBusinessData().getContract());
        if (Objects.nonNull(biContract) && CollUtil.isNotEmpty(biContract.getEngage())) {
            List<ViQuoteSpecialAgreement> viQuoteSpecialAgreements = buildQuoteSpecialAgreements(viInsPlcyInfTp, biContract, PolicyTypeEnum.BI);
            specialAgreementDtoList.addAll(viQuoteSpecialAgreements);
        }
        if (Objects.nonNull(ciContract) && CollUtil.isNotEmpty(ciContract.getEngage())) {
            List<ViQuoteSpecialAgreement> viQuoteSpecialAgreements = buildQuoteSpecialAgreements(viInsPlcyInfTp, ciContract, PolicyTypeEnum.CI);
            specialAgreementDtoList.addAll(viQuoteSpecialAgreements);
        }
    }

    private static List<ViQuoteSpecialAgreement> buildQuoteSpecialAgreements(ViInsPlcyInfTp viInsPlcyInfTp, HTICContract hticContract, PolicyTypeEnum policyTypeEnum) {
        return TunsBeanUtil.copyList(hticContract.getEngage(), o -> {
            ViQuoteSpecialAgreement viQuoteSpecialAgreement = new ViQuoteSpecialAgreement();
            viQuoteSpecialAgreement.setPolicyBatchId(viInsPlcyInfTp.getPolicyBatchId());
            viQuoteSpecialAgreement.setRecordType(policyTypeEnum);
            viQuoteSpecialAgreement.setPolicyId(viInsPlcyInfTp.getPolicyId());
            viQuoteSpecialAgreement.setEngageCode(o.getEngageCode());
            viQuoteSpecialAgreement.setEngageContent(o.getEngageDetail());
            viQuoteSpecialAgreement.setEngageTitle(o.getEngageTitle());
            return viQuoteSpecialAgreement;
        });
    }

    private void setPreUnderwritingMsg(PremiumCalculateResultDTO result, HTICQuotedPriceRes hticQuotedPriceRes) {

    }

    private void fixCarTaxType(HTICContract ciContract, PremiumCalculateResultDTO result) {
        if (Objects.nonNull(ciContract) && Objects.nonNull(ciContract.getTax())) {
            HTICTax tax = ciContract.getTax();
            result.getViInsPlcyInfTps().forEach(o -> o.setCarTaxType(HTICCarTaxTypeEnum.matchTunsValue(tax.getTaxConditionCode())));
        }
    }

    private void mapScore(PremiumCalculateResultDTO result, HTICQuotedPriceRes hticQuotedPriceRes) {
        HTICContract biContract = HTICRiskCodeEnum.getBiContract(hticQuotedPriceRes.getBusinessData().getContract());
        HTICContract ciContract = HTICRiskCodeEnum.getCiContract(hticQuotedPriceRes.getBusinessData().getContract());
        for (ViInsPlcyInfTp viInsPlcyInfTp : result.getViInsPlcyInfTps()) {
            if (PolicyTypeEnum.CI.getValue().equals(viInsPlcyInfTp.getPolicyType())) {
                String renewalFlag = ciContract.getContractMain().getRenewalFlag();
                InsuredTypeEnum insuredTypeEnum = HTICInsuredTypeEnum.matchTunsValue(renewalFlag);
                viInsPlcyInfTp.setInsuredType(insuredTypeEnum);
                viInsPlcyInfTp.setInsuredTypeCi(insuredTypeEnum);
                String category = ciContract.getContractMain().getCategory();
                viInsPlcyInfTp.setBusinessCategory(StrUtil.subBefore(category, "类", true));
            }
            if (PolicyTypeEnum.BI.getValue().equals(viInsPlcyInfTp.getPolicyType())) {
                String renewalFlag = biContract.getContractMain().getRenewalFlag();
                InsuredTypeEnum insuredTypeEnum = HTICInsuredTypeEnum.matchTunsValue(renewalFlag);
                viInsPlcyInfTp.setInsuredType(insuredTypeEnum);
                viInsPlcyInfTp.setInsuredTypeBi(insuredTypeEnum);
                String category = biContract.getContractMain().getCategory();
                viInsPlcyInfTp.setBusinessCategory(StrUtil.subBefore(category, "类", true));
            }
        }
    }

    private void mapCi(PremiumCalculateResultDTO result, HTICContract ciContract) {
        ViInsPlcyInfTp ci = result.getViInsPlcyInfTps().stream()
                .filter(viInsPlcyInfTp -> PolicyTypeEnum.CI.getValue().equals(viInsPlcyInfTp.getPolicyType()))
                .findFirst().orElse(null);
        if (Objects.nonNull(ci)) {
            ci.setInsPremiumNumber(ciContract.getContractMain().getContractNo());
            ci.setProposalNumber(ciContract.getContractMain().getContractNo());
            ci.setRecordStatus(RecordStatusEnum.SUCCEED);
            ci.setInsuredStatus(InsuredStatusEnum.PREMIUMCACULATE.getValue());
            if (Objects.nonNull(ciContract.getPlatFormMessage()) && StrUtil.isNotBlank(ciContract.getPlatFormMessage().getQuerySequenceNo())) {
                ci.setDemandNo(ciContract.getPlatFormMessage().getQuerySequenceNo());
            }
            BigDecimal insuredPremium = ciContract.getCoverage().stream().map(HTICCoverage::getCoveragePremium).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal standardPremium = ciContract.getCoverage().stream().map(HTICCoverage::getCoverageStandardPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal sumTax = ciContract.getTax().getSumTax();
            ci.setInsuredPremium(insuredPremium);
            ci.setAttachPremium(sumTax);
            ci.setStandardPremium(standardPremium);
        }
    }

    private void mapBi(PremiumCalculateResultDTO result, HTICContract biContract) {
        ViInsPlcyInfTp bi = result.getViInsPlcyInfTps().stream()
                .filter(viInsPlcyInfTp -> PolicyTypeEnum.BI.getValue().equals(viInsPlcyInfTp.getPolicyType()))
                .findFirst().orElse(null);
        if (Objects.nonNull(bi)) {
            bi.setInsPremiumNumber(biContract.getContractMain().getContractNo());
            bi.setProposalNumber(biContract.getContractMain().getContractNo());
            bi.setRecordStatus(RecordStatusEnum.SUCCEED);
            bi.setInsuredStatus(InsuredStatusEnum.PREMIUMCACULATE.getValue());
            if (Objects.nonNull(biContract.getPlatFormMessage()) && StrUtil.isNotBlank(biContract.getPlatFormMessage().getQuerySequenceNo())) {
                bi.setDemandNo(biContract.getPlatFormMessage().getQuerySequenceNo());
            }
            BigDecimal insuredPremium = biContract.getCoverage().stream().map(HTICCoverage::getCoveragePremium).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal standardPremium = biContract.getCoverage().stream().map(HTICCoverage::getCoverageStandardPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
            bi.setInsuredPremium(insuredPremium);
            bi.setStandardPremium(standardPremium);
        }
    }

    /**
     * 险种详情处理
     */
    private ViInsKindDetialTp kindDetailHandle(ViInsuranceInf insuranceInf, ViInsPlcyInfTp plcyInfTp, ItemKind buyKind, HTICCoverage hticCoverage) {
        String kindCode = insuranceInf.getInsuranceCode();
        ViInsKindDetialTp viInsKindDetialTp = new ViInsKindDetialTp();
        //交强险
        if (NewKindCodeEnum.CI_JQ.getValue().equals(kindCode)) {
            viInsKindDetialTp.setPolicyId(plcyInfTp.getPolicyId());
            viInsKindDetialTp.setPolicyBatchId(plcyInfTp.getPolicyBatchId());
            viInsKindDetialTp.setOrderNo(1);
            viInsKindDetialTp.setKindCode(insuranceInf.getInsuranceCode());
            viInsKindDetialTp.setKindName(insuranceInf.getKindName());
            viInsKindDetialTp.setQuantity(0);
            viInsKindDetialTp.setUnitAmount(BigDecimal.valueOf(0.0));
            viInsKindDetialTp.setAmount(plcyInfTp.getInsAmount());
            viInsKindDetialTp.setPremium(plcyInfTp.getInsuredPremium());
            viInsKindDetialTp.setDiscount(BigDecimal.valueOf(0.0));
            viInsKindDetialTp.setRate(BigDecimal.valueOf(0.0));
            viInsKindDetialTp.setAddlMark(AddlMarkEnum.NO.getCode());
            viInsKindDetialTp.setBuyAddl(BuyAddlMarkEnum.NO.getCode());
            viInsKindDetialTp.setValueType(null);
            viInsKindDetialTp.setBusinessMark(BusinessMarkEnum.NO.getCode());
            viInsKindDetialTp.setExtend(null);
            viInsKindDetialTp.setCreUser(plcyInfTp.getCreUser());
            viInsKindDetialTp.setMdfUser(plcyInfTp.getMdfUser());
            viInsKindDetialTp.setDelFlag(InsConstants.DelFlag.NORMAL);
        } else if (NewKindCodeEnum.CI_BT.getValue().equals(kindCode)) {
            //车船税
            viInsKindDetialTp.setPolicyId(plcyInfTp.getPolicyId());
            viInsKindDetialTp.setPolicyBatchId(plcyInfTp.getPolicyBatchId());
            viInsKindDetialTp.setOrderNo(2);
            viInsKindDetialTp.setKindCode(insuranceInf.getInsuranceCode());
            viInsKindDetialTp.setKindName(insuranceInf.getKindName());
            viInsKindDetialTp.setQuantity(0);
            viInsKindDetialTp.setUnitAmount(BigDecimal.valueOf(0.0));
            viInsKindDetialTp.setAmount(BigDecimal.valueOf(0.0));
            viInsKindDetialTp.setPremium(plcyInfTp.getAttachPremium());
            viInsKindDetialTp.setDiscount(BigDecimal.valueOf(0.0));
            viInsKindDetialTp.setRate(BigDecimal.valueOf(0.0));
            viInsKindDetialTp.setAddlMark(AddlMarkEnum.NO.getCode());
            viInsKindDetialTp.setBuyAddl(BuyAddlMarkEnum.NO.getCode());
            viInsKindDetialTp.setValueType(null);
            viInsKindDetialTp.setBusinessMark(BusinessMarkEnum.NO.getCode());
            viInsKindDetialTp.setExtend(null);
            viInsKindDetialTp.setCreUser(plcyInfTp.getCreUser());
            viInsKindDetialTp.setMdfUser(plcyInfTp.getMdfUser());
            viInsKindDetialTp.setDelFlag(InsConstants.DelFlag.NORMAL);
        } else {
            //商业险
            viInsKindDetialTp.setPolicyId(plcyInfTp.getPolicyId());
            viInsKindDetialTp.setPolicyBatchId(plcyInfTp.getPolicyBatchId());
            Integer orderNo = Optional.ofNullable(buyKind.getOrderNo()).orElse(0);
            viInsKindDetialTp.setOrderNo(orderNo);
            viInsKindDetialTp.setKindCode(insuranceInf.getInsuranceCode());
            viInsKindDetialTp.setKindName(insuranceInf.getKindName());
            Integer quantity = Optional.ofNullable(buyKind.getQuantity())
                    .orElse(null);
            viInsKindDetialTp.setQuantity(quantity);
            viInsKindDetialTp.setUnitAmount(buyKind.getUnitAmount());
            viInsKindDetialTp.setAmount(hticCoverage.getAmount());
            viInsKindDetialTp.setPremium(hticCoverage.getCoveragePremium());
            viInsKindDetialTp.setDiscount(BigDecimal.ZERO);
            viInsKindDetialTp.setRate(BigDecimal.ZERO);
            viInsKindDetialTp.setAddlMark(AddlMarkEnum.NO.getCode());
            viInsKindDetialTp.setBuyAddl(BuyAddlMarkEnum.NO.getCode());
            viInsKindDetialTp.setValueType(null);
            viInsKindDetialTp.setBusinessMark(BusinessMarkEnum.NO.getCode());
            viInsKindDetialTp.setExtend(null);
            if (NewKindCodeEnum.isAdditionalServiceTerm(insuranceInf.getKindCode())) {
                viInsKindDetialTp.setUnitAmount(buyKind.getUnitAmount() == null ? BigDecimal.valueOf(0) : buyKind.getUnitAmount());
            }
        }
        return viInsKindDetialTp;
    }

    @Override
    public InsEnum getSupportIns() {
        return InsEnum.HTIC;
    }

    /**
     * 华泰保费试算前置 查询车辆实际价值
     * @param apiPremiumCalculateDTO
     * @return
     */
    @Override
    public String preprocessThirdPartyQuotes(APIPremiumCalculateDTO apiPremiumCalculateDTO){
        log.debug("华泰API保费试算前置处理开始,batchId:{}",apiPremiumCalculateDTO.getPolicyBatchId());
        Map<String,String> dataMap = new HashMap<>();
        HTICActualValueRes hticActualValueRes = calculateCarActualPrice(apiPremiumCalculateDTO);
        String currentValue=hticActualValueRes.getActualValue();
        dataMap.put("actualPrice",currentValue);
        log.debug("华泰API保费试算前置处理结束,batchId:{},返回数据:{}",apiPremiumCalculateDTO.getPolicyBatchId(),JSONUtil.toJsonStr(dataMap));
        return JSONUtil.toJsonStr(dataMap);
    }
}
