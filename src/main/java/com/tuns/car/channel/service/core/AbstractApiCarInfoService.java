package com.tuns.car.channel.service.core;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.tuns.car.channel.dto.QueryCarActualPriceDTO;
import com.tuns.car.channel.matcher.CarModelAutoMatcher;
import com.tuns.car.channel.sort.CarModelSorter;
import com.tuns.car.channel.util.CalCarUserYearUtil;
import com.tuns.car.core.dto.car.CarInfoDTO;
import com.tuns.car.core.vo.car.CarInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 车型查询服务编排
 *
 * <AUTHOR>
 * @since 2025-1-21 14:01
 */
@Slf4j
public abstract class AbstractApiCarInfoService implements ApiCarInfoService {

    /**
     * 车型查询流程
     *
     * @param carInfoDTO
     * @return
     */
    @Override
    public List<CarInfoVO> queryCarInfo(CarInfoDTO carInfoDTO) {
        List<CarInfoVO> result;
        boolean exactQuery = Objects.nonNull(carInfoDTO.getExactQuery()) && carInfoDTO.getExactQuery();
        boolean secondQuery = Objects.nonNull(carInfoDTO.getQueryModel()) && BooleanUtil.toBoolean(carInfoDTO.getQueryModel());
        if (exactQuery) {
            result = brandModelQuery(carInfoDTO);
        } else if (secondQuery) {
            result = secondQuery(carInfoDTO);
        } else {
            result = normalQuery(carInfoDTO);
        }
        if (CollUtil.isEmpty(result)) {
            result = compensationScheme(carInfoDTO);
        }
        if (Objects.nonNull(carInfoDTO.getCarModelFix()) && carInfoDTO.getCarModelFix()) {
            return result;
        }
        if (carInfoDTO.getCarAutoConfirmSwitch() && Objects.nonNull(carInfoDTO.getCarAutoConfirm()) && carInfoDTO.getCarAutoConfirm()) {
            result = carConfirm(carInfoDTO, result);
        } else {
            result = CarModelSorter.sort(result, carInfoDTO.getRegDate(), carInfoDTO.getSeatCount());
        }
        return result;
    }

    /**
     * 1.根据业务规则匹配车型
     * 2.存在二次车型查询需处理二次车型查询
     *
     * @param carInfoDTO
     * @param result
     * @return
     */
    private List<CarInfoVO> carConfirm(CarInfoDTO carInfoDTO, List<CarInfoVO> result) {
        log.info("保险公司：{} 原始车型数据集：{}", carInfoDTO.getCompanyCode(), JSONUtil.toJsonStr(result));
        result = CarModelAutoMatcher.carMatch(result, carInfoDTO, carInfoDTO.getHomePageQuery());
        if (CollUtil.isEmpty(result)) {
            return result;
        }
        result = autoSecondQuery(result, carInfoDTO);
        if (CollUtil.isEmpty(result)) {
            return result;
        }
        result = CarModelAutoMatcher.carMatch(result, carInfoDTO, carInfoDTO.getHomePageQuery());
        result = filterResultWhenChanQuery(result, carInfoDTO);
        return result;
    }

    /**
     * 渠道查询需精准最终返回车型 并重置一些属性为首页查询时的查询结果
     * 3、当车辆信息页面的核定载人数（8）与当前最终获取的车牌品牌型号数据的核定载人数（7）与不一致，则程序无需更新核定载人数，直接用车辆信息页面的核定载人数（8）进行报价
     * <p>
     * 4、当车辆信息页面的核定载质量（455千克）与当前最终获取的车牌品牌型号数据的核定载质量（490千克）与不一致，则程序无需更新核定载质量，直接用车辆信息页面的核定载质量（455千克）进行报价
     * <p>
     * 5、当车辆信息页面的整备质量（2970千克）与当前最终获取的车牌品牌型号数据的整备质量（2972千克）与不一致，则程序无需更新整备质量，直接用车辆信息页面的整备质量（2970千克）进行报价
     * <p>
     *
     * @param result
     * @param carInfoDTO
     */
    private List<CarInfoVO> filterResultWhenChanQuery(List<CarInfoVO> result, CarInfoDTO carInfoDTO) {
        if (CollUtil.isEmpty(result)) {
            return Collections.emptyList();
        }
        if (Objects.nonNull(carInfoDTO.getHomePageQuery()) && carInfoDTO.getHomePageQuery()) {
            return result;
        }
        CarInfoVO carInfoVO = result.get(0);
        carInfoVO.setSeatCount(carInfoDTO.getSeatCount());
        if (Objects.nonNull(carInfoDTO.getWholeWeight()) && carInfoDTO.getWholeWeight() != 0) {
            carInfoVO.setWholeWeight(carInfoDTO.getWholeWeight());
        }
        carInfoVO.setCarTonnage(carInfoDTO.getCarTonnage());
        carInfoVO.setFuelType(carInfoDTO.getFuelType());
        return Collections.singletonList(carInfoVO);
    }

    /**
     * 自动匹配车型中 处理二次车型查询
     *
     * @param result
     * @param carInfoDTO
     * @return
     */
    protected List<CarInfoVO> autoSecondQuery(List<CarInfoVO> result, CarInfoDTO carInfoDTO) {
        return result;
    }

    /**
     * 提供给部分公司实现特有的补偿机制
     *
     * @param carInfoDTO
     * @return
     */
    protected List<CarInfoVO> compensationScheme(CarInfoDTO carInfoDTO) {
        return Collections.emptyList();
    }

    /**
     * 调用 {@link AbstractApiCarInfoService#vinQuery(CarInfoDTO)} 没有结果后 再通过 {@link AbstractApiCarInfoService#brandModelQuery(CarInfoDTO)}
     *
     * @param carInfoDTO
     * @return
     */
    private List<CarInfoVO> normalQuery(CarInfoDTO carInfoDTO) {
        List<CarInfoVO> carInfoList = vinQuery(carInfoDTO);
        if (CollUtil.isEmpty(carInfoList) && nonFusionQuery()) {
            carInfoList = brandModelQuery(carInfoDTO);
        }
        return carInfoList;
    }

    /**
     * 非融合查询 意为厂牌型号与VIN查询是独立的两个接口
     *
     * @return
     */
    protected boolean nonFusionQuery() {
        return false;
    }

    /**
     * 厂牌型号查询
     *
     * @param carInfoDTO
     * @return
     */
    protected abstract List<CarInfoVO> brandModelQuery(CarInfoDTO carInfoDTO);

    /**
     * VIN查询
     *
     * @param carInfoDTO
     * @return
     */
    protected abstract List<CarInfoVO> vinQuery(CarInfoDTO carInfoDTO);

    /**
     * 二次查询
     *
     * @param carInfoDTO
     * @return
     */
    protected List<CarInfoVO> secondQuery(CarInfoDTO carInfoDTO) {
        return Collections.emptyList();
    }

    @Override
    public Integer queryUserYears(QueryCarActualPriceDTO req) {
        String startDate = StringUtils.isNotBlank(req.getBaseInfo().getInsBeginBi()) ? req.getBaseInfo().getInsBeginBi() : req.getBaseInfo().getInsBeginCi();
        return CalCarUserYearUtil.calCarUserYear(startDate, req.getCarInfo().getRegDate());
    }
}
