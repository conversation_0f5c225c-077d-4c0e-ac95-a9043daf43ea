package com.tuns.car.channel.service.core.zcbx;

import cn.hutool.core.date.DatePattern;
import com.tuns.car.channel.dto.zcbx.req.ZCBXCheckCarCodeReq;
import com.tuns.car.channel.dto.zcbx.res.ZCBXCheckCarCodeBodyRes;
import com.tuns.car.channel.rpc.zcbx.ZCBXXmlRpcService;
import com.tuns.car.channel.service.core.AbstractApiCheckCarCodeService;
import com.tuns.car.core.constant.InsEnum;
import com.tuns.car.core.dto.special.CheckCarCodeDTO;
import com.tuns.car.core.dto.special.CheckCarCodeQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * 大家保险 -- 查询验车码业务处理类
 *
 * <AUTHOR>
 * @since 2022/6/6
 */
@Service
public class ZCBXApiCheckCarCodeServiceImpl extends AbstractApiCheckCarCodeService<ZCBXCheckCarCodeReq, String> {

    @Autowired
    private ZCBXXmlRpcService zcbxXmlRpcService;

    @Override
    public InsEnum getSupportIns() {
        return InsEnum.ZCBX;
    }

    @Override
    public ZCBXCheckCarCodeReq mapReq(CheckCarCodeQueryDTO checkCarCodeDTO) {
        ZCBXCheckCarCodeReq zcbxCheckCarCodeReq = new ZCBXCheckCarCodeReq();
        zcbxCheckCarCodeReq.setGenDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN)));
        return zcbxCheckCarCodeReq;
    }

    @Override
    public String doInvoke(ZCBXCheckCarCodeReq req, CheckCarCodeQueryDTO checkCarCodeDTO) {
        ZCBXCheckCarCodeBodyRes zcbxCheckCarCodeBodyRes = zcbxXmlRpcService.checkCarCode(req);
        String verCode = null;
        if(Objects.nonNull(zcbxCheckCarCodeBodyRes.getBodyDto())){
            verCode = zcbxCheckCarCodeBodyRes.getBodyDto().getVerCode();
        }
        return verCode;
    }

    @Override
    public CheckCarCodeDTO mapRes(String res, ZCBXCheckCarCodeReq o2, CheckCarCodeQueryDTO checkCarCodeQueryDTO) {
        CheckCarCodeDTO checkCarCodeDTO = new CheckCarCodeDTO();
        checkCarCodeDTO.setCheckCarCode(res);
        return checkCarCodeDTO;
    }


}
