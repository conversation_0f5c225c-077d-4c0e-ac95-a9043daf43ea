package com.tuns.car.channel.service.core;

import com.tuns.car.channel.constant.CallbackEnum;
import com.tuns.car.channel.dto.callback.CallBackDTO;
import com.tuns.car.channel.dto.callback.CallBackResDTO;
import com.tuns.car.core.InsServant;

/**
 * 通知回调处理接口
 * 一个类实现该接口代表这个类会提供 {@link #getSupportIns()}保险公司 {@link #getNotifyType()}通知消息的处理服务
 *
 * <AUTHOR>
 */
public interface ApiCallBackService extends InsServant {
    /**
     * 通知回调
     *
     * @param callBackDTO
     * @return
     */
    CallBackResDTO callback(CallBackDTO callBackDTO);

    /**
     * 获取支持处理的通知类型
     *
     * @return
     */
    CallbackEnum getNotifyType();
}
