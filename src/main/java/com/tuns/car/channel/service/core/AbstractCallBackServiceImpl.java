package com.tuns.car.channel.service.core;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.tuns.car.channel.dto.callback.CallBackDTO;
import com.tuns.car.channel.dto.callback.CallBackInsResDTO;
import com.tuns.car.channel.dto.callback.CallBackOwnDataDTO;
import com.tuns.car.channel.dto.callback.CallBackResDTO;
import com.tuns.core.boot.exception.TunsBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @description: 回调通知抽象服务
 * {@link InsData} 验签结果 保险公司对应的数据结构
 * {@link OwnData} 我方通用数据类型(不同通知结构不同)
 * @author: gk、lzy
 */
@Slf4j
public abstract class AbstractCallBackServiceImpl<InsData, OwnData extends CallBackOwnDataDTO> implements ApiCallBackService {

    @Autowired
    private List<InsCallBackResponse> insCallBackResponses;

    /**
     * 1.验签
     * 2.转化成保险公司数据结构object
     *
     * @param originRequestBody
     * @return
     */
    protected abstract InsData checkDataAndTransToInsData(String originRequestBody);

    /**
     * 将保险公司数据结构映射成我方通用数据结构(不同的通知类型结构不同)
     *
     * @param insData
     * @return
     */
    protected abstract OwnData mapToOwnData(InsData insData);


    @Override
    public CallBackResDTO callback(CallBackDTO callBackDTO) {
        // 获取保险公司对应的回调处理类
        InsCallBackResponse processService = getProcessService();
        try {
            // 1. 验签并转化成保险公司数据对象
            InsData insData = checkDataAndTransToInsData(callBackDTO.getRequestBody());
            // 2. 将保险公司数据结构映射为我方数据结构
            CallBackOwnDataDTO ownData = mapToOwnData(insData);
            // 3. 返回保险公司指定的回调响应
            CallBackInsResDTO callBackResDTO = processService.successResponse(callBackDTO.getNotifyType());
            return packageResult(callBackResDTO, ownData, JSONUtil.toJsonStr(insData));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            CallBackInsResDTO callBackInsResDTO = processService.failResponse(callBackDTO.getNotifyType());
            return packageResult(callBackInsResDTO, null, null);
        }
    }

    /**
     * 获取保险公司对应的 回调处理类
     *
     * @return
     */
    private InsCallBackResponse getProcessService() {
        InsCallBackResponse callbackResponse = insCallBackResponses.stream()
                .filter(insCallBackResponse -> insCallBackResponse.getSupportIns() == getSupportIns())
                .findFirst()
                .orElseThrow(() -> new TunsBusinessException("该保险公司还没实现api回调"));
        return callbackResponse;
    }

    private CallBackResDTO packageResult(CallBackInsResDTO callBackResDTO, CallBackOwnDataDTO ownData, String insData) {
        CallBackResDTO result = BeanUtil.toBean(callBackResDTO, CallBackResDTO.class);
        if (ownData != null) {
            result.setOwnData(JSONUtil.toJsonStr(ownData));
            result.setBusinessInfo(ownData);
            result.setInsData(insData);
        }
        return result;
    }
}
