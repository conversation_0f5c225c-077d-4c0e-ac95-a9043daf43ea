package com.tuns.car.channel.service.core;

import com.tuns.car.channel.dto.nocar.NoCarOrderSimpleDTO;
import com.tuns.car.channel.dto.nocar.ViPlcySimpleDTO;
import com.tuns.car.core.InsWebsiteServant;
import com.tuns.car.core.dto.gi.PaySuccessAutoCreatePolicyDTO;

import java.util.List;

/**
 *
 *个险 非车服务类
 *<AUTHOR>
 *@since          2022-12-06 16:33
 *
 **/
public interface GINoCarApiCommonService extends InsWebsiteServant {
    /**
     * 查询非车投保单号/保单号
     *
     * @param policyBatchId*/
    PaySuccessAutoCreatePolicyDTO getCarProposalNo(Long policyBatchId, List<ViPlcySimpleDTO> spViInsPlcyInf, List<NoCarOrderSimpleDTO> giOrders);
}
