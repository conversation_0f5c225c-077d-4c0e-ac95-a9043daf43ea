package com.tuns.car.channel.service.core;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.dto.special.CheckCarCodeDTO;
import com.tuns.car.core.dto.special.CheckCarCodeQueryDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * api查询保司验车码 流程抽象实现，负责编排整体的保费试算流程
 * 具体的每家保险公司查询保司验车码 对接，需要子类实现本类方法。
 * {@link Req} 查询保司验车码接口 请求数据结构
 * {@link Res} 查询保司验车码接口 响应数据结构
 *
 * <AUTHOR>
 * @since 2022/5/27
 */
@Slf4j
public abstract class AbstractApiCheckCarCodeService<Req, Res> implements ApiCheckCarCodeService, ApiInvoke<CheckCarCodeQueryDTO, CheckCarCodeDTO, Req, Res> {
    @Override
    public CheckCarCodeDTO checkCarCode(CheckCarCodeQueryDTO checkCarCodeQueryDTO) {
        Req req = mapReq(checkCarCodeQueryDTO);
        log.info(getSupportIns().getCompanyName() + "===>查询验车码：{}", JSON.toJSONString(req));
        Res res = doInvoke(req, null);
        log.info(getSupportIns().getCompanyName() + "<===查询验车码：{}", JSON.toJSONString(res));
        CheckCarCodeDTO result = mapRes(res, req, null);
        return result;
    }
}
