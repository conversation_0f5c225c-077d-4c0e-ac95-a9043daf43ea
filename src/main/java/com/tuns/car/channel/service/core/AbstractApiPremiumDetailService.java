package com.tuns.car.channel.service.core;

import cn.hutool.json.JSONUtil;
import com.tuns.car.core.dto.compare.QueryProposalDetailDTO;
import com.tuns.core.boot.utils.TransOption;
import com.tuns.core.boot.utils.TunsBeanUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * @Description: api报价单详情流程抽象实现，负责编排整体的报价单详情流程 <br />
 * * 具体的每家保险公司报价单详情对接，需要子类实现{@link ApiInvoke}的方法。  <br />
 * * {@link Req} 保险公司报价单详情接口 请求数据结构  <br />
 * * {@link Res} 保险公司报价单详情接口 响应数据结构  <br />
 * @Author: <a href="http://www.loserzhao.com">JoMin</a>
 * @Create: 2024-11-20 23:38
 */
@Slf4j
public abstract class AbstractApiPremiumDetailService<Req, Res> implements APIPremiumDetailService, ApiInvoke<QueryProposalDetailDTO, Map<String, Object>, Req, Res> {
    @Override
    public Map<String, Object> queryPremiumDetail(QueryProposalDetailDTO proposalDetailDTO) {
        Req req = mapReq(proposalDetailDTO);
        log.info(getSupportIns().getCompanyName() + "===>报价单详情流程：{}", JSONUtil.toJsonStr(req));
        Object detail = doInvoke(req, proposalDetailDTO);
        log.info(getSupportIns().getCompanyName() + "<===报价单详情流程：{}", JSONUtil.toJsonStr(detail));
        TransOption transOption = TransOption.create().ignoreNull().keepList();
        Map<String, Object> flatToMap = TunsBeanUtil.beanFlatToMap(detail, transOption);
        log.info("insData beanFlatToMap ===>{}", JSONUtil.toJsonStr(flatToMap));
        return flatToMap;
    }
}
