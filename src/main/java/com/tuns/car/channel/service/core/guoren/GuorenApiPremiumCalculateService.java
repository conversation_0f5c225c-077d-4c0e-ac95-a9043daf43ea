package com.tuns.car.channel.service.core.guoren;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.tuns.car.channel.constants.guoren.*;
import com.tuns.car.channel.dto.APIPremiumCalculateDTO;
import com.tuns.car.channel.dto.guoren.*;
import com.tuns.car.channel.dto.tpic.premium.GuorenPremiumReqDTO;
import com.tuns.car.channel.rpc.guoren.GuorenRpcService;
import com.tuns.car.channel.service.core.AbstractApiPremiumCalculateService;
import com.tuns.car.channel.util.LastClaimUtil;
import com.tuns.car.core.constant.*;
import com.tuns.car.core.dto.car.CarThirdInsureDTO;
import com.tuns.car.core.dto.car.PremiumCalculateResultDTO;
import com.tuns.car.core.dto.carprocess.premium.AreaDTO;
import com.tuns.car.core.dto.carprocess.premium.SpecialInfoDTO;
import com.tuns.car.core.dto.carprocess.premium.request.CarInfo;
import com.tuns.car.core.dto.carprocess.premium.request.ItemKind;
import com.tuns.car.core.dto.carprocess.premium.request.LicenseAddressInfo;
import com.tuns.car.core.dto.carprocess.premium.request.PersonInfo;
import com.tuns.car.core.dto.charging.ChargingKind;
import com.tuns.car.core.dto.charging.ChargingPileDTO;
import com.tuns.car.core.dto.gi.*;
import com.tuns.car.core.dto.offerorder.DeleteOfferOrderDTO;
import com.tuns.car.core.entity.*;
import com.tuns.core.boot.constant.InsConstants;
import com.tuns.core.boot.constant.YesNoNumberEnum;
import com.tuns.core.boot.exception.TunsBusinessException;
import com.tuns.core.boot.utils.BigDecimalUtil;
import com.tuns.core.boot.utils.DateTimeUtil;
import com.tuns.core.boot.utils.TunsBeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description 国任保费试算的处理类
 * @create 2022-06-07 10:52
 **/
@Slf4j
@Service
public class GuorenApiPremiumCalculateService extends AbstractApiPremiumCalculateService<GuorenPremCalculateReqDTO, GuorenPremCalculateDataResDTO, GuorenActualValue> {
    /**
     * 交强险
     */
    public static final String CI = "0507";

    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN);

    @Autowired
    private GuorenRpcService guoRenRpcService;
    @Autowired
    private GuorenActualValueQueryService guorenActualValueQueryService;
    @Autowired
    private GuorenPremiumCalculateErrorResponseHandler guorenPremiumCalculateErrorResponseHandler;
    @Autowired
    private GuorenApiInsureConfirmServiceImpl insureConfirmService;
    @Autowired
    private GuorenDeleteProposalServiceImpl deleteProposalService;

    @Autowired
    private GuorenChannelQueryService guorenChannelQueryService;


    @Override
    protected GuorenActualValue beforeProcess(APIPremiumCalculateDTO inputDTO) {
        GuorenQueryActualValueMapDTO guorenQueryActualValueMapDTO = new GuorenQueryActualValueMapDTO();
        guorenQueryActualValueMapDTO.setNewEnergy(inputDTO.getNewEnergy());
        Map<String, ViInsPlcyInfTp> viInsPlcyInfTpMap = inputDTO.getViInsPlcyInfTpMap();
        ViInsPlcyInfTp bi = viInsPlcyInfTpMap.get(PolicyTypeEnum.BI.getValue());
        ViInsPlcyInfTp ci = viInsPlcyInfTpMap.get(PolicyTypeEnum.CI.getValue());
        guorenQueryActualValueMapDTO.setInsBegin(Objects.nonNull(bi) ? bi.getInsBegin() : ci.getInsBegin());
        guorenQueryActualValueMapDTO.setLicenseColor(inputDTO.getSpecialInfo().getLicenseColor());
        guorenQueryActualValueMapDTO.setVehicleTypeRelation(inputDTO.getVehicleTypeRelation());
        guorenQueryActualValueMapDTO.setCarInfo(inputDTO.getCarInfo());
        guorenQueryActualValueMapDTO.setPersonInfoMap(inputDTO.getPersonInfoMap());
        guorenQueryActualValueMapDTO.setVehicleVhlCertDTO(inputDTO.getSpecialInfo().getVehicleVhlCertDTO());
        //保费试算前置获取数据
        GuorenPremiumReqDTO guorenPremiumReqDTO;
        if (StringUtils.isNotBlank(inputDTO.getPreQuoteResult())) {
            guorenPremiumReqDTO = JSONUtil.toBean(inputDTO.getPreQuoteResult(), GuorenPremiumReqDTO.class);
            return guorenPremiumReqDTO.getGuorenActualValue();
        }
        //查询车辆实际价值返回数据
        return guorenActualValueQueryService.queryActualValue(guorenQueryActualValueMapDTO);
    }

    @Override
    public GuorenPremCalculateReqDTO mapReq(APIPremiumCalculateDTO apiPremiumCalculateDTO, GuorenActualValue preRes) {
        GuorenPremiumReqDTO guorenPremiumReqDTO = null;
        if (org.apache.commons.lang.StringUtils.isNotBlank(apiPremiumCalculateDTO.getPreQuoteResult())) {
            guorenPremiumReqDTO = JSONUtil.toBean(apiPremiumCalculateDTO.getPreQuoteResult(), GuorenPremiumReqDTO.class);
        } else {
            throw new TunsBusinessException("国任API保费前置计算异常,批次号:{}", apiPremiumCalculateDTO.getPolicyBatchId());
        }
        Map<String, ViInsPlcyInfTp> viInsPlcyInfTpMap = apiPremiumCalculateDTO.getViInsPlcyInfTpMap();
        GuorenPremCalculateReqDTO guorenPremCalculateReqDTO = new GuorenPremCalculateReqDTO();
        guorenPremCalculateReqDTO.setUuid(preRes.getInputvo());
        guorenPremCalculateReqDTO.setActualValue(preRes.getActualValue());
        //商业险处理
        GuorenBusiness bi = guorenPremiumReqDTO.getGuorenBusiness();
        guorenPremCalculateReqDTO.setBi(bi);
        //交强险处理
        GuorenCompulsory ci = guorenPremiumReqDTO.getGuorenCompulsory();
        guorenPremCalculateReqDTO.setCi(ci);
        //投被保人信息
        GuorenPrpTinsuredDTO prpTinsured = populatePrpTinsured(apiPremiumCalculateDTO);
        GuorenPrpBaseBcDTO bc = new GuorenPrpBaseBcDTO();
        bc.setPrpTinsured(prpTinsured);
        guorenPremCalculateReqDTO.setBc(bc);
        return guorenPremCalculateReqDTO;
    }

    private GuorenPrpTinsuredDTO populatePrpTinsured(APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        GuorenPrpTinsuredDTO guorenPrpTinsuredDTO = new GuorenPrpTinsuredDTO();
        Map<String, PersonInfo> personInfoMap = apiPremiumCalculateDTO.getPersonInfoMap();
        Map<ViPersonTypeEnum, AreaDTO> addressMap = apiPremiumCalculateDTO.getAddressMap();
        PersonInfo holder = personInfoMap.get(ViPersonTypeEnum.HOLDER.getValue());
        guorenPrpTinsuredDTO.setAppliUnitNature(GuoRenUnitNatureEnum.getGuorenRelationTypeEnum(holder.getNature().getValue()));
        guorenPrpTinsuredDTO.setAppliName(holder.getPersonName());
        guorenPrpTinsuredDTO.setAppliInsuredNature(GuorenPersonNatureEnum.getGuorenRelationTypeEnum(holder.getNature().getValue()));
        guorenPrpTinsuredDTO.setAppliIdentifyType(GuorenIdentifyTypeEnum.getTunsCodeEnum(holder.getIdentifyType()).getCode());
        guorenPrpTinsuredDTO.setAppliIdentifyNumber(holder.getIdentifyNumber());
        guorenPrpTinsuredDTO.setAppliMobile(holder.getMobilePhone());
        guorenPrpTinsuredDTO.setAppliAddress(holder.getAddressComplete());
        guorenPrpTinsuredDTO.setAppliPostCode("410000");
        AreaDTO holderArea = addressMap.get(ViPersonTypeEnum.HOLDER);
        guorenPrpTinsuredDTO.setAppliProvinceName(holderArea.getProvinceName());
        guorenPrpTinsuredDTO.setAppliCityName(holderArea.getCityName());
        guorenPrpTinsuredDTO.setAppliEmail(holder.getEmail());
        guorenPrpTinsuredDTO.setAppliIDValidStartDate(holder.getIdentityValidityStart());
        guorenPrpTinsuredDTO.setAppliIDValidDate(holder.getIdentityValidity());
        //投保人为个人是必传 0:非长期有效，1：长期有效
        String holderLongFlag = holder.getIdentityValidity().contains("2099") ? YesNoNumberEnum.YES.getValue() : YesNoNumberEnum.NO.getValue();
        if (PersonNatureEnum.PERSON == holder.getNature()) {
            guorenPrpTinsuredDTO.setAppliValidDateLongFlag(holderLongFlag);
        } else {
            guorenPrpTinsuredDTO.setAppliUnitcodeTimes(holder.getIdentityValidity());
            guorenPrpTinsuredDTO.setAppliUnitcodeTimesLongFlag(holderLongFlag);//投保人为个人是必传 0:非长期有效，1：长期有效
        }
        PersonInfo insured = personInfoMap.get(ViPersonTypeEnum.INSURED.getValue());
        guorenPrpTinsuredDTO.setInsuredUnitNature(GuoRenUnitNatureEnum.getGuorenRelationTypeEnum(insured.getNature().getValue()));
        guorenPrpTinsuredDTO.setInsuredName(insured.getPersonName());
        guorenPrpTinsuredDTO.setInsuredInsuredNature(GuorenPersonNatureEnum.getGuorenRelationTypeEnum(insured.getNature().getValue()));
        guorenPrpTinsuredDTO.setInsuredMobile(insured.getMobilePhone());
        guorenPrpTinsuredDTO.setInsuredIdentifyType(GuorenIdentifyTypeEnum.getTunsCodeEnum(insured.getIdentifyType()).getCode());
        guorenPrpTinsuredDTO.setInsuredIdentifyNumber(insured.getIdentifyNumber());
        guorenPrpTinsuredDTO.setInsuredAddress(insured.getAddressComplete());
        guorenPrpTinsuredDTO.setInsuredPostCode("410000");
        guorenPrpTinsuredDTO.setInsuredEmail(insured.getEmail());
        AreaDTO insuredArea = addressMap.get(ViPersonTypeEnum.INSURED);
        guorenPrpTinsuredDTO.setInsuredProvinceName(insuredArea.getProvinceName());
        guorenPrpTinsuredDTO.setInsuredCityName(insuredArea.getCityName());
        guorenPrpTinsuredDTO.setInsuredIDValidStartDate(insured.getIdentityValidityStart());
        guorenPrpTinsuredDTO.setInsuredIDValidDate(insured.getIdentityValidity());
        //被保人为个人是必传 0:非长期有效，1：长期有效
        String insuredLongFlag = insured.getIdentityValidity().contains("2099") ? YesNoNumberEnum.YES.getValue() : YesNoNumberEnum.NO.getValue();
        if (PersonNatureEnum.PERSON == insured.getNature()) {
            guorenPrpTinsuredDTO.setInsuredValidDateLongFlag(insuredLongFlag);
        } else {
            guorenPrpTinsuredDTO.setInsuredUnitcodeTimes(insured.getIdentityValidity());
            guorenPrpTinsuredDTO.setInsuredUnitcodeTimesLongFlag(insuredLongFlag);
        }
        guorenPrpTinsuredDTO.setAppliNationality("中国");
        PersonInfo owner = personInfoMap.get(ViPersonTypeEnum.OWNER.getValue());
        CarInfo carInfo = apiPremiumCalculateDTO.getCarInfo();
        LicenseAddressInfo licenseAddressInfo = carInfo.getLicenseAddressInfo();
        if (Objects.nonNull(licenseAddressInfo)) {
            guorenPrpTinsuredDTO.setOcrOwnerAddress(licenseAddressInfo.getDetail());
        }
        return guorenPrpTinsuredDTO;
    }


    @Override
    public GuorenPremCalculateDataResDTO doInvoke(GuorenPremCalculateReqDTO guorenPremCalculateReqDTO, APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        updateActualValueAndAmount(guorenPremCalculateReqDTO, apiPremiumCalculateDTO);
        BigDecimal expectDiscount = apiPremiumCalculateDTO.getBaseDTO().getExpectDiscount();
        guorenPremCalculateReqDTO.getBi().getPrpTmain().setExpectDiscount("");
        guorenPremCalculateReqDTO.getBi().getPrpTmain().setFeeandDiscountFlag("0");
        if (expectDiscount != null) {
            //当需要调整折扣计算时，需要先不带折扣进行一次保费计算
            adjustDiscountByFirstCalc(guorenPremCalculateReqDTO, apiPremiumCalculateDTO);
            guorenPremCalculateReqDTO.getBi().getPrpTmain().setFeeandDiscountFlag("2");
            guorenPremCalculateReqDTO.getBi().getPrpTmain().setExpectDiscount(String.valueOf(expectDiscount));
        }
        log.debug("国任===>保费试算，{}", JSONUtil.toJsonStr(guorenPremCalculateReqDTO));
        GuorenPremCalculateDataResDTO guorenPremCalculateResDTO = guoRenRpcService.premiumCalculate(guorenPremCalculateReqDTO);
        log.debug("国任<===保费试算，{}", JSONUtil.toJsonStr(guorenPremCalculateResDTO));
        guorenPremiumCalculateErrorResponseHandler.handle(guorenPremCalculateResDTO, apiPremiumCalculateDTO);
        if ("0".equals(guorenPremCalculateResDTO.getResultCode())) {
            if (StringUtils.isNotEmpty(guorenPremCalculateResDTO.getData().getSumTopRate())) {
                if (StringUtils.isNotEmpty(guorenPremCalculateReqDTO.getBi().getPrpTmain().getExpectDiscount())) {
                    if (Double.parseDouble(guorenPremCalculateReqDTO.getBi().getPrpTmain().getExpectDiscount()) > Double.parseDouble(guorenPremCalculateResDTO.getData().getSumTopRate())
                            || Double.parseDouble(guorenPremCalculateReqDTO.getBi().getPrpTmain().getExpectDiscount()) < Double.parseDouble(guorenPremCalculateResDTO.getData().getSumLowerRate())) {
                        throw new TunsBusinessException("国任保险接口提示:最高折扣为 " + guorenPremCalculateResDTO.getData().getSumTopRate() + " 最低折扣为 " + guorenPremCalculateResDTO.getData().getSumLowerRate() + " 请修复后重新报价！");
                    }
                }
            }
        }
        return guorenPremCalculateResDTO;
    }

    @Override
    public PremiumCalculateResultDTO mapRes(GuorenPremCalculateDataResDTO res, GuorenPremCalculateReqDTO req, APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        if ("1".equals(res.getResultCode())) {
            throw new TunsBusinessException("国任保险接口调用异常，异常信息:" + res.getResultMsg());
        }
        //查询新转续标志
        GuorenQueryRenewalDTO guorenQueryRenewalDTO = new GuorenQueryRenewalDTO();
        guorenQueryRenewalDTO.setFrameNo(apiPremiumCalculateDTO.getCarInfo().getFrameNumber());
        guorenQueryRenewalDTO.setLicenseNo(apiPremiumCalculateDTO.getCarInfo().getPlateNumber());
        GuorenQueryRenewalRes guorenQueryRenewalRes = guoRenRpcService.queryRenewal(guorenQueryRenewalDTO);
        GuorenChannelResDTO guorenChannelResDTO = guorenChannelQueryService.queryChannel();
        List<ViInsPlcyInfTp> viInsPlcyInfTps = apiPremiumCalculateDTO.getViInsPlcyInfTps();
        List<ViInsKindDetialTp> viInsKindDetialTps = new ArrayList<>();
        for (ViInsPlcyInfTp viInsPlcyInfTp : viInsPlcyInfTps) {
            if (PolicyTypeEnum.BI.getValue().equals(viInsPlcyInfTp.getPolicyType())) {
                handleBi(res.getData(), apiPremiumCalculateDTO, viInsKindDetialTps, viInsPlcyInfTp, guorenQueryRenewalRes.getData());
            } else {
                handleCi(res.getData(), apiPremiumCalculateDTO, viInsKindDetialTps, viInsPlcyInfTp, guorenQueryRenewalRes.getData());
            }
            //决策单元
            GuorenChannel prptmain = guorenChannelResDTO.getPrptmain();
            viInsPlcyInfTp.setXdcxDmu(prptmain.getUnitMaintenanceName());
        }
        PremiumCalculateResultDTO result = BeanUtil.toBean(apiPremiumCalculateDTO, PremiumCalculateResultDTO.class);
        result.getCarInfo().setActualPrice(BigDecimal.valueOf(req.getActualValue()));
        result.setViInsPlcyInfTps(viInsPlcyInfTps);
        result.setViInsKindDetialTps(viInsKindDetialTps);
        //预核保
        String preUnderwritingMsg = doPreUnderwriting(apiPremiumCalculateDTO, result, guorenChannelResDTO);
        result.getViInsPlcyInfTps().forEach(viInsPlcyInfTp -> viInsPlcyInfTp.setPreUnderwritingMsg(preUnderwritingMsg));
        //特别约定
        List<ViQuoteSpecialAgreement> specialAgreementDtoList = result.getSpecialAgreementDtoList();
        List<String> listClauseCode = Lists.newLinkedList();
        if (CollUtil.isNotEmpty(specialAgreementDtoList)) {
            for (ViQuoteSpecialAgreement agreement : specialAgreementDtoList) {
                listClauseCode.add(agreement.getEngageCode());
            }
        }
        long policyBatchId = Long.parseLong(apiPremiumCalculateDTO.getPolicyBatchId());
        if (CollUtil.isNotEmpty(res.getData().getBiPrptenages())) {
            List<ViQuoteSpecialAgreement> biInsSpecialAgreement = resolveInsData(res.getData().getBiPrptenages(), policyBatchId, PolicyTypeEnum.BI);
            for (ViQuoteSpecialAgreement agreement : biInsSpecialAgreement) {
                if (!listClauseCode.contains(agreement.getEngageCode())) {
                    specialAgreementDtoList.add(agreement);
                }
            }
        }
        if (CollUtil.isNotEmpty(res.getData().getCiPrptenages())) {
            List<ViQuoteSpecialAgreement> ciInsSpecialAgreement = resolveInsData(res.getData().getCiPrptenages(), policyBatchId, PolicyTypeEnum.CI);
            for (ViQuoteSpecialAgreement agreement : ciInsSpecialAgreement) {
                if (!listClauseCode.contains(agreement.getEngageCode())) {
                    specialAgreementDtoList.add(agreement);
                }
            }
            specialAgreementDtoList.addAll(ciInsSpecialAgreement);
        }
        result.setSpecialAgreementDtoList(specialAgreementDtoList);
        return result;
    }

    private static List<ViQuoteSpecialAgreement> resolveInsData(List<GuorenPrptengage> prptengages, Long policyBatchId, PolicyTypeEnum policyType) {
        return TunsBeanUtil.copyList(prptengages, o -> {
            ViQuoteSpecialAgreement viQuoteSpecialAgreement = new ViQuoteSpecialAgreement();
            viQuoteSpecialAgreement.setRecordType(policyType);
            viQuoteSpecialAgreement.setEngageCode(o.getClauseCode());
            viQuoteSpecialAgreement.setEngageContent(o.getClausesContext());
            viQuoteSpecialAgreement.setEngageTitle(o.getClauses());
            viQuoteSpecialAgreement.setPolicyBatchId(policyBatchId);
            return viQuoteSpecialAgreement;
        });
    }


    private void adjustDiscountByFirstCalc(GuorenPremCalculateReqDTO guorenPremCalculateReqDTO, APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        log.debug("国任===>调整折扣，第一次调用保费试算请求，{}", JSONUtil.toJsonStr(guorenPremCalculateReqDTO));
        GuorenPremCalculateDataResDTO guorenPremCalculateResDTO = guoRenRpcService.premiumCalculate(guorenPremCalculateReqDTO);
        log.debug("国任<===调整折扣，第一次调用保费试算响应，{}", JSONUtil.toJsonStr(guorenPremCalculateResDTO));
        guorenPremiumCalculateErrorResponseHandler.handle(guorenPremCalculateResDTO, apiPremiumCalculateDTO);
    }

    /**
     * 支持修改车损功能
     * 1.修改车辆公允价值
     * 2.修改车损险保额
     * 3.修改新能源车损保额
     *
     * @param req                报价请求参数
     * @param premiumCaculateDTO 报价原始请求参数
     */
    private void updateActualValueAndAmount(GuorenPremCalculateReqDTO req, APIPremiumCalculateDTO premiumCaculateDTO) {
        SpecialInfoDTO specialInfo = premiumCaculateDTO.getSpecialInfo();
        Map<String, ViInsuranceInf> tpViInsuranceInfMapSelf = premiumCaculateDTO.getTpViInsuranceInfMapSelf();
        Double actualValue = req.getActualValue();
        Double damageInsAmount = Objects.isNull(specialInfo.getDamageInsAmount()) ? actualValue : specialInfo.getDamageInsAmount().doubleValue();
        req.setActualValue(damageInsAmount);
        ViInsuranceInf biVehicleLoss = tpViInsuranceInfMapSelf.get(NewKindCodeEnum.BI_VEHICLE_LOSS.getValue());
        ViInsuranceInf biRobberyTheft = tpViInsuranceInfMapSelf.get(NewKindCodeEnum.BI_ROBBERY_THEFT.getValue());
        ViInsuranceInf newEnergyExternalPowerFaultCoverage = tpViInsuranceInfMapSelf.get(NewKindCodeEnum.NEW_ENERGY_EXTERNAL_POWER_FAULT_COVERAGE.getValue());
        //当存在修改车损保额时 取前端传过来的保额
        List<ViInsuranceInf> viInsuranceInfs = Stream.of(biVehicleLoss, newEnergyExternalPowerFaultCoverage, biRobberyTheft).filter(Objects::nonNull).collect(Collectors.toList());
        setAmountByKinds(req, damageInsAmount, viInsuranceInfs);
    }

    /**
     * 修改车损保额
     *
     * @param req             报价请求参数
     * @param damageInsAmount 待修改的车损保额值
     * @param viInsuranceInf  待修改的险种信息
     */
    private void setAmountByKinds(GuorenPremCalculateReqDTO req, Double damageInsAmount, List<ViInsuranceInf> viInsuranceInf) {
        if (CollectionUtil.isNotEmpty(viInsuranceInf) && Objects.nonNull(req.getBi())) {
            Map<String, GuorenBiItemkind> biItemkindMap = req.getBi().getPrptitemkindList().stream().collect(Collectors.toMap(GuorenBiItemkind::getKindCode, Function.identity()));
            for (ViInsuranceInf insuranceInf : viInsuranceInf) {
                String kindCode = insuranceInf.getKindCode();
                GuorenBiItemkind guorenBiItemkind = biItemkindMap.get(kindCode);
                if (Objects.nonNull(guorenBiItemkind)) {
                    guorenBiItemkind.setAmount(damageInsAmount != null ? String.valueOf(damageInsAmount) : "");
                }
            }
        }
    }

    /**
     * 预核保
     *
     * @param apiPremiumCalculateDTO
     * @param result
     * @param guorenChannelResDTO
     * @return
     */
    private String doPreUnderwriting(APIPremiumCalculateDTO apiPremiumCalculateDTO, PremiumCalculateResultDTO result, GuorenChannelResDTO guorenChannelResDTO) {

        String preUnderwritingMsg = null;
        try {
            //预核保
            List<CarSameSaleGiOrderInfoDetailVO> giOrderList = apiPremiumCalculateDTO.getGiInsureDTO().getGiOrderList();
            CarThirdInsureDTO insureDTO = buildInsureDTO(apiPremiumCalculateDTO, giOrderList);
            GuorenNoCarOrderRes guorenNoCarOrderRes = insureConfirmService.noCar(insureDTO, guorenChannelResDTO.getPrptmain());
            GuorenSavePolicyResDTO guorenSavePolicyResDTO = insureConfirmService.policyTpSave(insureDTO, guorenNoCarOrderRes);
            //删除操作
            DeleteOfferOrderDTO offerOrderDTO = new DeleteOfferOrderDTO();
            List<ViInsPlcyInfTp> viInsPlcyInfTps = TunsBeanUtil.copyList(result.getViInsPlcyInfTps(), o -> {
                ViInsPlcyInfTp viInsPlcyInfTp = BeanUtil.copyProperties(o, ViInsPlcyInfTp.class);
                if (PolicyTypeEnum.CI.getValue().equals(viInsPlcyInfTp.getPolicyType()) && Objects.nonNull(guorenSavePolicyResDTO.getProposalNoDetailVo())) {
                    viInsPlcyInfTp.setProposalNumber(guorenSavePolicyResDTO.getProposalNoDetailVo().getProposalNoCI());
                }
                if (PolicyTypeEnum.BI.getValue().equals(viInsPlcyInfTp.getPolicyType()) && Objects.nonNull(guorenSavePolicyResDTO.getProposalNoDetailVo())) {
                    viInsPlcyInfTp.setProposalNumber(guorenSavePolicyResDTO.getProposalNoDetailVo().getProposalNoBI());
                }
                return viInsPlcyInfTp;
            });
            offerOrderDTO.setPlcyInfs(viInsPlcyInfTps);
            deleteProposalService.deleteProposal(offerOrderDTO);
        } catch (TunsBusinessException e) {
            preUnderwritingMsg = substringMaxWhenException(e.getMessage());
        }
        return preUnderwritingMsg;
    }

    /**
     * 异常时预核保信息数据库长度处理
     * 关联解决 bug：30869 APP车险国任api渠道报价失败
     *
     * @param preUnderwritingMsg
     * @return
     */
    private String substringMaxWhenException(String preUnderwritingMsg) {
        if (StringUtils.isBlank(preUnderwritingMsg)) {
            return preUnderwritingMsg;
        }
        log.error("国任预核保异常,截取异常信息1000个字符");
        int maxLength = 1000;
        // 异常信息截取前1000个字符，保证不超数据库长度
        if (preUnderwritingMsg.length() <= maxLength) {
            return preUnderwritingMsg;
        }
        return StringUtils.substring(preUnderwritingMsg, 0, maxLength);
    }

    private static CarThirdInsureDTO buildInsureDTO(APIPremiumCalculateDTO premiumCaculateDTO, List<CarSameSaleGiOrderInfoDetailVO> giOrderList) {
        CarThirdInsureDTO insureDTO = new CarThirdInsureDTO();
        List<PersonInfo> personInfos = premiumCaculateDTO.getPremiumCaculateRequest().getPersonInfos();
        List<PubPersInfTp> pubPersInfTps = new ArrayList<>();
        for (PersonInfo personInfo : personInfos) {
            PubPersInfTp pubPersInfTp = TunsBeanUtil.copy(PubPersInfTp.class, personInfo);
            pubPersInfTp.setNature(personInfo.getNature().getValue());
            pubPersInfTps.add(pubPersInfTp);
        }
        insureDTO.setPubPerList(pubPersInfTps);
        insureDTO.setSpecialAgreementDtoList(premiumCaculateDTO.getSpecialAgreementDtoList());
        insureDTO.setCarInfTp(BeanUtil.copyProperties(premiumCaculateDTO.getCarInfo(), ViInsCarInfTp.class));
        insureDTO.setCarSameSaleGiOrderInfoList(TunsBeanUtil.copyList(giOrderList, CarSameSaleGiOrderInfoVO.class));
        insureDTO.setChanDetailId(premiumCaculateDTO.getChanDetailId());
        insureDTO.setVehicleTypeRelation(premiumCaculateDTO.getVehicleTypeRelation());
        insureDTO.setPlcyInfList(premiumCaculateDTO.getViInsPlcyInfTps());
        if (CollUtil.isNotEmpty(giOrderList)) {
            GiOrderInfoVO giOrder = buildGiOrder(giOrderList);
            insureDTO.setGiOrder(giOrder);
        }
        insureDTO.setPubInsPlcy(premiumCaculateDTO.getViInsPlcyInfTps().get(0));
        Map<ViPersonTypeEnum, AreaDTO> addressMap = premiumCaculateDTO.getAddressMap();
        Map<String, AreaDTO> areaMap = new HashMap<>();
        addressMap.forEach((k, v) -> areaMap.put(k.getValue(), v));
        insureDTO.setAreaDTO(areaMap);
        return insureDTO;
    }

    /**
     * 构建一个创建非车订单所需的giOrder bug26646
     *
     * @param giOrderList
     * @return
     */
    private static GiOrderInfoVO buildGiOrder(List<CarSameSaleGiOrderInfoDetailVO> giOrderList) {
        List<GiOrderInsureVO> insureVOS = new ArrayList<>();
        List<GiInsurePrdVO> prdVOS = new ArrayList<>();
        for (CarSameSaleGiOrderInfoDetailVO carSameSaleGiOrderInfoDetailVO : giOrderList) {
            GiOrderInsureVO giOrderInsureVO = new GiOrderInsureVO();
            giOrderInsureVO.setStartDate(DateUtil.formatDateTime(carSameSaleGiOrderInfoDetailVO.getStartTime()));
            giOrderInsureVO.setEndDate(DateUtil.formatDateTime(carSameSaleGiOrderInfoDetailVO.getEndTime()));
            giOrderInsureVO.setProdName(carSameSaleGiOrderInfoDetailVO.getProdName());
            giOrderInsureVO.setProposalNo(carSameSaleGiOrderInfoDetailVO.getProposalNo());
            giOrderInsureVO.setPremium(carSameSaleGiOrderInfoDetailVO.getPrem());
            insureVOS.add(giOrderInsureVO);
            GiInsurePrdVO giInsurePrdVO = new GiInsurePrdVO();
            giInsurePrdVO.setPremium(carSameSaleGiOrderInfoDetailVO.getPrem());
            giInsurePrdVO.setPlanCode(carSameSaleGiOrderInfoDetailVO.getProjectCode());
            giInsurePrdVO.setCopies(carSameSaleGiOrderInfoDetailVO.getCopies());
            giInsurePrdVO.setPlanName(carSameSaleGiOrderInfoDetailVO.getProjectName());
            giInsurePrdVO.setProdCode(carSameSaleGiOrderInfoDetailVO.getProdCode());
            giInsurePrdVO.setProdName(carSameSaleGiOrderInfoDetailVO.getProdName());
            prdVOS.add(giInsurePrdVO);
        }
        GiOrderInfoVO giOrderInfoVO = new GiOrderInfoVO();
        giOrderInfoVO.setInsures(insureVOS);
        giOrderInfoVO.setPrds(prdVOS);
        return giOrderInfoVO;
    }

    /**
     * 处理商业险 拿到返回的保费以及一些折扣系数
     *
     * @param res                    返回信息
     * @param apiPremiumCalculateDTO 报价请求
     * @param viInsKindDetialTps     险种列表
     * @param viInsPlcyInfTp         报价单信息
     */
    private void handleBi(GuorenPremCalculateResDTO res, APIPremiumCalculateDTO apiPremiumCalculateDTO, List<ViInsKindDetialTp> viInsKindDetialTps, ViInsPlcyInfTp viInsPlcyInfTp, GuorenQueryRenewalData data) {
        viInsPlcyInfTp.setInsuredPremium(res.getSumPremiumBI());
        viInsPlcyInfTp.setTotalPremium(res.getSumPremiumBI());
        viInsPlcyInfTp.setAttachPremium(BigDecimal.ZERO);
        viInsPlcyInfTp.setInsAmount(BigDecimal.ZERO);
        viInsPlcyInfTp.setInsPremiumNumber(res.getUuid());
        viInsPlcyInfTp.setBillElr(res.getAutoPriceOutputXOMRespVo().getBillELR());
        viInsPlcyInfTp.setLossRatioBi(res.getAutoPriceOutputXOMRespVo().getBillCOMELR());
        viInsPlcyInfTp.setBusinessGroup(res.getBirate().getBillcomeGroup());
        viInsPlcyInfTp.setBusinessGroupName(res.getBirate().getTempLateName());
        viInsPlcyInfTp.setIndependentPriceRate(BigDecimalUtil.strToBigDecimal(res.getSelfAdjustRate()));
        viInsPlcyInfTp.setNoClaimDiscount(BigDecimalUtil.strToBigDecimal(res.getNoclaimRate()).add(new BigDecimal("1")));
        viInsPlcyInfTp.setDiscount(res.getSignDiscount() != null ? res.getSignDiscount() : viInsPlcyInfTp.getNoClaimDiscount().multiply(viInsPlcyInfTp.getIndependentPriceRate()));
        viInsPlcyInfTp.setTotalRatio2(res.getAutoPriceOutputXOMRespVo().getStrBillELR2());
        viInsPlcyInfTp.setInsuredType(Objects.nonNull(data) && Objects.nonNull(GuorenInsuredTypeEnum.getTunsCode(data.getBiRenw())) ? GuorenInsuredTypeEnum.getTunsCode(data.getBiRenw()) : InsuredTypeEnum.NEW_INSURANCE);
        viInsPlcyInfTp.setInsuredTypeBi(Objects.nonNull(data) && Objects.nonNull(GuorenInsuredTypeEnum.getTunsCode(data.getBiRenw())) ? GuorenInsuredTypeEnum.getTunsCode(data.getBiRenw()) : InsuredTypeEnum.NEW_INSURANCE);
        if (res.getSignDiscount() != null) {
            int claimCount = LastClaimUtil.lastClaimsCountCI(viInsPlcyInfTp.getDiscount().multiply(BigDecimal.valueOf(10000)).intValue());
            viInsPlcyInfTp.setLastClaimCount(claimCount);
        } else {
            viInsPlcyInfTp.setLastClaimCount(0);
        }


        //保险公司险种信息
        Map<String, ViInsuranceInf> viInsuranceInfMapTP = apiPremiumCalculateDTO.getTpViInsuranceInfMapTP();
        Map<String, ItemKind> buyInsuranceInfMap = apiPremiumCalculateDTO.getBuyInsuranceInfMap();
        for (GuorenItemKindRes tpItemKind : res.getItemVOList()) {
            if (CI.equals(tpItemKind.getRiskCode())) {
                //排除集合中的交强险
                continue;
            }
            ViInsuranceInf tpViInsuranceInf = viInsuranceInfMapTP.get(tpItemKind.getKindCode());
            ItemKind itemKind = buyInsuranceInfMap.get(tpViInsuranceInf.getInsuranceCode());
            //险种集合处理
            kindDetailHandle(null, viInsKindDetialTps, itemKind, tpItemKind, viInsPlcyInfTp, tpViInsuranceInf, res.getCarShipTaxRespVO());
        }
    }

    /**
     * 处理交强险 拿到返回的保费以及一些折扣系数
     *
     * @param res                    返回信息
     * @param apiPremiumCalculateDTO 报价请求
     * @param viInsKindDetialTps     险种列表
     * @param viInsPlcyInfTp         报价单信息
     */
    private void handleCi(GuorenPremCalculateResDTO res, APIPremiumCalculateDTO apiPremiumCalculateDTO, List<ViInsKindDetialTp> viInsKindDetialTps, ViInsPlcyInfTp viInsPlcyInfTp, GuorenQueryRenewalData data) {
        //交强险节点
        for (GuorenItemKindRes kindRes : res.getItemVOList()) {
            if ("BZ".equals(kindRes.getKindCode())) {
                viInsPlcyInfTp.setStandardPremium(BigDecimalUtil.strToBigDecimal(kindRes.getBenchMarkPremium()));
                viInsPlcyInfTp.setInsuredPremium(BigDecimalUtil.strToBigDecimal(kindRes.getPremium()));
                viInsPlcyInfTp.setInsAmount(BigDecimalUtil.doubleToBigDecimal(kindRes.getAmount()));
                viInsPlcyInfTp.setDiscount(viInsPlcyInfTp.getInsuredPremium().divide(viInsPlcyInfTp.getStandardPremium()));
            }
        }
        viInsPlcyInfTp.setInsuredTypeCi(Objects.nonNull(data) && Objects.nonNull(GuorenInsuredTypeEnum.getTunsCode(data.getCiRenw())) ? GuorenInsuredTypeEnum.getTunsCode(data.getCiRenw()) : InsuredTypeEnum.NEW_INSURANCE);
        viInsPlcyInfTp.setInsuredType(Objects.nonNull(data) && Objects.nonNull(GuorenInsuredTypeEnum.getTunsCode(data.getCiRenw())) ? GuorenInsuredTypeEnum.getTunsCode(data.getCiRenw()) : InsuredTypeEnum.NEW_INSURANCE);
        viInsPlcyInfTp.setAttachPremium(res.getSumPayTax());
        viInsPlcyInfTp.setTotalPremium(viInsPlcyInfTp.getInsuredPremium().add(viInsPlcyInfTp.getAttachPremium()));
        viInsPlcyInfTp.setInsPremiumNumber(res.getUuid());
        viInsPlcyInfTp.setBillElr(res.getAutoPriceOutputXOMRespVo().getBillELR());
        viInsPlcyInfTp.setBusinessGroup(res.getCirate().getBillcomeGroup());
        viInsPlcyInfTp.setBusinessGroupName(res.getCirate().getTempLateName());
        viInsPlcyInfTp.setIndependentPriceRate(Objects.isNull(res.getSelfAdjustRate()) ? BigDecimal.ZERO : BigDecimalUtil.strToBigDecimal(res.getSelfAdjustRate()));
        viInsPlcyInfTp.setTotalRatio2(res.getAutoPriceOutputXOMRespVo().getStrBillELR2());
        //计算理赔情况 上年理赔次数
        if (viInsPlcyInfTp.getDiscount() != null) {
            int claimCount = LastClaimUtil.lastClaimsCountCI(viInsPlcyInfTp.getDiscount().multiply(BigDecimal.valueOf(100)).intValue());
            viInsPlcyInfTp.setLastClaimCount(claimCount);
        } else {
            viInsPlcyInfTp.setLastClaimCount(0);
        }
        String taxRelifFlag = "";
        if (res.getCarShipTaxRespVO() != null) {
            taxRelifFlag = res.getCarShipTaxRespVO().getTaxRelifFlag();
        }
        // 纳税类型
        String carTaxType = GuorenTaxTypeEnum.getEnum(taxRelifFlag).getTunsCode();
        viInsPlcyInfTp.setCarTaxType(carTaxType);
        Map<String, ViInsuranceInf> tpViInsuranceInfMapSelf = apiPremiumCalculateDTO.getTpViInsuranceInfMapSelf();

        //记录交强险
        ViInsuranceInf tpViInsuranceInf = tpViInsuranceInfMapSelf.get(NewKindCodeEnum.CI_JQ.getValue());
        kindDetailHandle(NewKindCodeEnum.CI_JQ, viInsKindDetialTps, null, null, viInsPlcyInfTp, tpViInsuranceInf, res.getCarShipTaxRespVO());
        //记录车船税
        tpViInsuranceInf = tpViInsuranceInfMapSelf.get(NewKindCodeEnum.CI_BT.getValue());
        kindDetailHandle(NewKindCodeEnum.CI_BT, viInsKindDetialTps, null, null, viInsPlcyInfTp, tpViInsuranceInf, res.getCarShipTaxRespVO());
    }

    /**
     * 填充交强险信息
     *
     * @param apiPremiumCalculateDTO 报价信息
     * @param viInsPlcyInfTpCi       交强险信息
     * @return 交强险节点
     */
    private GuorenCompulsory polulateCi(APIPremiumCalculateDTO apiPremiumCalculateDTO, ViInsPlcyInfTp viInsPlcyInfTpCi, String comCode) {
        CarInfo carInfo = apiPremiumCalculateDTO.getCarInfo();
        GuorenCompulsory ci = new GuorenCompulsory();
        if (Objects.nonNull(viInsPlcyInfTpCi)) {
            buildCIInfoByExist(apiPremiumCalculateDTO, viInsPlcyInfTpCi, carInfo, ci, comCode);
        } else {
            defaultCiInfo(apiPremiumCalculateDTO, carInfo, ci);
        }
        return ci;
    }

    private static void defaultCiInfo(APIPremiumCalculateDTO apiPremiumCalculateDTO, CarInfo carInfo, GuorenCompulsory ci) {
        GuorenCiPrpTmain ciPrpTmain = new GuorenCiPrpTmain();
        GuorenCiPrpTitemCar ciPrpTitemCar = new GuorenCiPrpTitemCar();
        List<GuorenCiItemkind> ciItemkinds = new ArrayList<>();
        GuorenCiInsureDemand insureDemand = new GuorenCiInsureDemand();
        GuorenPrpTcarshipTax tcarshipTax = new GuorenPrpTcarshipTax();
        ci.setInputRisk("0");
        ci.setCalculateFlagCI("0");
        ci.setEditType("NEW");
        ci.setPrpTmainRate(new HashMap());
        ci.setPrpTitemCar(ciPrpTitemCar);
        ci.setPrptitemkindList(ciItemkinds);
        ci.setCiInsureDemand(insureDemand);
        ci.setPrpTcarshipTax(tcarshipTax);
        ci.setPrpTmain(ciPrpTmain);
        insureDemand.setDemandNo("");
        tcarshipTax.setPaidCertificate("");

        ciPrpTmain.setStartHour("00");
        ciPrpTmain.setEndHour("24");
        ciPrpTmain.setAnswer("");

        ciPrpTitemCar.setClauseType("F40");
        ciPrpTitemCar.setCarOwner(carInfo.getCarOwner());
        ciPrpTitemCar.setClauseTypeSystem(apiPremiumCalculateDTO.getNewEnergy() ? "05" : "04");
        ciPrpTitemCar.setTermsSystem(apiPremiumCalculateDTO.getNewEnergy() ? "05" : "04");
        ciPrpTitemCar.setCarOwnerIdentifyNumber(carInfo.getOwnerIdentify());
        ciPrpTitemCar.setOtherNature("0");
    }

    private void buildCIInfoByExist(APIPremiumCalculateDTO apiPremiumCalculateDTO, ViInsPlcyInfTp viInsPlcyInfTpCi, CarInfo carInfo, GuorenCompulsory ci, String comCode) {
        GuorenCiPrpTmain ciPrpTmain = new GuorenCiPrpTmain();
        GuorenCiPrpTitemCar ciPrpTitemCar = new GuorenCiPrpTitemCar();
        List<GuorenCiItemkind> ciItemkinds = new ArrayList<>();
        GuorenCiInsureDemand insureDemand = new GuorenCiInsureDemand();
        GuorenPrpTcarshipTax tcarshipTax = new GuorenPrpTcarshipTax();
        ci.setInputRisk("1");
        ci.setCalculateFlagCI("1");
        ci.setEditType("NEW");
        ci.setPrpTmainRate(new HashMap());
        ci.setPrpTitemCar(ciPrpTitemCar);
        ci.setPrptitemkindList(ciItemkinds);
        ci.setCiInsureDemand(insureDemand);
        ci.setPrpTcarshipTax(tcarshipTax);
        ci.setPrpTmain(ciPrpTmain);
        List<GuorenPrptengage> guorenPrptengages = insureConfirmService.buildCiAgreement(apiPremiumCalculateDTO.getSpecialAgreementDtoList(), comCode);
        if (CollUtil.isNotEmpty(guorenPrptengages)) {
            ci.setPrptengageList(guorenPrptengages);
        }
        insureDemand.setDemandNo("");
        tcarshipTax.setPaidCertificate("");

        String effectiveImmediately = viInsPlcyInfTpCi.getEffectiveImmediately();
        boolean isEffectiveImmediately = YesNoNumberEnum.YES.getValue().equals(effectiveImmediately);
        ciPrpTmain.setImmeValiFlag(isEffectiveImmediately ? YesNoNumberEnum.YES.getValue() : "");

        LocalDateTime insBeginDate = LocalDateTime.parse(viInsPlcyInfTpCi.getInsBegin(), DATETIME_FORMATTER);
        LocalDateTime insEndDate = LocalDateTime.parse(viInsPlcyInfTpCi.getInsEnd(), DATETIME_FORMATTER);

        String formattedInsBegin = insBeginDate.format(DATE_FORMATTER);
        String formattedInsEnd = insEndDate.format(DATE_FORMATTER);

        if (isEffectiveImmediately) {
            ciPrpTmain.setImmeValidStartDate(viInsPlcyInfTpCi.getInsBegin());
            ciPrpTmain.setImmeValidEndDate(viInsPlcyInfTpCi.getInsEnd());

            String nextDay = insBeginDate.plusDays(1).toLocalDate().toString();
            ciPrpTmain.setStartDate(nextDay);
            ciPrpTmain.setEndDate(formattedInsEnd);
        } else {
            ciPrpTmain.setImmeValidStartDate(StrUtil.EMPTY);
            ciPrpTmain.setImmeValidEndDate(StrUtil.EMPTY);
            ciPrpTmain.setStartDate(formattedInsBegin);
            ciPrpTmain.setEndDate(formattedInsEnd);
        }

        ciPrpTmain.setStartHour("00");
        ciPrpTmain.setEndHour("24");
        ciPrpTmain.setInputDate(DateTimeUtil.getDateTime10());
        ciPrpTmain.setSignDate(DateTimeUtil.getDateTime10());
        ciPrpTmain.setAnswer("");

        //判断是否报价非车
        if (apiPremiumCalculateDTO.getGiInsureDTO().getActualBuy()) {
            List<GuorenApiProdListDTO> insProdList = JSONUtil.toBean(apiPremiumCalculateDTO.getNoCarCheckResult(), new TypeReference<List<GuorenApiProdListDTO>>() {
            }, true);
            CarSameSaleGiOrderInfoDetailVO tunsNoCarInfo = apiPremiumCalculateDTO.getGiInsureDTO().getGiOrderList().get(0);
            GuorenApiProdListDTO insNoCarInfo = insProdList.get(0);
            ciPrpTmain.setNoCarFlag("1");
            ciPrpTmain.setOrderFlag("N");
            ciPrpTmain.setNoCarCodes(tunsNoCarInfo.getProdCode());
            BigDecimal prem = BigDecimal.valueOf(Long.parseLong(insNoCarInfo.getPremium())).multiply(BigDecimal.valueOf(tunsNoCarInfo.getCopies()));
            ciPrpTmain.setNoCarPremium(String.valueOf(prem));
        }

        GuorenCiItemkind ciItemkind = new GuorenCiItemkind();

        ciPrpTitemCar.setClauseType("F40");
        ciPrpTitemCar.setCarOwner(carInfo.getCarOwner());
        ciPrpTitemCar.setClauseTypeSystem(apiPremiumCalculateDTO.getNewEnergy() ? "05" : "04");
        ciPrpTitemCar.setTermsSystem(apiPremiumCalculateDTO.getNewEnergy() ? "05" : "04");
        ciPrpTitemCar.setCarOwnerIdentifyNumber(carInfo.getOwnerIdentify());
        ciPrpTitemCar.setOtherNature("0");


        ViInsuranceInf viInsuranceInf = apiPremiumCalculateDTO.getTpViInsuranceInfMapSelf().get(NewKindCodeEnum.CI_JQ.getValue());
        ciItemkind.setKindCode(viInsuranceInf.getKindCode());
        ciItemkind.setKindName(viInsuranceInf.getKindName());
        ciItemkind.setAmount("200000");
        ciItemkind.setDeductibleAmount("");
        ciItemkind.setFlag(" 2  03");
        ciItemkind.setDeductible("");
        ciItemkind.setRate("");
        ciItemkind.setNoTaxPremium("");
        ciItemkind.setTaxFee("");
        ciItemkind.setIsDeductible("true");
        ciItemkind.setEndDate(ciPrpTmain.getEndDate());
        ciItemkind.setStartDate(ciPrpTmain.getStartDate());
        ciItemkinds.add(ciItemkind);
    }

    /**
     * 填充商业险信息
     *
     * @param apiPremiumCalculateDTO 报价请求类
     * @param viInsPlcyInfTpBi       商业险信息
     * @return 商业险节点
     */
    private GuorenBusiness populateBi(APIPremiumCalculateDTO apiPremiumCalculateDTO, ViInsPlcyInfTp viInsPlcyInfTpBi, String comCode) {
        BigDecimal expectDiscount = apiPremiumCalculateDTO.getBaseDTO().getExpectDiscount();
        CarInfo carInfo = apiPremiumCalculateDTO.getCarInfo();
        GuorenBusiness bi = new GuorenBusiness();
        if (Objects.nonNull(viInsPlcyInfTpBi)) {
            buildBIInfoByExist(apiPremiumCalculateDTO, viInsPlcyInfTpBi, expectDiscount, carInfo, bi, comCode);
        } else {
            defaultBIInfo(apiPremiumCalculateDTO, expectDiscount, carInfo, bi);
        }
        return bi;
    }

    private static void defaultBIInfo(APIPremiumCalculateDTO apiPremiumCalculateDTO, BigDecimal expectDiscount, CarInfo carInfo, GuorenBusiness bi) {
        //商业险为空需要传默认值，不然会报错
        GuorenBiPrpTitemCar biPrpTitemCar = new GuorenBiPrpTitemCar();
        GuorenBiPrpTmain biPrpTmain = new GuorenBiPrpTmain();
        GuorenCiInsureDemand insureDemand = new GuorenCiInsureDemand();

        insureDemand.setDemandNo("");
        GuoRenProductTypeEnum productType = GuoRenProductTypeEnum.findProductType(carInfo.getUsingNature());
        biPrpTitemCar.setClauseType(productType.getClauseType());
        biPrpTitemCar.setCarOwner(carInfo.getCarOwner());
        biPrpTitemCar.setClauseTypeSystem(apiPremiumCalculateDTO.getNewEnergy() ? "05" : "04");
        biPrpTitemCar.setTermsSystem(apiPremiumCalculateDTO.getNewEnergy() ? "05" : "04");
        biPrpTitemCar.setCarOwnerIdentifyNumber(carInfo.getOwnerIdentify());
        biPrpTitemCar.setOtherNature("0");
        biPrpTmain.setSignDate(DateTimeUtil.getDateTime10());
        biPrpTmain.setInputDate(DateTimeUtil.getDateTime10());

        if (expectDiscount == null) {
            biPrpTmain.setExpectDiscount("");
            biPrpTmain.setFeeandDiscountFlag("0");
        } else {
            biPrpTmain.setExpectDiscount(String.valueOf(expectDiscount));
            biPrpTmain.setFeeandDiscountFlag("2");
        }
        biPrpTmain.setStartHour("00");
        biPrpTmain.setEndHour("24");
        biPrpTmain.setImmeValidStartDate("");
        biPrpTmain.setImmeValidEndDate("");
        biPrpTmain.setAnswer("");

        bi.setInputRisk("0");
        bi.setCalculateFlagBI("0");
        bi.setEditType("NEW");
        bi.setPrpTitemCar(biPrpTitemCar);
        bi.setPrpTmain(biPrpTmain);
        bi.setCiInsureDemand(insureDemand);
        bi.setCiInsureDemandPay(Collections.EMPTY_LIST);
        bi.setCiInsureDemandWarningClaim(Collections.EMPTY_LIST);
        bi.setPrpTmainRate(new HashMap());
        bi.setPrpTrenewal(Collections.EMPTY_LIST);
        bi.setPrptengageList(Collections.EMPTY_LIST);
        List<GuorenBiItemkind> guorenBiItemkindList = new ArrayList<>();
        bi.setPrptitemkindList(guorenBiItemkindList);
        List<GuorenPrpTChargingPostData> chargingPostData = new ArrayList<>();
        bi.setPrpTChargingPostDataList(chargingPostData);
    }

    private void buildBIInfoByExist(APIPremiumCalculateDTO apiPremiumCalculateDTO, ViInsPlcyInfTp viInsPlcyInfTpBi, BigDecimal expectDiscount, CarInfo carInfo, GuorenBusiness bi, String comCode) {
        GuorenBiPrpTitemCar biPrpTitemCar = new GuorenBiPrpTitemCar();
        GuorenBiPrpTmain biPrpTmain = new GuorenBiPrpTmain();
        GuorenCiInsureDemand insureDemand = new GuorenCiInsureDemand();

        insureDemand.setDemandNo("");

        GuoRenProductTypeEnum productType = GuoRenProductTypeEnum.findProductType(carInfo.getUsingNature());
        biPrpTitemCar.setClauseType(productType.getClauseType());
        biPrpTitemCar.setCarOwner(carInfo.getCarOwner());
        biPrpTitemCar.setClauseTypeSystem(apiPremiumCalculateDTO.getNewEnergy() ? "05" : "04");
        biPrpTitemCar.setTermsSystem(apiPremiumCalculateDTO.getNewEnergy() ? "05" : "04");
        biPrpTitemCar.setCarOwnerIdentifyNumber(carInfo.getOwnerIdentify());
        biPrpTitemCar.setOtherNature("0");

        //判断是否有非车有 非车必传值
        if (apiPremiumCalculateDTO.getGiInsureDTO().getActualBuy()) {
            List<GuorenApiProdListDTO> insProdList = JSONUtil.toBean(apiPremiumCalculateDTO.getNoCarCheckResult(), new TypeReference<List<GuorenApiProdListDTO>>() {
            }, true);
            CarSameSaleGiOrderInfoDetailVO tunsNoCarInfo = apiPremiumCalculateDTO.getGiInsureDTO().getGiOrderList().get(0);
            GuorenApiProdListDTO insNoCarInfo = insProdList.get(0);
            biPrpTmain.setNoCarFlag("1");
            biPrpTmain.setOrderFlag("N");
            biPrpTmain.setNoCarCodes(tunsNoCarInfo.getProdCode());
            BigDecimal prem = BigDecimal.valueOf(Long.parseLong(insNoCarInfo.getPremium())).multiply(BigDecimal.valueOf(tunsNoCarInfo.getCopies()));
            biPrpTmain.setNoCarPremium(String.valueOf(prem));
        }

        biPrpTmain.setSignDate(DateTimeUtil.getDateTime10());
        biPrpTmain.setInputDate(DateTimeUtil.getDateTime10());

        if (expectDiscount == null) {
            biPrpTmain.setExpectDiscount("");
            biPrpTmain.setFeeandDiscountFlag("0");
        } else {
            biPrpTmain.setExpectDiscount(String.valueOf(expectDiscount));
            biPrpTmain.setFeeandDiscountFlag("2");
        }
        biPrpTmain.setStartHour("00");
        biPrpTmain.setEndHour("24");


        String effectiveImmediately = viInsPlcyInfTpBi.getEffectiveImmediately();
        boolean isEffectiveImmediately = YesNoNumberEnum.YES.getValue().equals(effectiveImmediately);
        biPrpTmain.setImmeValiFlag(isEffectiveImmediately ? YesNoNumberEnum.YES.getValue() : "");

        LocalDateTime insBeginDate = LocalDateTime.parse(viInsPlcyInfTpBi.getInsBegin(), DATETIME_FORMATTER);
        LocalDateTime insEndDate = LocalDateTime.parse(viInsPlcyInfTpBi.getInsEnd(), DATETIME_FORMATTER);

        String formattedInsBegin = insBeginDate.format(DATE_FORMATTER);
        String formattedInsEnd = insEndDate.format(DATE_FORMATTER);

        if (isEffectiveImmediately) {
            biPrpTmain.setImmeValidStartDate(viInsPlcyInfTpBi.getInsBegin());
            biPrpTmain.setImmeValidEndDate(viInsPlcyInfTpBi.getInsEnd());

            String nextDay = insBeginDate.plusDays(1).toLocalDate().toString();
            biPrpTmain.setStartDate(nextDay);
            biPrpTmain.setEndDate(formattedInsEnd);
        } else {
            biPrpTmain.setImmeValidStartDate(StrUtil.EMPTY);
            biPrpTmain.setImmeValidEndDate(StrUtil.EMPTY);
            biPrpTmain.setStartDate(formattedInsBegin);
            biPrpTmain.setEndDate(formattedInsEnd);
        }
        biPrpTmain.setAnswer("");
        //填充险种信息
        List<GuorenBiItemkind> guorenBiItemkinds = populateKindInfoReq(apiPremiumCalculateDTO, biPrpTmain.getStartDate(), biPrpTmain.getEndDate());

        //填充充电桩信息
        List<ChargingPileDTO> chargingPiles = apiPremiumCalculateDTO.getChargingPiles();
        List<GuorenPrpTChargingPostData> chargingPostData = populateChargingPiles(chargingPiles);

        bi.setInputRisk("1");
        bi.setCalculateFlagBI("1");
        bi.setEditType("NEW");
        bi.setPrpTitemCar(biPrpTitemCar);
        bi.setPrpTmain(biPrpTmain);
        bi.setCiInsureDemand(insureDemand);
        bi.setPrptitemkindList(guorenBiItemkinds);
        bi.setPrpTChargingPostDataList(chargingPostData);
        bi.setCiInsureDemandPay(Collections.EMPTY_LIST);
        bi.setCiInsureDemandWarningClaim(Collections.EMPTY_LIST);
        bi.setPrpTmainRate(new HashMap());
        bi.setPrpTrenewal(Collections.EMPTY_LIST);
        //查询特别约定
        List<GuorenPrptengage> guorenPrptengages = insureConfirmService.buildBiAgreement(apiPremiumCalculateDTO.getSpecialAgreementDtoList(), comCode);
        if (CollUtil.isNotEmpty(guorenPrptengages)) {
            bi.setPrptengageList(guorenPrptengages);
        }
    }

    /**
     * 填充充电桩信息
     *
     * @param chargingPiles 充电桩信息
     */
    private List<GuorenPrpTChargingPostData> populateChargingPiles(List<ChargingPileDTO> chargingPiles) {
        if (CollectionUtil.isNotEmpty(chargingPiles)) {
            List<GuorenPrpTChargingPostData> chargingPostData = new ArrayList<>();
            GuorenPrpTChargingPostData tChargingPostData;
            int serialno = 0;
            for (ChargingPileDTO chargingPile : chargingPiles) {
                tChargingPostData = new GuorenPrpTChargingPostData();
                chargingPostData.add(tChargingPostData);
                tChargingPostData.setSerialno(BigDecimal.valueOf(++serialno));
                tChargingPostData.setRemark("");
                tChargingPostData.setChargingposttype(chargingPile.getChargingModel());
                tChargingPostData.setChargingpostcode(chargingPile.getChargingCode());
                tChargingPostData.setChargingpostaddress(chargingPile.getChargingAddrComplete());
                tChargingPostData.setChargingpostaddresstype(GuorenAddressTypeEnum.getTpValue(chargingPile.getChargingInstallAddrType()));
                tChargingPostData.setChargingpostkind(GuorenChargingTypeEnum.getTpValue(chargingPile.getChargingType()));
                tChargingPostData.setChargingpostyearlimit(GuorenChargingUseYearsEnum.getTpValue(chargingPile.getChargingUseYears()));
                List<ChargingKind> chargingKindList = chargingPile.getChargingKindList();
                if (CollectionUtil.isNotEmpty(chargingKindList)) {
                    // 1：单投充电桩损失保险 2：单投充电桩责任险 3：同时投保责任和损失险
                    int kindNum = 0;
                    for (ChargingKind chargingKind : chargingKindList) {
                        if (NewKindCodeEnum.NEW_ENERGY_PERSONAL_CHARGER_DAMAGE_COVERAGE.getValue().equals(chargingKind.getKindCode())) {
                            tChargingPostData.setCoverage("1");
                            tChargingPostData.setLossSumLimit(chargingKind.getAmount());
                        } else if (NewKindCodeEnum.NEW_ENERGY_PERSONAL_CHARGER_LIABILITY_COVERAGE.getValue().equals(chargingKind.getKindCode())) {
                            tChargingPostData.setCoverage("2");
                            tChargingPostData.setLiabilitySumLimit(chargingKind.getAmount());
                        }
                        kindNum++;
                    }
                    if (kindNum > 1) {
                        tChargingPostData.setCoverage("3");
                    }
                }
            }
            return chargingPostData;
        }
        return Collections.emptyList();
    }

    /**
     * 填充商业险种信息
     *
     * @param apiPremiumCalculateDTO 报价请求类
     * @param startDate              起保时间
     * @param endDate                终保时间
     * @return 险种
     */
    private List<GuorenBiItemkind> populateKindInfoReq(APIPremiumCalculateDTO apiPremiumCalculateDTO, String startDate, String endDate) {
        CarInfo carInfo = apiPremiumCalculateDTO.getCarInfo();
        List<GuorenBiItemkind> itemkindsReq = new ArrayList<>();

        for (ItemKind serviceTerm : apiPremiumCalculateDTO.getBuyInsuranceInfMap().values()) {
            if (Objects.nonNull(serviceTerm.getBeServiceTerm()) && serviceTerm.getBeServiceTerm()) {
                String kindCode = serviceTerm.getKindCode();

                BigDecimal amount = BigDecimal.ZERO;
                Integer quantity = serviceTerm.getQuantity();
                // 非[道路救援服务特约条款]险种时保额等于服务次数平方
                if (!NewKindCodeEnum.BI_ROADSIDE.getValue().equals(kindCode)) {
                    amount = NumberUtil.mul(quantity, quantity);
                }
                serviceTerm.setAmount(amount);
            }
        }
        for (ItemKind itemKind : apiPremiumCalculateDTO.getBuyInsuranceInfMap().values()) {
            String kindCode = itemKind.getKindCode();
            if (NewKindCodeEnum.CI_JQ.getValue().equals(kindCode) || NewKindCodeEnum.CI_BT.getValue().equals(kindCode)) {
                continue;//交强险/车船税无需添加
            }
            ViInsuranceInf tpViInsuranceInf = apiPremiumCalculateDTO.getTpViInsuranceInfMapSelf().get(itemKind.getKindCode());
            String amount = String.valueOf(itemKind.getAmount());
            if (tpViInsuranceInf == null) {
                throw new TunsBusinessException("不支持自动报价的险种,kindCode:{},kindName:{}", itemKind.getKindCode(), itemKind.getKindName());
            }
            List<String> actualPriceAmountKinds = Arrays.asList(NewKindCodeEnum.BI_VEHICLE_LOSS.getValue(), NewKindCodeEnum.NEW_ENERGY_EXTERNAL_POWER_FAULT_COVERAGE.getValue(), NewKindCodeEnum.BI_ROBBERY_THEFT.getValue());
            if (actualPriceAmountKinds.contains(kindCode)) {
                //车损
                amount = String.valueOf(carInfo.getActualPrice());
            }
            GuorenBiItemkind biItemkind = new GuorenBiItemkind();
            if (NewKindCodeEnum.isMainKind(itemKind.getKindCode())) {
                biItemkind.setMainKindFlag("M");
            } else {
                biItemkind.setMainKindFlag("S");
            }
            biItemkind.setKindCode(tpViInsuranceInf.getKindCode());
            biItemkind.setKindName(tpViInsuranceInf.getKindName());
            biItemkind.setAmount(amount);
            biItemkind.setModeCode("");
            biItemkind.setStartDate(startDate);
            biItemkind.setEndDate(endDate);
            biItemkind.setDeductible("0");
            if (itemKind.getBuyAddl() == null) {
                biItemkind.setIsDeductible("0");
            } else {
                biItemkind.setIsDeductible(itemKind.getBuyAddl());
            }

            if (itemKind.getQuantity() == null) {
                biItemkind.setQuantity("1");
            } else {
                biItemkind.setQuantity(itemKind.getQuantity().toString());
            }

            biItemkind.setRate(Objects.isNull(itemKind.getRate()) ? BigDecimal.ZERO : BigDecimal.valueOf(itemKind.getRate()));
            biItemkind.setUnitAmount(Objects.isNull(itemKind.getUnitAmount()) ? "0" : itemKind.getUnitAmount().toString());
            biItemkind.setFlag(GuorenKindFlagEnum.getKindFlag(tpViInsuranceInf.getKindCode()));
            biItemkind.setItemKindNo(GuorenKindNoEnum.getKindNo(tpViInsuranceInf.getKindCode()));
            biItemkind.setChecked("1");
            itemkindsReq.add(biItemkind);
        }
        return CollUtil.sort(itemkindsReq, Comparator.comparing(o -> "M".equals(o.getMainKindFlag()) ? 0 : 1));
    }

    /**
     * 险种详情处理
     */
    private void kindDetailHandle(NewKindCodeEnum kindCode, List<ViInsKindDetialTp> viInsKindDetialTps,
                                  ItemKind itemKind, GuorenItemKindRes tpItemKind,
                                  ViInsPlcyInfTp viInsPlcyInfTp, ViInsuranceInf tpViInsuranceInf, GuorenCarShipTax carShipTaxRespVO) {
        ViInsKindDetialTp viInsKindDetialTp;
        if (NewKindCodeEnum.CI_JQ.equals(kindCode)) {//交强险
            viInsKindDetialTp = new ViInsKindDetialTp();
            viInsKindDetialTp.setPolicyId(viInsPlcyInfTp.getPolicyId());//保单记录ID
            viInsKindDetialTp.setPolicyBatchId(viInsPlcyInfTp.getPolicyBatchId());//保单批次ID
            viInsKindDetialTp.setOrderNo(1);//序号
            viInsKindDetialTp.setKindCode(tpViInsuranceInf.getInsuranceCode());//险别代码
            viInsKindDetialTp.setKindName(tpViInsuranceInf.getKindName());//险别名称
            viInsKindDetialTp.setQuantity(0);//数量
            viInsKindDetialTp.setUnitAmount(BigDecimal.valueOf(0.0));//单位保额
            viInsKindDetialTp.setAmount(viInsPlcyInfTp.getInsAmount());//总保额
            viInsKindDetialTp.setPremium(viInsPlcyInfTp.getInsuredPremium());//保费
            viInsKindDetialTp.setDiscount(BigDecimal.valueOf(0.0));//折扣
            viInsKindDetialTp.setRate(BigDecimal.valueOf(0.0));//费率
            viInsKindDetialTp.setAddlMark(AddlMarkEnum.NO.getCode());//是否不计面免赔
            viInsKindDetialTp.setBuyAddl(BuyAddlMarkEnum.NO.getCode());//是否购买不计免赔险
            viInsKindDetialTp.setValueType(null);//附加类型
            viInsKindDetialTp.setBusinessMark(BusinessMarkEnum.NO.getCode());//是否商业险
            viInsKindDetialTp.setExtend(null);//扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）
            viInsKindDetialTp.setCreUser(viInsPlcyInfTp.getCreUser());//创建人
            viInsKindDetialTp.setMdfUser(viInsPlcyInfTp.getMdfUser());//更新人
            viInsKindDetialTp.setDelFlag(InsConstants.DelFlag.NORMAL);//删除标志

        } else if (NewKindCodeEnum.CI_BT.equals(kindCode)) {//车船税
            viInsKindDetialTp = new ViInsKindDetialTp();
            viInsKindDetialTp.setPolicyId(viInsPlcyInfTp.getPolicyId());//保单记录ID
            viInsKindDetialTp.setPolicyBatchId(viInsPlcyInfTp.getPolicyBatchId());//保单批次ID
            viInsKindDetialTp.setOrderNo(2);//序号
            viInsKindDetialTp.setKindCode(tpViInsuranceInf.getInsuranceCode());//险别代码
            viInsKindDetialTp.setKindName(tpViInsuranceInf.getKindName());//险别名称
            viInsKindDetialTp.setQuantity(0);//数量
            viInsKindDetialTp.setUnitAmount(BigDecimal.valueOf(0.0));//单位保额
            viInsKindDetialTp.setAmount(BigDecimal.valueOf(0.0));//总保额
            viInsKindDetialTp.setPremium(viInsPlcyInfTp.getAttachPremium());//保费
            viInsKindDetialTp.setDiscount(BigDecimal.valueOf(0.0));//折扣
            viInsKindDetialTp.setRate(BigDecimal.valueOf(0.0));//费率
            viInsKindDetialTp.setAddlMark(AddlMarkEnum.NO.getCode());//是否不计面免赔
            viInsKindDetialTp.setBuyAddl(BuyAddlMarkEnum.NO.getCode());//是否购买不计免赔险
            viInsKindDetialTp.setValueType(null);//附加类型
            viInsKindDetialTp.setBusinessMark(BusinessMarkEnum.NO.getCode());//是否商业险
            viInsKindDetialTp.setExtend(null);//扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）
            viInsKindDetialTp.setCreUser(viInsPlcyInfTp.getCreUser());//创建人
            viInsKindDetialTp.setMdfUser(viInsPlcyInfTp.getMdfUser());//更新人
            viInsKindDetialTp.setDelFlag(InsConstants.DelFlag.NORMAL);//删除标志
            //增加滞纳金、往年补缴、税款所属始期、税款所属止期
            log.info("carShipTaxRespVO==={}", JSONObject.toJSONString(carShipTaxRespVO));
            if (StringUtils.isNotEmpty(carShipTaxRespVO.getLateFee())) {
                viInsKindDetialTp.setLateFee(new BigDecimal(carShipTaxRespVO.getLateFee()));
            }
            if (StringUtils.isNotEmpty(carShipTaxRespVO.getPreviousPay())) {
                viInsKindDetialTp.setPreviousPay(new BigDecimal(carShipTaxRespVO.getPreviousPay()));
            }
            viInsKindDetialTp.setPayStartDate(carShipTaxRespVO.getPayStartDate());
            viInsKindDetialTp.setPayEndDate(carShipTaxRespVO.getPayEndDate());
            log.info("viInsKindDetialTp==={}", JSONObject.toJSONString(viInsKindDetialTp));
        } else {//商业险
            BigDecimal premium = StringUtils.isBlank(tpItemKind.getPremium()) ?
                    new BigDecimal("0") : new BigDecimal(tpItemKind.getPremium());
            BigDecimal unitAmount = itemKind.getUnitAmount() == null ? BigDecimal.valueOf(0) : itemKind.getUnitAmount();
            BigDecimal amount = tpItemKind.getAmount() == null ? BigDecimal.valueOf(0) : BigDecimal.valueOf(tpItemKind.getAmount());
            Integer quantity = itemKind.getQuantity() == null ? 0 : itemKind.getQuantity();

            viInsKindDetialTp = new ViInsKindDetialTp();
            viInsKindDetialTp.setOrderNo(itemKind.getOrderNo());//序号
            viInsKindDetialTp.setBusinessMark(BusinessMarkEnum.YES.getCode());//是否商业险
            viInsKindDetialTp.setPolicyId(viInsPlcyInfTp.getPolicyId());//保单记录ID
            viInsKindDetialTp.setPolicyBatchId(viInsPlcyInfTp.getPolicyBatchId());//保单批次ID
            viInsKindDetialTp.setKindCode(tpViInsuranceInf.getInsuranceCode());//险别代码
            viInsKindDetialTp.setKindName(tpViInsuranceInf.getKindName());//险别名称
            viInsKindDetialTp.setValueType(itemKind.getValueType());
            viInsKindDetialTp.setQuantity(quantity);//数量
            viInsKindDetialTp.setAmount(amount);//总保额
            viInsKindDetialTp.setPremium(premium);//保费
            viInsKindDetialTp.setDiscount(viInsPlcyInfTp.getDiscount());//折扣
            viInsKindDetialTp.setExtend(null);//扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）
            viInsKindDetialTp.setCreUser(viInsPlcyInfTp.getCreUser());//创建人
            viInsKindDetialTp.setMdfUser(viInsPlcyInfTp.getMdfUser());//更新人
            viInsKindDetialTp.setDelFlag(InsConstants.DelFlag.NORMAL);//删除标志
            viInsKindDetialTp.setUnitAmount(unitAmount);
        }
        viInsKindDetialTps.add(viInsKindDetialTp);
    }


    @Override
    public InsEnum getSupportIns() {
        return InsEnum.XDCX;
    }

    /**
     * 保费试算接口前置调用  包括查询商业险、交强险特约信息、车辆实际价值
     *
     * @return
     */
    @Override
    public String preprocessThirdPartyQuotes(APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        log.debug("国任API保费试算前置处理开始,batchId:{}", apiPremiumCalculateDTO.getPolicyBatchId());
        GuorenPremiumReqDTO guorenPremiumReqDTO = new GuorenPremiumReqDTO();
        Map<String, ViInsPlcyInfTp> viInsPlcyInfTpMap = apiPremiumCalculateDTO.getViInsPlcyInfTpMap();
        ViInsPlcyInfTp viInsPlcyInfTpBi = viInsPlcyInfTpMap.get(PolicyTypeEnum.BI.getValue());
        ViInsPlcyInfTp viInsPlcyInfTpCi = viInsPlcyInfTpMap.get(PolicyTypeEnum.CI.getValue());
        GuorenChannelResDTO guorenChannelResDTO = guorenChannelQueryService.queryChannel(apiPremiumCalculateDTO.getCompanyId(), apiPremiumCalculateDTO.getChanDetailId());
        //商业险处理
        GuorenBusiness bi = populateBi(apiPremiumCalculateDTO, viInsPlcyInfTpBi, guorenChannelResDTO.getPrptmain().getComCode());

        //交强险处理
        GuorenCompulsory ci = polulateCi(apiPremiumCalculateDTO, viInsPlcyInfTpCi, guorenChannelResDTO.getPrptmain().getComCode());
        guorenPremiumReqDTO.setGuorenBusiness(bi);
        guorenPremiumReqDTO.setGuorenCompulsory(ci);
        //查询车辆实际价值
        guorenPremiumReqDTO.setGuorenActualValue(beforeProcess(apiPremiumCalculateDTO));
        log.debug("国任API保费试算前置处理结束,batchId:{},返回数据:{}", apiPremiumCalculateDTO.getPolicyBatchId(), JSONUtil.toJsonStr(guorenPremiumReqDTO));
        return JSONUtil.toJsonStr(guorenPremiumReqDTO);
    }
}
