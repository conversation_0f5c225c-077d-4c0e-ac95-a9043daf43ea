package com.tuns.car.channel.service.core.paic;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tuns.car.channel.config.ChannelConfig;
import com.tuns.car.channel.constants.paic.*;
import com.tuns.car.channel.dto.QueryCarActualPriceDTO;
import com.tuns.car.channel.dto.paic.car.*;
import com.tuns.car.channel.dto.paic.res.PAICQuoteConfig;
import com.tuns.car.channel.rpc.paic.PAICRpcService;
import com.tuns.car.channel.service.core.AbstractApiCarInfoService;
import com.tuns.car.channel.util.ApiConfContext;
import com.tuns.car.channel.util.JacksonJsonUtil;
import com.tuns.car.channel.util.UnitTransUtil;
import com.tuns.car.core.constant.*;
import com.tuns.car.core.dto.car.CarInfoDTO;
import com.tuns.car.core.dto.carprocess.premium.request.PersonInfo;
import com.tuns.car.core.vo.car.CarInfoVO;
import com.tuns.core.boot.exception.TunsBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 平安 车型查询
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Slf4j
@Service
public class PAICApiCarInfoServiceImpl extends AbstractApiCarInfoService {
    @Autowired
    private PAICRpcService paicRpcService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private JacksonJsonUtil jacksonJsonUtil;
    @Autowired
    private ChannelConfig config;

    private static final String SUCCESS_CORE = "0";
    private static final String SUCCESS_RENEWAL_CODE = "1";
    private static final String NEED_CAR_CONFIRM_CODE = "2";
    private static final String PAIC_CAR_CONFIRM = "PAIC:CAR:CONFIRM:";
    private static final int MAX_RECURSION_DEPTH = 4;

    @Override
    protected List<CarInfoVO> brandModelQuery(CarInfoDTO carInfoDTO) {
        // 调用标的检查获取后续流程所需的流程ID，userId等参数
        PAICIndexRes paicIndexRes = indexCall(carInfoDTO);
        // 构建车型搜索所需参数
        PAICCarQueryReq paicCarQueryReq = buildSearchVehicleReq(carInfoDTO, paicIndexRes);
        PAICCarQueryContextRes res = new PAICCarQueryContextRes();
        PAICCarSearchRes paicCarSearchRes = carSearchCall(paicCarQueryReq);
        res.setCarSearchRes(paicCarSearchRes);
        return mapRes(res, paicCarQueryReq, carInfoDTO);
    }

    private void mapModelRes(String carSearchRes, List<PAICCarQueryModel> models, List<CarInfoVO> result) {
        models.forEach(model -> {
            CarInfoVO car = new CarInfoVO();
            car.setSerialNo(model.getModelIndex());
            car.setModelCode(model.getPaModelCode());
            car.setPrfsModelCode(model.getPaModelCode());
            car.setModelName(model.getModelName());
            car.setBrandName(model.getBrandName());
            car.setPurchasePrice(model.getPrice());
            car.setSeatCount(model.getSeats());
            car.setFuelType(PAICJYFuelTypEnum.getOwnEnumByValue(model.getPowerTypeCode()));
            car.setExhaustScale(UnitTransUtil.exhaustScaleTrans(model.getExhaustMeasure()));
            car.setMarketDate(model.getMarketDate());
            String[] split = model.getFullWeight().split(";");
            car.setWholeWeight(StrUtil.isNotBlank(model.getFullWeight()) ? Integer.parseInt(split[0]) : null);
            car.setCarInfoCompany(InsEnum.PAIC.getCompanyId());
            car.setExtendField(carSearchRes);
            car.setAgainConfirm(true);
            result.add(car);
        });
    }

    @Override
    protected List<CarInfoVO> autoSecondQuery(List<CarInfoVO> result, CarInfoDTO carInfoDTO) {
        CarInfoVO searchVehicle = result.get(0);
        carInfoDTO.setQueryModel("1");
        carInfoDTO.setPrfsModelCode(searchVehicle.getPrfsModelCode());
        carInfoDTO.setExtendField(searchVehicle.getExtendField());
        List<CarInfoVO> vehicleList = secondQuery(carInfoDTO);
        if (CollUtil.isEmpty(vehicleList)) {
            return Collections.emptyList();
        }
        return recursiveConfirm(carInfoDTO, vehicleList, 0);
    }

    private List<CarInfoVO> recursiveConfirm(CarInfoDTO carInfoDTO, List<CarInfoVO> vehicleList, int recursionDepth) {
        if (recursionDepth > MAX_RECURSION_DEPTH) {
            log.error("递归次数超过最大限制，请检查数据");
            return Collections.emptyList();
        }
        List<Boolean> isAgainConfirm = vehicleList.stream().map(CarInfoVO::isAgainConfirm).collect(Collectors.toList());
        if (!isAgainConfirm.contains(true)) {
            return vehicleList;
        }
        carInfoDTO.setExtendField(vehicleList.get(0).getExtendField());
        Map<String, CarInfoVO> confirmRes = vehicleList.stream().collect(Collectors.toMap(CarInfoVO::getType, Function.identity(), (v1, v2) -> v1));
        CarInfoVO circCarInfo = confirmRes.get("circ");
        CarInfoVO jyCarInfo = confirmRes.get("jy");
        if (Objects.nonNull(circCarInfo)) {
            carInfoDTO.setVehicleStyleUniqueId(circCarInfo.getPrfsModelCode());
        }
        if (Objects.nonNull(jyCarInfo)) {
            carInfoDTO.setPrfsModelCode(jyCarInfo.getPrfsModelCode());
        }
        List<CarInfoVO> confirmVehicleList = secondQuery(carInfoDTO);
        return recursiveConfirm(carInfoDTO, confirmVehicleList, recursionDepth + 1);
    }


    private void mapJyRes(String confirmRes, List<CarInfoVO> result, PAICVehicleDTO vehicle, PAICVehicleModelDTO x, String resultCode, String inputAmountDefault) {
        x.getJyModels().forEach(model -> {
            CarInfoVO car = new CarInfoVO();
            car.setModelCode(model.getPaModelCode());
            car.setModelName(model.getModelName());
            car.setBrandName(model.getBrandName());
            car.setCarName(model.getBrandName());
            car.setPurchasePrice(model.getPrice());
            car.setActualPrice(StrUtil.isNotBlank(inputAmountDefault) ? new BigDecimal(inputAmountDefault) : null);
            car.setPrfsModelCode(model.getPaModelCode());
            car.setSeatCount(model.getSeats());
            car.setFuelType(PAICFuelTypEnum.getOwnEnumByValue(vehicle.getEnergyType()));
            car.setExhaustScale(UnitTransUtil.exhaustScaleTrans(vehicle.getExhaustMeasure()));
            car.setMarketDate(model.getMarketDate());
            car.setWholeWeight(findWholeWeight(vehicle));
            car.setCarTonnage(UnitTransUtil.carTonnageTrans(vehicle.getTonNumber()));
            car.setCarInfoCompany(InsEnum.PAIC.getCompanyId());
            car.setExtendField(confirmRes);
            car.setType("jy");
            car.setAgainConfirm(NEED_CAR_CONFIRM_CODE.equals(resultCode));
            result.add(car);
        });
    }

    private Integer findWholeWeight(PAICVehicleDTO vehicle) {
        if (StrUtil.isNotBlank(vehicle.getWholeWeight())) {
            return new BigDecimal(vehicle.getWholeWeight()).multiply(BigDecimal.valueOf(1000)).intValue();
        }
        if (Objects.nonNull(vehicle.getFullWeight())) {
            return vehicle.getFullWeight().multiply(BigDecimal.valueOf(1000)).intValue();
        }
        return null;
    }

    private void mapCircRes(String confirmRes, List<CarInfoVO> result, PAICVehicleDTO vehicle, PAICVehicleModelDTO x, String resultCode, String inputAmountDefault) {
        x.getCircModels().forEach(model -> {
            CarInfoVO car = new CarInfoVO();
            car.setModelCode(model.getVehicleStyleUniqueId());
            car.setModelName(model.getModelName());
            car.setBrandName(model.getBrandName());
            car.setPurchasePrice(model.getPrice());
            car.setActualPrice(StrUtil.isNotBlank(inputAmountDefault) ? new BigDecimal(inputAmountDefault) : null);
            car.setSeatCount(model.getSeats());
            car.setPrfsModelCode(model.getVehicleStyleUniqueId());
            car.setFuelType(PAICFuelTypEnum.getOwnEnumByValue(vehicle.getEnergyType()));
            car.setExhaustScale(UnitTransUtil.exhaustScaleTrans(vehicle.getExhaustMeasure()));
            car.setMarketDate(model.getMarketDate());
            car.setWholeWeight(findWholeWeight(vehicle));
            car.setCarInfoCompany(InsEnum.PAIC.getCompanyId());
            car.setExtendField(confirmRes);
            car.setType("circ");
            car.setAgainConfirm(NEED_CAR_CONFIRM_CODE.equals(resultCode));
            result.add(car);
        });
    }

    private List<CarInfoVO> mapRes(PAICCarQueryContextRes res, PAICCarQueryReq req, CarInfoDTO carInfoDTO) {
        //该次属于车型搜索的返回
        if (StrUtil.isBlank(carInfoDTO.getQueryModel())) {
            if (Objects.isNull(res.getCarSearchRes()) || CollUtil.isEmpty(res.getCarSearchRes().getModels())) {
                return Collections.emptyList();
            }
            String carSearchRes = jacksonJsonUtil.toJsonStr(res.getCarSearchRes());
            List<CarInfoVO> result = new ArrayList<>();
            mapModelRes(carSearchRes, res.getCarSearchRes().getModels(), result);
            return result;
        } else {
            res.getCarConfirmRes().setUserId(config.getPaicUserId());
            String confirmRes = jacksonJsonUtil.toJsonStr(res.getCarConfirmRes());
            PAICCarConfirmRes carConfirmRes = res.getCarConfirmRes();
            PAICQuoteConfig bizQuoteConfig = carConfirmRes.getBizQuoteConfig();
            String inputAmountDefault;
            if (Objects.nonNull(bizQuoteConfig)) {
                inputAmountDefault = String.valueOf(bizQuoteConfig.getDynamicProp(PAICKindParamConstant.PREFIX.AMOUNT, PAICKindParamConstant.SUFFIX.Default, "01"));
            } else {
                inputAmountDefault = null;
            }
            // 保险公司车型确认会返回两个车型列表，需要用户从两个列表中分别选择一款 再次进行车型确认
            List<CarInfoVO> result = new ArrayList<>();
            if (CollUtil.isNotEmpty(carConfirmRes.getVehicleModels())) {
                PAICVehicleDTO vehicle = carConfirmRes.getVehicle();
                carConfirmRes.getVehicleModels().forEach(x -> {
                    if (CollUtil.isNotEmpty(x.getJyModels())) {
                        mapJyRes(confirmRes, result, vehicle, x, carConfirmRes.getResultCode(), inputAmountDefault);
                    }
                    //发现当不需要在进行车型确认时 如果返回circ数据会导致重新报价的时候 查询不到对应车型
                    if (CollUtil.isNotEmpty(x.getCircModels()) && NEED_CAR_CONFIRM_CODE.equals(carConfirmRes.getResultCode())) {
                        mapCircRes(confirmRes, result, vehicle, x, carConfirmRes.getResultCode(), inputAmountDefault);
                    }
                });
            }
            return result;
        }
    }

    private static PAICCarQueryReq buildSearchVehicleReq(CarInfoDTO carInfoDTO, PAICIndexRes paicIndexRes) {
        PAICCarQueryReq carQueryReq = new PAICCarQueryReq();
        carQueryReq.setFlowid(paicIndexRes.getFlowid());
        carQueryReq.setKeyword(carInfoDTO.getModelName());
        carQueryReq.setUserId(paicIndexRes.getUserId());
        return carQueryReq;
    }

    private PAICCarSearchRes carSearchCall(PAICCarQueryReq req) {
        PAICCarSearchRes paicCarSearchRes = paicRpcService.queryCarInfo(req);
        //需要将标的检查中获取的flowid返回
        paicCarSearchRes.setFlowid(req.getFlowid());
        if (!SUCCESS_CORE.equals(paicCarSearchRes.getResultCode())) {
            throw new TunsBusinessException("车型搜索平台返回：" + paicCarSearchRes.getResultMessage());
        }
        return paicCarSearchRes;
    }

    /**
     * 标的检查
     *
     * @param carInfoDTO
     * @return
     */
    private PAICIndexRes indexCall(CarInfoDTO carInfoDTO) {
        PAICQueryIndexReq indexReq = new PAICQueryIndexReq();
        Map<CustomSettingFieldEnum, String> fieldEnumStringMap = ApiConfContext.getConf().getMap();
        log.info("平安自定义字段获取值:{}", fieldEnumStringMap);
        if (MapUtil.isNotEmpty(fieldEnumStringMap)) {
            indexReq.setCityCode(MapUtil.getStr(fieldEnumStringMap, CustomSettingFieldEnum.SETTING_CODE_041));
        }
        PAICIndexDetailReq detailReq = new PAICIndexDetailReq();
        detailReq.setFrameNo(carInfoDTO.getFrameNumber());
        if (!"*".equals(carInfoDTO.getPlateNumber())) {
            detailReq.setLicenseNo(carInfoDTO.getPlateNumber());
        }
        indexReq.setVehicle(detailReq);
        PAICIndexRes paicIndexRes = paicRpcService.queryIndex(indexReq);
        //0-[成功], 1-上年在平安有投保记录[成功]
        if (!(SUCCESS_CORE.equals(paicIndexRes.getResultCode()) || SUCCESS_RENEWAL_CODE.equals(paicIndexRes.getResultCode()))) {
            throw new TunsBusinessException("标的检查发生错误，错误原因：" + paicIndexRes.getResultMessage());
        }
        if (CollUtil.isNotEmpty(paicIndexRes.getTargetInfoList())) {
            throw new TunsBusinessException("该车辆查询到多标信息,请联系管理人员排查!");
        }
        return paicIndexRes;
    }

    @Override
    protected List<CarInfoVO> vinQuery(CarInfoDTO carInfoDTO) {
        // 调用标的检查获取后续流程所需的流程ID，userId等参数
        PAICIndexRes paicIndexRes = indexCall(carInfoDTO);
        // 构建车型搜索所需参数
        PAICCarQueryReq paicCarQueryReq = buildSearchVehicleReq(carInfoDTO, paicIndexRes);
        PAICCarQueryContextRes res = new PAICCarQueryContextRes();
        PAICCarSearchRes paicCarSearchRes = carSearchCall(paicCarQueryReq);
        res.setCarSearchRes(paicCarSearchRes);
        return mapRes(res, paicCarQueryReq, carInfoDTO);
    }

    @Override
    protected List<CarInfoVO> secondQuery(CarInfoDTO carInfoDTO) {
        PAICCarQueryContextRes res = new PAICCarQueryContextRes();
        //构建车型确认所需参数
        PAICCarQueryReq paicCarQueryReq = buildCarConfirmReq(carInfoDTO);
        PAICCarConfirmRes paicCarConfirmRes = carConfirm(paicCarQueryReq, false);
        res.setCarConfirmRes(paicCarConfirmRes);
        redisTemplate.opsForValue().set(PAIC_CAR_CONFIRM + paicCarConfirmRes.getFlowid(), JSONUtil.toJsonStr(paicCarQueryReq.getConfirmReq()), 1, TimeUnit.DAYS);
        return mapRes(res, paicCarQueryReq, carInfoDTO);
    }

    /**
     * 车型确认
     *
     * @param req
     * @return
     */
    private PAICCarConfirmRes carConfirm(PAICCarQueryReq req, boolean autoConfirmFlag) {
        PAICCarConfirmRes carConfirmRes = paicRpcService.carConfirm(req.getConfirmReq());

        if (!SUCCESS_CORE.equals(carConfirmRes.getResultCode()) && !NEED_CAR_CONFIRM_CODE.equals(carConfirmRes.getResultCode())) {
            throw new TunsBusinessException("车型确认平台返回：" + carConfirmRes.getResultMessage());
        }

        Predicate<List<PAICVehicleModelDTO>> oneVehicleModelPredicate = x -> CollUtil.isNotEmpty(x) && x.size() == 1;
        Predicate<List<PAICVehicleStyleModelDTO>> oneVehicleStylePredicate = x -> CollUtil.isNotEmpty(x) && x.size() == 1;

        if (oneVehicleModelPredicate.test(carConfirmRes.getVehicleModels()) && !autoConfirmFlag && NEED_CAR_CONFIRM_CODE.equals(carConfirmRes.getResultCode())) {
            PAICVehicleModelDTO vehicleModelDTO = carConfirmRes.getVehicleModels().get(0);
            boolean isOneJy = oneVehicleStylePredicate.test(vehicleModelDTO.getJyModels());
            boolean isOneCirc = oneVehicleStylePredicate.test(vehicleModelDTO.getCircModels());
            //仅有一条精友数据且仅有一条平台数据时 自动帮用户进行车型确认
            if (isOneJy && isOneCirc) {
                String vehicleStyleUniqueId = vehicleModelDTO.getCircModels().get(0).getVehicleStyleUniqueId();
                String paModelCode = vehicleModelDTO.getJyModels().get(0).getPaModelCode();
                req.getConfirmReq().getVehicle().setVehicleStyleUniqueId(vehicleStyleUniqueId);
                req.getConfirmReq().getVehicle().setPaModelCode(paModelCode);
                return carConfirm(req, true);
            }
        }
        return carConfirmRes;
    }

    /**
     * 构建车险确认参数
     *
     * @param carInfoDTO
     * @return
     */
    private PAICCarQueryReq buildCarConfirmReq(CarInfoDTO carInfoDTO) {
        PAICCarSearchRes paicCarSearchRes = null;
        PAICCarConfirmRes paicCarConfirmRes = null;
        if (StrUtil.isNotBlank(carInfoDTO.getQueryModel())) {
            paicCarConfirmRes = jacksonJsonUtil.toBean(carInfoDTO.getExtendField(), PAICCarConfirmRes.class);
        } else {
            paicCarSearchRes = jacksonJsonUtil.toBean(carInfoDTO.getExtendField(), PAICCarSearchRes.class);
        }

        Map<String, PersonInfo> personInfoMap = carInfoDTO.getPersonInfos().stream().collect(Collectors.toMap(PersonInfo::getPersonType, Function.identity()));
        PersonInfo ownerData = personInfoMap.get(ViPersonTypeEnum.OWNER.getValue());
        PersonInfo holderData = personInfoMap.get(ViPersonTypeEnum.HOLDER.getValue());
        PersonInfo insuredData = personInfoMap.get(ViPersonTypeEnum.INSURED.getValue());

        PAICVehicleDTO vehicleDTO = buildVehicle(carInfoDTO);
        PAICOwnerDTO paicOwnerDTO = buildOwner(ownerData);
        PAICInsuredDTO paicInsuredDTO = buildInsured(insuredData);
        PAICApplicantDTO paicApplicantDTO = buildHolder(holderData);
        PAICBizQuoteDTO paicBizQuoteDTO = buildQuote(carInfoDTO);
        PAICTaxFormDTO paicTaxFormDTO = buildTax(PAICFuelTypEnum.getValueByOwnValue(carInfoDTO.getFuelType()));

        PAICCarConfirmReq carConfirmReq = new PAICCarConfirmReq();
        String flowid = Objects.nonNull(paicCarConfirmRes) ? paicCarConfirmRes.getFlowid() : paicCarSearchRes.getFlowid();
        carConfirmReq.setFlowid(flowid);
        carConfirmReq.setUserId(config.getPaicUserId());
        carConfirmReq.setEnergyType(PAICFuelTypEnum.getValueByOwnValue(carInfoDTO.getFuelType()));
        carConfirmReq.setIsNewEnergy(FuelTypeEnum.isNewEnergy(carInfoDTO.getFuelType()));
        carConfirmReq.setVehicle(vehicleDTO);
        carConfirmReq.setRegister(paicOwnerDTO);
        carConfirmReq.setInsured(paicInsuredDTO);
        carConfirmReq.setApplicant(paicApplicantDTO);
        carConfirmReq.setBizQuote(paicBizQuoteDTO);
        carConfirmReq.setTaxForm(paicTaxFormDTO);
        PAICCarQueryReq carQueryReq = new PAICCarQueryReq();
        carQueryReq.setConfirmReq(carConfirmReq);
        return carQueryReq;
    }

    /**
     * 税务信息
     *
     * @param energyType
     * @return
     */
    private PAICTaxFormDTO buildTax(String energyType) {
        PAICTaxFormDTO taxFormDTO = new PAICTaxFormDTO();
        if (PAICFuelTypEnum.IS_NEW_ENERGY.test(energyType)) {
            taxFormDTO.setTaxType(PAICCarTaxTypeEnum.FREE.getCode());
        } else {
            taxFormDTO.setTaxType(PAICCarTaxTypeEnum.NORMAL.getValue());
        }
        return taxFormDTO;
    }

    /**
     * 其他信息
     *
     * @param carInfoDTO
     * @return
     */
    private PAICBizQuoteDTO buildQuote(CarInfoDTO carInfoDTO) {
        PAICBizQuoteDTO quoteDTO = new PAICBizQuoteDTO();
        quoteDTO.setSpecialCarFlag(carInfoDTO.getTransferMark());
        quoteDTO.setSpecialCarDate(carInfoDTO.getTransferDate());
        return quoteDTO;
    }

    /**
     * 投保人信息
     *
     * @param holderData
     * @return
     */
    private PAICApplicantDTO buildHolder(PersonInfo holderData) {
        PAICApplicantDTO applicantDTO = new PAICApplicantDTO();
        applicantDTO.setName(holderData.getPersonName());
        applicantDTO.setCertificateValidDate(holderData.getIdentityValidity());
        applicantDTO.setCertificateIssueDate(holderData.getIdentityValidityStart());
        return applicantDTO;
    }

    /**
     * 被保人信息
     *
     * @param insuredData
     * @return
     */
    private PAICInsuredDTO buildInsured(PersonInfo insuredData) {
        PAICInsuredDTO insuredDTO = new PAICInsuredDTO();
        insuredDTO.setName(insuredData.getPersonName());
        insuredDTO.setIdNo(insuredData.getIdentifyNumber());
        insuredDTO.setMobile(insuredData.getMobilePhone());
        if (PersonNatureEnum.PERSON != insuredData.getNature()) {
            insuredDTO.setIdType(PAICIdentifyOrganizeTypeEnum.getByOwnEnum(insuredData.getIdentifyType()));
            insuredDTO.setPersonnelType("0");
            insuredDTO.setOrganizationType("04");
            insuredDTO.setPhoneExchange("0731-6772365");
            insuredDTO.setContactName("刘三顺");
            insuredDTO.setMobile("18827441017");
        } else {
            insuredDTO.setIdType(PAICIdentifyTypeEnum.getByOwnEnum(insuredData.getIdentifyType()));
        }

        insuredDTO.setAddress(insuredData.getAddressComplete());
        return insuredDTO;
    }

    /**
     * 车主信息
     *
     * @param ownerData
     * @return
     */
    private PAICOwnerDTO buildOwner(PersonInfo ownerData) {
        PAICOwnerDTO ownerDTO = new PAICOwnerDTO();
        ownerDTO.setName(ownerData.getPersonName());
        ownerDTO.setIdNo(ownerData.getIdentifyNumber());
        if (PersonNatureEnum.PERSON != ownerData.getNature()) {
            ownerDTO.setIdType(PAICIdentifyOrganizeTypeEnum.getByOwnEnum(ownerData.getIdentifyType()));
            ownerDTO.setPersonnelType("0");
        } else {
            ownerDTO.setIdType(PAICIdentifyTypeEnum.getByOwnEnum(ownerData.getIdentifyType()));
        }
        //如果是身份证，则通过身份证信息截取用户的实际性别
        if(PAICIdentifyTypeEnum.C01.getCode().equals(ownerData.getIdentifyType())){
            //ownerDTO.setGender("1".equals(ownerData.getPersonSex()) ? "M" : "F");
            ownerDTO.setGender(IdcardUtil.getGenderByIdCard(ownerData.getIdentifyNumber()) == 1 ? "M" : "F");
        }
        ownerDTO.setBirthday(ownerData.getBirthDate());
        ownerDTO.setMobile(ownerData.getMobilePhone());
        return ownerDTO;
    }

    /**
     * 构造车型相关参数
     *
     * @param carInfoDTO
     * @return
     */
    private PAICVehicleDTO buildVehicle(CarInfoDTO carInfoDTO) {
        PAICVehicleDTO vehicleDTO = new PAICVehicleDTO();
        vehicleDTO.setEngineNo(carInfoDTO.getEngineNumber());
        vehicleDTO.setFrameNo(carInfoDTO.getFrameNumber());
        vehicleDTO.setSeats(carInfoDTO.getSeatCount());
        vehicleDTO.setPaModelCode(carInfoDTO.getPrfsModelCode());
        vehicleDTO.setVehicleStyleUniqueId(carInfoDTO.getVehicleStyleUniqueId());
        vehicleDTO.setRegisterDate(carInfoDTO.getRegDate());
        vehicleDTO.setOwnAttribute(PAICNatureEnum.getValue(carInfoDTO.getOwnershipNature()));
        vehicleDTO.setUseAttribute(OperationNatureEnum.OPERATION.getValue().equals(carInfoDTO.getOperationNature()) ? "01" : "02");
        Optional.ofNullable(carInfoDTO.getCarTonnage())
                .map(ton -> ton.divide(BigDecimal.valueOf(1000), 3, RoundingMode.HALF_UP))
                .filter(value -> value.compareTo(BigDecimal.ZERO) != 0)
                .ifPresent(value -> vehicleDTO.setTonNumber(String.valueOf(value)));
        Optional.ofNullable(carInfoDTO.getWholeWeight())
                .map(weight -> new BigDecimal(weight).divide(BigDecimal.valueOf(1000), 3, RoundingMode.HALF_UP))
                .filter(value -> value.compareTo(BigDecimal.ZERO) != 0)
                .ifPresent(value -> vehicleDTO.setWholeWeight(String.valueOf(value)));
        return vehicleDTO;
    }

    @Override
    public BigDecimal queryCarActualPrice(QueryCarActualPriceDTO req) {
        PAICCarConfirmRes insCar = jacksonJsonUtil.toBean(req.getCarInfo().getExtendField(), PAICCarConfirmRes.class);
        PAICQuoteConfig bizQuoteConfig = insCar.getBizQuoteConfig();
        if (Objects.isNull(bizQuoteConfig)) {
            throw new TunsBusinessException("车型数据异常，未返回车损浮动范围，请暂时不购买车损并联系管理人员排查！");
        }
        String inputAmount = String.valueOf(bizQuoteConfig.getDynamicProp(PAICKindParamConstant.PREFIX.AMOUNT, PAICKindParamConstant.SUFFIX.Default, "01"));
        return new BigDecimal(inputAmount);

    }

    @Override
    public String queryRenewalInfo(CarInfoDTO carInfoDTO) {
        return null;
    }

    @Override
    public InsEnum getSupportIns() {
        return InsEnum.PAIC;
    }
}
