package com.tuns.car.channel.service.core.paic;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.tuns.car.channel.constants.paic.*;
import com.tuns.car.channel.dto.APIPremiumCalculateDTO;
import com.tuns.car.channel.dto.paic.car.PAICCarConfirmRes;
import com.tuns.car.channel.dto.paic.req.PAICBaseQuote;
import com.tuns.car.channel.dto.paic.req.PAICChargingPost;
import com.tuns.car.channel.dto.paic.req.PAICPremiumReq;
import com.tuns.car.channel.dto.paic.res.PAICPremiumRes;
import com.tuns.car.channel.dto.paic.res.PAICQuoteConfig;
import com.tuns.car.channel.util.ApiConfContext;
import com.tuns.car.channel.util.JacksonJsonUtil;
import com.tuns.car.core.constant.CustomSettingFieldEnum;
import com.tuns.car.core.constant.InsuranceMappingTypeEnum;
import com.tuns.car.core.constant.NewKindCodeEnum;
import com.tuns.car.core.constant.PolicyTypeEnum;
import com.tuns.car.core.dto.carprocess.premium.request.ItemKind;
import com.tuns.car.core.dto.charging.ChargingPileDTO;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import com.tuns.car.core.entity.ViInsuranceInf;
import com.tuns.core.boot.constant.InsConstants;
import com.tuns.core.boot.exception.TunsBusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 平安 保费计算请求封装
 * {@link PAICApiPremiumCalculateServiceImpl} 引用
 *
 * <AUTHOR>
 * @since 2023-12-5 16:32
 */
@Service
public class PAICPremiumBusinessService {

    private static final String SUCCESS_CODE = "0";

    @Autowired
    private JacksonJsonUtil jacksonJsonUtil;


    public PAICPremiumReq mapReq(APIPremiumCalculateDTO ownReq) {
        PAICCarConfirmRes insCar = jacksonJsonUtil.toBean(ownReq.getCarInfo().getExtendField(), PAICCarConfirmRes.class);
        if (!SUCCESS_CODE.equals(insCar.getResultCode())) {
            throw new TunsBusinessException("车型确认存在异常！请重试或联系相关人员处理！");
        }
        Map<String, ViInsPlcyInfTp> viInsPlcyInfTpMap = ownReq.getViInsPlcyInfTpMap();
        List<ChargingPileDTO> chargingPiles = ownReq.getChargingPiles();
        PAICPremiumReq paicPremiumReq = new PAICPremiumReq();
        Map<CustomSettingFieldEnum, String> map = ApiConfContext.getConf().getMap();
        //N 中介价1 Y 中介价2 1 线上价
        if (StrUtil.isNotBlank(map.get(CustomSettingFieldEnum.SETTING_CODE_003))) {
            paicPremiumReq.setIsOnlineProducts(map.get(CustomSettingFieldEnum.SETTING_CODE_003));
        }
        paicPremiumReq.setFlowid(insCar.getFlowid());
        paicPremiumReq.setUserId(insCar.getUserId());
        //映射投保基础信息  投保日期等
        mapProposalBase(paicPremiumReq, viInsPlcyInfTpMap);
        //映射险种
        mapKind(insCar.getBizQuoteConfig(), paicPremiumReq, ownReq);
        //新能源充电桩
        if (ownReq.getNewEnergy()) {
            Map<String, List<PAICChargingPost>> chargingMap = mapCharging(chargingPiles);
            if (Objects.nonNull(chargingMap)) {
                List<PAICChargingPost> amount22XNYChargingPostList = chargingMap.get(NewKindCodeEnum.NEW_ENERGY_PERSONAL_CHARGER_DAMAGE_COVERAGE.getValue());
                List<PAICChargingPost> amount23XNYChargingPostList = chargingMap.get(NewKindCodeEnum.NEW_ENERGY_PERSONAL_CHARGER_LIABILITY_COVERAGE.getValue());
                paicPremiumReq.setAmount22XNYChargingPostList(CollectionUtil.isNotEmpty(amount22XNYChargingPostList) ? amount22XNYChargingPostList : null);
                paicPremiumReq.setAmount23XNYChargingPostList(CollectionUtil.isNotEmpty(amount23XNYChargingPostList) ? amount23XNYChargingPostList : null);
            }
        }
        //非通融报价
        paicPremiumReq.setAccommodationFlag("false");
        return paicPremiumReq;
    }

    private void mapKind(PAICQuoteConfig bizQuoteConfig, PAICPremiumReq paicPremiumReq, APIPremiumCalculateDTO ownReq) {
        //平安不支持自己勾选服务条款，这里先把购买险种集合中的服务条款剔除
        String[] additionalTerm = ArrayUtil.toArray(NewKindCodeEnum.getAdditionalTermList(), String.class);
        MapUtil.removeAny(ownReq.getBuyInsuranceInfMap(), additionalTerm);
        ownReq.getSpecialInfo().setServiceTerms(new ArrayList<>());
        Map<String, ItemKind> buyInsuranceInfMap = ownReq.getBuyInsuranceInfMap();
        Map<String, ViInsuranceInf> tpViInsuranceInfMapSelf = ownReq.getTpViInsuranceInfMapSelf();
        for (ItemKind itemKind : buyInsuranceInfMap.values()) {
            String kindCode = itemKind.getKindCode();
            ViInsuranceInf viInsuranceInf = tpViInsuranceInfMapSelf.get(itemKind.getKindCode());
            String insKindCode = viInsuranceInf.getKindCode();
            String amount = String.valueOf(itemKind.getAmount());
            if(Objects.nonNull(itemKind.getAmount())){
                amount = itemKind.getAmount().stripTrailingZeros().toPlainString();
            }
            //仅需要设置amount为 1 投保 无需保额
            buildOnlySetApplyKind(bizQuoteConfig, paicPremiumReq, kindCode, viInsuranceInf, insKindCode);
            // 单独投保 or 与主险共享保额
            buildSetApplyAndSetAmountByType(paicPremiumReq, itemKind, viInsuranceInf, ownReq);
            //只需要设置保额 乘客险保额设置为单位保额
            buildOnlySetAmount(paicPremiumReq, itemKind, kindCode, insKindCode, amount);
        }
    }

    private static void buildOnlySetAmount(PAICPremiumReq paicPremiumReq, ItemKind itemKind, String kindCode, String insKindCode, String amount) {
        if (PAICKindApplySceneEnum.isOnlySetAmount(kindCode)) {
            paicPremiumReq.putDynamicProp(PAICKindParamConstant.PREFIX.AMOUNT, insKindCode, amount);
            if (NewKindCodeEnum.BI_PERSONNEL_PASSENGER.getValue().equals(kindCode)) {
                String unitAmount = String.valueOf(itemKind.getUnitAmount());
                if(Objects.nonNull(itemKind.getUnitAmount())){
                    unitAmount = itemKind.getUnitAmount().stripTrailingZeros().toPlainString();
                }
                paicPremiumReq.putDynamicProp(PAICKindParamConstant.PREFIX.AMOUNT, insKindCode, unitAmount);
            }
        }
    }

    private void buildSetApplyAndSetAmountByType(PAICPremiumReq paicPremiumReq, ItemKind itemKind, ViInsuranceInf viInsuranceInf, APIPremiumCalculateDTO ownReq) {
        String insKindCode = viInsuranceInf.getKindCode();
        //平安保险不能是50000.00这种,应为50000
        String amount = String.valueOf(itemKind.getAmount());
        if(Objects.nonNull(itemKind.getAmount())){
            amount = itemKind.getAmount().stripTrailingZeros().toPlainString();
        }
        String kindCode = itemKind.getKindCode();
        Map<String, ViInsuranceInf> tpViInsuranceInfMapTP = ownReq.getTpViInsuranceInfMapTP();
        Map<String, ItemKind> buyInsuranceInfMap = ownReq.getBuyInsuranceInfMap();
        if (PAICKindApplySceneEnum.isSetApplyAndSetAmountByType(kindCode)) {
            String mainKindCode = viInsuranceInf.getMainKindCode();
            ViInsuranceInf mainInsuranceInf = tpViInsuranceInfMapTP.get(mainKindCode);
            ItemKind mainKind = buyInsuranceInfMap.get(mainInsuranceInf.getInsuranceCode());
            if (isSharedAmount(itemKind, mainKind)) {
                paicPremiumReq.putDynamicProp(PAICKindParamConstant.PREFIX.AMOUNT, insKindCode, "999");
            } else {
                separateInsure(paicPremiumReq, itemKind, kindCode, insKindCode, amount);
            }
        }
    }

    private static void buildOnlySetApplyKind(PAICQuoteConfig bizQuoteConfig, PAICPremiumReq paicPremiumReq, String kindCode, ViInsuranceInf viInsuranceInf, String insKindCode) {
        if (PAICKindApplySceneEnum.isOnlySetApply(kindCode)) {
            paicPremiumReq.putDynamicProp(PAICKindParamConstant.PREFIX.AMOUNT, insKindCode, "1");
            //车损险比较特殊 需要设置保额 且保额存在上下限范围 与产品沟通后取上限 未返回则取车辆实际价值
            if (NewKindCodeEnum.BI_VEHICLE_LOSS.getValue().equals(kindCode)) {
                if (Objects.isNull(bizQuoteConfig)) {
                    throw new TunsBusinessException("车型数据异常，未返回车损浮动范围，请暂时不购买车损并联系管理人员排查！");
                }
                String inputAmount = String.valueOf(bizQuoteConfig.getDynamicProp(PAICKindParamConstant.PREFIX.AMOUNT, PAICKindParamConstant.SUFFIX.Default, "01"));
                if (InsuranceMappingTypeEnum.NEW_ENERGY == viInsuranceInf.getMappingType()) {
                    paicPremiumReq.setInputAmountXNY(inputAmount);
                } else {
                    paicPremiumReq.setInputAmount(inputAmount);
                }
            }
        }
    }

    //判断依据为 与其主险的保额是否相同 相同则认为是共享保额
    private boolean isSharedAmount(ItemKind itemKind, ItemKind mainKind) {
        if (Objects.isNull(mainKind)) {
            return false;
        }
        return NumberUtil.equals(itemKind.getAmount(), mainKind.getAmount());
    }

    // 设置为单独投保 且设置保额 保额字段为amount*Fee *为险种代码
    private static void separateInsure(PAICPremiumReq paicPremiumReq, ItemKind itemKind, String kindCode, String insKindCode, String amount) {
        paicPremiumReq.putDynamicProp(PAICKindParamConstant.PREFIX.AMOUNT, insKindCode, "1");
        paicPremiumReq.putDynamicProp(PAICKindParamConstant.PREFIX.AMOUNT, PAICKindParamConstant.SUFFIX.FEE, insKindCode, amount);
        if (NewKindCodeEnum.BI_MEDICAL_PASSENGER.getValue().equals(kindCode)) {
            String unitAmount = itemKind.getUnitAmount().toBigInteger().toString();
            paicPremiumReq.putDynamicProp(PAICKindParamConstant.PREFIX.AMOUNT, PAICKindParamConstant.SUFFIX.FEE, insKindCode, unitAmount);
        }
    }

    private Map<String, List<PAICChargingPost>> mapCharging(List<ChargingPileDTO> chargingPiles) {
        if (CollectionUtil.isNotEmpty(chargingPiles)) {
            Map<String, List<PAICChargingPost>> map = new HashMap<>();
            List<PAICChargingPost> amount22XNYChargingPostList = new ArrayList<>();
            List<PAICChargingPost> amount23XNYChargingPostList = new ArrayList<>();
            for (int i = 0; i < chargingPiles.size(); i++) {
                ChargingPileDTO chargingPile = chargingPiles.get(i);
                PAICChargingPost paicChargingPost = new PAICChargingPost();
                paicChargingPost.setSubjectName("设备" + (i + 1));
                paicChargingPost.setChargingPostType(chargingPile.getChargingModel());
                paicChargingPost.setChargingPostCode(chargingPile.getChargingCode());
                paicChargingPost.setChargingPostAddress(chargingPile.getChargingAddrComplete());
                paicChargingPost.setChargingPostAddressType(PAICChargingPostAddressTypeEnum.matchValue(chargingPile.getChargingInstallAddrType()));
                paicChargingPost.setChargingPostKind(PAICChargingPostTypeEnum.matchValue(chargingPile.getChargingType()));
                chargingPile.getChargingKindList().forEach(kind -> {
                    if (NewKindCodeEnum.NEW_ENERGY_PERSONAL_CHARGER_DAMAGE_COVERAGE.getValue().equals(kind.getKindCode())) {
                        if (InsConstants.YNFlag.YES.equals(kind.getChooseFlag())) {
                            paicChargingPost.setInsuredAmount(String.valueOf(kind.getAmount()));
                            amount22XNYChargingPostList.add(paicChargingPost);
                        }
                    } else if (NewKindCodeEnum.NEW_ENERGY_PERSONAL_CHARGER_LIABILITY_COVERAGE.getValue().equals(kind.getKindCode())) {
                        if (InsConstants.YNFlag.YES.equals(kind.getChooseFlag())) {
                            paicChargingPost.setInsuredAmount(String.valueOf(kind.getAmount()));
                            amount23XNYChargingPostList.add(paicChargingPost);
                        }
                    }
                });
            }
            map.put(NewKindCodeEnum.NEW_ENERGY_PERSONAL_CHARGER_DAMAGE_COVERAGE.getValue(), amount22XNYChargingPostList);
            map.put(NewKindCodeEnum.NEW_ENERGY_PERSONAL_CHARGER_LIABILITY_COVERAGE.getValue(), amount23XNYChargingPostList);
            return map;
        }
        return null;
    }

    private void mapProposalBase(PAICPremiumReq paicPremiumReq, Map<String, ViInsPlcyInfTp> viInsPlcyInfTpMap) {
        ViInsPlcyInfTp bi = viInsPlcyInfTpMap.get(PolicyTypeEnum.BI.getValue());
        ViInsPlcyInfTp ci = viInsPlcyInfTpMap.get(PolicyTypeEnum.CI.getValue());
        PAICBaseQuote paicBizQuote = null;
        if (Objects.nonNull(bi)) {
            paicPremiumReq.setApplyBiz("true");
            paicBizQuote = buildBizQuote(bi);
        }
        PAICBaseQuote paicForceQuote = null;
        if (Objects.nonNull(ci)) {
            paicPremiumReq.setApplyForce("true");
            paicForceQuote = buildForceQuote(ci);
        }
        paicPremiumReq.setForceQuote(paicForceQuote);
        paicPremiumReq.setBizQuote(paicBizQuote);
    }

    private PAICBaseQuote buildForceQuote(ViInsPlcyInfTp ci) {
        PAICBaseQuote paicBaseQuote = new PAICBaseQuote();
        paicBaseQuote.setBeginDate(ci.getInsBegin());

        return paicBaseQuote;
    }

    private PAICBaseQuote buildBizQuote(ViInsPlcyInfTp bi) {
        PAICBaseQuote paicBaseQuote = new PAICBaseQuote();
        paicBaseQuote.setBeginDate(bi.getInsBegin());
        return paicBaseQuote;
    }

    public String mergeErrorMsg(PAICPremiumRes premiumRes) {
        StringBuilder sb = new StringBuilder();
        //封装基本的错误信息
        String baseMsg = buildBaseMsg(premiumRes);
        if (StrUtil.isNotBlank(baseMsg)) {
            sb.append(baseMsg).append("；");
        }
        //交强险or商业险错误信息
        String bizAndForceMsg = buildBizAndForceMsg(premiumRes);
        if (StrUtil.isNotBlank(bizAndForceMsg)) {
            sb.append(bizAndForceMsg).append("\n");
        }
        return sb.toString();
    }

    private static String buildBaseMsg(PAICPremiumRes premiumRes) {
        StringBuilder sb = new StringBuilder();
        String resultCode = premiumRes.getResultCode();
        String resultMessage = premiumRes.getResultMessage();
        if (StrUtil.isBlank(resultMessage)) {
            String message = PAICPremiumBusinessCodeEnum.match(resultCode);
            if (StrUtil.isNotBlank(message)) {
                sb.append(message).append("\n");
            }
        } else {
            sb.append(resultMessage).append("\n");
        }
        return sb.toString();
    }

    private static String buildBizAndForceMsg(PAICPremiumRes premiumRes) {
        StringBuilder sb = new StringBuilder();
        if (Objects.nonNull(premiumRes.getBizQuoteResult()) && StrUtil.isNotBlank(premiumRes.getBizQuoteResult().getValidMessage())) {
            String validMessage = premiumRes.getBizQuoteResult().getValidMessage();
            sb.append("商业险报价提示：").append(validMessage).append("\n");
        }
        if (Objects.nonNull(premiumRes.getForceQuoteResult()) && StrUtil.isNotBlank(premiumRes.getForceQuoteResult().getValidMessage())) {
            String validMessage = premiumRes.getForceQuoteResult().getValidMessage();
            sb.append("交强险报价提示：").append(validMessage).append("\n");
        }
        return sb.toString();
    }
}
