package com.tuns.car.channel.service.core;

import com.tuns.car.channel.constant.CallbackEnum;
import com.tuns.car.channel.dto.callback.CallBackInsResDTO;
import com.tuns.car.core.InsServant;

/**
 * 保险公司回调响应 处理类
 *
 * <AUTHOR>
 */
public interface InsCallBackResponse extends InsServant {

    /**
     * 根据通知类型，返回一个成功的回调通知响应
     *
     * @param callbackEnum 通知类型
     * @return
     */
    CallBackInsResDTO successResponse(CallbackEnum callbackEnum);

    /**
     * 根据通知类型，返回一个失败的回调通知响应
     *
     * @param callbackEnum 通知类型
     * @return
     */
    CallBackInsResDTO failResponse(CallbackEnum callbackEnum);
}
