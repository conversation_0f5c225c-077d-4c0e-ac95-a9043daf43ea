package com.tuns.car.channel.service.core;

import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 消息抽取器
 */
public class MessageExtractor<T> {
    /**
     * 是否需要抛出异常
     */
    private Boolean defaultThrowException;

    /**
     * 字段抽取方法
     */
    private Function<T, String> filedFunc;

    /**
     * 匹配方法
     */
    private Predicate<T> matchFunc;

    private MessageExtractor(Boolean defaultThrowException, Predicate<T> matchFunc, Function<T, String> filedFunc) {
        this.defaultThrowException = defaultThrowException;
        this.matchFunc = matchFunc;
        this.filedFunc = filedFunc;
    }

    /**
     * @param defaultThrowException 当消息内容不属于特殊信息时，默认是否抛出异常
     * @param filedFunc
     * @param <T>
     * @return
     */
    public static <T> MessageExtractor<T> build(Boolean defaultThrowException, Function<T, String> filedFunc) {
        return new MessageExtractor<>(defaultThrowException, t -> true, filedFunc);
    }

    /**
     * @param defaultThrowException 当消息内容不属于特殊信息时，默认是否抛出异常
     * @param filedFunc
     * @param <T>
     * @return
     */
    public static <T> MessageExtractor<T> build(Boolean defaultThrowException, Predicate<T> matchFunc, Function<T, String> filedFunc) {
        return new MessageExtractor<>(defaultThrowException, matchFunc, filedFunc);
    }


    public Boolean getDefaultThrowException() {
        return defaultThrowException;
    }

    public Function<T, String> getFiledFunc() {
        return filedFunc;
    }

    public Predicate<T> getMatchFunc() {
        return matchFunc;
    }
}
