package com.tuns.car.channel.service.core.capli;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.tuns.car.channel.constants.capli.EffectiveImmediatelyEnum;
import com.tuns.car.channel.constants.capli.*;
import com.tuns.car.channel.dto.APIPremiumCalculateDTO;
import com.tuns.car.channel.dto.capli.CapLiApiBaseResDTO;
import com.tuns.car.channel.dto.capli.car.CapLiApiCarInfoReqDTO;
import com.tuns.car.channel.dto.capli.car.CapLiCarQueryModelRes;
import com.tuns.car.channel.dto.capli.car.CapLiCarQueryRes;
import com.tuns.car.channel.dto.capli.premium.*;
import com.tuns.car.channel.entity.ViInsApiCompanyConf;
import com.tuns.car.channel.rpc.capli.CapLiRpcService;
import com.tuns.car.channel.service.core.AbstractApiPremiumCalculateService;
import com.tuns.car.channel.util.ApiConfContext;
import com.tuns.car.channel.util.capli.CapLiTimeHandleUtils;
import com.tuns.car.core.constant.*;
import com.tuns.car.core.dto.car.PremiumCalculateResultDTO;
import com.tuns.car.core.dto.carprocess.premium.request.CarInfo;
import com.tuns.car.core.dto.carprocess.premium.request.ItemKind;
import com.tuns.car.core.dto.carprocess.premium.request.PersonInfo;
import com.tuns.car.core.dto.charging.ChargingPileDTO;
import com.tuns.car.core.entity.ViInsKindDetialTp;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import com.tuns.car.core.entity.ViInsuranceInf;
import com.tuns.car.spider.dto.chan.ComsChanDetailDTO;
import com.tuns.car.spider.feign.carspider.SpiderChanFeign;
import com.tuns.core.boot.constant.InsConstants;
import com.tuns.core.boot.constant.InsErrorCode;
import com.tuns.core.boot.exception.TunsBusinessException;
import com.tuns.core.boot.utils.ArithUtil;
import com.tuns.core.boot.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 长安保费试算实现类
 * @date 2022/5/30 15:38
 */
@Slf4j
@Service
public class CapLiApiPremiumCalculateServiceImpl extends AbstractApiPremiumCalculateService<CapLiApiCalculateReq, CapLiApiBaseResDTO<CapLiApiCalculateRes>, CapLiCarQueryRes> {
    @Autowired
    private CapLiRpcService capLiRpcService;
    @Autowired
    private CapLiApiCarInfoServiceImpl carInfoService;
    @Autowired
    private CapLiPremiumCalculateErrorResponseHandler capLiPremiumCalculateErrorResponseHandler;
    private static final int NUM = -999;
    private static final String SYMBOL = "*";
    @Autowired
    private SpiderChanFeign spiderChan;

    @Override
    protected CapLiCarQueryRes beforeProcess(APIPremiumCalculateDTO inputDTO) {
        // 1. 查询车辆实际价值
        return actualValue(inputDTO, inputDTO.getCarInfo());
    }

    @Override
    public CapLiApiCalculateReq mapReq(APIPremiumCalculateDTO apiPremiumCalculateDTO, CapLiCarQueryRes res) {
        ComsChanDetailDTO comsChanDetailDTO = spiderChan.getById(apiPremiumCalculateDTO.getChanDetailId());
        Collection<PersonInfo> personInfo = apiPremiumCalculateDTO.getPersonInfoMap().values();
        Map<String, PersonInfo> personInfoMap = personInfo.stream()
                .collect(Collectors.toMap(PersonInfo::getPersonType, Function.identity()));

        //车主
        PersonInfo owner = personInfoMap.get(ViPersonTypeEnum.OWNER.getValue());
        //被保人
        PersonInfo insured = personInfoMap.get(ViPersonTypeEnum.INSURED.getValue());
        //车辆信息 实时获取
        CapLiCarQueryModelRes carQueryModelRes = res.getModelList().get(0);
        //构建保费试算请求参数
        CapLiApiCalculateReq calculateReq = new CapLiApiCalculateReq();
        //构建基础必填属性
        buildBasicsInfo(apiPremiumCalculateDTO, calculateReq);
        //构建车辆信息
        buildCarInfo(apiPremiumCalculateDTO, calculateReq, res);
        //构建险种信息  交强险  商业险
        buildKindListInfo(apiPremiumCalculateDTO, calculateReq, owner, insured, carQueryModelRes);
        //构建关系人信息
        buildInsuredInfo(apiPremiumCalculateDTO, calculateReq);
        //构建合作方
        buildPartner(calculateReq, comsChanDetailDTO, needAnotherPartner(apiPremiumCalculateDTO));
        return calculateReq;
    }

    @Override
    public CapLiApiBaseResDTO<CapLiApiCalculateRes> doInvoke(CapLiApiCalculateReq capLiApiCalculateReq, APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        //调用保司保费试算api
        CapLiApiBaseResDTO<CapLiApiCalculateRes> capLiApiCalculateResCapLiApiBaseResDTO = capLiRpcService.capLiQuote(capLiApiCalculateReq);
        capLiPremiumCalculateErrorResponseHandler.handle(capLiApiCalculateResCapLiApiBaseResDTO, apiPremiumCalculateDTO);
        return capLiApiCalculateResCapLiApiBaseResDTO;
    }

    @Override
    public PremiumCalculateResultDTO mapRes(CapLiApiBaseResDTO<CapLiApiCalculateRes> resDTO, CapLiApiCalculateReq req, APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        PremiumCalculateResultDTO premiumCalculatedDTO = BeanUtil.toBean(apiPremiumCalculateDTO, PremiumCalculateResultDTO.class);
        ViInsApiCompanyConf companyConf = ApiConfContext.getConf().getCompanyConf();
        ViInsPlcyInfTp ci = apiPremiumCalculateDTO.getViInsPlcyInfTpMap().get(PolicyTypeEnum.CI.getValue());
        ViInsPlcyInfTp bi = apiPremiumCalculateDTO.getViInsPlcyInfTpMap().get(PolicyTypeEnum.BI.getValue());
        List<ViInsPlcyInfTp> infTps = apiPremiumCalculateDTO.getViInsPlcyInfTps();
        CapLiApiCalculateRes res = resDTO.getResultInfo();
        String msg = resDTO.getErrMsg();
        BigDecimal score = new BigDecimal("0");
        if (Objects.nonNull(bi)) {
            List<ExtendInfoRes> extendInfo = res.getExtendInfoList();
            if (null != extendInfo) {
                for (ExtendInfoRes extend : extendInfo) {
                    if ("autonomyScore".equals(extend.getKey())) {
                        //评分
                        score = new BigDecimal(extend.getValue());
                    }
                }
            }
        }

        //上年理赔次数

        //交强险理赔次数
        int lastClaimCountCi = 0;
        //商业险理赔次数
        int lastClaimCountBi = 0;
        for (ClaimInfoListRes tpClaimInfo : res.getClaimInfoList()) {
            if ("0501".equals(tpClaimInfo.getCoverageType())) {
                lastClaimCountBi++;
            } else if ("0507".equals(tpClaimInfo.getCoverageType())) {
                lastClaimCountCi++;
            }
        }
        //保险公司险种信息
        Map<String, ViInsuranceInf> viInsuranceInfMapTP = apiPremiumCalculateDTO.getTpViInsuranceInfMapTP();
        //险种明细
        List<ViInsKindDetialTp> viInsKindDetailTps = new ArrayList<>();

        for (ViInsPlcyInfTp viInsPlcyInfTp : infTps) {
            //商业
            if (PolicyTypeEnum.BI.getValue().equals(viInsPlcyInfTp.getPolicyType())) {
                fillBusinessData(viInsPlcyInfTp, score, res, lastClaimCountBi, viInsPlcyInfTp);
            } //交强
            else {
                fillComplyData(ci, viInsKindDetailTps, apiPremiumCalculateDTO.getTpViInsuranceInfMapSelf(), res, lastClaimCountCi, viInsPlcyInfTp);
            }
            //保费计算识别码/保险公司订单号码
            viInsPlcyInfTp.setInsPremiumNumber(res.getQuoteNo());
            //总保费
            viInsPlcyInfTp.setTotalPremium(ArithUtil.add(viInsPlcyInfTp.getInsuredPremium(), viInsPlcyInfTp.getAttachPremium()));
            //报价成功
            viInsPlcyInfTp.setRecordStatus(RecordStatusEnum.SUCCEED);
            //投保状态
            viInsPlcyInfTp.setInsuredStatus(InsuredStatusEnum.PREMIUMCACULATE.getValue());
            //报价审核人ID
            viInsPlcyInfTp.setReviewerId(companyConf.getCompanyId());
            //报价审核人名称
            viInsPlcyInfTp.setReviewerName(companyConf.getCompanyName());
            //报价时间
            viInsPlcyInfTp.setReviewTime(LocalDateTime.now());
            //报价备注信息
            viInsPlcyInfTp.setReviewMsg(msg);
            //设置riskCode
            viInsPlcyInfTp.setQuerySequenceNo(apiPremiumCalculateDTO.getNewEnergy() ? "0564" : "0560");
            //设置合作方
            viInsPlcyInfTp.setPartnerName(req.getPartnerName2());
        }
        //填充险种信息
        fillKindDetailInfo(ci, bi, viInsKindDetailTps, apiPremiumCalculateDTO.getBuyInsuranceInfMap(), res, viInsuranceInfMapTP);

        try {
            //获取特约信息
            getSpecialAgreement(infTps, res);
        } catch (Exception e) {
            log.error("CapliReq.process.specialAgreements.Exception:", e);
            throw new TunsBusinessException("获取特别约定失败");
        }

        premiumCalculatedDTO.setViInsPlcyInfTps(infTps);
        premiumCalculatedDTO.setViInsKindDetialTps(viInsKindDetailTps);
        return premiumCalculatedDTO;
    }

    @Override
    public InsEnum getSupportIns() {
        return InsEnum.CAPLI;
    }

    void buildBasicsInfo(APIPremiumCalculateDTO apiPremiumCalculateDTO, CapLiApiCalculateReq calculateReq) {
        ViInsPlcyInfTp ci = apiPremiumCalculateDTO.getViInsPlcyInfTps().stream()
                .collect(Collectors.toMap(ViInsPlcyInfTp::getPolicyType, Function.identity())).get(PolicyTypeEnum.CI.getValue());
        ViInsPlcyInfTp bi = apiPremiumCalculateDTO.getViInsPlcyInfTps().stream()
                .collect(Collectors.toMap(ViInsPlcyInfTp::getPolicyType, Function.identity())).get(PolicyTypeEnum.BI.getValue());
        ViInsPlcyInfTp pubMap = Optional.ofNullable(bi).orElse(ci);

        //是否第一次报价  取报价单的保费计算识别码
        boolean isFirst = StringUtils.isBlank(pubMap.getInsPremiumNumber());
        calculateReq.setSourceCode(CapLiResEnum.TS.getSourceCode());
        //报价单号 如果为空，就是第一次报价
        calculateReq.setQuoteNo(pubMap.getInsPremiumNumber());
        //报价类型
        calculateReq.setQuoteTimes(isFirst ? "1" : "2");
        calculateReq.setPartnerCode(getSupportIns().getCompanyCode());
        calculateReq.setCityCode(apiPremiumCalculateDTO.getCarInfo().getCityNumber());
        //报价日期
        calculateReq.setQuoteDate(DateTimeUtil.getDateTime());
        //整单折扣  报价类型为1时，默认为：1，报价类型为2时，根据1次报价返回的最大折扣和最小折扣的浮动范围来调整值。
        calculateReq.setOrderDiscount(new BigDecimal("1.0"));
        calculateReq.setIsFeeChangeFlag(BooleanEnum.YES.getValue());
        //获取险种
        Map<String, ViInsPlcyInfTp> viInsPlyInfTpMap = apiPremiumCalculateDTO.getViInsPlcyInfTpMap();
        // 交强险记录
        ViInsPlcyInfTp viInsPlyInfTpCi = viInsPlyInfTpMap.get(PolicyTypeEnum.CI.getValue());
        // 商业险记录
        ViInsPlcyInfTp viInsPlyInfTpBi = viInsPlyInfTpMap.get(PolicyTypeEnum.BI.getValue());
        if (Objects.nonNull(viInsPlyInfTpBi)) {
            Map<String, String> resolveDate = CapLiTimeHandleUtils.resolveInsDate(viInsPlyInfTpBi.getInsBegin());
            //商业险起保日期
            calculateReq.setStartDateBI(resolveDate.get("date"));
            //商业险起保时间
            calculateReq.setStartHourBI(resolveDate.get("hour_minute"));
            //商业实时起保标志
            calculateReq.setInstantInureFlagBI(EffectiveImmediatelyEnum.getCode(viInsPlyInfTpBi.getEffectiveImmediately()));
        }
        if (Objects.nonNull(viInsPlyInfTpCi)) {
            Map<String, String> resolveDate = CapLiTimeHandleUtils.resolveInsDate(viInsPlyInfTpCi.getInsBegin());
            //交强险起保日期
            calculateReq.setStartDateCI(resolveDate.get("date"));
            //交强险起保时间
            calculateReq.setStartHourCI(resolveDate.get("hour_minute"));
            //交强实时起保标志
            calculateReq.setInstantInureFlagCI(EffectiveImmediatelyEnum.getCode(viInsPlyInfTpCi.getEffectiveImmediately()));
        }
    }

    void buildCarInfo(APIPremiumCalculateDTO apiPremiumCalculateDTO, CapLiApiCalculateReq calculateReq, CapLiCarQueryRes res) {
        CarInfo carInfo = apiPremiumCalculateDTO.getCarInfo();
        ///CapLiCarQueryModelRes carQueryModelRes = JSONUtil.toBean(carInfo.getExtendField(), CapLiCarQueryModelRes.class);//res.getModelList().get(0);
        CapLiCarQueryModelRes carQueryModelRes = res.getModelList().get(0);
        CarInfoReq carInfoReq = new CarInfoReq();
        //车牌号  如果是 * 号 要改成""
        carInfoReq.setLicenseNo(SYMBOL.equals(carInfo.getPlateNumber()) ? "" : carInfo.getPlateNumber());
        //发动机号
        carInfoReq.setEngineNo(carInfo.getEngineNumber());
        //车架号
        carInfoReq.setFrameNo(carInfo.getFrameNumber());
        //初登日期
        carInfoReq.setEnrollDate(carInfo.getRegDate());
        //车型名称
        carInfoReq.setModelName(carQueryModelRes.getModelName());
        //车型代码
        carInfoReq.setModelCode(carQueryModelRes.getModelCode());
        //新车购置价
        carInfoReq.setPurchasePrice(carQueryModelRes.getPurchasePrice());
        //新车实际价值
        carInfoReq.setActualValue(carQueryModelRes.getActualValue());
        //核定载客人数
        carInfoReq.setSeatCount(carInfo.getSeatCount() == 0 ? carQueryModelRes.getSeatCount() : carInfo.getSeatCount());
        //燃料种类
        carInfoReq.setFuelType(CapLiFuelTypeEnum.getCode(carInfo.getFuelType()));
        //排量
        carInfoReq.setExhaustScale(carQueryModelRes.getExhaustScale());
        //功率
        carInfoReq.setPowerScale(carQueryModelRes.getPowerScale());
        //是否过户
        carInfoReq.setTransferVehicleFlag(CapLiTransferMarkEnum.getValue(carInfo.getTransferMark()));
        //过户日期
        carInfoReq.setTransferDate(BooleanEnum.YES.getValue().equals(carInfo.getTransferMark()) ? carInfo.getTransferDate() : null);
        //市场公允价值
        carInfoReq.setCarFairValue(carQueryModelRes.getActualValue());
        //开具车辆来历凭证所载日期
        carInfoReq.setCertificateDateBJ("");
        //车辆来历凭证种类
        carInfoReq.setCertificateType("");
        //车辆来历凭证编号
        carInfoReq.setCertificateNo("");
        //行业车型编码
        carInfoReq.setIndustryModelCode(carQueryModelRes.getIndustryModelCode());
        //交管车辆查询码
        carInfoReq.setCheckNo(null);
        //发证日期
        carInfoReq.setCertificateDate(carInfo.getCertDate());
        //打印车型
        carInfoReq.setIsPrintModelAlias(carInfo.getModelName());
        //车辆使用性质
        carInfoReq.setUseNatureCode(CapLiUsingNatureEnum.getCode(carInfo.getUsingNature()));
        //使用性质
        carInfoReq.setUseNature1(CapLiOperationNatureEnum.getValueByOwnEnumValue(carInfo.getOperationNature()));
        //车辆用途
        carInfoReq.setUseNature2(UseNatureEnum.getCode(carInfo.getOwnershipNature()));
        //车辆大类
        carInfoReq.setCarType1("A");
        //纯电续航里程（公里）
        carInfoReq.setPureRange(carQueryModelRes.getPureRange());
        //能源类型
        carInfoReq.setModelEnergyType(carQueryModelRes.getModelEnergyType());
        calculateReq.setCarInfo(carInfoReq);
        //车辆实际价格
        carInfo.setActualPrice(carQueryModelRes.getActualValue());

        log.debug("保费计算时传的车型编码:{}", carInfoReq.getIndustryModelCode());
    }

    void buildKindListInfo(APIPremiumCalculateDTO apiPremiumCalculateDTO, CapLiApiCalculateReq calculateReq, PersonInfo owner, PersonInfo insured, CapLiCarQueryModelRes carInfoVO) {
        List<KindListReq> kindList = new ArrayList<>();

        //创建记录基础数据
        Map<String, ViInsPlcyInfTp> plyMap = apiPremiumCalculateDTO.getViInsPlcyInfTpMap();
        //交强险记录
        ViInsPlcyInfTp ci = plyMap.get(PolicyTypeEnum.CI.getValue());
        //商业险记录
        ViInsPlcyInfTp bi = plyMap.get(PolicyTypeEnum.BI.getValue());

        List<ViInsPlcyInfTp> tpList = new ArrayList<>();
        if (Objects.nonNull(ci)) {
            //构建交强险
            buildComply(apiPremiumCalculateDTO, calculateReq, kindList);

            //构建车船税
            buildCarTaxInfo(apiPremiumCalculateDTO, calculateReq, owner, insured, carInfoVO);
            tpList.add(ci);

        }
        if (Objects.nonNull(bi)) {
            //构建商业险
            buildBusiness(apiPremiumCalculateDTO, calculateReq, carInfoVO, kindList);
            tpList.add(bi);
        }
        apiPremiumCalculateDTO.setViInsPlcyInfTps(tpList);
        calculateReq.setKindList(kindList);
    }

    /**
     * 合作伙伴配置化
     */
    void buildPartner(CapLiApiCalculateReq calculateReq, ComsChanDetailDTO comsChanDetailDTO, boolean flag) {
        Map<CustomSettingFieldEnum, String> dtoMap = comsChanDetailDTO.getMap();
        calculateReq.setSalesName(dtoMap.get(CustomSettingFieldEnum.SETTING_CODE_006));
        calculateReq.setComCode(dtoMap.get(CustomSettingFieldEnum.SETTING_CODE_021));
        if (flag) {
            calculateReq.setPartnerName2(dtoMap.get(CustomSettingFieldEnum.SETTING_CODE_030));
            calculateReq.setPartnerId2(dtoMap.get(CustomSettingFieldEnum.SETTING_CODE_029));
        } else {
            calculateReq.setPartnerName2(dtoMap.get(CustomSettingFieldEnum.SETTING_CODE_032));
            calculateReq.setPartnerId2(dtoMap.get(CustomSettingFieldEnum.SETTING_CODE_031));
        }
        calculateReq.setPartnerClass(dtoMap.get(CustomSettingFieldEnum.SETTING_CODE_041));
        calculateReq.setPartnerFlag("1");
    }

    /**
     * 长安渠道特殊逻辑，当渠道配置了 合作伙伴编码1 时，需要判断 三责、责任（乘客）、责任（司机）的保额是否满足一定的条件，
     * 三责>=200W 座位>=2W 腾顺2022
     */
    private boolean needAnotherPartner(APIPremiumCalculateDTO apiPremiumCalculateDTO) {
        boolean liability = false;
        boolean passenger = false;
        boolean driver = false;
        for (ItemKind value : apiPremiumCalculateDTO.getBuyInsuranceInfMap().values()) {
            if (value.getKindCode().equals(NewKindCodeEnum.BI_TP_LIABILITY.getValue())) {
                if (value.getAmount().compareTo(new BigDecimal("2000000")) >= 0) {
                    liability = true;
                }
            } else if (NewKindCodeEnum.BI_PERSONNEL_PASSENGER.getValue().equals(value.getKindCode())) {
                if (value.getUnitAmount().compareTo(new BigDecimal("20000")) >= 0) {
                    passenger = true;
                }
            } else if (NewKindCodeEnum.BI_PERSONNEL_DRIVER.getValue().equals(value.getKindCode())) {
                if (value.getUnitAmount().compareTo(new BigDecimal("20000")) >= 0) {
                    driver = true;
                }
            }
        }
        return liability && passenger && driver;
    }

    void buildInsuredInfo(APIPremiumCalculateDTO apiPremiumCalculateDTO, CapLiApiCalculateReq calculateReq) {
        List<InsuredInfoListReq> infoListReq = new ArrayList<>();

        for (PersonInfo personInfo : apiPremiumCalculateDTO.getPersonInfoMap().values()) {
            if (ViPersonTypeEnum.OWNER.getValue().equals(personInfo.getPersonType()) ||
                    ViPersonTypeEnum.INSURED.getValue().equals(personInfo.getPersonType()) ||
                    ViPersonTypeEnum.HOLDER.getValue().equals(personInfo.getPersonType())) {
                InsuredInfoListReq infoDTO = new InsuredInfoListReq();
                infoListReq.add(infoDTO);
                //关系人标志
                infoDTO.setInsuredFlag(PersonTypeEnum.getCode(personInfo.getPersonType()));
                //关系人名称
                infoDTO.setInsuredName(personInfo.getPersonName());
                //关系人性质
                infoDTO.setInsuredNature(InsuredNatureEnum.getCode(personInfo.getNature().getValue()));
                //关系人类型
                infoDTO.setInsuredType(CapLiNatureEnum.getCode(personInfo.getNature().getValue()));
                //证件类型
                infoDTO.setIdentifyType(CapLiIdentifyTypeEnum.getCode(personInfo.getIdentifyType()));
                //证件号码
                infoDTO.setIdentifyNumber(personInfo.getIdentifyNumber());
                //移动电话
                infoDTO.setMobile(personInfo.getMobilePhone());
                //性别
                infoDTO.setSex(PersonSexEnum.getCode(personInfo.getPersonSex()));
                //驾照
                infoDTO.setDrivingLicenseNo(null);
                //年龄
                infoDTO.setDriverAge(Integer.parseInt(personInfo.getPersonAge()));
                //初次领证日期
                infoDTO.setAcceptLicenseDate(null);
                //主驾驶员标志
                infoDTO.setMainDriverFlag(null);
                //驾驶证准驾车
                infoDTO.setDriverType(null);
                //出生年月
                infoDTO.setBirthday(personInfo.getBirthDate());
                //邮箱
                infoDTO.setEmail(personInfo.getEmail());
                //地址
                infoDTO.setAddress(personInfo.getAddressDetail());
            }
        }
        calculateReq.setInsuredInfoList(infoListReq);
    }

    void buildComply(APIPremiumCalculateDTO apiPremiumCalculateDTO, CapLiApiCalculateReq calculateReq, List<KindListReq> kindList) {
        KindListReq kindListReq = new KindListReq();
        ViInsuranceInf tpViInsuranceInf = apiPremiumCalculateDTO.getTpViInsuranceInfMapSelf().get(NewKindCodeEnum.CI_JQ.getValue());
        kindListReq.setKindCode(tpViInsuranceInf.getKindCode());
        kindListReq.setAmount(new BigDecimal(CIBZAmountDefaultEnum.NEW_DEFAULT));
        kindListReq.setUnitAmount(new BigDecimal("0.0"));
        kindListReq.setQuantity(0);
        kindListReq.setSpecailFlag(AddlMarkEnum.NO.getCode());
        kindList.add(kindListReq);
        calculateReq.setKindList(kindList);
    }

    void buildCarTaxInfo(APIPremiumCalculateDTO apiPremiumCalculateDTO, CapLiApiCalculateReq calculateReq, PersonInfo holder, PersonInfo insured, CapLiCarQueryModelRes carInfoVO) {
        CarTaxInfoReq carTaxInfoReq = new CarTaxInfoReq();
        //缴税标志
        carTaxInfoReq.setTaxFlag(CapLiTaxMarkEnum.getCode(apiPremiumCalculateDTO.getCarInfo().getTaxCutsMark()));
        //纳税人名称
        carTaxInfoReq.setTaxPayerName(holder.getPersonName());
        //纳税人证件类型
        carTaxInfoReq.setTaxPayerCertiType(CapLiIdentifyTypeEnum.getCode(holder.getIdentifyType()));
        //纳税人证件号码
        carTaxInfoReq.setTaxPayerCertiCode(holder.getIdentifyNumber());
        //纳税人识别号
        carTaxInfoReq.setTaxPayerIdentificationCode(holder.getIdentifyNumber());
        //被保险人名称
        carTaxInfoReq.setInsuredName(insured.getPersonName());
        if (!CapLiTaxCutsMarkEnum.NORMAL.getValue().equals(carInfoVO.getHfName())) {
            //减免税原因代码 9-能源减免
            carTaxInfoReq.setDeDuctiondueCode("9");
            //减免税/完税凭证号
            carTaxInfoReq.setDeDuctionDocumentNumber("12061001");
        }
        calculateReq.setCarTaxInfo(carTaxInfoReq);
    }

    void buildBusiness(APIPremiumCalculateDTO apiPremiumCalculateDTO, CapLiApiCalculateReq calculateReq, CapLiCarQueryModelRes carQueryModelRes, List<KindListReq> kindList) {
        List<String> serviceTerms = Arrays.asList(NewKindCodeEnum.BI_ROADSIDE.getValue(), NewKindCodeEnum.BI_SAFETY_MONITORING.getValue(), NewKindCodeEnum.BI_SUBSTITUTE.getValue(), NewKindCodeEnum.BI_INSPECT.getValue());
        //充电桩信息
        List<ChargingPileDTO> chargingPiles = apiPremiumCalculateDTO.getChargingPiles();
        List<CarChargingPostsReq> carCharging;
        CarChargingPostsReq chargingDTO;
        int serialNo = 0;
        if (CollectionUtil.isNotEmpty(chargingPiles)) {
            carCharging = new ArrayList<>();
            for (ChargingPileDTO chargingPile : chargingPiles) {
                serialNo++;
                chargingDTO = new CarChargingPostsReq();
                BeanUtil.copyProperties(chargingPile, chargingDTO);
                chargingDTO.setSerialNo(serialNo + "");
                chargingDTO.setChargingPostType(chargingPile.getChargingModel());
                chargingDTO.setChargingPostCode(chargingPile.getChargingCode());
                chargingDTO.setChargingPostAddress(chargingPile.getChargingAddrDetail());
                chargingDTO.setChargingPostAddressType(AddressTypeEnum.getTpValue(chargingPile.getChargingInstallAddrType()));
                chargingDTO.setChargingPostKind(CapLiChargingTypeEnum.getCode(chargingPile.getChargingType().getValue()));
                chargingDTO.setChargingPostYearLimit(CapLiChargingUseYearsEnum.getCode(chargingPile.getChargingUseYears().getValue()));
                chargingDTO.setIsLiability("1");
                chargingDTO.setLiabilityPremium(null);
                chargingDTO.setIsLoss("1");
                chargingDTO.setLossPremium(null);
                carCharging.add(chargingDTO);
            }
            calculateReq.setCarChargingPosts(carCharging);
        }

        //新能源险种
        Map<String, ViInsuranceInf> viInsuranceInfMapEnergy = apiPremiumCalculateDTO.getTpViInsuranceInfMapTP();
        Map<String, ViInsuranceInf> tpViInsuranceInfMapSelf = apiPremiumCalculateDTO.getTpViInsuranceInfMapSelf();
        //0560/0564;投保商业险不传默认0560
        calculateReq.setBiRiskCode(apiPremiumCalculateDTO.getNewEnergy() ? "0564" : "0560");
        //构建险种明细
        buildInsurance(apiPremiumCalculateDTO, calculateReq, kindList, serviceTerms, carQueryModelRes, viInsuranceInfMapEnergy, tpViInsuranceInfMapSelf);
        ExtendInfoReq extendInfo;
        List<ExtendInfoReq> list = new ArrayList<>();
        if (Objects.nonNull(apiPremiumCalculateDTO.getBaseDTO().getExpectDiscount())) {
            extendInfo = new ExtendInfoReq();
            extendInfo.setKey("pricingAdjustExpDiscount");
            extendInfo.setValue(apiPremiumCalculateDTO.getBaseDTO().getExpectDiscount().toString());
            list.add(extendInfo);
        }
        if (StringUtils.isNotBlank(carQueryModelRes.getAbsFlag())) {
            extendInfo = new ExtendInfoReq();
            extendInfo.setKey("absFlag");
            extendInfo.setValue(carQueryModelRes.getAbsFlag());
            list.add(extendInfo);
        }
        calculateReq.setExtendInfoDtos(list);
    }

    void buildInsurance(APIPremiumCalculateDTO apiPremiumCalculateDTO, CapLiApiCalculateReq calculateReq, List<KindListReq> kindList, List<String> serviceTerms, CapLiCarQueryModelRes carQueryModelRes, Map<String, ViInsuranceInf> viInsuranceInfMapEnergy, Map<String, ViInsuranceInf> tpViInsuranceInfMapSelf) {
        for (ItemKind itemKind : apiPremiumCalculateDTO.getBuyInsuranceInfMap().values()) {
            //险种代码
            String kindCode = itemKind.getKindCode();
            ViInsuranceInf tpViInsuranceInf;
            if (InsConstants.YNFlag.NO.equals(carQueryModelRes.getIsOnlyNewEnerg())) {
                tpViInsuranceInf = viInsuranceInfMapEnergy.get(kindCode);
            } else {
                tpViInsuranceInf = tpViInsuranceInfMapSelf.get(kindCode);
            }

            if (Objects.isNull(tpViInsuranceInf)) {
                throw new TunsBusinessException(InsErrorCode.VI_ISSUE_PREMIUMCACULATE.getMsg(), ViIssueConstants.IssueErrorMessage.UNSUPPORTED_KIND + itemKind.getKindName());
            }
            if (NewKindCodeEnum.CI_BT.getValue().equals(kindCode) || AddlMarkEnum.YES.getCode().equals(itemKind.getAddlMark()) || NewKindCodeEnum.CI_JQ.getValue().equals(kindCode)) {
                continue;//车船税/不计免赔/交强险/无需添加
            }

            //险种保额
            BigDecimal amount = itemKind.getAmount();
            KindListReq tpKind = new KindListReq();

            //险别代码
            tpKind.setKindCode(tpViInsuranceInf.getKindCode());
            //每人保额
            tpKind.setUnitAmount(itemKind.getUnitAmount());
            //是否投保不计免赔
            tpKind.setSpecailFlag(Optional.ofNullable(itemKind.getBuyAddl()).orElse("0"));
            //比率
            tpKind.setDeductibleRate(null);
            //免赔额度
            tpKind.setDeductibleAmount(new BigDecimal(0));

            //车损
            if (NewKindCodeEnum.BI_VEHICLE_LOSS.getValue().equals(kindCode) || NewKindCodeEnum.NEW_ENERGY_EXTERNAL_POWER_FAULT_COVERAGE.getValue().equals(kindCode)) {
                amount = carQueryModelRes.getActualValue();
            }
            //保额
            tpKind.setAmount(amount);
            //投保人数
            tpKind.setQuantity(Objects.nonNull(itemKind.getQuantity()) ? itemKind.getQuantity() : 0);
            //附加类型
            tpKind.setModeCode(null);
            //专修类型
            tpKind.setSpecifiedType(null);
            //专修协议比例
            tpKind.setSpecifiedRate(Objects.isNull(itemKind.getRate()) ? BigDecimal.ZERO : BigDecimal.valueOf(itemKind.getRate()));
            if (serviceTerms.contains(kindCode)) {
                tpKind.setServiceTimes(itemKind.getQuantity());
            }
            if (NewKindCodeEnum.NEW_ENERGY_PERSONAL_CHARGER_LIABILITY_COVERAGE.getValue().equals(kindCode)) {
                tpKind.setQuantity(calculateReq.getCarChargingPosts().size());
            }
            kindList.add(tpKind);
        }
    }

    CapLiCarQueryRes actualValue(APIPremiumCalculateDTO apiPremiumCalculateDTO, CarInfo carInfo) {
        log.debug("长安==>>二次车型查询");
        CapLiApiCarInfoReqDTO req = new CapLiApiCarInfoReqDTO();
        //后构建第二次车型查询的数据
        req.setEngineNo(carInfo.getEngineNumber());
        req.setFrameNo(carInfo.getFrameNumber());
        req.setEnrollDate(carInfo.getRegDate());
        req.setModelName(carInfo.getModelName());
        req.setCityCode(carInfo.getCityNumber());
        req.setSourceCode(CapLiResEnum.TS.getSourceCode());
        req.setLicenseNo(SYMBOL.equals(carInfo.getPlateNumber()) ? "" : carInfo.getPlateNumber());
        String insBegin = Optional.ofNullable(ArrayUtil.firstNonNull(apiPremiumCalculateDTO.getBaseDTO().getInsBeginCi(), apiPremiumCalculateDTO.getBaseDTO().getInsBeginBi()))
                .orElse(null);
        //单交强/交商同保
        Map<String, String> resolveDate = CapLiTimeHandleUtils.resolveInsDate(insBegin);
        req.setStartDate(resolveDate.get("date"));
        req.setStartHour(resolveDate.get("hour") + ":" + resolveDate.get("minute"));
        req.setSeatCount(carInfo.getSeatCount());
        req.setIndustryModelCode(carInfo.getPrfsModelCode());
        req.setReplacement(carInfo.getPurchasePrice().doubleValue());
        if (apiPremiumCalculateDTO.getNewEnergy()) {
            req.setBiRiskCode("0564");
        }
        req.setModelEnergyType(CapLiFuelTypeEnum.getCode(carInfo.getFuelType()));
        req.setModelCode(carInfo.getModelCode());
        req.setPurchasePrice(carInfo.getPurchasePrice());
        req.setActualValue(carInfo.getActualPrice());
        req.setFuelType(CapLiFuelTypeEnum.getCode(carInfo.getFuelType()));
        req.setExhaustScale(new BigDecimal(carInfo.getExhaustScale()));
        req.setPowerScale(new BigDecimal(carInfo.getPower()));
        req.setCertificateDate(carInfo.getCertDate());
        req.setTransferVehicleFlag("0");
        req.setCheckNo(null);
        req.setLicenseType("02");
        req.setUseNature1(CapLiOperationNatureEnum.getValueByOwnEnumValue(carInfo.getOperationNature()));
        req.setUseNature2(UseNatureEnum.getCode(carInfo.getOwnershipNature()));
        req.setCarkindcode(CapLiTimeHandleUtils.tpCarKindCode(carInfo.getSeatCount()));
        req.setCarType1("A");
        req.setCarType2(CapLiTimeHandleUtils.tpCarType2(carInfo.getSeatCount()));
        req.setCarType3(CapLiTimeHandleUtils.tpCarType3(req.getCarType2()));
        req.setVehicleType(CapLiTimeHandleUtils.tpVehicleType(req.getCarType3()));

        log.debug("二次车型查询前的车型编码:=====>{}", req.getIndustryModelCode());

        CapLiCarQueryRes capLiCarQueryRes = carInfoService.performCarInfoQuery(req);

        log.debug("二次车型查询后出来的车型编码:=====>{}", capLiCarQueryRes.getModelList().get(0).getIndustryModelCode());

        log.debug("长安<<==二次车型查询");
        return capLiCarQueryRes;
    }

    void fillBusinessData(ViInsPlcyInfTp config, BigDecimal score, CapLiApiCalculateRes tpResultInfo, int lastClaimCountBi, ViInsPlcyInfTp viInsPlcyInfTp) {
        if (StringUtils.isNotBlank(tpResultInfo.getEndDateBI())) {
            //保险止期
            viInsPlcyInfTp.setInsEnd(tpResultInfo.getEndDateBI() + " " + tpResultInfo.getEndHourBI() + ":00");
        }
        int lastClaimsCountBiTemp = CapLiTimeHandleUtils.lastclaimscountbi(ArithUtil.mul(tpResultInfo.getSumDiscountBI().doubleValue(), 10000).intValue());
        if (lastClaimsCountBiTemp == 0 || lastClaimsCountBiTemp == NUM) {
            lastClaimsCountBiTemp = lastClaimCountBi;
        }
        viInsPlcyInfTp.setTrafficAccidentRecord(tpResultInfo.getClaimInfoList().toString());
        viInsPlcyInfTp.setLastYearInsBegin(tpResultInfo.getLastlyPolicy().getLastPolicyEffectiveDate());
        viInsPlcyInfTp.setLastYearInsEnd(tpResultInfo.getLastlyPolicy().getLastPolicyExpireDate());
        //上年理赔次数
        viInsPlcyInfTp.setLastClaimCount(lastClaimsCountBiTemp);
        //业务类型
        viInsPlcyInfTp.setInsuredType(CapLiInsuredTypeEnum.getCode(tpResultInfo.getInsuranceFlagBI()));
        //折扣 0~2
        viInsPlcyInfTp.setDiscount(tpResultInfo.getSumDiscountBI());
        //保费
        viInsPlcyInfTp.setInsuredPremium(tpResultInfo.getSumPremiumBI());
        viInsPlcyInfTp.setSelfScore(score);
        viInsPlcyInfTp.setClaimAdjustLevel(tpResultInfo.getClaimAdjustLevel());
        viInsPlcyInfTp.setClaimTimes(tpResultInfo.getClaimTimes());
        viInsPlcyInfTp.setInsureYears(tpResultInfo.getInsureYears());
        viInsPlcyInfTp.setTrafficViolationCoefficient(new BigDecimal(tpResultInfo.getProfitTrafficRate()));
        viInsPlcyInfTp.setNoClaimDiscount(new BigDecimal(tpResultInfo.getProfitClaimRate()));
        viInsPlcyInfTp.setIndependentPriceRate(config.getIndependentPriceRate() == null ? (StringUtils.isNotEmpty(tpResultInfo.getProfitN05()) ? BigDecimal.valueOf(Double.parseDouble(tpResultInfo.getProfitN05())) : null) : config.getIndependentPriceRate());
        //标准保费
        viInsPlcyInfTp.setStandardPremium(tpResultInfo.getSumDiscountBI().intValue() > 0 ?
                ArithUtil.div(tpResultInfo.getSumPremiumBI(), tpResultInfo.getSumDiscountBI(), 2) : new BigDecimal("0.0"));
        //商业险投保查询码
        viInsPlcyInfTp.setDemandNo(tpResultInfo.getQuerySequenceNo());
    }

    void fillComplyData(ViInsPlcyInfTp viInsPlyInfTpCi, List<ViInsKindDetialTp> viInsKindDealTps, Map<String, ViInsuranceInf> tpViInsuranceInfMapSelf, CapLiApiCalculateRes tpResultInfo, int lastClaimCountCi, ViInsPlcyInfTp viInsPlcyInfTp) {
        //附加保费
        BigDecimal attachPremium = Objects.nonNull(tpResultInfo.getCarShipTax()) ? tpResultInfo.getCarShipTax() : new BigDecimal("0.0");
        //交强险投保查询码
        viInsPlcyInfTp.setDemandNo(tpResultInfo.getQuerySequenceBZNo());
        //附加保费
        viInsPlcyInfTp.setAttachPremium(attachPremium);
        if (StringUtils.isNotBlank(tpResultInfo.getEndDateCI())) {
            //保险起期
            viInsPlcyInfTp.setInsEnd(tpResultInfo.getEndDateCI() + " " + tpResultInfo.getEndHourCI() + ":00");
        }
        int lastClaimsCountCiTemp = CapLiTimeHandleUtils.lastClaimsCountCI(ArithUtil.mul(tpResultInfo.getSumDiscountCI().doubleValue(), 100).intValue());
        if (lastClaimsCountCiTemp == 0 || lastClaimsCountCiTemp == NUM) {
            lastClaimsCountCiTemp = lastClaimCountCi;
        }
        //上年理赔次数
        viInsPlcyInfTp.setLastClaimCount(lastClaimsCountCiTemp);

        //记录车船税
        ViInsuranceInf tpViInsuranceInf = tpViInsuranceInfMapSelf.get(NewKindCodeEnum.CI_BT.getValue());
        ViInsKindDetialTp viInsKindDetialTp = new ViInsKindDetialTp();
        //保单记录ID
        viInsKindDetialTp.setPolicyId(viInsPlyInfTpCi.getPolicyId());
        //保单批次ID
        viInsKindDetialTp.setPolicyBatchId(viInsPlyInfTpCi.getPolicyBatchId());
        //序号
        viInsKindDetialTp.setOrderNo(2);
        //险别代码
        viInsKindDetialTp.setKindCode(tpViInsuranceInf.getInsuranceCode());
        //险别名称
        viInsKindDetialTp.setKindName(tpViInsuranceInf.getKindName());
        //数量
        viInsKindDetialTp.setQuantity(0);
        //单位保额
        viInsKindDetialTp.setUnitAmount(new BigDecimal("0.0"));
        //总保额
        viInsKindDetialTp.setAmount(new BigDecimal("0.0"));
        //保费
        viInsKindDetialTp.setPremium(viInsPlyInfTpCi.getAttachPremium());
        //折扣
        viInsKindDetialTp.setDiscount(new BigDecimal("0.0"));
        //费率
        viInsKindDetialTp.setRate(new BigDecimal("0.0"));
        //是否不计面免赔
        viInsKindDetialTp.setAddlMark(AddlMarkEnum.NO.getCode());
        //是否购买不计免赔险
        viInsKindDetialTp.setBuyAddl(BuyAddlMarkEnum.NO.getCode());
        //是否商业险
        viInsKindDetialTp.setBusinessMark(BusinessMarkEnum.NO.getCode());
        //创建人
        viInsKindDetialTp.setCreUser(viInsPlyInfTpCi.getCreUser());
        //更新人
        viInsKindDetialTp.setMdfUser(viInsPlyInfTpCi.getMdfUser());
        //删除标志
        viInsKindDetialTp.setDelFlag(InsConstants.DelFlag.NORMAL);
        //业务类型
        viInsPlcyInfTp.setInsuredType(CapLiInsuredTypeEnum.getCode(tpResultInfo.getInsuranceFlagCI()));
        //车船税缴税类型
        viInsPlcyInfTp.setCarTaxType(CapLiTaxShipEnum.getCode(tpResultInfo.getTaxFlag()));
        //折扣 0~2
        viInsPlcyInfTp.setDiscount(tpResultInfo.getSumDiscountCI());
        //保费
        viInsPlcyInfTp.setInsuredPremium(tpResultInfo.getSumPremiumCI());
        //标准保费
        viInsPlcyInfTp.setStandardPremium(tpResultInfo.getSumDiscountCI().intValue() > 0 ?
                ArithUtil.div(tpResultInfo.getSumPremiumCI(), tpResultInfo.getSumDiscountCI(), 2) : new BigDecimal("0.0"));
        viInsKindDealTps.add(viInsKindDetialTp);
    }

    void fillKindDetailInfo(ViInsPlcyInfTp ci, ViInsPlcyInfTp bi, List<ViInsKindDetialTp> viInsKindDealTps, Map<String, ItemKind> itemKindMap, CapLiApiCalculateRes tpResultInfo, Map<String, ViInsuranceInf> tpViInsuranceInfMapTP) {
        ViInsPlcyInfTp viInsPlyInfTpPub = Optional.ofNullable(ci).orElse(bi);
        //险种明细
        ViInsKindDetialTp kindDealTp = null;
        ViInsKindDetialTp kindDealTp1 = null;
        for (KindListRes tpKind : tpResultInfo.getKindList()) {
            ViInsuranceInf tpViInsuranceInf = tpViInsuranceInfMapTP.get(tpKind.getKindCode());
            ItemKind itemKind = itemKindMap.get(tpViInsuranceInf.getInsuranceCode());

            ViInsKindDetialTp viInsKindDetialTp = new ViInsKindDetialTp();

            if (tpViInsuranceInf.getInsuranceCode().equals(ViIssueConstants.KindCode.BI_VI_DAMAGE)) {
                kindDealTp = viInsKindDetialTp;
            } else if (tpViInsuranceInf.getInsuranceCode().equals(ViIssueConstants.KindCode.BI_DESIGNATED_REPAIR_SHOP)) {
                kindDealTp1 = viInsKindDetialTp;
            }

            viInsKindDealTps.add(viInsKindDetialTp);
            if (BusinessMarkEnum.YES.getCode().equals(tpViInsuranceInf.getComFlag())) {
                //保单记录ID
                viInsKindDetialTp.setPolicyId(bi.getPolicyId());
                //保单批次ID
                viInsKindDetialTp.setPolicyBatchId(bi.getPolicyBatchId());
                //序号
                viInsKindDetialTp.setOrderNo(itemKind.getOrderNo());
                //是否不计面免赔
                viInsKindDetialTp.setAddlMark(itemKind.getAddlMark());
                //是否购买不计免赔险
                viInsKindDetialTp.setBuyAddl(itemKind.getBuyAddl());
                //附加类型
                viInsKindDetialTp.setValueType(itemKind.getValueType());
                //是否商业险
                viInsKindDetialTp.setBusinessMark(BusinessMarkEnum.YES.getCode());
            } else {
                //保单记录ID
                viInsKindDetialTp.setPolicyId(ci.getPolicyId());
                //保单批次ID
                viInsKindDetialTp.setPolicyBatchId(ci.getPolicyBatchId());
                //序号
                viInsKindDetialTp.setOrderNo(1);
                //是否不计面免赔
                viInsKindDetialTp.setAddlMark(AddlMarkEnum.NO.getCode());
                //是否购买不计免赔险
                viInsKindDetialTp.setBuyAddl(BuyAddlMarkEnum.NO.getCode());
                //附加类型
                viInsKindDetialTp.setValueType(null);
                //是否商业险
                viInsKindDetialTp.setBusinessMark(BusinessMarkEnum.NO.getCode());
            }
            //险别代码
            viInsKindDetialTp.setKindCode(tpViInsuranceInf.getInsuranceCode());
            //险别名称
            viInsKindDetialTp.setKindName(tpViInsuranceInf.getKindName());
            //司机座位险
            if (tpViInsuranceInf.getInsuranceCode().equals(ViIssueConstants.KindCode.BI_DRIVER_SEAT_LIABILITY)) {
                //数量
                viInsKindDetialTp.setQuantity(itemKind.getQuantity());
                //单位保额
                viInsKindDetialTp.setUnitAmount(itemKind.getUnitAmount());
            } else {
                //数量
                viInsKindDetialTp.setQuantity(tpKind.getQuantity());
                //单位保额
                viInsKindDetialTp.setUnitAmount(tpKind.getUnitAmount());
            }
            //总保额
            viInsKindDetialTp.setAmount(tpKind.getAmount());
            //保费
            viInsKindDetialTp.setPremium(tpKind.getPremium());
            //折扣
            viInsKindDetialTp.setDiscount(Objects.nonNull(tpKind.getBenchmarkPremium()) && tpKind.getBenchmarkPremium().doubleValue() > 0 ?
                    ArithUtil.div(tpKind.getPremium(), tpKind.getBenchmarkPremium(), 4) : new BigDecimal("0.0"));
            //费率
            viInsKindDetialTp.setRate(tpKind.getDeductibleRate());
            //扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）
            viInsKindDetialTp.setExtend(null);
            //创建人
            viInsKindDetialTp.setCreUser(viInsPlyInfTpPub.getCreUser());
            //更新人
            viInsKindDetialTp.setMdfUser(viInsPlyInfTpPub.getMdfUser());
            //删除标志
            viInsKindDetialTp.setDelFlag(InsConstants.DelFlag.NORMAL);
        }

        if (Objects.nonNull(kindDealTp1) && Objects.nonNull(kindDealTp) && kindDealTp.getPremium().intValue() > 0) {
            //费率
            kindDealTp1.setRate(ArithUtil.div(kindDealTp1.getPremium(), kindDealTp.getPremium(), 4));
        }
    }

    void getSpecialAgreement(List<ViInsPlcyInfTp> viInsPlyInfTps, CapLiApiCalculateRes tpResponse) {
        //查询特别约定
        CapLiApiBaseResDTO<SpecialAgreementRes> tpSpecialAgreementResponse = capLiRpcService.getSpecialAgreement(tpResponse.getQuoteNo());
        //特别约定
        for (ViInsPlcyInfTp viInsPlcyInfTp : viInsPlyInfTps) {
            List<SpecialAgreementDTO> specialAgreements = new ArrayList<>();
            for (EngageRes tpEngage : tpSpecialAgreementResponse.getResultInfo().getEngageList()) {
                //商业
                if (PolicyTypeEnum.BI.getValue().equals(viInsPlcyInfTp.getPolicyType())) {
                    if (BooleanEnum.YES.getValue().equals(tpEngage.getRiskType())) {
                        continue;
                    }
                } else {//交强
                    if ("2".equals(tpEngage.getRiskType())) {
                        continue;
                    }
                }
                SpecialAgreementDTO specialAgreement = new SpecialAgreementDTO();
                specialAgreements.add(specialAgreement);
                specialAgreement.setClauseCode(tpEngage.getClausecode());
                specialAgreement.setClauseName("");
                specialAgreement.setClauseOrder(String.valueOf(tpEngage));
                specialAgreement.setClauseDetial(tpEngage.getClauseDesc());
            }
            String specialAgreement = JSON.toJSONString(specialAgreements);
            viInsPlcyInfTp.setSpecialAgreement(specialAgreement);
        }
    }
}