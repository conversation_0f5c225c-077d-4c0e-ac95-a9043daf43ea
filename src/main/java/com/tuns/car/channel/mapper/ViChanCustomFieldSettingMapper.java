package com.tuns.car.channel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tuns.car.channel.entity.ViChanCustomFieldSetting;
import com.tuns.car.spider.constants.CustomFieldScopeEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
  * 自定义业务字段配置表
  *
  * <AUTHOR>
  * @since 2024-01-19 17:15:03
  */
@Mapper
public interface ViChanCustomFieldSettingMapper extends BaseMapper<ViChanCustomFieldSetting> {

    /**
     * 查询渠道自定义字段配置 根据作用域
     *
     * @param chanDetailId
     * @param scope
     * @return
     */
    List<ViChanCustomFieldSetting> queryChanCustomSettingByScope(@Param("chanDetailId") Integer chanDetailId,@Param("scope") CustomFieldScopeEnum scope);
}
