package com.tuns.car.core.vo.chan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 公司包装渠道类
 *
 * <AUTHOR>
 * @since 2025-3-2 11:44
 */
@Data
@Accessors(chain = true)
public class CompanyChannelPackageVO {

    @ApiModelProperty(value = "保险公司ID")
    private String companyId;

    @ApiModelProperty(value = "渠道列表")
    private List<ChannelDetailSimpleVO> channelDetailSimpleList;
}
