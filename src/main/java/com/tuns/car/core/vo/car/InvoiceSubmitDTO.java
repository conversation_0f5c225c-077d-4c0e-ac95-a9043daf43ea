package com.tuns.car.core.vo.car;

import com.baomidou.mybatisplus.annotation.TableId;
import com.tuns.car.core.constant.InvoicePayerTypeEnum;
import com.tuns.car.core.constant.InvoiceTypeEnum;
import com.tuns.car.core.constant.ViPersonTypeEnum;
import com.tuns.car.core.dto.car.IssueOrderChanInfoDTO;
import com.tuns.car.core.entity.PubPersInf;
import com.tuns.car.core.entity.ViInsPlcyCustomInvoice;
import com.tuns.car.core.entity.ViInsPlcyInf;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName InvoiceSubmitDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/5/12 15:02
 * @Version 1.0
 */

@Data
@Accessors(chain = true)
public class InvoiceSubmitDTO extends IssueOrderChanInfoDTO {

    private List<ViInsPlcyInf> viInsPlcyInfList;

    /**
     * 投保人信息
     */
    private PubPersInf persInf;

    /**
     * 保单批次id
     */
    @ApiModelProperty("保单批次id")
    private Long policyBatchId;

    /**
     * 发票类型
     */
    @ApiModelProperty("发票类型")
    private InvoiceTypeEnum invoiceType;

    /**
     * 发票类型
     */
    @ApiModelProperty("发票类型")
    private InvoicePayerTypeEnum invoicePayerType;

    /**
     * 开票对象
     */
    @ApiModelProperty("开票对象 01车主,02被保人,03投保人")
    private ViPersonTypeEnum invoiceTarget;

    /**
     * 纳税人识别号/统一社会信用代码
     */
    @ApiModelProperty("纳税人识别号/统一社会信用代码")
    private String identifyNumber;

    /**
     * 电子发票接收手机号
     */
    @ApiModelProperty("电子发票接收手机号")
    private String mobile;

    /**
     * 发票（电话）
     */
    @ApiModelProperty("发票（电话）")
    private String invoiceMobile;

    /**
     * 税务登记地址
     */
    @ApiModelProperty("税务登记地址")
    private String addressComplete;

    /**
     * 开户行名称
     */
    @ApiModelProperty("开户行名称")
    private String bankDeposit;

    /**
     * 银行账号
     */
    @ApiModelProperty("银行账号")
    private String bankAccount;
}
