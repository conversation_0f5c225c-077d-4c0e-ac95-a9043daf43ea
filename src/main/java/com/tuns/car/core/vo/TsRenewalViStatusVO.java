package com.tuns.car.core.vo;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class TsRenewalViStatusVO implements Serializable {

    private static final long serialVersionUID = 8344520342189805391L;
    /**
     * 业务主键id
     */
    private String businessId;
    /**
     * 续保状态 01=待续保 02=已续保 03=已过期,为空表示要每次查询时计算
     */
    private String status;
    /**
     * 通知次数
     */
    private Integer noticeCount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 续保倒计时
     */
    private Integer countdownDay;
    /**
     * 保单id
     */
    private Long policyId;
    /**
     * 保单号码
     */
    private String policyNumber;
    /**
     * 发送时间
     */
    private Date sendDate;
    /**
     * 拓展人员姓名
     */
    private String expandUserName;
    /**
     * 拓展人员身份证
     */
    private String expandCard;
    /**
     * 拓展人员手机号
     */
    private String expandPhone;
    /**
     * 跟进状态
     */
    private String followStatus;
    /**
     * 保单记录批次id
     */
    private Long policyBatchId;
    /**
     * 保单保费
     */
    private BigDecimal insuredPremium;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
