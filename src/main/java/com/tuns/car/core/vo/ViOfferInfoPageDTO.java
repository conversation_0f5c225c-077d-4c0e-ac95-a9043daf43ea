package com.tuns.car.core.vo;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.constant.QuoteTypeEnum;
import com.tuns.car.core.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName ViOfferInfoPageDTO
 * @Description APP车险报价单列表分页查询请求数据类
 * <AUTHOR> yi
 * @Date 2020/6/8 17:50
 * @Version 1.0
 */
@Data
public class ViOfferInfoPageDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 5786446413889152864L;


    /**
     * 报价类型 0全部,1待投保
     */
    @ApiModelProperty(value = "报价类型 0全部,1待投保", required = true)
    private QuoteTypeEnum type;

    /**
     * 模糊搜索名称
     */
    @ApiModelProperty(value = "模糊搜索名称")
    private String keyword;

    /**
     * 当前用户id
     */
    private Integer userId;



    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
