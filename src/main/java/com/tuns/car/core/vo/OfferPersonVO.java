package com.tuns.car.core.vo;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @ClassName OfferPersonVO
 * @Description 车险报价详情查询返回 关系人信息 数据类
 * <AUTHOR> yi
 * @Date 2020/6/15 16:10
 * @Version 1.0
 */
public class OfferPersonVO implements Serializable {

    private static final long serialVersionUID = 4903634933225557690L;


    /**
     * 关系人类型 01车主,02被保人,03投保人,04受益人,05收件人
     */
    @ApiModelProperty(value = "关系人类型 01车主,02被保人,03投保人,04受益人,05收件人", required = true)
    private String personType;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    private String personName;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型", required = true)
    private String identifyType;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码", required = true)
    private String identifyNumber;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", required = true)
    private String mobilePhone;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", required = true)
    private String addressComplete;

    /**
     * 电子邮箱
     */
    @ApiModelProperty(value = "电子邮箱", required = true)
    private String email;

    /**
     * 性别 1-男 2-女
     */
    @ApiModelProperty(value = "性别 1男, 2女", required = true)
    private String personSex;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期", required = true)
    private String birthDate;

    /**
     * 性质 1-个人 2-机关 3-企业
     */
    @ApiModelProperty(value = "性质 1个人, 2机关, 3企业", required = true)
    private String nature;

    /**
     * 与主要关系人所属关系
     */
    @ApiModelProperty(value = "与主要关系人所属关系 00默认, E1车主, E2被保人, E3投保人, E4受益人, E5收件人", required = true)
    private String mainPersRelation;


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "批次id")
    private Long policyBatchId;

    /**
     * 证件有效起期
     */
    @ApiModelProperty(value = "证件有效起期")
    private String identityValidityStart;
    /**
     * 证件有效止期
     */
    @ApiModelProperty(value = "证件有效止期")
    private String identityValidity;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPolicyBatchId() {
        return policyBatchId;
    }

    public void setPolicyBatchId(Long policyBatchId) {
        this.policyBatchId = policyBatchId;
    }

    public String getPersonType() {
        return personType;
    }

    public void setPersonType(String personType) {
        this.personType = personType;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getIdentifyType() {
        return identifyType;
    }

    public void setIdentifyType(String identifyType) {
        this.identifyType = identifyType;
    }

    public String getIdentifyNumber() {
        return identifyNumber;
    }

    public void setIdentifyNumber(String identifyNumber) {
        this.identifyNumber = identifyNumber;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getAddressComplete() {
        return addressComplete;
    }

    public void setAddressComplete(String addressComplete) {
        this.addressComplete = addressComplete;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPersonSex() {
        return personSex;
    }

    public void setPersonSex(String personSex) {
        this.personSex = personSex;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public String getNature() {
        return nature;
    }

    public void setNature(String nature) {
        this.nature = nature;
    }

    public String getMainPersRelation() {
        return mainPersRelation;
    }

    public void setMainPersRelation(String mainPersRelation) {
        this.mainPersRelation = mainPersRelation;
    }

    public String getIdentityValidityStart() {
        return identityValidityStart;
    }

    public OfferPersonVO setIdentityValidityStart(String identityValidityStart) {
        this.identityValidityStart = identityValidityStart;
        return this;
    }

    public String getIdentityValidity() {
        return identityValidity;
    }

    public OfferPersonVO setIdentityValidity(String identityValidity) {
        this.identityValidity = identityValidity;
        return this;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
