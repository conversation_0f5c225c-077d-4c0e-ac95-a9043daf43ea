package com.tuns.car.core.vo.car;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/12 15:19
 */
@Data
public class GrabPolicyPageVO implements Serializable {

    private static final long serialVersionUID = 654526786858396411L;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "保单记录ID")
    private String policyId;

    @ApiModelProperty(value = "保单批次ID")
    private String policyBatchId;

    @ApiModelProperty(value = "保单号")
    private String policyNumber;

    /**
     * 出单类型 1-单交强 2-单商业 3-交商同保
     */
    @ApiModelProperty(value = "出单类型")
    private String recordType;
    /**
     * 保单类型 1-交强单 2-商业单
     */
    @ApiModelProperty(value = "保单类型")
    private String policyType;//保单类型 1-交强单 2-商业单
    /**
     * 保单记录来源 1-客户端 2-内勤 3-录单
     */
    @ApiModelProperty(value = "保单记录来源")
    private String recordSource;//保单记录来源 1-客户端 2-内勤 3-录单

    @ApiModelProperty(value = "保险起期")
    private String insBegin;

    @ApiModelProperty(value = "保险止期")
    private String insEnd;

    @ApiModelProperty(value = "保费")
    private Double insuredPremium;

    @ApiModelProperty(value = "附加保费/车船税")
    private Double attachPremium;
    /**
     * 总保费=保费+附加保费
     */
    @ApiModelProperty(value = "总保费")
    private Double totalPremium;

    @ApiModelProperty(value = "保险金额")
    private Double insAmount;

    @ApiModelProperty(value = "奖励")
    private Double insuredReward;

    @ApiModelProperty(value = "附加奖励")
    private Double attachReward;
    /**
     * 总奖励=佣金+附加奖励
     */
    @ApiModelProperty(value = "总奖励")
    private Double totalReward;

    @ApiModelProperty(value = "承保公司ID")
    private String companyId;

    @ApiModelProperty(value = "承保公司名称")
    private String companyName;

    @ApiModelProperty(value = "行政区域代码-省")
    private String provinceNumber;

    @ApiModelProperty(value = "行政区域-省")
    private String provinceName;

    @ApiModelProperty(value = "行政区域代码-市")
    private String cityNumber;

    @ApiModelProperty(value = "行政区域-市")
    private String cityName;

    @ApiModelProperty(value = "特别约定")
    private String specialAgreement;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    /**
     * 投保日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date issueTime;
    /**
     * 报价日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date offerTime;

    @ApiModelProperty(value = "本公司组织机构代码")
    private String departCode;

    @ApiModelProperty(value = "保险公司合作渠道代码")
    private String channelId;

    @ApiModelProperty(value = "报价评分")
    private String recordScore;
    /**
     * 业务类型 1-新保 2-续保 3-转保
     */
    @ApiModelProperty(value = "业务类型")
    private String insuredType;

    @ApiModelProperty(value = "本渠道续保标志")
    private String thisRenewal;
    /**
     * 佣金手续费比例 0~1
     */
    @ApiModelProperty(value = "佣金手续费比例")
    private Double rewardPercent;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "承保时间")
    private Date insuredTime;

    @ApiModelProperty(value = "保单审核人id")
    private String policyReviewerId;

    @ApiModelProperty(value = "保单审核人名称")
    private String policyReviewerName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "保单审核时间")
    private Date policyReviewTime;

    @ApiModelProperty(value = "保单审核备注信息")
    private String policyReviewMsg;

    /**
     * 保单审核状态 0-未审核 1-已审核
     */
    @ApiModelProperty(value = "保单审核状态")
    private String policyReviewMark;

    @ApiModelProperty(value = "保单是否打印")
    private String policyPrintMark;

    @ApiModelProperty(value = "保单打印人名称")
    private String policyPrinterName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "保单打印时间")
    private Date policyPrintTime;

    @ApiModelProperty(value = "是否新保单")
    private String policyNewMark;

    @ApiModelProperty(value = "单证编号")
    private String documentNumber;

    @ApiModelProperty(value = "invoice_number")
    private String invoiceNumber;

    @ApiModelProperty(value = " ci_mark_number")
    private String ciMarkNumber;

    @ApiModelProperty(value = "原保单记录ID")
    private Long lastPolicyId;//原保单记录ID

    @ApiModelProperty(value = "批单号")
    private String endorsementNumber;//批单号

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date creTm;

    @ApiModelProperty(value = "交商同保评分")
    private String totalRecordScore;

    /**
     * 车辆类型编码
     */
    @ApiModelProperty(value = "车辆类型编码")
    private String carTypeCode;

    @ApiModelProperty(value = "车主")
    private String carOwner;

    @ApiModelProperty(value = "车主性质")
    private String ownerNature;

    @ApiModelProperty(value = "车主证件号码")
    private String ownerIdentify;

    @ApiModelProperty(value = "车主证件类型")
    private String ownerIdentifyType;

    @ApiModelProperty(value = "车牌号码")
    private String plateNumber;

    @ApiModelProperty(value = "车架号")
    private String frameNumber;

    @ApiModelProperty(value = "发动机号码")
    private String engineNumber;
    /**
     * 注册日期 yyyy-MM-dd
     */
    @ApiModelProperty(value = "注册日期")
    private String regDate;
    /**
     * 发证日期 yyyy-MM-dd
     */
    @ApiModelProperty(value = "发证日期")
    private String certDate;

    @ApiModelProperty(value = "过户标志")
    private String transferMark;//过户标志
    /**
     * 过户日期 yyyy-MM-dd
     */
    @ApiModelProperty(value = "过户日期")
    private String transferDate;
    /**
     * 新车标致 0-非新车 1-新车
     */
    @ApiModelProperty(value = "新车标致")
    private String newCarMark;

    @ApiModelProperty(value = "品牌名称")
    private String modelName;

    @ApiModelProperty(value = "核定载客")
    private Integer seatCount;

    @ApiModelProperty(value = "能源类型")
    private String fuelType;

    @ApiModelProperty(value = "排量")
    private Integer exhaustScale;

    @ApiModelProperty(value = "新车购置价")
    private Double purchasePrice;

    @ApiModelProperty(value = "车辆实际价")
    private Double actualPrice;//车辆实际价
    /**
     * 贷款车辆标志 0-非贷款车 1-贷款车
     */
    @ApiModelProperty(value = "贷款车辆标志")
    private String loanCarMark;

    @ApiModelProperty(value = "整备质量")
    private Integer wholeWeight;

    /**
     * 年款 yyyyMM/yyyy
     */
    @ApiModelProperty(value = "年款")
    private String marketDate;
    /**
     * 车辆产地 1-进口 2-国产 3-合资
     */
    @ApiModelProperty(value = "车辆产地")
    private String carOrigin;//车辆产地 1-进口 2-国产 3-合资
    /**
     * 车辆吨位/核定载质量(吨)
     */
    @ApiModelProperty(value = "车辆吨位/核定载质量")
    private Double carTonnage;

    @ApiModelProperty(value = "家庭车辆台数")
    private Integer familyCarCount;
    /**
     * 减税车型标志 0-正常 04-减免税 05-减税
     */
    @ApiModelProperty(value = "减税车型标志")
    private String taxCutsMark;

    @ApiModelProperty(value = "减税比例")
    private Double taxCutProportion;
    /**
     * 所属性质 1-个人 2-机关 3-企业
     */
    @ApiModelProperty(value = "所属性质")
    private String ownershipNature;
    /**
     * 营运性质 0-非营运 1-营运
     */
    @ApiModelProperty(value = "营运性质")
    private String operationNature;

    @ApiModelProperty(value = "使用性质")
    private String usingNature;

    @ApiModelProperty(value = "使用年限")
    private Integer useYears;

    @ApiModelProperty(value = "车身颜色")
    private String bodyColor;

    @ApiModelProperty(value = "备注")
    private String note;

    @ApiModelProperty(value = "保险公司合作渠道名称")
    private String channelName;

    @ApiModelProperty(value = "业务员id")
    private String salesmanId;

    @ApiModelProperty(value = "业务员名称")
    private String salesmanName;

    @ApiModelProperty(value = "业务员手机号")
    private String salesmanPhone;

    @ApiModelProperty(value = "业务员证件号")
    private String salesmanCard;

    @ApiModelProperty(value = "被保险人名称")
    private String personName;

    @ApiModelProperty(value = "投保人名称")
    private String applName;

    @ApiModelProperty(value = "创建人名称")
    private String creName;

    @ApiModelProperty(value = "净保费")
    private Double netPremium;
    @ApiModelProperty(value = "部门名称")
    private String departName;
    @ApiModelProperty(value = "审核部门名称")
    private String checkDepartName;
    /**
     * 车牌所在省代码
     */
    @ApiModelProperty(value = "车牌所在省代码")
    private String province;
    @ApiModelProperty(value = "车牌所在市名称")
    private String city;
    @ApiModelProperty(value = "endorsement_type")
    private String endorsementType;//批单类型  00 未批, 01 已批, 02 商业险加保, 03 商业险减保, 04商业险退保, 05全单退保
    @ApiModelProperty(value = "投保状态")
    private String insuredStatus;
    @ApiModelProperty(value = "投保子状态")
    private String insuredSubStatus;
    @ApiModelProperty(value = "询价人备注类型")
    private String inquirerMsgType;
    @ApiModelProperty(value = "询价人备注")
    private String inquirerMsg;
    @ApiModelProperty(value = "支付失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;
    @ApiModelProperty(value = "支付url")
    private String payImgUrl;
    @ApiModelProperty(value = "订单号")
    private String orderId;
    /**
     * 冲账状态 0未冲,1被冲,2对冲,3已冲
     */
    @ApiModelProperty(value = "冲账状态")
    private String reverseState;
    /**
     * 是否匹配到基础政策 0 未匹配到，1 匹配1个，2 匹配多个
     */
    @ApiModelProperty(value = "是否匹配到基础政策")
    private String mateBasePolicy;

    @ApiModelProperty(value = "报价单号")
    private String insPremiumNumber;

    @ApiModelProperty(value = "投保单号")
    private String proposalNumber;

    /**
     * 交强险预期赔付率
     */
    @ApiModelProperty(value = "交强险预期赔付率")
    private Double lossRatioCi;

    /**
     * 商业险预期赔付率
     */
    @ApiModelProperty(value = "商业险预期赔付率")
    private Double lossRatioBi;

    /**
     * 保单成本率
     */
    @ApiModelProperty(value = "保单成本率")
    private Double policyCostRate;

    /**
     * 业务分组
     */
    @ApiModelProperty(value = "业务分组")
    private String businessGroup;

    /**
     * 客户评分等级
     */
    @ApiModelProperty(value = "客户评分等级")
    private BigDecimal customerRiskRating;

    /**
     * 自主定价系数
     */
    @ApiModelProperty(value = "自主定价系数")
    private Double independentPriceRate;

    /**
     * ncd系数级别
     */
    @ApiModelProperty(value = "ncd系数级别")
    private BigDecimal noClaimLevel;

    /**
     * 整单预期赔付率
     */
    @ApiModelProperty(value = "整单预期赔付率")
    private Double totalRatio;

    /**
     * 私家车车联网分档
     */
    @ApiModelProperty(value = "私家车车联网分档")
    private String carSpreading;

    /**
     * 网约车分级
     */
    @ApiModelProperty(value = "网约车分级")
    private Integer wyCarType;

    /**
     * 无赔款折扣系数(NCD系数)
     */
    @ApiModelProperty(value = "无赔款折扣系数")
    private Double noClaimDiscount;

    /**
     * 期望折扣双系数
     */
    @ApiModelProperty(value = "期望折扣双系数")
    private String expectedDiscount;

    /**
     * 1:一单一议政策; 2:常规政策；3:未匹配政策'
     */
    @ApiModelProperty(value = "政策")
    private String policyStatus;


    /**
     * 非车险套餐信息,多个“,”隔开
     */
    @ApiModelProperty(value = "非车险套餐信息")
    private String carProductName;

    /**
     * 非车险保全费,多个“,”隔开
     */
    @ApiModelProperty(value = "非车险保全费")
    private String carSaveCost;

    /**
     * 非车险保单号，多个用 ","隔开
     */
    private String policyNumbers;

    /**
     * 领用状态(抓单数据特有字段)
     */
    private String pickUpFlag;

    /**
     * 领用时间(抓单数据特有字段)
     */
    private String pickUpTm;

    /**
     * 【奖励佣金金额】：奖励佣金金额1+奖励佣金金额2
     */
    private Double awardCommissionAmount;

    /**
     * 【奖励佣金率】：（奖励佣金金额1+奖励佣金金额2）/全保费
     */
    private Double awardCommissionRate;

    /**
     * 保单是否存在
     */
    private Boolean policyExists;

    /**
     * 操作人
     */
    private String operatorUserName;
    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
