package com.tuns.car.core.vo.car;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CheckImageBasisVO implements Serializable {
    /**
     * 保司一级编码
     */
    String insFieldFirstLevelCode;

    /**
     * 保司一级类目名称
     */
    String insFieldFirstLevelName;

    /**
     * 二级编码对应文件任意传一个时 为true
     * 二级编码对应文件必须全部都传时 为false
     */
    boolean anyOne;

    /**
     * 保司二级类目列表
     */
    List<InsFieldSecondLevel> secondLevelList;

    @Data
    public static class InsFieldSecondLevel {
        /**
         * 保司编码
         */
        String insFieldCode;

        /**
         * 保司类目名称
         */
        String insFieldName;

        /**
         * 一二级组合编码
         */
        String fullCode;
    }
}
