package com.tuns.car.core.vo.car;

import com.tuns.car.core.constant.ViAttachTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 车险保单车辆附件表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
public class ViInsAttachInfVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 保单批次ID
     */
    @ApiModelProperty(value = "保单批次ID")
    private Long policyBatchId;
    /**
     * 保单批次ID
     */
    @ApiModelProperty(value = "保单批次ID")
    private Long serialNumber;
    /**
     * 附件类型
     */
    @ApiModelProperty(value = "附件类型")
    private ViAttachTypeEnum type;
    /**
     * 附件路径
     */
    @ApiModelProperty(value = "附件路径")
    private String key;
    /**
     * 附件文件名
     */
    @ApiModelProperty(value = "附件文件名")
    private String fileName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    /**
     * 域名
     */
    @ApiModelProperty(value = "域名")
    private String domain;

    /**
     * 域名
     */
    @ApiModelProperty(value = "完整文件地址")
    private String fullFilePath;
}
