package com.tuns.car.core.vo;

import com.alibaba.fastjson.JSON;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


public class RatioPolicyCarInfoVO implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 722423504983054330L;

    /**
     * 流程流水号/报价批次号
     */
    private Long serialNumber;

    /**
     * 保单记录ID
     */
    private Long policyId;

    /**
     * 保单批次ID
     */
    private Long policyBatchId;

    /**
     * 保单号
     */
    private String policyNumber;

    /**
     * 主保单号
     */
    private String mainPolicyNumber;

    /**
     * 投保单号
     */
    private String proposalNumber;

    /**
     * 出单类型 1-单交强 2-单商业 3-交商同保
     */
    private String recordType;

    /**
     * 保单类型 1-交强单 2-商业单
     */
    private String policyType;

    /**
     * 自动报价标志
     */
    private String autoMark;

    /**
     * 保单记录来源 1-客户端 2-内勤 3-录单
     */
    private String recordSource;

    /**
     * 保险起期
     */
    private String insBegin;

    /**
     * 保险止期
     */
    private String insEnd;

    /**
     * 承保公司ID
     */
    private String companyId;

    /**
     * 保单配送方式 1-快递配送 2-门店自提
     */
    private String receiverType;

    /**
     * 业务员ID/出单员
     */
    private String salesmanId;

    /**
     * 询价人ID
     */
    private String inquirerId;

    /**
     * 本公司组织机构代码
     */
    private String departCode;

    /**
     * 保险公司合作渠道代码
     */
    private String channelId;

    /**
     * 报价评分
     */
    private BigDecimal recordScore;

    /**
     * 业务类型 1-新保 2-续保 3-转保
     */
    private String insuredType;

    /**
     * 本渠道续保标志
     */
    private String thisRenewal;


    /**
     * 承保时间
     */
    private LocalDateTime insuredTime;

    /**
     * 是否新保单 原始数据
     */
    private String policyNewMark;
    /**
     * 交商同保评分
     */
    private String totalRecordScore;

    /**
     * 免验车原因
     */
    private String exemptVerifyReason;

    /**
     * 车船税缴税类型
     */
    private String carTaxType;

    /**
     * 是否验证通过：0-否，1-是
     */
    private String verifyStatus;

    /**
     * 需补传的资料 01车主身份证照片,02被保人身份证照片,03机动车验车照,04机动车登记证书,
     * 05机动车销售统一发票,06机动车整车出厂合格证,07驾驶证照片,08车船税证明,09其他
     */
    private String needInfomation;
    /**
     * 无赔款折扣系数
     */
    private BigDecimal noClaimDiscount;
    /**
     * 连续承保年数
     */
    private String insureYears;

    /**
     * 车牌号码
     */
    private String plateNumber;

    /**
     * 注册日期 yyyy-MM-dd
     */
    private String regDate;

    /**
     * 发证日期 yyyy-MM-dd
     */
    private String certDate;

    /**
     * 过户标志
     */
    private String transferMark;

    /**
     * 过户日期 yyyy-MM-dd
     */
    private String transferDate;

    /**
     * 新车标致 0-非新车 1-新车
     */
    private String newCarMark;

    /**
     * 品牌名称
     */
    private String modelName;

    /**
     * 车型代码
     */
    private String modelCode;

    /**
     * 能源类型
     */
    private String fuelType;

    /**
     * 新车购置价
     */
    private BigDecimal purchasePrice;

    /**
     * 贷款车辆标志 0-非贷款车 1-贷款车
     */
    private String loanCarMark;

    /**
     * 整备质量
     */
    private Integer wholeWeight;

    /**
     * 年款 yyyyMM/yyyy
     */
    private String marketDate;

    /**
     * 车辆类型描述
     */
    private String carStyleNote;

    /**
     * 车款名称
     */
    private String carName;

    /**
     * 车辆产地 1-进口 2-国产 3-合资
     */
    private String carOrigin;

    /**
     * 车辆吨位/核定载质量(吨)
     */
    private BigDecimal carTonnage;

    /**
     * 行业车型编码
     */
    private String prfsModelCode;

    /**
     * 减税车型标志 0-正常 04-减免税 05-减税
     */
    private String taxCutsMark;

    /**
     * 车系名称
     */
    private String familyName;

    /**
     * 所属性质 1-个人 2-机关 3-企业
     */
    private String ownershipNature;

    /**
     * 营运性质 0-非营运 1-营运
     */
    private String operationNature;

    /**
     * 使用性质 附件码表C1.使用性质
     */
    private String usingNature;

    /**
     * 使用年限
     */
    private Integer useYears;

    /**
     * 车主性质
     */
    private String ownerNature;

    /**
     * 车辆实际价
     */
    private BigDecimal actualPrice;
    
    /**
     * 商业险预期赔付率
     */
    private BigDecimal lossRatioBi;

    /**
     * 交强险预期赔付率
     */
    private BigDecimal lossRatioCi;

    /**
     * 整单预期赔付率
     */
    private BigDecimal totalRatio;
    
    /**
     * 自主定价系数
     */
    private BigDecimal independentPriceRate;

    public Long getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Long serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Long getPolicyId() {
        return policyId;
    }

    public void setPolicyId(Long policyId) {
        this.policyId = policyId;
    }

    public Long getPolicyBatchId() {
        return policyBatchId;
    }

    public void setPolicyBatchId(Long policyBatchId) {
        this.policyBatchId = policyBatchId;
    }

    public String getPolicyNumber() {
        return policyNumber;
    }

    public void setPolicyNumber(String policyNumber) {
        this.policyNumber = policyNumber;
    }

    public String getMainPolicyNumber() {
        return mainPolicyNumber;
    }

    public void setMainPolicyNumber(String mainPolicyNumber) {
        this.mainPolicyNumber = mainPolicyNumber;
    }

    public String getProposalNumber() {
        return proposalNumber;
    }

    public void setProposalNumber(String proposalNumber) {
        this.proposalNumber = proposalNumber;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getAutoMark() {
        return autoMark;
    }

    public void setAutoMark(String autoMark) {
        this.autoMark = autoMark;
    }

    public String getRecordSource() {
        return recordSource;
    }

    public void setRecordSource(String recordSource) {
        this.recordSource = recordSource;
    }

    public String getInsBegin() {
        return insBegin;
    }

    public void setInsBegin(String insBegin) {
        this.insBegin = insBegin;
    }

    public String getInsEnd() {
        return insEnd;
    }

    public void setInsEnd(String insEnd) {
        this.insEnd = insEnd;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getReceiverType() {
        return receiverType;
    }

    public void setReceiverType(String receiverType) {
        this.receiverType = receiverType;
    }

    public String getSalesmanId() {
        return salesmanId;
    }

    public void setSalesmanId(String salesmanId) {
        this.salesmanId = salesmanId;
    }

    public String getInquirerId() {
        return inquirerId;
    }

    public void setInquirerId(String inquirerId) {
        this.inquirerId = inquirerId;
    }

    public String getDepartCode() {
        return departCode;
    }

    public void setDepartCode(String departCode) {
        this.departCode = departCode;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public BigDecimal getRecordScore() {
        return recordScore;
    }

    public void setRecordScore(BigDecimal recordScore) {
        this.recordScore = recordScore;
    }

    public String getInsuredType() {
        return insuredType;
    }

    public void setInsuredType(String insuredType) {
        this.insuredType = insuredType;
    }

    public String getThisRenewal() {
        return thisRenewal;
    }

    public void setThisRenewal(String thisRenewal) {
        this.thisRenewal = thisRenewal;
    }

    public LocalDateTime getInsuredTime() {
        return insuredTime;
    }

    public void setInsuredTime(LocalDateTime insuredTime) {
        this.insuredTime = insuredTime;
    }

    public String getPolicyNewMark() {
        return policyNewMark;
    }

    public void setPolicyNewMark(String policyNewMark) {
        this.policyNewMark = policyNewMark;
    }

    public String getTotalRecordScore() {
        return totalRecordScore;
    }

    public void setTotalRecordScore(String totalRecordScore) {
        this.totalRecordScore = totalRecordScore;
    }

    public String getExemptVerifyReason() {
        return exemptVerifyReason;
    }

    public void setExemptVerifyReason(String exemptVerifyReason) {
        this.exemptVerifyReason = exemptVerifyReason;
    }

    public String getCarTaxType() {
        return carTaxType;
    }

    public void setCarTaxType(String carTaxType) {
        this.carTaxType = carTaxType;
    }
    
    public String getVerifyStatus() {
        return verifyStatus;
    }

    public void setVerifyStatus(String verifyStatus) {
        this.verifyStatus = verifyStatus;
    }

    public String getNeedInfomation() {
        return needInfomation;
    }

    public void setNeedInfomation(String needInfomation) {
        this.needInfomation = needInfomation;
    }

    public BigDecimal getNoClaimDiscount() {
        return noClaimDiscount;
    }

    public void setNoClaimDiscount(BigDecimal noClaimDiscount) {
        this.noClaimDiscount = noClaimDiscount;
    }

    public String getInsureYears() {
        return insureYears;
    }

    public void setInsureYears(String insureYears) {
        this.insureYears = insureYears;
    }

    public String getPlateNumber() {
        return plateNumber;
    }

    public void setPlateNumber(String plateNumber) {
        this.plateNumber = plateNumber;
    }

    public String getRegDate() {
        return regDate;
    }

    public void setRegDate(String regDate) {
        this.regDate = regDate;
    }

    public String getCertDate() {
        return certDate;
    }

    public void setCertDate(String certDate) {
        this.certDate = certDate;
    }

    public String getTransferMark() {
        return transferMark;
    }

    public void setTransferMark(String transferMark) {
        this.transferMark = transferMark;
    }

    public String getTransferDate() {
        return transferDate;
    }

    public void setTransferDate(String transferDate) {
        this.transferDate = transferDate;
    }

    public String getNewCarMark() {
        return newCarMark;
    }

    public void setNewCarMark(String newCarMark) {
        this.newCarMark = newCarMark;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getFuelType() {
        return fuelType;
    }

    public void setFuelType(String fuelType) {
        this.fuelType = fuelType;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public String getLoanCarMark() {
        return loanCarMark;
    }

    public void setLoanCarMark(String loanCarMark) {
        this.loanCarMark = loanCarMark;
    }

    public Integer getWholeWeight() {
        return wholeWeight;
    }

    public void setWholeWeight(Integer wholeWeight) {
        this.wholeWeight = wholeWeight;
    }

    public String getMarketDate() {
        return marketDate;
    }

    public void setMarketDate(String marketDate) {
        this.marketDate = marketDate;
    }

    public String getCarStyleNote() {
        return carStyleNote;
    }

    public void setCarStyleNote(String carStyleNote) {
        this.carStyleNote = carStyleNote;
    }

    public String getCarName() {
        return carName;
    }

    public void setCarName(String carName) {
        this.carName = carName;
    }

    public String getCarOrigin() {
        return carOrigin;
    }

    public void setCarOrigin(String carOrigin) {
        this.carOrigin = carOrigin;
    }

    public BigDecimal getCarTonnage() {
        return carTonnage;
    }

    public void setCarTonnage(BigDecimal carTonnage) {
        this.carTonnage = carTonnage;
    }

    public String getPrfsModelCode() {
        return prfsModelCode;
    }

    public void setPrfsModelCode(String prfsModelCode) {
        this.prfsModelCode = prfsModelCode;
    }

    public String getTaxCutsMark() {
        return taxCutsMark;
    }

    public void setTaxCutsMark(String taxCutsMark) {
        this.taxCutsMark = taxCutsMark;
    }

    public String getFamilyName() {
        return familyName;
    }

    public void setFamilyName(String familyName) {
        this.familyName = familyName;
    }

    public String getOwnershipNature() {
        return ownershipNature;
    }

    public void setOwnershipNature(String ownershipNature) {
        this.ownershipNature = ownershipNature;
    }

    public String getOperationNature() {
        return operationNature;
    }

    public void setOperationNature(String operationNature) {
        this.operationNature = operationNature;
    }

    public String getUsingNature() {
        return usingNature;
    }

    public void setUsingNature(String usingNature) {
        this.usingNature = usingNature;
    }

    public Integer getUseYears() {
        return useYears;
    }

    public void setUseYears(Integer useYears) {
        this.useYears = useYears;
    }

    public String getOwnerNature() {
        return ownerNature;
    }

    public void setOwnerNature(String ownerNature) {
        this.ownerNature = ownerNature;
    }

    public BigDecimal getActualPrice() {
        return actualPrice;
    }

    public void setActualPrice(BigDecimal actualPrice) {
        this.actualPrice = actualPrice;
    }

    public BigDecimal getLossRatioBi() {
        return lossRatioBi;
    }

    public void setLossRatioBi(BigDecimal lossRatioBi) {
        this.lossRatioBi = lossRatioBi;
    }

    public BigDecimal getLossRatioCi() {
        return lossRatioCi;
    }

    public void setLossRatioCi(BigDecimal lossRatioCi) {
        this.lossRatioCi = lossRatioCi;
    }

    public BigDecimal getTotalRatio() {
        return totalRatio;
    }

    public void setTotalRatio(BigDecimal totalRatio) {
        this.totalRatio = totalRatio;
    }

    public BigDecimal getIndependentPriceRate() {
        return independentPriceRate;
    }

    public void setIndependentPriceRate(BigDecimal independentPriceRate) {
        this.independentPriceRate = independentPriceRate;
    }

    @Override
    public String toString() {
      return JSON.toJSONString(this);
    }
}
