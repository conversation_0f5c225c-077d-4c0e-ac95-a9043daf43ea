package com.tuns.car.core.vo.car;

import com.tuns.car.core.dto.car.ViCompanyImageMappingDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 图片必填校验类
 *
 * <AUTHOR>
 */
@Data
public class CarImageConfigVO {

    @ApiModelProperty(value = "保司文件列表")
    List<ViCompanyImageMappingDTO> imageConfiguration;

    @ApiModelProperty(value = "需要校验的文件")
    List<CheckImageBasisVO> checkImageBasisList;

    @ApiModelProperty(value = "保司文件和腾顺文件映射")
    List<FileMappingVO> fileMapping;

}
