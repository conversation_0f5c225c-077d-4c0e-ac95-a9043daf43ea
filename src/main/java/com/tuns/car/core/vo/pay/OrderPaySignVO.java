package com.tuns.car.core.vo.pay;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ClassName OrderPaySignVO
 * @Description 车险订单支付签名返回数据类
 * <AUTHOR> yi
 * @Date 2020/6/24 10:16
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class OrderPaySignVO implements Serializable {

    private static final long serialVersionUID = 6353225111877479407L;

    /**
     * 订单uuid
     */
    @ApiModelProperty(value = "订单uuid", required = true)
    private String orderUuid;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", required = true)
    private String orderNumber;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态", required = true)
    private String orderStatus;

    /**
     * 支付请求地址
     */
    @ApiModelProperty(value = "支付请求地址", required = true)
    private String action;

    /**
     * 表单内容，不为空时需配合action表单提交
     */
    @ApiModelProperty(value = "表单内容,不为空时需配合action表单提交", required = true)
    private Object form;

    @ApiModelProperty(value = "非车真实日期，如果不为空时，需更改界面的非车时间")
    private String noCarRealDate;

    /**
     * 二维码地址
     * */
    private String qrCode;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
