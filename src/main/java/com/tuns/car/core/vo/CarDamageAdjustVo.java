package com.tuns.car.core.vo;

import com.tuns.car.core.constant.FloatProportionStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.minidev.json.annotate.JsonIgnore;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 车损险调整
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CarDamageAdjustVo implements Serializable {
    /**
     * companyCode
     */
    @Valid
    @NotNull
    @ApiModelProperty("companyCode")
    private String companyCode;

    /**
     * 车辆实际价值
     */
    @Valid
    @NotNull
    @ApiModelProperty("车辆实际价值")
    private BigDecimal actualPrice;

    /**
     *原值
     */
    @Valid
    @NotNull
    @ApiModelProperty("原值")
    private BigDecimal originalValue;


    /**
     * 浮动比例
     */
    @Valid
    @NotNull
    @ApiModelProperty("浮动比例")
    private BigDecimal floatProportion;

    /**
     * 上浮或者下浮
     */
    @ApiModelProperty("浮动比例")
    @JsonIgnore
    private FloatProportionStatusEnum status;


}
