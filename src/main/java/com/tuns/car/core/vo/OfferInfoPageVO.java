package com.tuns.car.core.vo;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tuns.car.core.constant.AutoMarkEnum;
import com.tuns.car.core.constant.ChanTypeEnum;
import com.tuns.car.core.dto.gi.GiOrderInsureVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName OfferInfoPageVO
 * @Description 车险报价单列表页查询返回类
 * <AUTHOR> yi
 * @Date 2020/6/8 15:43
 * @Version 1.0
 */
@Data
public class OfferInfoPageVO implements Serializable {

    private static final long serialVersionUID = 4889660431695271639L;

    /**
     * 报价批次id
     */
    @ApiModelProperty(value = "报价批次id", required = true)
    private String policyBatchId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 订单业务id
     */
    @ApiModelProperty(value = "订单业务id(订单时必有)", required = true)
    private String orderUuid;

    /**
     * 车牌号码
     */
    @ApiModelProperty(value = "车牌号码", required = true)
    private String plateNumber;

    /**
     * 被保人名称
     */
    @ApiModelProperty(value = "被保人名称", required = true)
    private String insured;

    /**
     * 交强险起期
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "交强险起期", required = true)
    private String compelBegin;

    /**
     * 商业险起期
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "商业险起期", required = true)
    private String busBegin;

    /**
     * 交强险保费
     */
    @ApiModelProperty(value = "交强险保费")
    private BigDecimal compelPremium;

    /**
     * 商业险险保费
     */
    @ApiModelProperty(value = "商业险保费")
    private BigDecimal busPremium;

    /**
     * 保险公司LOGO 图片地址
     */
    @ApiModelProperty(value = "保险公司LOGO 图片地址", required = true)
    private String companyLogo;

    /**
     * 报价状态
     */
    @ApiModelProperty(value = "报价状态 A1等待报价,A2等待报价（处理中）,A2-1 等待报价（需补传资料）,A3报价成功,A4报价失败（等待补传资料）,A5报价失败（需调整投保方案）,A6报价失败,A7报价取消,B1等待核保,B1-1 等待核保（需补传资料）,B2核保通过，已生成订单,B3核保失败（等待补传资料）,B4核保失败（需调整投保方案）,B5核保失败,B6核保取消", required = true)
    private String subStatus;

    /**
     * 报价创建时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报价创建时间", required = true)
    private LocalDateTime creTm;

    /**
     * 报价保费合计金额
     */
    @ApiModelProperty(value = "报价保费合计金额", required = true)
    private BigDecimal premium;

    /**
     * 报价标志 0人工,1自动
     */
    @ApiModelProperty(value = "报价标志 0人工,1自动", required = true)
    private AutoMarkEnum autoMark;

    /**
     * 订单类型(交商同保二合一) 1=2019-09-25 00:00:00,2=2019-09-25 00:00:00 1交强 + 时间 逗号隔开 2商业 +
     * 时间
     */
    private String policyType;

    /**
     * 商业起保日期
     */
    private String insBegin;

    /**
     * 非车险
     */
    private List<GiOrderInsureVO> giInsures;

    /**
     * 金额类型
     * 2=629.15,1=1100.00
     */
    private String policyPremium;

    private String partPolicyPremium;

	/**
	 * 状态描述
	 */
	private String desc;

	/**
	 * 投保单号
	 */
	@ApiModelProperty(value = "投保单号")
	private String proposalNumber;

	/***
	 * 当前订单状态是否允许撤销核保【1-可以，0-不允许】
	 */
	@ApiModelProperty(value = "当前订单状态是否允许撤销核保【1-可以，0-不允许】")
	private Integer isAllowCancelUnderwrite;

    /**
     * 渠道类型
     */
    @ApiModelProperty(value = "渠道类型")
    private ChanTypeEnum chanType;

    @ApiModelProperty(value = "非车产品名称")
    private String carProductName;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
