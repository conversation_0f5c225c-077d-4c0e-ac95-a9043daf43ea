package com.tuns.car.core.vo.car;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.tuns.car.core.constant.PolicyTypeEnum;
import com.tuns.car.core.constant.RecordTypeEnum;
import com.tuns.car.core.constant.ViPersonTypeEnum;
import com.tuns.car.core.dto.car.InsOrderDTO;
import com.tuns.car.core.dto.gi.CarSameSaleGiOrderInfoVO;
import com.tuns.car.core.entity.PubPersInfTp;
import com.tuns.car.core.entity.ViInsCarInfTp;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import com.tuns.car.core.entity.ViOrderInfo;
import com.tuns.core.boot.constant.InsConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName OrderQueryVO
 * @Description 支付查询返回数据类
 * <AUTHOR> yi
 * @Date 2020/7/7 19:45
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class OrderQueryVO implements Serializable {

    private static final long serialVersionUID = -7792614422972136415L;

    /**
     * 订单业务id
     */
    @ApiModelProperty(value = "订单业务id", required = true)
    private String orderUuid;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", required = true)
    private String orderNumber;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额", required = true)
    private BigDecimal orderAmount;

    /**
     * 订单状态 6等待支付,7支付成功,11支付失败
     */
    @ApiModelProperty(value = "订单状态 6等待支付,7支付成功,11支付失败", required = true)
    private String orderStatus;

    /**
     * 支付方式 client 客户端, weChat直接微信, weChatOnly仅支持微信打开, aliPay直接支付宝,
     * aliPayOnly仅支持支付宝打开, unionPay银联, kuaiQian快钱, yeepay易宝支付, noneType立即支付,
     * other其它方式
     */
    @ApiModelProperty(value = "支付方式 client 客户端, weChat直接微信, weChatOnly仅支持微信打开, aliPay直接支付宝, aliPayOnly仅支持支付宝打开, unionPay银联, kuaiQian快钱, yeepay易宝支付, noneType立即支付, other其它方式", required = true)
    private List<String> payList;

    /**
     * 当前的支付方式
     */
    @ApiModelProperty("当前的支付方式")
    private String currentPayType;

    /**
     * 支付转账数据
     */
    @ApiModelProperty(value = "支付转账数据")
    private List<PayeeData> payeeInfo;

    /**
     * 订单创建时间
     */
    @ApiModelProperty(value = "订单创建时间", required = true)
    private LocalDateTime creTm;

    /**
     * 支付二维码超时时间
     */
    @ApiModelProperty(value = "支付二维码超时时间", required = true)
    private Date expireTime;

    /**
     * 是否支付
     */
    @ApiModelProperty(value = "是否支付 0否,1是", required = true)
    private String payState;

    /**
     * 是否已过期
     */
    @ApiModelProperty(value = "是否已过期 0否,1是", required = true)
    private String payLate;

    /**
     * 业务类型标志
     */
    @ApiModelProperty(value = "业务类型标志 VI车险,PI个险,GI团险", required = true)
    private String mark;

    /**
     * 二维码支付图片地址
     */
    @ApiModelProperty(value = "二维码支付图片地址", required = true)
    private String payUrl;

    /**
     * 支付二维码刷新状态 0未刷新,1刷新中,2已刷新
     */
    @ApiModelProperty(value = "支付二维码刷新状态 0未刷新,1刷新中,2已刷新", required = true)
    private String flushType;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间", required = true)
    private LocalDateTime payTime;

    /**
     * 订单数据
     */
    @ApiModelProperty(value = "订单数据", required = true)
    private List<OrderData> orderInfo;
    /**
     * 是否需要验证码1是0否
     */
    @ApiModelProperty(value = "是否需要验证码1是0否", required = true)
    private String needIssueCode;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID", required = true)
    private String businessNumber;
    /**
     * 投保人电话
     */
    @ApiModelProperty(value = "投保人电话", required = true)
    private String holderMobilePhone;

    /**
     * 渠道类型 core ： api
     */
    private String chanType;

    public OrderQueryVO(InsOrderDTO dto) {
        ViOrderInfo orderInfo = dto.getOrderInfo();
        List<PubPersInfTp> persInfTp = dto.getPersInfTp();
        List<ViInsPlcyInfTp> insList = dto.getInsList();
        Map<String, PubPersInfTp> personMap = persInfTp.stream().collect(Collectors.toMap(PubPersInfTp::getPersonType, pubPersInfTp -> pubPersInfTp));
        this.setPayList(dto.getPayList());
        this.setCurrentPayType(dto.getCurrentPayType());
        this.setPayeeInfo(dto.getPayeeInfo());
        this.setOrderUuid(dto.getOrderInfo().getOrderUuid());
        this.setOrderNumber(orderInfo.getOrderNo());
        this.setPayState(dto.getPayState());
        this.setMark(dto.getMark());
        this.setOrderStatus(dto.getOrderStatus());
        this.setOrderAmount(orderInfo.getPrem());
        this.setCreTm(orderInfo.getCreTm());
        this.setExpireTime(orderInfo.getPayExpireTime());
        this.setFlushType(orderInfo.getFlushType());
        this.setPayTime(orderInfo.getPayTime());
        this.setNeedIssueCode(dto.getNeedIssueCode());
        this.setChanType(dto.getChanType());
        if (orderInfo.getPayExpireTime() != null) {
            // before，当Date1小于Date2时，返回TRUE，当大于等于时，返回false；
            if (orderInfo.getPayExpireTime().before(new Date())) {
                this.setPayLate(InsConstants.YNFlag.YES);
            } else {
                this.setPayLate(InsConstants.YNFlag.NO);
            }
        }
        // 构造订单信息
        List<OrderData> orderData = buildOrderInfo(dto);
        this.setOrderInfo(orderData);
        this.setBusinessNumber(orderInfo.getQuoteId().toString());
        PubPersInfTp holder = personMap.get(ViPersonTypeEnum.HOLDER.getValue());
        if (Objects.nonNull(holder)) {
            this.setHolderMobilePhone(holder.getMobilePhone());
        }
    }

    private List<OrderData> buildOrderInfo(InsOrderDTO dto) {
        List<ViInsPlcyInfTp> insList = dto.getInsList();
        ViInsCarInfTp insCar = dto.getInsCar();
        List<PubPersInfTp> persInfTp = dto.getPersInfTp();
        Map<String, PubPersInfTp> personMap = persInfTp.stream().collect(Collectors.toMap(PubPersInfTp::getPersonType, pubPersInfTp -> pubPersInfTp));
        List<CarSameSaleGiOrderInfoVO> noCarOrderDTOS = dto.getNoCarOrderDTOS();
        List<OrderData> list = new ArrayList<>();
        OrderData companyName = new OrderData();
        companyName.setKey("companyName");
        companyName.setValue(insList.get(0).getCompanyName());
        companyName.setTitle("保险公司");
        list.add(companyName);
        // 关系人
        OrderData relatePerson = new OrderData();
        relatePerson.setKey("relatePerson");
        relatePerson.setTitle("投保人/被保人/车主姓名");
        String holderName = Optional.ofNullable(personMap.get(ViPersonTypeEnum.HOLDER.getValue())).map(PubPersInfTp::getPersonName).orElse("");
        String insuredName = Optional.ofNullable(personMap.get(ViPersonTypeEnum.INSURED.getValue())).map(PubPersInfTp::getPersonName).orElse("");
        String ownerName = Optional.ofNullable(personMap.get(ViPersonTypeEnum.OWNER.getValue())).map(PubPersInfTp::getPersonName).orElse("");
        relatePerson.setValue(CollectionUtil.join(Arrays.asList(holderName, insuredName, ownerName), "/"));
        list.add(relatePerson);
        OrderData plateNumber = new OrderData();
        plateNumber.setKey("plateNumber");
        plateNumber.setValue(insCar.getPlateNumber());
        plateNumber.setTitle("车牌号码");
        list.add(plateNumber);
        // 险种信息
        Map<String, ViInsPlcyInfTp> insMap = insList.stream().collect(Collectors.toMap(ViInsPlcyInfTp::getPolicyType, insPlcyInfTp -> insPlcyInfTp));
        ViInsPlcyInfTp ciIns = Optional.ofNullable(insMap.get(PolicyTypeEnum.CI.getValue())).orElse(null);
        ViInsPlcyInfTp biIns = Optional.ofNullable(insMap.get(PolicyTypeEnum.BI.getValue())).orElse(null);
        if (Objects.nonNull(ciIns)) {
            OrderData carTax = new OrderData();
            carTax.setTitle("车船税");
            carTax.setKey("carTax");
            carTax.setExtra("￥" + ciIns.getAttachPremium());
            carTax.setExtra("￥" + ciIns.getAttachPremium());
            list.add(carTax);
            OrderData ciPlcy = new OrderData();
            ciPlcy.setTitle("交强险起保日期");
            ciPlcy.setKey("ci");
            ciPlcy.setValue(ciIns.getInsBegin());
            ciPlcy.setExtra("￥" + ciIns.getInsuredPremium());
            list.add(ciPlcy);
        }
        if (Objects.nonNull(biIns)) {
            OrderData biPlcy = new OrderData();
            biPlcy.setTitle("商业险起保日期");
            biPlcy.setKey("bi");
            biPlcy.setValue(biIns.getInsBegin());
            biPlcy.setExtra("￥" + biIns.getInsuredPremium());
            list.add(biPlcy);
        }
        for (int i = 0; i < noCarOrderDTOS.size(); i++) {
            CarSameSaleGiOrderInfoVO giNoCarOrderDTO = noCarOrderDTOS.get(i);
            OrderData noCar = new OrderData();
            int index = i + 1;
            noCar.setTitle("非车险" + index);
            noCar.setKey("noCar" + index);
            noCar.setValue(DateUtil.formatDateTime(giNoCarOrderDTO.getStartTime()));
            noCar.setExtra("￥" + giNoCarOrderDTO.getPrem().setScale(2, BigDecimal.ROUND_HALF_UP));
            list.add(noCar);
        }
        // 这个字段只有分享图片的时候需要显示
        OrderData ord4 = new OrderData();
        ord4.setKey("prem");
        ord4.setValue("￥" + this.getOrderAmount());
        String totalPremTitle = "";
        if (RecordTypeEnum.CI.getValue().equals(insList.get(0).getRecordType())) {
            totalPremTitle = "保费合计(含车船税)";
        } else if (RecordTypeEnum.BI.getValue().equals(insList.get(0).getRecordType())) {
            totalPremTitle = "保费合计(商业险)";
        } else {
            totalPremTitle = "保费合计(含车船税)";
        }
        ord4.setTitle(totalPremTitle);
        ord4.setHide(true);
        list.add(ord4);
        return list;
    }

    /**
     * 支付信息
     */
    @Data
    @Accessors(chain = true)
    public static class PayeeData {

        /**
         * 字段
         */
        private String key;
        /**
         * 字段名称
         */
        private String title;
        /**
         * 字段值
         */
        private String value;
    }

    @Data
    @Accessors(chain = true)
    public static class OrderData {

        /**
         * 字段
         */
        private String key;
        /**
         * 字段名称
         */
        private String title;
        /**
         * 字段值
         */
        private String value;

        /**
         * 拓展信息
         */
        private String extra;

        /**
         * 是否隐藏 （详情页不显示，但是分享出去的图片要显示）
         */
        private Boolean hide = false;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
