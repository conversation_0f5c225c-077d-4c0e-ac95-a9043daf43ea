package com.tuns.car.core.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.tuns.car.core.constant.ChanTypeEnum;
import com.tuns.core.boot.constant.SwitchFlagEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 车险数据比对字段映射配置表
 *
 * <AUTHOR>
 * @email
 * @date 2022-08-25 12:32:20
 */
@Data
@ApiModel(value = "系统-保险公司渠道配置--保险公司配置-数据比对配置-响应[查询配置]")
public class CarDataCompareVO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Integer id;
    /**
     * 保险公司代码
     */
    @ApiModelProperty(value = "保险公司代码")
    private String companyId;
    /**
     * 渠道类型：核心-core，API-api
     */
    @ApiModelProperty(value = "渠道类型：核心-core，API-api")
    private ChanTypeEnum chanType;
    /**
     * 我司字段数据字典详情id
     */
    @ApiModelProperty(value = "我司字段数据字典详情id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tunsFieldDictId;

    /**
     * 我司字段名称
     */
    @ApiModelProperty(value = "我司字段名称")
    private String tunsFieldName;

    /**
     * 我司字段路径
     */
    @ApiModelProperty(value = "我司字段路径")
    private String tunsFieldPath;
    /**
     * 保司字段名称
     */
    @ApiModelProperty(value = "保司字段名称")
    private String insFieldName;
    /**
     * 保司字段路径
     */
    @ApiModelProperty(value = "保司字段路径")
    private String insFieldPath;
    /**
     * 映射规则
     */
    @ApiModelProperty(value = "映射规则")
    private String extendRule;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 启停用标志(0 停用 1 启用)
     */
    @ApiModelProperty(value = "启停用标志(0 停用 1 启用)")
    private SwitchFlagEnum isEnable;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 出单网址简称
     */
    private String linkAddr;

    /**
     * 出单网址
     */
    private String linkAddress;

    /**
     * 出单网址ID（关联出单平台_保险公司地址表）
     */
    private Integer linkAddressId;
}
