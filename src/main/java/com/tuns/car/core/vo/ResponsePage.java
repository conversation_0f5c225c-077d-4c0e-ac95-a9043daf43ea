package com.tuns.car.core.vo;

/**
 * @ClassName Page
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/08/25 09:02:04
 * @Version 1.0
 */

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

@JsonInclude(Include.ALWAYS)
@ApiModel(
        value = "Page",
        description = "分页信息对象"
)
public class ResponsePage<T> implements Serializable {
    private static final long serialVersionUID = -3058669795510316149L;
    @ApiModelProperty("当前页数")
    private Integer page = 1;
    @ApiModelProperty("每页条数")
    private Integer pageSize = 20;
    @ApiModelProperty("总条数")
    private Long total = 0L;
    @ApiModelProperty("返回的列表数据")
    private List<T> list;

    public ResponsePage() {
    }

    public ResponsePage(Integer currentPage, Integer pageSize, Long totalCount, List<T> list) {
        this.page = currentPage;
        this.pageSize = pageSize;
        this.total = totalCount;
        this.list = list;
    }

    public Integer getPage() {
        return this.page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getTotal() {
        return this.total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<T> getList() {
        return this.list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}

