package com.tuns.car.core.vo.car;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件映射vo类
 *
 * <AUTHOR>
 */
@Data
public class FileMappingVO {

    /**
     * 腾顺影像资料类型
     */
    @ApiModelProperty(value = "腾顺影像资料类型")
    private String attachType;
    /**
     * 保司文件类型一级编码
     */
    @ApiModelProperty(value = "保司文件类型一级编码")
    private String insFieldFirstLevelCode;

    /**
     * 保司文件类型一级编码
     */
    @ApiModelProperty(value = "保司文件类型一级编码")
    private String insFieldSecondLevelCode;

    /**
     * 保司文件类型完整编码
     */
    @ApiModelProperty(value = "保司文件类型完整编码")
    private String insFieldFullCode;


    public FileMappingVO(String attachType, String insFieldFirstLevelCode, String insFieldSecondLevelCode) {
        this.attachType = attachType;
        this.insFieldFirstLevelCode = insFieldFirstLevelCode;
        this.insFieldSecondLevelCode = insFieldSecondLevelCode;
        this.insFieldFullCode = insFieldFirstLevelCode + "-" + insFieldSecondLevelCode;
    }
}
