package com.tuns.car.core.vo.car;

import com.tuns.core.boot.constant.YesNoEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CarQuoteApplyRecordComparisonResultDTO {

    /**
     * 信息头：如 车牌号、车架号、等
     */
    @ApiModelProperty(value = "信息头：如 车牌号、车架号、等")
    private String informationHead;

    /**
     * 我司数据结果
     */
    @ApiModelProperty(value = "我司数据结果")
    private String weInformation;

    /**
     * 保司数据结果
     */
    @ApiModelProperty(value = "保司数据结果")
    private String thirdPartyInformation;

    @ApiModelProperty(value = "是否阻断")
    private YesNoEnum isStop = YesNoEnum.YES;

    public CarQuoteApplyRecordComparisonResultDTO(String informationHead, String thirdPartyInformation, YesNoEnum isStop) {
        this.informationHead = informationHead;
        this.thirdPartyInformation = thirdPartyInformation;
        this.isStop = isStop;
    }
    public CarQuoteApplyRecordComparisonResultDTO(String informationHead, String weInformation,String thirdPartyInformation, YesNoEnum isStop) {
        this.informationHead = informationHead;
        this.weInformation = weInformation;
        this.thirdPartyInformation = thirdPartyInformation;
        this.isStop = isStop;
    }
}
