package com.tuns.car.core.vo.car;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.constant.ViAttachTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName OfferAttachVO
 * @Description APP车险附件资料查询返回数据类
 * <AUTHOR> yi
 * @Date 2020/6/24 16:16
 * @Version 1.0
 */
@Data
public class OfferAttachVO implements Serializable {

  private static final long serialVersionUID = -1509709168246915226L;

  /**
   * 附件类型
   */
  @ApiModelProperty(value = "附件图片类型 01被保人身份证正面,02被保人身份证反面,03行驶证正本,04行驶证副本,05营业执照/组织机构代码证,06机动车左前,"
      + "07机动车左后,08机动车右前,09机动车右后,10机动车车架号,11机动车辆登记证书,12机动车销售统一发票,13机动车整车出产合格证,14驾驶证,15车船税证明,"
      + "16交强险电子保单,17商业险电子保单,18交强险电子发票,19商业险电子发票,20交强险电子标签,21车主身份证正面,22车主身份证反面,23投保人身份证正面,24投保人身份证反面,25上年度投保险种信息99其它", required = true)
  private ViAttachTypeEnum type;

  /**
   * 附件路径
   */
  @ApiModelProperty(value = "附件路径", required = true)
  private String key;

  /**
   * 附件路径(全)
   */
  @ApiModelProperty(value = "附件路径(全)", required = true)
  private String src;

  /**
   * 附件文件名
   */
  @ApiModelProperty(value = "附件文件名", required = true)
  private String fileName;

  /**
   * 文件类型
   */
  @ApiModelProperty(value = "文件类型", required = true)
  private String fileType;



  @Override
  public String toString() {
    return JSON.toJSONString(this);
  }
}
