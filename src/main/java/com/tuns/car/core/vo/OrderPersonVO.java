package com.tuns.car.core.vo;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName OrderPersonVO
 * @Description 车险订单详情查询返回 关系人信息 数据类
 * <AUTHOR> yi
 * @Date 2020/6/9 19:53
 * @Version 1.0
 */
@Data
public class OrderPersonVO implements Serializable {

    private static final long serialVersionUID = -5657698706675896802L;


    /**
     * 关系人类型 01车主,02被保人,03投保人,04受益人,05收件人
     */
    @ApiModelProperty(value = "关系人类型 01车主,02被保人,03投保人,04受益人,05收件人", required = true)
    private String personType;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    private String personName;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型", required = true)
    private String identifyType;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码", required = true)
    private String identifyNumber;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", required = true)
    private String mobilePhone;

    /**
     * 完整地址
     */
    @ApiModelProperty(value = "完整地址", required = true)
    private String addressComplete;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", required = true)
    private String addressDetail;

    /**
     * 电子邮箱
     */
    @ApiModelProperty(value = "电子邮箱", required = true)
    private String email;

    /**
     * 性质
     */
    @ApiModelProperty(value = "性质", required = true)
    private String nature;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
