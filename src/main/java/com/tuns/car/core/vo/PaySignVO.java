package com.tuns.car.core.vo;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ClassName PaySignVO
 * @Description 第三方支付签名返回类
 * <AUTHOR> yi
 * @Date 2020/6/22 10:15
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class PaySignVO implements Serializable {

    private static final long serialVersionUID = 6811212732911178185L;

    /**
     * 支付请求地址
     */
    private String action;

    /**
     * 表单内容，不为空时需配合action表单提交
     */
    private Object form;

    private String paySerialNumber;

    /**
     * 申能需要从支付界面拿到非车日期，其他保司如要用，直接设置
     */
    private String realNoCarDate;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
