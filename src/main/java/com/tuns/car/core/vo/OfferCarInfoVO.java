package com.tuns.car.core.vo;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName OfferCarInfoVO
 * @Description 车险报价单详情查询 车辆信息 返回类
 * <AUTHOR> yi
 * @Date 2020/6/11 17:19
 * @Version 1.0
 */
public class OfferCarInfoVO implements Serializable {

    private static final long serialVersionUID = -698019019648601537L;

    /**
     * 车牌号码
     */
    @ApiModelProperty(value = "车牌号码", required = true)
    private String plateNumber;

    /**
     * 车主姓名
     */
    @ApiModelProperty(value = "车主姓名", required = true)
    private String carOwner;

    /**
     * 车辆识别代号
     */
    @ApiModelProperty(value = "车辆识别代号", required = true)
    private String frameNumber;

    /**
     * 发动机号
     */
    @ApiModelProperty(value = "发动机号", required = true)
    private String engineNumber;

    /**
     * 车辆品牌型号
     */
    @ApiModelProperty(value = "车辆品牌型号", required = true)
    private String modelName;

    /**
     * 核定载人数
     */
    @ApiModelProperty(value = "核定载人数", required = true)
    private Integer seatCount;

    /**
     * 使用性质 见附件码表
     */
    @ApiModelProperty(value = "使用性质", required = true)
    private String usingNature;

    /**
     * 使用性质展示文本
     */
    @ApiModelProperty(value = "使用性质展示文本")
    private String usingNatureText;

    /**
     * 排气量
     */
    @ApiModelProperty(value = "排气量", required = true)
    private Double exhaustScale;

    /**
     * 核定载质量
     */
    @ApiModelProperty(value = "核定载质量", required = true)
    private Double carTonnage;

    /**
     * 整备质量
     */
    @ApiModelProperty(value = "整备质量", required = true)
    private Double wholeWeight;

    /**
     * 注册日期
     */
    @ApiModelProperty(value = "注册日期", required = true)
    private String regDate;

    /**
     * 发证日期
     */
    @ApiModelProperty(value = "发证日期", required = true)
    private String certDate;

    /**
     * 新车标致 0-非新车 1-新车
     */
    @ApiModelProperty(value = "新车标致", required = true)
    private String newCarMark;

    /**
     * 过户标志 0-非过户 1-过户
     */
    @ApiModelProperty(value = "过户标志", required = true)
    private String transferMark;

    /**
     * 可能为过户标志 0-非过户 1-过户
     */
    @ApiModelProperty(value = "可能为过户标志", required = true)
    private String transferMarkPossible;


    @ApiModelProperty(value = "省", required = true)
    private String province;
    @ApiModelProperty(value = "省Name", required = true)
    private String provinceName;
    @ApiModelProperty(value = "省编号", required = true)
    private String provinceNumber;

    @ApiModelProperty(value = "市", required = true)
    private String city;
    @ApiModelProperty(value = "市Name", required = true)
    private String cityName;
    @ApiModelProperty(value = "市编号", required = true)
    private String cityNumber;
    @ApiModelProperty(value = "车主证件号码", required = false)
    private String ownerIdentify;
    @ApiModelProperty(value = "车主证件类型", required = false)
    private String ownerIdentifyType;

    /**
     * 过户日期 yyyy-MM-dd
     */
    @ApiModelProperty(value = "过户日期 yyyy-MM-dd", required = true)
    private String transferDate;
    /**
     * 使用年限
     */
    @ApiModelProperty(value = "使用年限", required = true)
    private Integer useYears;

    @ApiModelProperty(value = "修改时间", required = true)
    private LocalDateTime mdfTm;
    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime creTm;

    @ApiModelProperty(value = "车辆基本信息展示文本 车型编号| 车型名称| 排量|座位数| 年份款型| 能源类型| 新车购置价")
    private String carShowText;

    /**
     * 营运性质 0-非营运 1-营运
     */
    @ApiModelProperty(value = "营运性质 0-非营运 1-营运")
    private String operationNature;

    /**
     * 车主性质
     * */
    @ApiModelProperty(value = "车主性质")
    private String ownerNature;

    public String getOwnerNature() {
        return ownerNature;
    }

    public void setOwnerNature(String ownerNature) {
        this.ownerNature = ownerNature;
    }

    public String getOperationNature() {
        return operationNature;
    }

    public void setOperationNature(String operationNature) {
        this.operationNature = operationNature;
    }

    public LocalDateTime getMdfTm() {
        return mdfTm;
    }

    public void setMdfTm(LocalDateTime mdfTm) {
        this.mdfTm = mdfTm;
    }

    public LocalDateTime getCreTm() {
        return creTm;
    }

    public void setCreTm(LocalDateTime creTm) {
        this.creTm = creTm;
    }

    public String getOwnerIdentify() {
        return ownerIdentify;
    }

    public void setOwnerIdentify(String ownerIdentify) {
        this.ownerIdentify = ownerIdentify;
    }

    public String getOwnerIdentifyType() {
        return ownerIdentifyType;
    }

    public void setOwnerIdentifyType(String ownerIdentifyType) {
        this.ownerIdentifyType = ownerIdentifyType;
    }

    public Integer getUseYears() {
        return useYears;
    }

    public void setUseYears(Integer useYears) {
        this.useYears = useYears;
    }

    public String getTransferDate() {
        return transferDate;
    }

    public void setTransferDate(String transferDate) {
        this.transferDate = transferDate;
    }

    public String getNewCarMark() {
        return newCarMark;
    }

    public void setNewCarMark(String newCarMark) {
        this.newCarMark = newCarMark;
    }

    public String getTransferMark() {
        return transferMark;
    }

    public void setTransferMark(String transferMark) {
        this.transferMark = transferMark;
    }

    public String getTransferMarkPossible() {
        return transferMarkPossible;
    }

    public void setTransferMarkPossible(String transferMarkPossible) {
        this.transferMarkPossible = transferMarkPossible;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getProvinceNumber() {
        return provinceNumber;
    }

    public void setProvinceNumber(String provinceNumber) {
        this.provinceNumber = provinceNumber;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCityNumber() {
        return cityNumber;
    }

    public void setCityNumber(String cityNumber) {
        this.cityNumber = cityNumber;
    }

    public String getPlateNumber() {
        return plateNumber;
    }

    public void setPlateNumber(String plateNumber) {
        this.plateNumber = plateNumber;
    }

    public String getCarOwner() {
        return carOwner;
    }

    public void setCarOwner(String carOwner) {
        this.carOwner = carOwner;
    }

    public String getFrameNumber() {
        return frameNumber;
    }

    public void setFrameNumber(String frameNumber) {
        this.frameNumber = frameNumber;
    }

    public String getEngineNumber() {
        return engineNumber;
    }

    public void setEngineNumber(String engineNumber) {
        this.engineNumber = engineNumber;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public Integer getSeatCount() {
        return seatCount;
    }

    public void setSeatCount(Integer seatCount) {
        this.seatCount = seatCount;
    }

    public String getUsingNature() {
        return usingNature;
    }

    public void setUsingNature(String usingNature) {
        this.usingNature = usingNature;
    }

    public Double getExhaustScale() {
        return exhaustScale;
    }

    public void setExhaustScale(Double exhaustScale) {
        this.exhaustScale = exhaustScale;
    }

    public Double getCarTonnage() {
        return carTonnage;
    }

    public void setCarTonnage(Double carTonnage) {
        this.carTonnage = carTonnage;
    }

    public Double getWholeWeight() {
        return wholeWeight;
    }

    public void setWholeWeight(Double wholeWeight) {
        this.wholeWeight = wholeWeight;
    }

    public String getRegDate() {
        return regDate;
    }

    public void setRegDate(String regDate) {
        this.regDate = regDate;
    }

    public String getCertDate() {
        return certDate;
    }

    public void setCertDate(String certDate) {
        this.certDate = certDate;
    }

    public String getCarShowText() {
        return carShowText;
    }

    public void setCarShowText(String carShowText) {
        this.carShowText = carShowText;
    }

    public String getUsingNatureText() {
        return usingNatureText;
    }

    public void setUsingNatureText(String usingNatureText) {
        this.usingNatureText = usingNatureText;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
