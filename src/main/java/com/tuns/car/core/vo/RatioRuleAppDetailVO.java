package com.tuns.car.core.vo;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * app自主定价系数详情返回
 * @ClassName RatioRuleAppDetailVO
 * @Description app自主定价系数详情返回 
 * <AUTHOR>
 * @Date 2021/10/20 11:38:48 
 * @Version 1.0
 */
@Data
public class RatioRuleAppDetailVO implements Serializable {
    
    /**
     * 
     */
    private static final long serialVersionUID = 3617064523487393693L;

    @ApiModelProperty(value = "自主定价系数最小值")
    private BigDecimal ratioMin;
    
    @ApiModelProperty(value = "自主定价系数最大值")
    private BigDecimal ratioMax;
    
    @ApiModelProperty(value = "自主定价系数推荐值")
    private BigDecimal ruleRatio;

    @Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}