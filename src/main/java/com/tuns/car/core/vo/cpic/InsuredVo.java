package com.tuns.car.core.vo.cpic;

import com.alibaba.fastjson.JSON;

import java.io.Serializable;

/**
 * @ClassName InsuredVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/01/26 09:07:08
 * @Version 1.0
 */
public class InsuredVo implements Serializable {
    private String address;
    private String certificateCode;
    private String certificateType;
    private Long id;
    private String isVerifyTruth;
    private String issueInvoice;
    private String name;
    private String otherInfo;
    private String partyType;
    private String relationship;
    private String sameAsHolder;
    private String secretCertificateCode;
    private String secretHolderTelphone;
    private String stCertificateValidity;
    private String secretMail;
    private String secretMobile;
    private String telephone;

    public String getAddress() {
        return address;
    }

    public InsuredVo setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getCertificateCode() {
        return certificateCode;
    }

    public InsuredVo setCertificateCode(String certificateCode) {
        this.certificateCode = certificateCode;
        return this;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public InsuredVo setCertificateType(String certificateType) {
        this.certificateType = certificateType;
        return this;
    }

    public Long getId() {
        return id;
    }

    public InsuredVo setId(Long id) {
        this.id = id;
        return this;
    }

    public String getIsVerifyTruth() {
        return isVerifyTruth;
    }

    public InsuredVo setIsVerifyTruth(String isVerifyTruth) {
        this.isVerifyTruth = isVerifyTruth;
        return this;
    }

    public String getIssueInvoice() {
        return issueInvoice;
    }

    public InsuredVo setIssueInvoice(String issueInvoice) {
        this.issueInvoice = issueInvoice;
        return this;
    }

    public String getName() {
        return name;
    }

    public InsuredVo setName(String name) {
        this.name = name;
        return this;
    }

    public String getOtherInfo() {
        return otherInfo;
    }

    public InsuredVo setOtherInfo(String otherInfo) {
        this.otherInfo = otherInfo;
        return this;
    }

    public String getPartyType() {
        return partyType;
    }

    public InsuredVo setPartyType(String partyType) {
        this.partyType = partyType;
        return this;
    }

    public String getRelationship() {
        return relationship;
    }

    public InsuredVo setRelationship(String relationship) {
        this.relationship = relationship;
        return this;
    }

    public String getSameAsHolder() {
        return sameAsHolder;
    }

    public InsuredVo setSameAsHolder(String sameAsHolder) {
        this.sameAsHolder = sameAsHolder;
        return this;
    }

    public String getSecretCertificateCode() {
        return secretCertificateCode;
    }

    public InsuredVo setSecretCertificateCode(String secretCertificateCode) {
        this.secretCertificateCode = secretCertificateCode;
        return this;
    }

    public String getSecretHolderTelphone() {
        return secretHolderTelphone;
    }

    public InsuredVo setSecretHolderTelphone(String secretHolderTelphone) {
        this.secretHolderTelphone = secretHolderTelphone;
        return this;
    }

    public String getSecretMail() {
        return secretMail;
    }

    public InsuredVo setSecretMail(String secretMail) {
        this.secretMail = secretMail;
        return this;
    }

    public String getSecretMobile() {
        return secretMobile;
    }

    public InsuredVo setSecretMobile(String secretMobile) {
        this.secretMobile = secretMobile;
        return this;
    }

    public String getStCertificateValidity() {
        return stCertificateValidity;
    }

    public void setStCertificateValidity(String stCertificateValidity) {
        this.stCertificateValidity = stCertificateValidity;
    }

    public String getTelephone() {
        return telephone;
    }

    public InsuredVo setTelephone(String telephone) {
        this.telephone = telephone;
        return this;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
