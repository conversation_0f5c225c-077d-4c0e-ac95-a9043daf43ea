package com.tuns.car.core.vo.car;

import cn.hutool.core.bean.BeanUtil;
import com.tuns.car.core.dto.vi.ViInsChargingPileInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 车险充电桩信息
 *
 * <AUTHOR>
 * @since 2021/12/21
 */
@ApiModel
@Data
public class ViInsChargingPileMiniVO implements Serializable {

    private static final long serialVersionUID = 8040047551014148202L;
    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("充电桩型号")
    private String chargingModel;

    @ApiModelProperty("充电桩编码")
    private String chargingCode;

    @ApiModelProperty("保额")
    private BigDecimal amount;

    @ApiModelProperty(value = "险种代码")
    private String kindCode;

    public ViInsChargingPileMiniVO(ViInsChargingPileInfoVO viInsChargingPileInfoVO) {
        BeanUtil.copyProperties(viInsChargingPileInfoVO, this);
    }

}
