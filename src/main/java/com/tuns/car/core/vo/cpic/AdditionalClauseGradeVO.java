package com.tuns.car.core.vo.cpic;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/26 9:00
 */
public class AdditionalClauseGradeVO {

    @ApiModelProperty(value = "等级ID", hidden = true)
    private String additionClauseGradeId;

    @ApiModelProperty(value = "服务条款主键ID", hidden = true)
    private String additionClauseId;

    @ApiModelProperty("等级（1：档位 2：公里数 3：款型）")
    private Integer grade;

    @ApiModelProperty("等级对应的值")
    private String gradeValue;

    @ApiModelProperty("服务条款次数")
    private List<AdditionalClauseDetailVO> additionalClauseDetails;

    public Integer getGrade() {
        return grade;
    }

    public AdditionalClauseGradeVO setGrade(Integer grade) {
        this.grade = grade;
        return this;
    }

    public String getGradeValue() {
        return gradeValue;
    }

    public AdditionalClauseGradeVO setGradeValue(String gradeValue) {
        this.gradeValue = gradeValue;
        return this;
    }

    public String getAdditionClauseGradeId() {
        return additionClauseGradeId;
    }

    public AdditionalClauseGradeVO setAdditionClauseGradeId(String additionClauseGradeId) {
        this.additionClauseGradeId = additionClauseGradeId;
        return this;
    }

    public String getAdditionClauseId() {
        return additionClauseId;
    }

    public AdditionalClauseGradeVO setAdditionClauseId(String additionClauseId) {
        this.additionClauseId = additionClauseId;
        return this;
    }

    public List<AdditionalClauseDetailVO> getAdditionalClauseDetails() {
        return additionalClauseDetails;
    }

    public AdditionalClauseGradeVO setAdditionalClauseDetails(List<AdditionalClauseDetailVO> additionalClauseDetails) {
        this.additionalClauseDetails = additionalClauseDetails;
        return this;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
