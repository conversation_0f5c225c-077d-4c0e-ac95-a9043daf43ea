package com.tuns.car.core.vo.car;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.tuns.car.core.constant.ViAttachTypeEnum;
import com.tuns.car.core.dto.carprocess.premium.EpolicyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;

/**
 *
 */
@Slf4j
@ApiModel
public class ViInsEPolicyRecordVO implements Serializable {

    private static final long serialVersionUID = -3001488439605337429L;

    /**
     * 电子保单类型 1交强保单  2商业保单 3非车保单
     */
    @ApiModelProperty("电子保单类型")
    private ViAttachTypeEnum ePolicyType;

    /**
     * 文件下载地址
     */
    @ApiModelProperty("文件下载地址")
    private String ePolicyUrl;
    /**
     * oss短链接key
     */
    @ApiModelProperty("oss短链接key")
    private String ePolicyUrlKey;

    /**
     * 电子保单名称
     */
    @ApiModelProperty("电子保单名称")
    private String name;

    /***
     * 构造函数，必须
     */
    public ViInsEPolicyRecordVO() {
    }

    public ViInsEPolicyRecordVO(EpolicyDTO epolicyDTO) {
        this.setePolicyType(epolicyDTO.getType());
        this.setePolicyUrl(epolicyDTO.getOwnUrl());
        this.ePolicyUrlKey = epolicyDTO.getOwnOssKey();
        if (StrUtil.isEmpty(epolicyDTO.getFileName())) {
            if (StrUtil.isNotEmpty(epolicyDTO.getOwnUrl())) {
                String temp = epolicyDTO.getOwnUrl().substring(epolicyDTO.getOwnUrl().lastIndexOf("/") + 1);
                String name = temp.substring(0, temp.indexOf("."));
                try {
                    this.setName(URLDecoder.decode(name,"UTF-8"));
                } catch (UnsupportedEncodingException e) {
                    log.error("字符串解码失败 name={}",name,e);
                    throw new RuntimeException(e);
                }
            }
        } else {
            this.setName(epolicyDTO.getFileName());
        }
    }

    public ViAttachTypeEnum getePolicyType() {
        return ePolicyType;
    }

    public void setePolicyType(ViAttachTypeEnum ePolicyType) {
        this.ePolicyType = ePolicyType;
    }

    public String getePolicyUrl() {
        return ePolicyUrl;
    }

    public void setePolicyUrl(String ePolicyUrl) {
        this.ePolicyUrl = ePolicyUrl;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getePolicyUrlKey() {
        return ePolicyUrlKey;
    }

    public void setePolicyUrlKey(String ePolicyUrlKey) {
        this.ePolicyUrlKey = ePolicyUrlKey;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
