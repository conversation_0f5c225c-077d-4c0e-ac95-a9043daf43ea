package com.tuns.car.core.vo.car;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName HistoryCarInfoVO
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/5 17:35
 * @Version 1.0
 */
@Data
@ApiModel(value = "HistoryCarInfoVO", description = "历史车辆")
public class HistoryCarInfoVO implements Serializable {

    private static final long serialVersionUID = -4339237274038843424L;
    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号", required = true)
    private String plateNumber;
    /**
     * 批次id
     */
    @ApiModelProperty(value = "批次id", required = true)
    private String policyBatchId;
    /**
     * 车主姓名
     */
    @ApiModelProperty(value = "车主姓名", required = true)
    private String carOwner;
    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号", required = true)
    private String frameNumber;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
