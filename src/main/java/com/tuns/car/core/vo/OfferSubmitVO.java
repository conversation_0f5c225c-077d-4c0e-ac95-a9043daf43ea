package com.tuns.car.core.vo;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName OfferSubmitVO
 * @Description APP车险补传附件资料提交返回数据类
 * <AUTHOR> yi
 * @Date 2020/6/28 10:51
 * @Version 1.0
 */
@Data
public class OfferSubmitVO implements Serializable {

  private static final long serialVersionUID = -3261526622665411400L;

  /**
   * 报价批次id
   */
  @ApiModelProperty(value = "报价批次id", required = true)
  private Long policyBatchId;

  @Override
  public String toString() {
    return JSON.toJSONString(this);
  }
}
