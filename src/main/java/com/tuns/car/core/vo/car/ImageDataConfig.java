package com.tuns.car.core.vo.car;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.constant.ImageDataEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName ImageDataConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/17 15:08
 * @Version 1.0
 */
@Data
public class ImageDataConfig {

    /**
     * 渠道ID
     */
    @ApiModelProperty(value = "渠道ID")
    private Integer chanDetailId;

    /**
     * 是否必传1是0否
     */
    @ApiModelProperty(value = "是否必传1是0否")
    private String whetherWillPass;

    /**
     * 影像资料
     */
    @ApiModelProperty(value = "影像资料")
    private ImageDataEnum imageData;
    /**
     * 影像资料
     */
    @ApiModelProperty(value = "影像资料类型")
    private String attachType;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
