package com.tuns.car.core.vo.car;

import com.tuns.car.core.constant.CarTypeEnum;
import com.tuns.car.core.constant.TractorTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 车型查询返回类
 *
 * <AUTHOR>
 */
@Data
public class CarInfoVO implements Serializable {

    private static final long serialVersionUID = 3424828270095982398L;
    @ApiModelProperty(value = "车型系列号")
    private String serialNo;

    @ApiModelProperty(value = "显示文本")
    private String showText;

    @ApiModelProperty(value = "发动机号码")
    private String engineNumber;

    @ApiModelProperty(value = "车架号")
    private String frameNumber;

    @ApiModelProperty(value = "新车标致 0-非新车 1-新车")
    private String newCarMark;

    @ApiModelProperty(value = "过户日期 yyyy-MM-dd")
    private String transferDate;

    @ApiModelProperty(value = "车型代码")
    private String modelCode;


    @ApiModelProperty(value = "车型代码Old")
    private String modelCodeOld;

    @ApiModelProperty(value = "新车标识")
    private String plateNumberMark;

    @ApiModelProperty(value = "车主证件号码")
    private String ownerIdentify;

    @ApiModelProperty(value = "品牌名称", required = true)
    private String modelName;

    @ApiModelProperty(value = "车款名称")
    private String carName;
    /**
     * 新车购置价
     */
    @ApiModelProperty(value = "新车购置价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "车辆类型描述")
    private String carStyleNote;

    @ApiModelProperty(value = "核定载客")
    private Integer seatCount;

    @ApiModelProperty(value = "默认车辆类型")
    private CarTypeEnum carTypeCode;

    @ApiModelProperty(value = "车辆吨位/核定载质量(吨)")
    private BigDecimal carTonnage;

    @ApiModelProperty(value = "能源类型")
    private String fuelType;

    @ApiModelProperty(value = "能源类型名称")
    private String fuelName;

    @ApiModelProperty(value = "排量(ML)")
    private Integer exhaustScale;

    @ApiModelProperty(value = "功率(瓦)")
    private BigDecimal power;

    @ApiModelProperty(value = "车系名称")
    private String familyName;

    @ApiModelProperty(value = "减税车型标志 0-正常 04-减免税 05-减税")
    private String taxCutsMark;

    @ApiModelProperty(value = "减税比例 0~1")
    private BigDecimal taxCutProportion;

    @ApiModelProperty(value = "年款 yyyyMM/yyyy")
    private String marketDate;

    @ApiModelProperty(value = "公告型号")
    private String noticeType;

    @ApiModelProperty(value = "行业车型编码")
    private String prfsModelCode;

    @ApiModelProperty(value = "整备质量")
    private Integer wholeWeight;

    @ApiModelProperty(value = "车辆产地 1-进口 2-国产 3-合资")
    private String carOrigin;

    @ApiModelProperty(value = "车型来源公司ID")
    private String carInfoCompany;

    @ApiModelProperty(value = "注册日期 yyyy-MM-dd")
    private String regDate;

    @ApiModelProperty(value = "发证日期 yyyy-MM-dd")
    private String certDate;
    @ApiModelProperty(value = "扩展字段")
    private String extendField;
    @ApiModelProperty("车辆实际价值")
    private BigDecimal actualPrice;

    @ApiModelProperty("保险公司ID")
    private String companyId;

    @ApiModelProperty("保险公司code")
    private String companyCode;
    /**
     * 续航里程
     */
    @ApiModelProperty("续航里程")
    private String pureRange;

    @ApiModelProperty(value = "精准查询")
    private Boolean exactQuery;

    private String brandName;

    private String deptName;

    private String tradeName;

    private String searchCode;
    /**
     * 平安 区分jy和circ
     */
    private String type;
    /**
     * 是否需要再次确认
     */
    private boolean isAgainConfirm;

    @ApiModelProperty("车辆类型")
    private String vehicleType;
    /**
     * 牵引总质量
     */
    @ApiModelProperty("牵引总质量")
    private BigDecimal totalTractionMass;

    /**
     * 是否牵引车 0:否,1:是
     */
    @ApiModelProperty("是否牵引车 0:否,1:是")
    private String tractorMark;

    @ApiModelProperty("牵引车类型")
    private TractorTypeEnum tractorType;

    @ApiModelProperty("车型数据提示信息")
    private String tips;

    /**
     * 众城车辆查询流程号 - 自动修复车型使用
     */
    private String zcTradeNo;

}
