package com.tuns.car.core.vo.car;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @ClassName OrderCarInfoVO
 * @Description 车险订单详情查询返回 车险信息 数据类
 * <AUTHOR> yi
 * @Date 2020/6/9 19:49
 * @Version 1.0
 */
public class OrderCarInfoVO implements Serializable {

    private static final long serialVersionUID = 2855606413445818586L;

    /**
     * 车牌号码
     */
    @ApiModelProperty(value = "车牌号码", required = true)
    private String plateNumber;

    /**
     * 车辆识别代号
     */
    @ApiModelProperty(value = "车辆识别代号", required = true)
    private String frameNumber;

    /**
     * 发动机号
     */
    @ApiModelProperty(value = "发动机号", required = true)
    private String engineNumber;

    /**
     * 车辆品牌型号
     */
    @ApiModelProperty(value = "车辆品牌型号", required = true)
    private String modelName;

    /**
     * 核定载人数
     */
    @ApiModelProperty(value = "核定载人数", required = true)
    private Integer seatCount;

    /**
     * 排气量
     */
    @ApiModelProperty(value = "排气量", required = true)
    private Double exhaustScale;

    /**
     * 核定载质量
     */
    @ApiModelProperty(value = "核定载质量", required = true)
    private Double carTonnage;

    /**
     * 整备质量
     */
    @ApiModelProperty(value = "整备质量", required = true)
    private Integer wholeWeight;


    /**
     * 注册日期
     */
    @ApiModelProperty(value = "注册日期", required = true)
    private String regDate;

    /**
     * 发证日期
     */
    @ApiModelProperty(value = "发证日期", required = true)
    private String certDate;

    /**
     * 使用性质 见附件码表
     */
    @ApiModelProperty(value = "使用性质", required = true)
    private String usingNature;

    /**
     * 过户标识
     */
    @ApiModelProperty(value = "过户标识", required = true)
    private String transferMark;

    /**
     * 营运性质 0-非营运 1-营运
     */
    @ApiModelProperty(value = "营运性质 0-非营运 1-营运")
    private String operationNature;

    @ApiModelProperty(value = "行驶证厂牌型号")
    private String licenseBrand;

    public String getOperationNature() {
        return operationNature;
    }

    public void setOperationNature(String operationNature) {
        this.operationNature = operationNature;
    }

    public String getPlateNumber() {
        return plateNumber;
    }

    public void setPlateNumber(String plateNumber) {
        this.plateNumber = plateNumber;
    }

    public String getFrameNumber() {
        return frameNumber;
    }

    public void setFrameNumber(String frameNumber) {
        this.frameNumber = frameNumber;
    }

    public String getEngineNumber() {
        return engineNumber;
    }

    public void setEngineNumber(String engineNumber) {
        this.engineNumber = engineNumber;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public Integer getSeatCount() {
        return seatCount;
    }

    public void setSeatCount(Integer seatCount) {
        this.seatCount = seatCount;
    }

    public Double getExhaustScale() {
        return exhaustScale;
    }

    public void setExhaustScale(Double exhaustScale) {
        this.exhaustScale = exhaustScale;
    }

    public Double getCarTonnage() {
        return carTonnage;
    }

    public void setCarTonnage(Double carTonnage) {
        this.carTonnage = carTonnage;
    }

    public Integer getWholeWeight() {
        return wholeWeight;
    }

    public void setWholeWeight(Integer wholeWeight) {
        this.wholeWeight = wholeWeight;
    }

    public String getRegDate() {
        return regDate;
    }

    public void setRegDate(String regDate) {
        this.regDate = regDate;
    }

    public String getCertDate() {
        return certDate;
    }

    public void setCertDate(String certDate) {
        this.certDate = certDate;
    }

    public String getUsingNature() {
        return usingNature;
    }

    public void setUsingNature(String usingNature) {
        this.usingNature = usingNature;
    }

    public String getTransferMark() {
        return transferMark;
    }

    public OrderCarInfoVO setTransferMark(String transferMark) {
        this.transferMark = transferMark;
        return this;
    }

    public String getLicenseBrand() {
        return licenseBrand;
    }

    public void setLicenseBrand(String licenseBrand) {
        this.licenseBrand = licenseBrand;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
