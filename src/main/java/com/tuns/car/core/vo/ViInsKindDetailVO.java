package com.tuns.car.core.vo;

import com.tuns.core.boot.constant.YesNoNumberEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/11/08
 **/
@ApiModel
@Data
public class ViInsKindDetailVO extends BaseEntity {

    @ApiModelProperty(value = "险别代码")
    private String kindCode;

    /**
     * 险别名称
     */
    @ApiModelProperty(value = "险别名称")
    private String kindName;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer quantity;
    /**
     * 单位保额
     */
    @ApiModelProperty(value = "单位保额")
    private BigDecimal unitAmount;
    /**
     * 总保额
     */
    @ApiModelProperty(value = "总保额")
    private BigDecimal amount;

    /**
     * 保费
     */
    @ApiModelProperty(value = "保费")
    private BigDecimal premium;

    /**
     * 是否商业险
     */
    @ApiModelProperty(value = "是否商业险")
    private String businessMark;

    /**
     * 是否共享保额
     */
    @ApiModelProperty(value = "是否共享保额")
    private YesNoNumberEnum isSharedAmount;

}
