package com.tuns.car.core.vo;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName OrderInfoVO
 * @Description 车险订单详情查询返回 订单信息 数据类
 * <AUTHOR> yi
 * @Date 2020/6/9 19:46
 * @Version 1.0
 */
@Data
public class OrderInfoVO implements Serializable {

    private static final long serialVersionUID = 2707042586106513902L;


    /**
     * 订单业务id
     */
    @ApiModelProperty(value = "订单业务id", required = true)
    private String orderUuid;

    /**
     * 订单类型 0人工,1自动
     */
    @ApiModelProperty(value = "订单类型", required = true)
    private String autoMark;

    /**
     * 订单状态  C1等待支付,C1-1已支付待确认,C1-9支付成功未承保,C2支付成功已承保,C3支付失败,C4订单取消,D1保单自取,D2保单配送,D3订单完成,D4订单完成待评价,D5订单完成已评价
     */
    @ApiModelProperty(value = "订单状态  A1等待报价,A2等待报价（处理中）,A2-1等待报价（需补传资料）,A3报价成功,A4报价失败（等待补传资料）,A5报价失败（需调整投保方案）,A6报价失败,A7报价取消,B1等待核保,B1-1等待核保（需补传资料）,B2核保通过，已生成订单,B3核保失败（等待补传资料）,B4核保失败（需调整投保方案）,B5核保失败,B6核保取消,C1等待支付,C1-1已支付待确认,C1-9支付成功未承保,C2支付成功已承保,C3支付失败,C4订单取消,D1保单自取,D2保单配送,D3订单完成,D4订单完成待评价,D5订单完成已评价", required = true)
    private String subStatus;

    /**
     * 订单状态(描述)
     */
    @ApiModelProperty(value = "订单状态(描述)", required = true)
    private String subStatusDescription;

    /**
     * 订单号码
     */
    @ApiModelProperty(value = "订单号码", required = true)
    private String orderNo;

    /**
     * 订单创建时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订单创建时间", required = true)
    private LocalDateTime creTm;

    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "订单总金额", required = true)
    private BigDecimal premium;


    /**
     * 批次id
     */
    @ApiModelProperty(value = "批次id", required = true)
    private String policyBatchId;

    /**
     * 省份编码
     */
    private String provinceNumber;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityNumber;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 支付二维码刷新状态 0未刷新,1刷新中,2已刷新
     */
    private String flushType;

    private Long quoteId;

    /**
     * 保费计算识别码/保险公司订单号码
     */
    private String insPremiumNumber;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因", required = true)
    private String failedMsg;

    private String companyId;
    /**
     * 支付流水号
     */
    private String paySerialNumber;

    //缴费人姓名
    private String payerName;
    //缴费人类型
    private String payerCertificateType;
    //缴费人证码
    private String payerCertificateCode;
    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
