package com.tuns.car.core.vo.car;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.entity.ViInsCarInfTp;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import com.tuns.car.core.entity.ViQuoteSpecialAgreement;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName CompareInsureVO
 * @Description 车险立即投保(确认投保)返回类
 * <AUTHOR> yi
 * @Date 2021/1/26 15:06
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class CarInsureVO implements Serializable {

    private static final long serialVersionUID = -6874214893179189496L;

    /**
     * 报价批次id
     */
    @ApiModelProperty(value = "报价批次id,报价单号", required = true)
    private String policyBatchId;

    /**
     * 订单业务id
     */
    @ApiModelProperty(value = "订单业务id 核保通过后必有", required = true)
    private String orderUuid;

    /**
     * 状态 B1等待核保,B1-1 等待核保（需补传资料）,B2核保通过，已生成订单,B3核保失败（等待补传资料）,
     * B4核保失败（需调整投保方案）,B5核保失败
     */
    @ApiModelProperty(value = "核保状态 B1等待核保,B1-1 等待核保（需补传资料）,B2核保通过，已生成订单,B3核保失败（等待补传资料）,B4核保失败（需调整投保方案）,B5核保失败", required = true)
    private String subStatus;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因", required = true)
    private String failedMsg;

    /**
     * 父状态
     */
    private String insuredStatus;
    /**
     * 弹框标识 1是 0否  （太平洋专用）
     */
    private String popFlag;

    /**
     * 标识是否需要取消上笔投保单
     */
    private String repeatProposalNo;

    /**
     * 报价单信息
     */
    private List<ViInsPlcyInfTp> plcyInfList;

    /**
     * 报价批次id
     */
    @ApiModelProperty(value = "渠道", required = true)
    private String chanType;

    /**
     * 报价批次id
     */
    @ApiModelProperty(value = "公司id", required = true)
    private String companyId;

    /**
     * 非车投保单号 <br />
     * 该字段已废弃，需要使用noCarVOS集合传递 car_3.3.118
     */
    @Deprecated
    private String noCarProposalNumber;
    /**
     * 非车投保信息
     */
    private List<CarInsureNoCarVO> noCarVOS;

    /**
     * 非车订单表的insOrderNo  保险公司提供的订单号 <br />
     * 该字段已废弃，需要使用noCarVOS集合传递 car_3.3.118
     */
    @Deprecated
    private String insOrderNo;


    /**
     * 是否转人工核保标识：Y是 N否
     */
    private String changeManualMark;

    /**
     * 特约信息
     */
    private List<ViQuoteSpecialAgreement> specialAgreementDtoList;

    /**
     * 车辆信息
     */
    private ViInsCarInfTp carInfTp;

    /**
     * 投保人性质 nature 1-个人 2-机关 3-企业
     *
     * @return
     */
    private String holderNature;

    /**
     * 是否申请报批
     *
     * @return
     */
    private boolean applyApproval = false;

    /**
     * OCR影像比对结果
     *
     * @return
     */
    private List<CarQuoteApplyRecordComparisonResultDTO> ocrData;

    /**
     * 投保申请状态
     */
    private String applyStatus;

    /**
     * OCR影像比对结果JSON
     */
    @ApiModelProperty(value = "OCR影像比对结果JSON")
    private String comparisonResultJson;

    /**
     * 申请记录ID
     */
    @ApiModelProperty(value = "申请记录ID")
    private String applyRecordId;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
