package com.tuns.car.core.vo.share;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName OrderInfoShareVO
 * @Description 车险自动订单分享查询返回数据类
 * <AUTHOR> yi
 * @Date 2020/6/16 19:48
 * @Version 1.0
 */
@Data
public class OrderInfoShareVO implements Serializable {

  private static final long serialVersionUID = -2159755751024895013L;

  /**
   * 订单保费
   */
  private BigDecimal prem;

  /**
   * 订单状态
   */
  private String status;

  /**
   * 车牌号码
   */
  private String plateNumber;

  /**
   * 保险公司id
   */
  private String companyId;

  /**
   * 公司logo图片
   */
  private String companyLogo;

  /**
   * 自助报价标识 1自助,0人工
   */
  private String autoMark;


  @Override
  public String toString() {
    return JSON.toJSONString(this);
  }
}
