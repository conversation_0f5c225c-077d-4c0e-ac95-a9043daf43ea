package com.tuns.car.core.vo.intelligent;

import com.tuns.car.core.dto.carprocess.premium.request.BaseInfo;
import com.tuns.car.core.dto.carprocess.premium.request.CarInfo;
import com.tuns.car.core.dto.carprocess.premium.request.ItemKind;
import com.tuns.car.core.dto.carprocess.premium.request.PersonInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-23 10:23
 */
@Data
public class CarSortMatchVO {

    @NotNull
    @ApiModelProperty(value = "基础信息", required = true)
    private BaseInfo baseInfo;

    @Valid
    @NotNull
    @ApiModelProperty(value = "车辆信息", required = true)
    private CarInfo carInfo;

    @NotNull
    @ApiModelProperty(value = "险种信息", required = true)
    private List<ItemKind> itemKinds;

    @NotNull
    @ApiModelProperty(value = "关系人信息", required = true)
    private List<PersonInfo> personInfos;

}
