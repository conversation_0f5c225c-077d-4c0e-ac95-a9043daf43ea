package com.tuns.car.core.vo.car;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName OfferAttachGroupVO
 * @Description APP车险附件资料查询返回数据类
 * <AUTHOR> yi
 * @Date 2020/6/24 17:51
 * @Version 1.0
 */
@Data
public class OfferAttachGroupVO implements Serializable {

  private static final long serialVersionUID = 6891662126106211876L;

  /**
   * 车辆行驶证照片
   */
  @ApiModelProperty(value = "车辆行驶证照片", required = true)
  private List<OfferAttachVO> driving;

  /**
   * 车主身份证照片
   */
  @ApiModelProperty(value = "车主身份证照片", required = true)
  private List<OfferAttachVO> owner;

  /**
   * 投保人身份证照片
   */
  @ApiModelProperty(value = "投保人身份证照片", required = true)
  private List<OfferAttachVO> holder;

  /**
   * 被保人身份证照片
   */
  @ApiModelProperty(value = "被保人身份证照片", required = true)
  private List<OfferAttachVO> insured;

  /**
   * 其它
   */
  @ApiModelProperty(value = "其它", required = true)
  private List<OfferAttachVO> rests;


  @Override
  public String toString() {
    return JSON.toJSONString(this);
  }
}
