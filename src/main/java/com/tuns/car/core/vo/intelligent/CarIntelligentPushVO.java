package com.tuns.car.core.vo.intelligent;

import com.tuns.car.core.dto.carprocess.premium.request.BaseInfo;
import com.tuns.car.core.dto.carprocess.premium.request.CarInfo;
import com.tuns.car.core.dto.carprocess.premium.request.ItemKind;
import com.tuns.car.core.dto.carprocess.premium.request.PersonInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 车险智能推送请求参数
 *
 * <AUTHOR>
 * @since 2024-06-17 14:50
 **/
@Data
public class CarIntelligentPushVO {

    @NotNull
    @ApiModelProperty(value = "渠道ID集合", required = true)
    private List<Integer> chanDetailIds;

    @NotNull
    @ApiModelProperty(value = "基础信息", required = true)
    private BaseInfo baseInfo;

    @Valid
    @NotNull
    @ApiModelProperty(value = "车辆信息", required = true)
    private CarInfo carInfo;

    @NotNull
    @ApiModelProperty(value = "险种信息", required = true)
    private List<ItemKind> itemKinds;

    @NotNull
    @ApiModelProperty(value = "关系人信息", required = true)
    private List<PersonInfo> personInfos;

    @NotNull
    @ApiModelProperty(value = "承保公司ID", required = true)
    private List<String> companyIdList;

    @ApiModelProperty(value = "用户id", hidden = true)
    private Integer userId;
}
