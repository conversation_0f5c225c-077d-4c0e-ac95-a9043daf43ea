package com.tuns.car.core.vo;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.dto.gi.CarGiOrderInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class EndDateVO {

    @ApiModelProperty(value = "发动机号码")
    private String engineNumber;

    @ApiModelProperty(value = "车架号")
    private String frameNumber;

    @ApiModelProperty(value = "车牌号码")
    private String plateNumber;

    @ApiModelProperty(value = "校验时间")
    private String checkDate;
    /**
     * 交强险
     */
    @ApiModelProperty(value = "交强险信息")
    private CompulsoryInsuranceVO compulsoryInsuranceVO;

    /**
     * 商业险
     */
    @ApiModelProperty(value = "商业险信息")
    private CommercialInsuranceVO commercialInsuranceVO;

    /**
     * 个险信息
     */
    private List<CarGiOrderInfoDTO> giInfo;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
