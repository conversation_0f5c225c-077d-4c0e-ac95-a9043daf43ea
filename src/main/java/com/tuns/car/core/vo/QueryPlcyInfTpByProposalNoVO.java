package com.tuns.car.core.vo;

import com.tuns.car.core.entity.ViInsPlcyInfTp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/9 14:11
 */
@Data
@NoArgsConstructor
public class QueryPlcyInfTpByProposalNoVO {

    /**
     * 保单批次ID
     */
    @ApiModelProperty(value = "保单批次ID")
    private Long policyBatchId;

    /**
     * 承保公司ID
     */
    @ApiModelProperty(value = "承保公司ID")
    private String companyId;

    /**
     * 渠道ID
     */
    @ApiModelProperty(value = "渠道ID")
    private String channelId;

    /**
     * 出单渠道id
     */
    @ApiModelProperty(value = "出单渠道id")
    private Integer chanDetailId;

    public QueryPlcyInfTpByProposalNoVO(ViInsPlcyInfTp plcyInfTp) {
        this.policyBatchId = plcyInfTp.getPolicyBatchId();
        this.companyId = plcyInfTp.getCompanyId();
        this.chanDetailId = plcyInfTp.getChanDetailId();
        this.channelId = plcyInfTp.getChannelId();
    }
}
