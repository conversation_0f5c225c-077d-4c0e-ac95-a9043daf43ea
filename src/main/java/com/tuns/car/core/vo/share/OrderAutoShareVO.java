package com.tuns.car.core.vo.share;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName OrderAutoShareVO
 * @Description 车险自动订单分享返回数据类
 * <AUTHOR> yi
 * @Date 2020/6/8 11:44
 * @Version 1.0
 */
@Data
public class OrderAutoShareVO implements Serializable {

  private static final long serialVersionUID = -8668338722411708009L;

  /**
   * 标题
   */
  @ApiModelProperty(value = "标题", required = true)
  private String title;

  /**
   * 获取分享页面url接口地址
   */
  @ApiModelProperty(value = "获取分享页面url接口地址", required = true)
  private String url;

  /**
   * 保险公司的LOGO图片
   */
  @ApiModelProperty(value = "保险公司的LOGO图片", required = true)
  private String logo;

  /**
   * 分享内容文案
   */
  @ApiModelProperty(value = "分享内容文案", required = true)
  private String content;


  @Override
  public String toString() {
    return JSON.toJSONString(this);
  }
}
