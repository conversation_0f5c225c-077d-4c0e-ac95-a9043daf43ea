package com.tuns.car.core.vo.pay;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.entity.PubPersInfTp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName OrderApplyVO
 * @Description 支付订单申请支付返回数据类
 * <AUTHOR> yi
 * @Date 2020/6/23 15:09:04
 * @Version 1.0
 */
@Data
public class OrderApplyVO implements Serializable {

    private static final long serialVersionUID = 5474854982103703269L;

    /**
     * 支付网址 新的支付网址 或 直接重定向到这里  https://wp.tuns.com.cn/mobile/payment?orderUuid=2ga45fjqewyedfd435
     */
    @ApiModelProperty(value = "支付网址", required = true)
    private String paymentUrl;
    /**
     * 投保人信息
     */
    @ApiModelProperty(value = "支付网址")
    private PubPersInfTp holder;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
