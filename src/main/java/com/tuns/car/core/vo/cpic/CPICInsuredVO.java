package com.tuns.car.core.vo.cpic;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ClassName InsuredVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/01/26 09:07:08
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class CPICInsuredVO implements Serializable {
    private String address;
    private String certificateCode;
    private String certificateType;
    private Long id;
    private String isVerifyTruth;
    private String issueInvoice;
    private String name;
    private String otherInfo;
    private String partyType;
    private String relationship;
    private String sameAsHolder;
    private String secretCertificateCode;
    private String secretHolderTelphone;
    private String stCertificateValidity;
    private String secretMail;
    private String secretMobile;
    private String telephone;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
