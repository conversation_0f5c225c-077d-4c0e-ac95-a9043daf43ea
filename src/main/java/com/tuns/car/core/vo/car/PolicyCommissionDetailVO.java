package com.tuns.car.core.vo.car;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 批单详情
 *
 * <AUTHOR>
 * @since 2023-04-23 16:01
 **/
@Data
public class PolicyCommissionDetailVO {

    @ApiModelProperty(value = "展示的佣金金额")
    private String commAmt;

    @ApiModelProperty(value = "展示的佣金率")
    private String commRate;

    @ApiModelProperty(value = "是否有单件 1有，0无")
    private String isHasSingle;

    @ApiModelProperty(value = "佣金率", hidden = true)
    private BigDecimal baseCommRate;

    @ApiModelProperty(value = "单率", hidden = true)
    private BigDecimal singleRate;

    @ApiModelProperty(value = "单件费", hidden = true)
    private BigDecimal singleFee;

}
