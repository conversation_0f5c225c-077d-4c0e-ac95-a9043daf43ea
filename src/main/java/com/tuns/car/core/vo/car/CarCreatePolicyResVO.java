package com.tuns.car.core.vo.car;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车险创建保单响应vo
 *
 * <AUTHOR>
 * @date 2023/02/21
 **/
@ApiModel
@Data
public class CarCreatePolicyResVO {

    @ApiModelProperty(value = "车险政策匹配的错误信息")
    private String errorReason;

    @ApiModelProperty(value = "个险政策匹配的错误信息")
    private String giErrorReason;

    @ApiModelProperty(value = "单件政策匹配的错误信息")
    private String singleErrorReason;
}
