package com.tuns.car.core.vo;

import com.tuns.car.query.vo.policymanage.imagereview.ImageReviewDetailVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@ApiModel("APP-补传影像资料-结果VO")
public class MakeUpImageVO implements Serializable {
    /**
     * 影像资料审核信息（null：表示无需审核影像资料）
     */

    private ImageReviewDetailVO imageReviewDetailVO;
}
