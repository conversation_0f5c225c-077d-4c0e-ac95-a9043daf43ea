package com.tuns.car.core.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description
 * @create 2022-05-16 10:33
 **/

public class CustomLocalDateToStringSerializer extends JsonSerializer<LocalDate> {


    @Override
    public void serialize(LocalDate value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeString(value.toString());
    }
}
