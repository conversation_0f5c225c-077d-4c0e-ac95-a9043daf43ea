package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_policy_batch")
public class ViPolicyBatch implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 批次编号
	 */
	@ApiModelProperty(value = "批次编号")
	private String batchNumber;
	/**
	 * 批次名称
	 */
	@ApiModelProperty(value = "批次名称")
	private String batchName;
	/**
	 * 保险公司id
	 */
	@ApiModelProperty(value = "保险公司id")
	private String companyId;
	/**
	 * 保险公司渠道id
	 */
	@ApiModelProperty(value = "保险公司渠道id")
	private String channelId;
	/**
	 * 启用时间
	 */
	@ApiModelProperty(value = "启用时间")
	private Date beginTm;
	/**
	 * 停用时间
	 */
	@ApiModelProperty(value = "停用时间")
	private Date endTm;
	/**
	 * 优先级
	 */
	@ApiModelProperty(value = "优先级")
	private Integer sort;
	/**
	 * 原优先级
	 */
	@ApiModelProperty(value = "原优先级")
	private Integer oldSort;
	/**
	 * 政策来源1基础政策 2 单件
	 */
	@ApiModelProperty(value = "政策来源1基础政策 2 单件")
	private String policySource;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 流水状态  0 编辑中 1 待审核  2待修改  3已审核  4已启用  5已停用
	 */
	@ApiModelProperty(value = "流水状态  0 编辑中 1 待审核  2待修改  3已审核  4已启用  5已停用")
	private String status;
	/**
	 * 审核用户
	 */
	@ApiModelProperty(value = "审核用户")
	private Integer ckUser;
	/**
	 * 审核时间
	 */
	@ApiModelProperty(value = "审核时间")
	private Date ckTm;
	/**
	 * 创建用户
	 */
	@ApiModelProperty(value = "创建用户")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改用户
	 */
	@ApiModelProperty(value = "修改用户")
	private Integer mdfUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 是否删除  0否,1是
	 */
	@ApiModelProperty(value = "是否删除  0否,1是")
	private String delFlag;
	/**
	 * 是否老数据
	 */
	@ApiModelProperty(value = "是否老数据")
	private String oldFlag;
	/**
	 * 创建人名字
	 */
	@ApiModelProperty(value = "创建人名字")
	private String creUserName;
	/**
	 * 更新人名字
	 */
	@ApiModelProperty(value = "更新人名字")
	private String mdfUserName;
	/**
	 * 附件地址
	 */
	@ApiModelProperty(value = "附件地址")
	private String fileUrl;
	/**
	 * 附件全路径
	 */
	@ApiModelProperty(value = "附件全路径")
	private String fileFullUrl;
	/**
	 * 附件名称
	 */
	@ApiModelProperty(value = "附件名称")
	private String fileName;
	/**
	 * 匹配结果 0：匹配中 1：匹配完成
	 */
	@ApiModelProperty(value = "匹配结果 0：匹配中 1：匹配完成")
	private String policyStatus;

}
