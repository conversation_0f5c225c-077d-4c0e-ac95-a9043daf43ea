package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_insur_order")
public class ViInsurOrder implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 订单号
	 */
	@ApiModelProperty(value = "订单号")
	@TableId
	private Integer id;
	/**
	 * 订单编号
	 */
	@ApiModelProperty(value = "订单编号")
	private String orderId;
	/**
	 * 产品编号
	 */
	@ApiModelProperty(value = "产品编号")
	private Integer productId;
	/**
	 * 保单表对应人的保单批次ID-policy_batch_id
	 */
	@ApiModelProperty(value = "保单表对应人的保单批次ID-policy_batch_id")
	private Long policyNumber;
	/**
	 * 产品名
	 */
	@ApiModelProperty(value = "产品名")
	private String productName;
	/**
	 * 创建日期
	 */
	@ApiModelProperty(value = "创建日期")
	private Date createTm;
	/**
	 * 保费
	 */
	@ApiModelProperty(value = "保费")
	private BigDecimal policyAmount;
	/**
	 * 订单时间
	 */
	@ApiModelProperty(value = "订单时间")
	private BigDecimal amountDue;
	/**
	 * 订单状态(0:全部,1:未支付,2:已支付,3:待支付,4:已完成,5:已取消)
	 */
	@ApiModelProperty(value = "订单状态(0:全部,1:未支付,2:已支付,3:待支付,4:已完成,5:已取消)")
	private String orderStatus;
	/**
	 * 付款时间
	 */
	@ApiModelProperty(value = "付款时间")
	private Date paymentTm;
	/**
	 * 删除标志(0 删除,1正常)
	 */
	@ApiModelProperty(value = "删除标志(0 删除,1正常)")
	private String delFlag;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private Integer mdfUser;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 1:车险2:寿险3:非车险
	 */
	@ApiModelProperty(value = "1:车险2:寿险3:非车险")
	private String orderType;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date expireTime;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String payImgUrl;

}
