package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@Data
@TableName("fm_user_wallet")
public class FmUserWallet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 可用余额
     */
    private BigDecimal availableAmount;

    /**
     * 冻结中金额
     */
    private BigDecimal frozenAmount;

    /**
     * 是否删除：0-否，1-是
     */
    private String delFlag;

    /**
     * 创建用户
     */
    private Integer creUser;

    /**
     * 创建时间
     */
    private LocalDateTime creTm;

    /**
     * 修改用户
     */
    private Integer mdfUser;

    /**
     * 修改时间
     */
    private LocalDateTime mdfTm;


    @Override
    public String toString() {
        return "FmUserWallet{" +
                "id=" + id +
                ", userId=" + userId +
                ", totalAmount=" + totalAmount +
                ", availableAmount=" + availableAmount +
                ", frozenAmount=" + frozenAmount +
                ", delFlag=" + delFlag +
                ", creUser=" + creUser +
                ", creTm=" + creTm +
                ", mdfUser=" + mdfUser +
                ", mdfTm=" + mdfTm +
                "}";
    }
}
