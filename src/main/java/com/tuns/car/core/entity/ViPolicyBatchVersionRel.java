package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_policy_batch_version_rel")
public class ViPolicyBatchVersionRel implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水
	 */
	@ApiModelProperty(value = "流水")
	@TableId
	private Integer id;
	/**
	 * 批次序列号
	 */
	@ApiModelProperty(value = "批次序列号")
	private Long batchSeq;
	/**
	 * 批次编号
	 */
	@ApiModelProperty(value = "批次编号")
	private Long batchId;
	/**
	 * 模板编号
	 */
	@ApiModelProperty(value = "模板编号")
	private String tempId;
	/**
	 * 模板名称
	 */
	@ApiModelProperty(value = "模板名称")
	private String tempName;
	/**
	 * 版本编号
	 */
	@ApiModelProperty(value = "版本编号")
	private String verId;
	/**
	 * 商业险
	 */
	@ApiModelProperty(value = "商业险")
	private BigDecimal businessIns;
	/**
	 * 交强险
	 */
	@ApiModelProperty(value = "交强险")
	private BigDecimal compulsoryIns;
	/**
	 * 车船税
	 */
	@ApiModelProperty(value = "车船税")
	private BigDecimal vehicleVesselTax;
	/**
	 * 是否含税 0 否 1是
	 */
	@ApiModelProperty(value = "是否含税 0 否 1是")
	private String isIncludTax;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 创建用户
	 */
	@ApiModelProperty(value = "创建用户")
	private Integer creUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 修改用户
	 */
	@ApiModelProperty(value = "修改用户")
	private Integer mdfUser;
	/**
	 * 时间戳
	 */
	@ApiModelProperty(value = "时间戳")
	private Date tmSmp;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String creUserName;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String mdfUserName;

}
