package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险结出账单明细
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
@TableName("vi_ins_bear_bill_detail")
public class ViInsBearBillDetail implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@ApiModelProperty(value = "主键ID")
	@TableId
	private Integer id;
	/**
	 * 保单业务ID
	 */
	@ApiModelProperty(value = "保单业务ID")
	private String insPolicyId;
	/**
	 * 保单号
	 */
	@ApiModelProperty(value = "保单号")
	private String policyNumber;
	/**
	 * 批单号
	 */
	@ApiModelProperty(value = "批单号")
	private String endorsementNumber;
	/**
	 * 政策编号
	 */
	@ApiModelProperty(value = "政策编号")
	private String policyCode;
	/**
	 * 政策名称
	 */
	@ApiModelProperty(value = "政策名称")
	private String policyName;
	/**
	 * 政策类型:(佣金类型  6调整佣金 ,7协作费,11基础佣金率1,12基础佣金率2
	 */
	@ApiModelProperty(value = "政策类型:(佣金类型  6调整佣金 ,7协作费,11基础佣金率1,12基础佣金率2")
	private String policyType;
	/**
	 * 1-交强 2-商业 3-车船税
	 */
	@ApiModelProperty(value = "1-交强 2-商业 3-车船税")
	private String riskType;
	/**
	 * 佣金率
	 */
	@ApiModelProperty(value = "佣金率")
	private BigDecimal commissionRate;
	/**
	 * 应结金额
	 */
	@ApiModelProperty(value = "应结金额")
	private BigDecimal shouldBeAmount;
	/**
	 * 已结金额
	 */
	@ApiModelProperty(value = "已结金额")
	private BigDecimal knotAmount;
	/**
	 * 未结金额
	 */
	@ApiModelProperty(value = "未结金额")
	private BigDecimal outstandingAmount;
	/**
	 * 部门编号
	 */
	@ApiModelProperty(value = "部门编号")
	private String departCode;
	/**
	 * 部门名称
	 */
	@ApiModelProperty(value = "部门名称")
	private String departName;
	/**
	 * 合作伙伴ID
	 */
	@ApiModelProperty(value = "合作伙伴ID")
	private Integer partnerId;
	/**
	 * 合作伙伴名称
	 */
	@ApiModelProperty(value = "合作伙伴名称")
	private String partnerName;
	/**
	 * 身份证号码
	 */
	@ApiModelProperty(value = "身份证号码")
	private String idCardNo;
	/**
	 * 账单批次号
	 */
	@ApiModelProperty(value = "账单批次号")
	private String bearBatchNo;
	/**
	 * 结算方式 1全保费 0净保费
	 */
	@ApiModelProperty(value = "结算方式 1全保费 0净保费")
	private String baseFeeType;
	/**
	 * 结算对象 01总公司 02分公司 03营业部 04业务部
	 */
	@ApiModelProperty(value = "结算对象 01总公司 02分公司 03营业部 04业务部")
	private String settleObject;
	/**
	 * 结算状态 1结算中 2已结算
	 */
	@ApiModelProperty(value = "结算状态 1结算中 2已结算")
	private String settleStatus;
	/**
	 * 结算日期
	 */
	@ApiModelProperty(value = "结算日期")
	private Date settleDate;
	/**
	 * 支付类型: 1线下打款 2收益提现
	 */
	@ApiModelProperty(value = "支付类型: 1线下打款 2收益提现")
	private String payType;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 冲账状态 0未冲,1被冲,2对冲,3已冲
	 */
	@ApiModelProperty(value = "冲账状态 0未冲,1被冲,2对冲,3已冲")
	private String reverseState;

}
