package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.tuns.core.boot.constant.YesNoNumberEnum;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_insurances_basics")
public class ViInsurancesBasics implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据库id
	 */
	@ApiModelProperty(value = "数据库id")
	@TableId
	private Integer id;
	/**
	 * 险种id
	 */
	@ApiModelProperty(value = "险种id")
	private Long insuranceId;
	/**
	 * 险种代码
	 */
	@ApiModelProperty(value = "险种代码")
	private String insuranceCode;
	/**
	 * 险种名称
	 */
	@ApiModelProperty(value = "险种名称")
	private String insurance;
	/**
	 * 保险公司编号
	 */
	@ApiModelProperty(value = "保险公司编号")
	private String companyId;
	/**
	 * 保险公司名称
	 */
	@ApiModelProperty(value = "保险公司名称")
	private String companyName;
	/**
	 * 主附险标志 1 主险 0 附加险
	 */
	@ApiModelProperty(value = "主附险标志 1 主险 0 附加险")
	private String insFlag;
	/**
	 * 是否商业险 1是 0否
	 */
	@ApiModelProperty(value = "是否商业险 1是 0否")
	private String comFlag;
	/**
	 * 是否不计免赔 1 是 0 否
	 */
	@ApiModelProperty(value = "是否不计免赔 1 是 0 否")
	private String addlMark;
	/**
	 * 附加险所在主险id
	 */
	@ApiModelProperty(value = "附加险所在主险id")
	private Long mainInsuranceId;
	@ApiModelProperty(value = "启用状态，0待启用，1已启用，2已停用")
	private String isEnable;
	/**
	 * 上下架状态，0待上架，1上架，2下架
	 */
	@ApiModelProperty(value = "上下架状态，0待上架，1已上架，2已下架")
	private String shelfSts;
	/**
	 * 保额数组，JSON格式：[{"value":"100000","text":"10万元"}，{"value":"100000","text":"100000元"}]
	 */
	@ApiModelProperty(value = "保额，JSON数组格式", required = true)
	private String insuredAmountArr;
	/**
	 * 是否共享保额 0否 1是
	 */
	@ApiModelProperty(value = "是否共享保额 0否 1是", required = true)
	private YesNoNumberEnum isSharedAmount;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 创建人id
	 */
	@ApiModelProperty(value = "创建人id")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改人id
	 */
	@ApiModelProperty(value = "修改人id")
	private Integer mdfUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 删除标志  0 删除 1未删除
	 */
	@ApiModelProperty(value = "删除标志  0 删除 1未删除")
	private String delFlag;
	/**
	 * 附加险所在主险代码
	 */
	@ApiModelProperty(value = "附加险所在主险代码")
	private String mainInsuranceCode;
	/**
	 * 险别别名
	 */
	@ApiModelProperty(value = "险别别名")
	private String insuranceAlias;
	/**
	 * 使用类型 1:老版车险, 2:20版车险
	 */
	@ApiModelProperty(value = "使用类型 1:老版车险, 2:20版车险")
	private String useType;

}
