package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_self_ratio_rule_detail")
public class ViSelfRatioRuleDetail implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@TableId
	private Integer id;
	/**
	 * 批次id
	 */
	@ApiModelProperty(value = "批次id")
	private String batchId;
	/**
	 * 规则id
	 */
	@ApiModelProperty(value = "规则id")
	private String ruleId;
	/**
	 * 规则因素
	 */
	@ApiModelProperty(value = "规则因素")
	private String policyType;
	/**
	 * 条件类型 1指定 2除外 3区间
	 */
	@ApiModelProperty(value = "条件类型 1指定 2除外 3区间")
	private String conditionType;
	/**
	 * 条件明细
	 */
	@ApiModelProperty(value = "条件明细")
	private String conditionDetail;
	/**
	 * 开始值
	 */
	@ApiModelProperty(value = "开始值")
	private String beginValue;
	/**
	 * 结束值
	 */
	@ApiModelProperty(value = "结束值")
	private String endValue;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 创建人名字
	 */
	@ApiModelProperty(value = "创建人名字")
	private String creUserName;
	/**
	 * 删除标志 0否, 1是
	 */
	@ApiModelProperty(value = "删除标志 0否, 1是")
	private String delFlag;

}
