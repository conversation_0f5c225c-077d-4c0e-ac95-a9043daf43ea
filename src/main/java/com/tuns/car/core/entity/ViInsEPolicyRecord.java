package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险电子保单下载记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
@TableName("vi_ins_e_policy_record")
public class ViInsEPolicyRecord implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 批次id
	 */
	@ApiModelProperty(value = "批次id")
	private Long policyBatchId;
	/**
	 * 保单号码
	 */
	@ApiModelProperty(value = "保单号码")
	private String policyNumber;
	/**
	 * 保险公司ID
	 */
	@ApiModelProperty(value = "保险公司ID")
	private String companyId;
	/**
	 * 电子保单类型
	 */
	@ApiModelProperty(value = "电子保单类型")
	private Integer ePolicyType;
	/**
	 * 文件下载地址
	 */
	@ApiModelProperty(value = "文件下载地址")
	private String ePolicyUrl;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date creTm;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer creUser;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date mdfTm;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer mdfUser;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String delFlag;

}
