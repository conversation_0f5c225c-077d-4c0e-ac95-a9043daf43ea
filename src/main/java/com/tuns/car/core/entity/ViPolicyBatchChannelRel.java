package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_policy_batch_channel_rel")
public class ViPolicyBatchChannelRel implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 批次编号
	 */
	@ApiModelProperty(value = "批次编号")
	private Long batchId;
	/**
	 * 机构编号
	 */
	@ApiModelProperty(value = "机构编号")
	private String departCode;
	/**
	 * 批次内部序列
	 */
	@ApiModelProperty(value = "批次内部序列")
	private Long batchSeq;
	/**
	 * 版本编号
	 */
	@ApiModelProperty(value = "版本编号")
	private String verId;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;

}
