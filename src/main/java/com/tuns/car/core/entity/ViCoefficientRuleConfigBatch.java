package com.tuns.car.core.entity;

import com.tuns.car.core.constant.EnableEnum;
import com.tuns.core.boot.entity.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * 车险系数规则批次表
 * @date 2024-5-6 16:15
 */
@Data
public class ViCoefficientRuleConfigBatch extends BaseEntity {
    /**
     * 批次编号
     */
    private String batchNumber;
    /**
     * 批次名称
     */
    private String batchName;
    /**
     * 保险公司id
     */
    private String companyId;
    /**
     * 规则数量
     */
    private Integer ruleQuantity;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态 0未启用 1已启用
     */
    private EnableEnum status;

}
