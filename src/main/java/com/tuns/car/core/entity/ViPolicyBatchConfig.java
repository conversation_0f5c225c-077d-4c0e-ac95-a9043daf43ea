package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_policy_batch_config")
public class ViPolicyBatchConfig implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 批次序列号
	 */
	@ApiModelProperty(value = "批次序列号")
	private Long batchSeq;
	/**
	 * 批次编号
	 */
	@ApiModelProperty(value = "批次编号")
	private Long batchId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 创建用户
	 */
	@ApiModelProperty(value = "创建用户")
	private Integer creUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 修改用户
	 */
	@ApiModelProperty(value = "修改用户")
	private Integer mdfUser;
	/**
	 * 时间戳
	 */
	@ApiModelProperty(value = "时间戳")
	private Date tmSmp;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String creUserName;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String mdfUserName;

}
