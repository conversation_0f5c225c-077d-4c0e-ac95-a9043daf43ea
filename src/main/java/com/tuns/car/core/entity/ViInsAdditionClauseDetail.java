package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险投保附加服务次数表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_ins_addition_clause_detail")
public class ViInsAdditionClauseDetail implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@TableId
	private Integer id;
	/**
	 * 服务条款主键ID
	 */
	@ApiModelProperty(value = "服务条款主键ID")
	private String additionClauseId;
	/**
	 * 服务次数
	 */
	@ApiModelProperty(value = "服务次数")
	private Integer serviceNumber;

}
