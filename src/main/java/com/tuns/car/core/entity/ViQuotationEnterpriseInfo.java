package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 车险企业单位信息
 *
 * <AUTHOR>
 * @since 2024-06-27 14:24
 **/
@Data
@TableName("vi_quote_enterprise_info")
@Accessors(chain = true)
public class ViQuotationEnterpriseInfo extends BaseEntity {

    /**
     * 报价批次ID
     */
    private Long policyBatchId;

    /**
     * 单位组织机构类型
     */
    private String organizationType;

    /**
     * 单位电话总机
     */
    private String phone;

    /**
     * 单位联系人姓名
     */
    private String contactName;

    /**
     * 单位联系人方式 手机号码
     */
    private String phoneNumber;

    /**
     * 单位经办人姓名
     */
    private String operatorName;

    /**
     * 单位经办人证件号
     */
    private String credentialNo;

    /**
     * 单位经办人邮箱
     */
    private String operatorEmail;
}
