package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险自助投保影像资料配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-17 15:51:48
 */
@Data
@TableName("vi_image_data_conf")
public class ViImageDataConf implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@TableId
	private Integer id;
	/**
	 * 关联业务ID
	 */
	@ApiModelProperty(value = "关联业务ID")
	private Integer bussinseId;
	/**
	 * 影像资料配置ID
	 */
	@ApiModelProperty(value = "影像资料配置ID")
	private String imageDataConfId;
	/**
	 * 险种名称
	 */
	@ApiModelProperty(value = "险种名称")
	private String insuranceName;
	/**
	 * 险种代码
	 */
	@ApiModelProperty(value = "险种代码")
	private String insuranceCode;
	/**
	 * 是否必传1是0否
	 */
	@ApiModelProperty(value = "是否必传1是0否")
	private String whetherWillPass;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date creTm;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer creUser;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date mdfTm;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer mdfUser;

}
