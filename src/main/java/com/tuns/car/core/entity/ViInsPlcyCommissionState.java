package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName: ViInsPlcyCommissionState
 * @Description: 车险保单佣金表
 * @date 2022年7月12日
 */
@Data
@TableName("vi_ins_plcy_commission_state")
public class ViInsPlcyCommissionState extends BaseEntity {

    /**
     * 保单记录ID
     */
    private Long policyId;

    /**
     * 佣金是否超过直属上级佣金（'0'否，'1'是）
     */
    private String isOver;

    /**
     * 是否超政策上限 0否，1是
     */
    private String isOverPolicy;

    /**
     * 是否匹配单件政策 0否，1是
     */
    private String isMatchingSingle;

    /**
     * 单件政策上限值
     */
    private BigDecimal singlePolicyCeiling;

    /**
     * 单件奖佣金率(保单匹配所有规则之和)
     */
    private BigDecimal singleTotalRate;

    /**
     * 单件奖佣金费(保单匹配所有规则之和)
     */
    private BigDecimal singleTotalAmt;

    @ApiModelProperty(value = "保单批次id")
    private Long policyBatchId;

}