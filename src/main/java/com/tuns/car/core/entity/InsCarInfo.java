package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 车五项基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@TableName("tuns_ins_car_info")
@Data
public class InsCarInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "车主", required = true)
    @NotBlank(message = "车主不能为空")
    private String carOwner;

    @ApiModelProperty(value = "车主证件号码")
    private String ownerIdentify;

    @ApiModelProperty(value = "车主证件类型")
    private String ownerIdentifyType;

    @ApiModelProperty(value = "车牌号码", required = true)
    @NotBlank(message = "车牌号码不能为空")
    private String plateNumber;

    @ApiModelProperty(value = "车架号", required = true)
    @NotBlank(message = "车架号不能为空")
    private String frameNumber;

    @ApiModelProperty(value = "发动机号码", required = true)
    @NotBlank(message = "发动机号码不能为空")
    private String engineNumber;

    @ApiModelProperty(value = "注册日期 yyyy-MM-dd", required = true)
    @NotBlank(message = "注册日期 yyyy-MM-dd不能为空")
    private String regDate;

    @ApiModelProperty(value = "发证日期 yyyy-MM-dd", required = true)
    @NotBlank(message = "发证日期不能为空")
    private String certDate;

    @ApiModelProperty(value = "过户标志", required = true)
    @NotBlank(message = "过户标志不能为空")
    private String transferMark;

    @ApiModelProperty(value = "过户日期 yyyy-MM-dd")
    private String transferDate;

    @ApiModelProperty(value = "新车标志 0-非新车 1-新车", required = true)
    @NotBlank(message = "新车标志不能为空")
    private String newCarMark;

    @ApiModelProperty(value = "品牌名称", required = true)
    @NotBlank(message = "品牌名称不能为空")
    private String modelName;

    @ApiModelProperty(value = "车型代码")
    private String modelCode;

    @ApiModelProperty(value = "行驶里程 (公里/千米)", required = true)
    @NotNull(message = "行驶里程不能为空")
    private Integer runMiles;

    @ApiModelProperty(value = "核定载客", required = true)
    @NotNull(message = "核定载客不能为空")
    private Integer seatCount;

    @ApiModelProperty(value = "能源类型", required = true)
    @NotBlank(message = "能源类型不能为空")
    private String fuelType;

    @ApiModelProperty(value = "排量(ML)", required = true)
    @NotNull(message = "排量不能为空")
    private Integer exhaustScale;

    @ApiModelProperty(value = "功率(瓦)", required = true)
    @NotNull(message = "功率不能为空")
    private Integer power;

    @ApiModelProperty(value = "新车购置价", required = true)
    @NotNull(message = "新车购置价不能为空")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "贷款车辆标志 0-非贷款车 1-贷款车", required = true)
    @NotBlank(message = "贷款车辆标志不能为空")
    private String loanCarMark;

    @ApiModelProperty(value = "整备质量", required = true)
    @NotNull(message = "整备质量不能为空")
    private Integer wholeWeight;

    @ApiModelProperty(value = "年款 yyyyMM/yyyy", required = true)
    @NotBlank(message = "年款不能为空")
    private String marketDate;

    @ApiModelProperty(value = "车辆类型描述")
    private String carStyleNote;

    @ApiModelProperty(value = "车款名称")
    private String carName;

    @ApiModelProperty(value = "车辆产地 1-进口 2-国产 3-合资", required = true)
    @NotBlank(message = "车辆产地不能为空")
    private String carOrigin;

    @ApiModelProperty(value = "车辆吨位/核定载质量(吨)", required = true)
    @NotNull(message = "车辆吨位不能为空")
    private BigDecimal carTonnage;

    @ApiModelProperty(value = "行业车型编码")
    private String prfsModelCode;

    @ApiModelProperty(value = "是否安装GPS 0-未安装 1-安装")
    private String gpsMark;

    @ApiModelProperty(value = "家庭车辆台数", required = true)
    @NotNull(message = "家庭车辆台数不能为空")
    private Integer familyCarCount;

    @ApiModelProperty(value = "减税车型标志 0-正常 04-减免税 05-减税")
    private String taxCutsMark;

    @ApiModelProperty(value = "减税比例 0~1")
    private BigDecimal taxCutProportion;

    @ApiModelProperty(value = "车系名称")
    private String familyName;

    @ApiModelProperty(value = "公告型号")
    private String noticeType;

    @ApiModelProperty(value = "所属性质 1-个人 2-机关 3-企业", required = true)
    @NotBlank(message = "所属性质不能为空")
    private String ownershipNature;

    @ApiModelProperty(value = "营运性质 0-非营运 1-营运", required = true)
    @NotBlank(message = "营运性质不能为空")
    private String operationNature;

    @ApiModelProperty(value = "使用性质 附件码表C1.使用性质", required = true)
    @NotBlank(message = "使用性质不能为空")
    private String usingNature;

    @ApiModelProperty(value = "行政区域代码 省级")
    private String provinceNumber;

    @ApiModelProperty(value = "行政区域代码 市级")
    private String cityNumber;

    @ApiModelProperty(value = "行驶区域")
    private String runAreaCode;

    @ApiModelProperty(value = "使用年限", required = true)
    @NotNull(message = "使用年限不能为空")
    private Integer useYears;

    @ApiModelProperty(value = "车身颜色")
    private String bodyColor;

    @ApiModelProperty(value = "备注")
    private String note;

    @ApiModelProperty(value = "车型来源公司ID")
    private String carInfoCompany;

    @ApiModelProperty(value = "扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）")
    private String extend;

    @ApiModelProperty(value = "创建时间", required = true)
    @NotNull(message = "创建时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @TableField(fill= FieldFill.INSERT)
    private Date creTm;

    @ApiModelProperty(value = "创建人", required = true)
    @NotNull(message = "创建人不能为空")
    private Integer creUser;

    @ApiModelProperty(value = "更新时间", required = true)
    @NotNull(message = "更新时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date mdfTm;

    @ApiModelProperty(value = "更新人", required = true)
    @NotNull(message = "更新人不能为空")
    private Integer mdfUser;

    @ApiModelProperty(value = "删除标志 0-已删除 1-存储", required = true)
    @NotBlank(message = "删除标志不能为空")
    @TableField(fill= FieldFill.INSERT)
    private String delFlag;

    @ApiModelProperty(value = "车主性质", required = true)
    @NotBlank(message = "车主性质不能为空")
    private String ownerNature;

    @ApiModelProperty(value = "车辆实际价", required = true)
    @NotNull(message = "车辆实际价不能为空")
    private BigDecimal actualPrice;

    @ApiModelProperty(value = "轴距")
    private String wheelBase;
    
    @ApiModelProperty(value = "轴数")
    private String shaft;
    
    @ApiModelProperty(value = "生产日期")
    private String manufacturingDate;
    
    @ApiModelProperty(value = "前轮距")
    private String frontWheelBase;
    
    @ApiModelProperty(value = "发动机型号")
    private String engineModel;
    
    @ApiModelProperty(value = "额定载质量")
    private String ratedQuality;
    
    @ApiModelProperty(value = "品牌")
    private String brand;
    
    @ApiModelProperty(value = "后轮距")
    private String rearWheelBase;
    



}
