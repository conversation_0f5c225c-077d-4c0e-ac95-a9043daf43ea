package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_ins_plcy_inf")
@Accessors(chain = true)
public class ViInsPlcyInf extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/**
	 * 流程流水号/报价批次号
	 */
	@ApiModelProperty(value = "流程流水号/报价批次号")
	private Long serialNumber;
	/**
	 * 保单记录ID
	 */
	@ApiModelProperty(value = "保单记录ID")
	private Long policyId;
	/**
	 * 保单批次ID
	 */
	@ApiModelProperty(value = "保单批次ID")
	private Long policyBatchId;
	/**
	 * 保单号
	 */
	@ApiModelProperty(value = "保单号")
	private String policyNumber;
	/**
	 * 主保单号
	 */
	@ApiModelProperty(value = "主保单号")
	private String mainPolicyNumber;
	/**
	 * 投保单号
	 */
	@ApiModelProperty(value = "投保单号")
	private String proposalNumber;
	/**
	 * 主投保单号
	 */
	@ApiModelProperty(value = "主投保单号")
	private String mainProposalNumber;
	/**
	 * 上年保单号
	 */
	@ApiModelProperty(value = "上年保单号")
	private String lastPolicy;
	/**
	 * 上年承保公司ID
	 */
	@ApiModelProperty(value = "上年承保公司ID")
	private String lastCompanyId;
	/**
	 * 上年承保公司名称
	 */
	@ApiModelProperty(value = "上年承保公司名称")
	private String lastCompanyName;
	/**
	 * 出单类型 1-单交强 2-单商业 3-交商同保
	 */
	@ApiModelProperty(value = "出单类型 1-单交强 2-单商业 3-交商同保")
	private String recordType;
	/**
	 * 保单类型 1-交强单 2-商业单 3-车船
	 */
	@ApiModelProperty(value = "保单类型 1-交强单 2-商业单 3-车船")
	private String policyType;
	/**
	 * 自动报价标志
	 */
	@ApiModelProperty(value = "自动报价标志")
	private String autoMark;
	/**
	 * 保单记录来源 1-客户端 2-内勤 3-录单
	 */
	@ApiModelProperty(value = "保单记录来源 1-客户端 2-内勤 3-录单")
	private String recordSource;
	/**
	 * 保险起期
	 */
	@ApiModelProperty(value = "保险起期")
	private String insBegin;
	/**
	 * 保险止期
	 */
	@ApiModelProperty(value = "保险止期")
	private String insEnd;
	/**
	 * 保费
	 */
	@ApiModelProperty(value = "保费")
	private BigDecimal insuredPremium;
	/**
	 * 附加保费/车船税
	 */
	@ApiModelProperty(value = "附加保费/车船税")
	private BigDecimal attachPremium;
	/**
	 * 总保费=保费+附加保费
	 */
	@ApiModelProperty(value = "总保费=保费+附加保费")
	private BigDecimal totalPremium;
	/**
	 * 保险金额
	 */
	@ApiModelProperty(value = "保险金额")
	private BigDecimal insAmount;
	/**
	 * 奖励
	 */
	@ApiModelProperty(value = "奖励")
	private BigDecimal insuredReward;
	/**
	 * 附加奖励
	 */
	@ApiModelProperty(value = "附加奖励")
	private BigDecimal attachReward;
	/**
	 * 总奖励=佣金+附加奖励
	 */
	@ApiModelProperty(value = "总奖励=佣金+附加奖励")
	private BigDecimal totalReward;
	/**
	 * 折扣 0~2
	 */
	@ApiModelProperty(value = "折扣 0~2")
	private BigDecimal discount;
	/**
	 * 承保公司ID
	 */
	@ApiModelProperty(value = "承保公司ID")
	private String companyId;
	/**
	 * 承保公司名称
	 */
	@ApiModelProperty(value = "承保公司名称")
	private String companyName;
	/**
	 * 行政区域代码 省
	 */
	@ApiModelProperty(value = "行政区域代码 省")
	private String provinceNumber;
	/**
	 * 行政区域 省
	 */
	@ApiModelProperty(value = "行政区域 省")
	private String provinceName;
	/**
	 * 行政区域代码 市
	 */
	@ApiModelProperty(value = "行政区域代码 市")
	private String cityNumber;
	/**
	 * 行政区域 市
	 */
	@ApiModelProperty(value = "行政区域 市")
	private String cityName;
	/**
	 * 保费计算识别码/保险公司订单号码
	 */
	@ApiModelProperty(value = "保费计算识别码/保险公司订单号码")
	private String insPremiumNumber;
	/**
	 * 特别约定
	 */
	@ApiModelProperty(value = "特别约定")
	private String specialAgreement;
	/**
	 * 报价审核人ID 自动报价时为保险公司ID/人工报价时为内勤人员ID
	 */
	@ApiModelProperty(value = "报价审核人ID 自动报价时为保险公司ID/人工报价时为内勤人员ID")
	private String reviewerId;
	/**
	 * 报价审核人姓名  自动报价时为保险公司/人工报价时为内勤人员
	 */
	@ApiModelProperty(value = "报价审核人姓名  自动报价时为保险公司/人工报价时为内勤人员")
	private String reviewerName;
	/**
	 * 报价审核时间
	 */
	@ApiModelProperty(value = "报价审核时间")
	private Date reviewTime;
	/**
	 * 报价状态 0-报价失败 1-报价成功 3-报价等待
	 */
	@ApiModelProperty(value = "报价状态 0-报价失败 1-报价成功 3-报价等待")
	private String recordStatus;
	/**
	 * 报价备注信息
	 */
	@ApiModelProperty(value = "报价备注信息")
	private String reviewMsg;
	/**
	 * 上年理赔次数
	 */
	@ApiModelProperty(value = "上年理赔次数")
	private Integer lastClaimCount;
	/**
	 * 自动报价时为保险公司ID/人工报价时为内勤人员ID
	 */
	@ApiModelProperty(value = "自动报价时为保险公司ID/人工报价时为内勤人员ID")
	private String insuredReviewerId;
	/**
	 * 投保审核人姓名 自动报价时为保险公司/人工报价时为内勤人员
	 */
	@ApiModelProperty(value = "投保审核人姓名 自动报价时为保险公司/人工报价时为内勤人员")
	private String insuredReviewerName;
	/**
	 * 投保审核时间
	 */
	@ApiModelProperty(value = "投保审核时间")
	private Date insuredReviewTime;
	/**
	 * 投保备注信息
	 */
	@ApiModelProperty(value = "投保备注信息")
	private String insuredMsg;
	/**
	 * 投保状态 附件码表
	 */
	@ApiModelProperty(value = "投保状态 附件码表")
	private String insuredStatus;
	/**
	 * 支付时间
	 */
	@ApiModelProperty(value = "支付时间")
	private Date payTime;
	/**
	 * 保单配送方式 1-快递配送 2-门店自提
	 */
	@ApiModelProperty(value = "保单配送方式 1-快递配送 2-门店自提")
	private String receiverType;
	/**
	 * 业务员ID/出单员
	 */
	@ApiModelProperty(value = "业务员ID/出单员")
	private String salesmanId;
	/**
     * 询价人ID
     */
    @ApiModelProperty(value = "询价人ID")
    private String inquirerId;
    /**
     * 本公司组织机构代码
     */
    @ApiModelProperty(value = "本公司组织机构代码")
    private String departCode;
    /**
     * 本公司组织机构名称
     */
    @ApiModelProperty(value = "本公司组织机构名称")
    private String departName;
    /**
     * 保险公司合作渠道代码
     */
    @ApiModelProperty(value = "保险公司合作渠道代码")
    private String channelId;
    /**
     * 报价评分
     */
    @ApiModelProperty(value = "报价评分")
    private String recordScore;
    /**
	 * 业务类型 1-新保 2-续保 3-转保
	 */
	@ApiModelProperty(value = "业务类型 1-新保 2-续保 3-转保")
	private String insuredType;
	/**
	 * 本渠道续保标志
	 */
	@ApiModelProperty(value = "本渠道续保标志")
	private String thisRenewal;
	/**
	 * 佣金手续费比例 0~1
	 */
	@ApiModelProperty(value = "佣金手续费比例 0~1")
	private BigDecimal rewardPercent;
	/**
	 * 承保时间
	 */
	@ApiModelProperty(value = "承保时间")
	private LocalDateTime insuredTime;
	/**
	 * 保单审核人ID
	 */
	@ApiModelProperty(value = "保单审核人ID")
	private String policyReviewerId;
	/**
	 * 保单审核人名称
	 */
	@ApiModelProperty(value = "保单审核人名称")
	private String policyReviewerName;
	/**
	 * 保单审核时间
	 */
	@ApiModelProperty(value = "保单审核时间")
	private Date policyReviewTime;
	/**
	 * 保单审核备注信息
	 */
	@ApiModelProperty(value = "保单审核备注信息")
	private String policyReviewMsg;
	/**
	 *  保单审核状态 0-编辑中 1-待审核 2-已审核 3-待修改
	 */
	@ApiModelProperty(value = " 保单审核状态 0-编辑中 1-待审核 2-已审核 3-待修改")
	private String policyReviewMark;
	/**
	 * 保单是否打印
	 */
	@ApiModelProperty(value = "保单是否打印")
	private String policyPrintMark;
	/**
	 * 保单打印人ID
	 */
	@ApiModelProperty(value = "保单打印人ID")
	private String policyPrinterId;
	/**
	 * 保单打印人名称
	 */
	@ApiModelProperty(value = "保单打印人名称")
	private String policyPrinterName;
	/**
	 * 保单打印时间
	 */
	@ApiModelProperty(value = "保单打印时间")
	private Date policyPrintTime;
	/**
	 * 是否新保单 原始数据
	 */
	@ApiModelProperty(value = "是否新保单 原始数据")
	private String policyNewMark;
	/**
	 * 单证编号
	 */
	@ApiModelProperty(value = "单证编号")
	private String documentNumber;
	/**
	 * 发票编号
	 */
	@ApiModelProperty(value = "发票编号")
	private String invoiceNumber;
	/**
	 * 交强险标志编号
	 */
	@ApiModelProperty(value = "交强险标志编号")
	private String ciMarkNumber;
	/**
	 * 税额
	 */
	@ApiModelProperty(value = "税额")
	private BigDecimal tax;
	/**
	 * 标准保费
	 */
	@ApiModelProperty(value = "标准保费")
	private BigDecimal standardPremium;
	/**
	 * 原保单记录ID
	 */
	@ApiModelProperty(value = "原保单记录ID")
	private Long lastPolicyId;
	/**
	 * 批单号
	 */
	@ApiModelProperty(value = "批单号")
	private String endorsementNumber;
	/**
	 * 扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）
	 */
	@ApiModelProperty(value = "扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）")
	private String extend;
	/**
	 * 交商同保评分
	 */
	@ApiModelProperty(value = "交商同保评分")
	private String totalRecordScore;
	/**
	 * 询价人所在部门
	 */
	@ApiModelProperty(value = "询价人所在部门")
	private String inquirerDepartCode;
	/**
	 * 净保费
	 */
	@ApiModelProperty(value = "净保费")
	private BigDecimal netPremium;
	/**
	 * 税率
	 */
	@ApiModelProperty(value = "税率")
	private BigDecimal taxRate;
	/**
	 * 发票类型
	 */
	@ApiModelProperty(value = "发票类型")
	private String invoiceMark;
	/**
	 * 即时起保
	 */
	@ApiModelProperty(value = "即时起保")
	private String effectiveImmediately;
	/**
	 * 询价人备注类型
	 */
	@ApiModelProperty(value = "询价人备注类型")
	private String inquirerMsgType;
	/**
	 * 询价人备注
	 */
	@ApiModelProperty(value = "询价人备注")
	private String inquirerMsg;
	/**
	 * 批单类型
	 */
	@ApiModelProperty(value = "批单类型")
	private String endorsementType;
	/**
	 * 免验车原因
	 */
	@ApiModelProperty(value = "免验车原因")
	private String exemptVerifyReason;
	/**
	 * 车船税缴税类型
	 */
	@ApiModelProperty(value = "车船税缴税类型")
	private String carTaxType;
	/**
	 * 投保子状态
	 */
	@ApiModelProperty(value = "投保子状态")
	private String insuredSubStatus;
	/**
	 * 最近操作人 指内勤 不同于修改人
	 */
	@ApiModelProperty(value = "最近操作人 指内勤 不同于修改人")
	private Integer lastMdfUser;
	/**
	 * 冲账状态 0未冲,1被冲,2对冲,3已冲
	 */
	@ApiModelProperty(value = "冲账状态 0未冲,1被冲,2对冲,3已冲")
	private String reverseState;
	/**
	 * 冲账对应原保单id
	 */
	@ApiModelProperty(value = "冲账对应原保单id")
	private String commRemark;
	/**
	 * 冲账对应原保单id
	 */
	@ApiModelProperty(value = "冲账对应原保单id")
	private Long reversePolicyId;
	/**
	 * 是否匹配到基础政策 0 未匹配到，1 匹配1个，2 匹配多个
	 */
	@ApiModelProperty(value = "是否匹配到基础政策 0 未匹配到，1 匹配1个，2 匹配多个")
	private String mateBasePolicy;
	/**
	 * 无赔款折扣系数(NCD系数)
	 */
	@ApiModelProperty(value = "无赔款折扣系数(NCD系数)")
	private BigDecimal noClaimDiscount;
	/**
	 * 自主定价系数
	 */
	@ApiModelProperty(value = "自主定价系数")
	private BigDecimal independentPriceRate;
	/**
	 * 客户评分等级
	 */
	@ApiModelProperty(value = "客户评分等级")
	private BigDecimal customerRiskRating;
	/**
	 * 私家车车联网分档
	 */
	@ApiModelProperty(value = "私家车车联网分档")
	private String carSpreading;
	/**
	 * 网约车分级
	 */
	@ApiModelProperty(value = "网约车分级")
	private BigDecimal wyCarType;
	/**
	 * 商业险预期赔付率
	 */
	@ApiModelProperty(value = "商业险预期赔付率")
	private BigDecimal lossRatioBi;
	/**
	 * 交强险预期赔付率
	 */
	@ApiModelProperty(value = "交强险预期赔付率")
	private BigDecimal lossRatioCi;
	/**
	 * 整单预期赔付率
	 */
	@ApiModelProperty(value = "整单预期赔付率")
	private BigDecimal totalRatio;

    /**
     * 整单预期赔付率2
     */
    @ApiModelProperty(value = "整单预期赔付率2")
    private BigDecimal totalRatio2;
	/**
	 * ncd系数级别
	 */
	@ApiModelProperty(value = "ncd系数级别")
	private BigDecimal noClaimLevel;
	/**
	 * 业务分组
	 */
	@ApiModelProperty(value = "业务分组")
	private String businessGroup;
	/**
	 * 保单成本率
	 */
	@ApiModelProperty(value = "保单成本率")
	private BigDecimal policyCostRate;
	/**
	 * 大家分
	 */
	@ApiModelProperty(value = "大家分")
	private String allScore;
	/**
	 * 期望
	 */
	@ApiModelProperty(value = "期望")
	private BigDecimal expectedDiscount;
	/**
	 * 商业险手续费
	 */
	@ApiModelProperty(value = "商业险手续费")
	private BigDecimal handFeeBi;
	/**
	 * 天马指数-核保
	 */
	@ApiModelProperty(value = "天马指数-核保")
	private BigDecimal tianMaIndexFloorPrice;
	/**
	 * 天马指数-标费
	 */
	@ApiModelProperty(value = "天马指数-标费")
	private BigDecimal tianMaIndexBasicPrice;
	/**
	 * 渠道id
	 */
	@ApiModelProperty(value = "渠道id")
	private Integer chanDetailId;
	/**
	 * 渠道名称
	 */
	@ApiModelProperty(value = "渠道名称")
	private String linkName;
	/**
	 * 渠道工号
	 */
	@ApiModelProperty(value = "渠道工号")
	private String linkNo;
	/**
	 * 非车险套餐信息
	 */
	@ApiModelProperty(value = "非车险套餐信息")
	private String carProductName;
	/**
	 * 非车险保全费
	 */
	@ApiModelProperty(value = "非车险保全费")
	private String carSaveCost;
	/**
	 * 三者+车损分档
	 */
	@ApiModelProperty(value = "三者+车损分档")
	private String thirdCarRank;
	/**
	 * 交强险手续费
	 */
	@ApiModelProperty(value = "交强险手续费")
	private BigDecimal handFeeCi;
	/**
	 * 是否计入收益 1计入 0不计入
	 */
	@ApiModelProperty(value = "是否计入收益 1计入 0不计入")
	private String incomeFlag;
	/**
	 * 送修代码
	 */
	@ApiModelProperty(value = "送修代码")
	private String repairCode;
	/**
	 * 项目代码
	 */
	@ApiModelProperty(value = "项目代码")
	private String projectCode;
	/**
	 * 上年度出单渠道类型
	 */
	@ApiModelProperty(value = "上年度出单渠道类型")
	private String lastChannelType;
	/**
	 * 交强险续保提前天数
	 */
	@ApiModelProperty(value = "交强险续保提前天数")
	private Integer daysInAdvanceOfRenewalCi;
	/**
	 * 商业险续保提前天数
	 */
	@ApiModelProperty(value = "商业险续保提前天数")
	private Integer daysInAdvanceOfRenewalBi;
	/**
	 * 是否送修 1是 0否
	 */
	@ApiModelProperty(value = "是否送修 1是 0否")
	private String isRepair;
	/**
	 * 自主评分
	 */
	@ApiModelProperty(value = "自主评分")
	private BigDecimal selfScore;
	/**
	 * 政策匹配状态 1:一单一议政策; 2:常规政策；3:未匹配政策
	 */
	@ApiModelProperty(value = "政策匹配状态 1:一单一议政策; 2:常规政策；3:未匹配政策")
	private String policyStatus;
	/**
	 * 连续承保期间出险次数
	 */
	@ApiModelProperty(value = "连续承保期间出险次数")
	private BigDecimal numberOfUnderwriting;
	/**
	 * 交强险精算纯风险保费
	 */
	@ApiModelProperty(value = "交强险精算纯风险保费")
	private BigDecimal actuarialPureRiskPremiumCi;
	/**
	 * 商业险精算纯风险保费
	 */
	@ApiModelProperty(value = "商业险精算纯风险保费")
	private BigDecimal actuarialPureRiskPremiumBi;
	/**
	 * 承保年限
	 */
	@ApiModelProperty(value = "承保年限")
	private BigDecimal underwritingPeriod;
	/**
	 * 交通违法系数
	 */
	@ApiModelProperty(value = "交通违法系数")
	private BigDecimal trafficViolationCoefficient;
	/**
	 * 车辆的评分
	 */
	@ApiModelProperty(value = "车辆的评分")
	private BigDecimal vehicleRating;
	/**
	 * 合作伙伴用户名
	 */
	@ApiModelProperty(value = "合作伙伴用户名")
	private String realName;
	/**
	 * 合作伙伴身份证号码
	 */
	@ApiModelProperty(value = "合作伙伴身份证号码")
	private String idCardNo;

	/**
	 * 投保日期
	 */
	private LocalDateTime issueTime;
	/**
	 * 报价日期
	 */
	private LocalDateTime offerTime;

	/**
	 * 修改前出单员
	 */
	private String creUserBeforeName;

	/**
	 * 修改前出单员用户id
	 */
	private Integer creUserBeforeUserId;

	/**
	 * 无赔优系数级别
	 */
	@ApiModelProperty(value = "无赔优系数级别")
	private String claimAdjustLevel;
	/**
	 * 提交审核人id
	 */
	@ApiModelProperty(value = "提交审核人id")
	private Integer submitterId;
	/**
	 * 提交审核人名称
	 */
	@ApiModelProperty(value = "提交审核人名称")
	private String submitterName;
	/**
	 * 提交审核人机构代码
	 */
	@ApiModelProperty(value = "提交审核人机构代码")
	private String submitterDepartCode;
	/**
	 * 提交审核人机构名称
	 */
	@ApiModelProperty(value = "提交审核人机构名称")
	private String submitterDepartName;
	/**
	 * 提交审核人时间
	 */
	@ApiModelProperty(value = "提交审核人时间")
	private Date submitTime;
}
