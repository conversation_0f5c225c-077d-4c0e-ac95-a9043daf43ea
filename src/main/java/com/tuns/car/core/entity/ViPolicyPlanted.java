package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 车险政策险种表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_policy_planted")
public class ViPolicyPlanted implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    @TableId
    private Integer id;
    /**
     * 批次id
     */
    @ApiModelProperty(value = "批次id")
    private Integer batchId;
    /**
     * 险种代码
     */
    @ApiModelProperty(value = "险种代码")
    private String plantCode;
    /**
     * 险种名称
     */
    @ApiModelProperty(value = "险种名称")
    private String plantName;
    /**
     * 险种内容详情
     */
    @ApiModelProperty(value = "险种内容详情")
    private String plantDetail;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Integer creUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date creTm;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private Integer mdfUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date mdfTm;
    /**
     * 是否删除  0否,1是
     */
    @ApiModelProperty(value = "是否删除  0否,1是")
    private String delFlag;
    /**
     * 创建人名字
     */
    @ApiModelProperty(value = "创建人名字")
    private String creUserName;
    /**
     * 更新人名字
     */
    @ApiModelProperty(value = "更新人名字")
    private String mdfUserName;
    /**
     * 政策类型（1：交商险政策  2：奖励政策）
     * 同时配置了交商险政跟奖励政策 存储数据为1,2
     */
    @ApiModelProperty(value = "政策类型（1：交商险政策  2：奖励政策） 同时配置了交商险政跟奖励政策 存储数据为1,2")
    private String policyType;

}
