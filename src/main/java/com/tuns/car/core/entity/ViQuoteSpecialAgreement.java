package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.PolicyTypeEnum;
import com.tuns.core.boot.entity.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * 报价-特约
 * @since 2023-5-16 14:58
 */
@TableName("vi_quote_special_agreement")
@Data
public class ViQuoteSpecialAgreement extends BaseEntity {
    /**
     * 批次ID
     */
    private Long policyBatchId;
    /**
     * 保单记录ID
     */
    private Long policyId;
    /**
     * 特约代码
     */
    private String engageCode;
    /**
     * 特约内容
     */
    private String engageContent;
    /**
     * 特约标题
     */
    private String engageTitle;
    /**
     * 交强险1/商业险2
     */
    private PolicyTypeEnum recordType;
    /**
     * 备注
     */
    private String remark;

    /**
     * 是否保司返回 1是；0否
     */
    private Integer insReturn;
}
