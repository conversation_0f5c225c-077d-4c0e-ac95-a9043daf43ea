package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险基础政策
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_base_policy")
public class ViBasePolicy implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 基础政策代码
	 */
	@ApiModelProperty(value = "基础政策代码")
	private String policyId;
	/**
	 * 政策名称
	 */
	@ApiModelProperty(value = "政策名称")
	private String policyName;
	/**
	 * 公司编码
	 */
	@ApiModelProperty(value = "公司编码")
	private String companyId;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 渠道编号
	 */
	@ApiModelProperty(value = "渠道编号")
	private String channelId;
	/**
	 * 渠道名称
	 */
	@ApiModelProperty(value = "渠道名称")
	private String channelName;
	/**
	 * 状态  0 待提交 1 未审核  2审核拒绝 3审核通过 4启用 5停用
	 */
	@ApiModelProperty(value = "状态  0 待提交 1 未审核  2审核拒绝 3审核通过 4启用 5停用")
	private String status;
	/**
	 * 政策详情
	 */
	@ApiModelProperty(value = "政策详情")
	private String policyDetail;
	/**
	 * 政策来源1基础政策 2 单件
	 */
	@ApiModelProperty(value = "政策来源1基础政策 2 单件")
	private String policySource;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 创建用户
	 */
	@ApiModelProperty(value = "创建用户")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改用户
	 */
	@ApiModelProperty(value = "修改用户")
	private Integer mdfUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;

}
