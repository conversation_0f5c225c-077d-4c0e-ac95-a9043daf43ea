package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.ViAttachTypeEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车险保单车辆附件表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
@TableName("vi_ins_attach_inf")
public class ViInsAttachInf extends BaseEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 保单批次ID
	 */
	@ApiModelProperty(value = "保单批次ID")
	private Long policyBatchId;
	/**
	 * 保单批次ID
	 */
	@ApiModelProperty(value = "保单批次ID")
	private Long serialNumber;
	/**
	 * 附件类型
	 */
	@ApiModelProperty(value = "附件类型")
	private ViAttachTypeEnum type;
	/**
	 * 附件路径
	 */
	@ApiModelProperty(value = "附件路径")
	@TableField("`key`")
	private String key;
	/**
	 * 附件文件名
	 */
	@ApiModelProperty(value = "附件文件名")
	private String fileName;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 文件类型
	 */
	@ApiModelProperty(value = "文件类型")
	private String fileType;

    /**
     * 验车图片类型(提供验车图片使用)
     */
    @ApiModelProperty(value = "附件类型")
    private ViAttachTypeEnum carImgType;

}
