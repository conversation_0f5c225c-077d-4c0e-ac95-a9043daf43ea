package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据典明细表 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-03 15:06:08
 */
@Data
@TableName("cmm_dict_detailed")
public class CmmDictDetailed extends BaseEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 字典明细id
	 */
	@ApiModelProperty(value = "字典明细id")
	private Long detailedId;
	/**
	 * 类型编号
	 */
	@ApiModelProperty(value = "类型编号")
	private String typeCode;
	/**
	 * 字典编码
	 */
	@ApiModelProperty(value = "字典编码")
	private String code;
	/**
	 * 字典值
	 */
	@ApiModelProperty(value = "字典值")
	private String value;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer indexs;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
