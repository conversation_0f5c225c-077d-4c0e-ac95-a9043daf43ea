package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险投保附加服务条款影响因素详情表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
@TableName("vi_ins_addition_clause_element_detail")
public class ViInsAdditionClauseElementDetail implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@ApiModelProperty(value = "主键ID")
	@TableId
	private Integer id;
	/**
	 * 影响因素详情id
	 */
	@ApiModelProperty(value = "影响因素详情id")
	private String additionClauseElementDetailId;
	/**
	 * 影响因素id
	 */
	@ApiModelProperty(value = "影响因素id")
	private String additionClauseElementId;
	/**
	 * 影响因素
	 */
	@ApiModelProperty(value = "影响因素")
	private String influenceFactor;
	/**
	 * 条件类型
	 */
	@ApiModelProperty(value = "条件类型")
	private String conditionType;
	/**
	 * 条件列表
	 */
	@ApiModelProperty(value = "条件列表")
	private String conditionList;

}
