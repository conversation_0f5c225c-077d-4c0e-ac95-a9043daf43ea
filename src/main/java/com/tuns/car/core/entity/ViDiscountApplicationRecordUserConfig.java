package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 折扣审核记录用户配置表
 *
 * <AUTHOR>
 * @since 2024-05-06 16:39
 **/
@Data
@TableName("vi_discount_application_record_user_config")
public class ViDiscountApplicationRecordUserConfig extends BaseEntity {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Integer userId;

}
