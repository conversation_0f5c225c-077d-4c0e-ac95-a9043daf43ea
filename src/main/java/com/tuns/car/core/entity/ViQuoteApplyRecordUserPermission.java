package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.SimpleBaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-08-08 18:50
 **/
@Data
@TableName("vi_quote_apply_record_user_permission")
public class ViQuoteApplyRecordUserPermission extends SimpleBaseEntity {
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Integer userId;
}
