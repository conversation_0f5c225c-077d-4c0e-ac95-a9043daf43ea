package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * company  公司信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-03 14:44:18
 */
@Data
@TableName("cmm_company")
public class CmmCompany implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    @TableId
    private Integer id;
    /**
     * 保险公司编号四位组成：1001
     * 第一位:公司类型 , 后三位：保险公司
     * 1001:产险-人保
     * 1:产险 2:养老寿 3:寿险
     */
    @ApiModelProperty(value = "保险公司编号四位组成：1001 第一位:公司类型 , 后三位：保险公司 1001:产险-人保 1:产险 2:养老寿 3:寿险")
    private String companyId;
    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称")
    private String companyName;
    /**
     * 保险公司代码
     */
    @ApiModelProperty(value = "保险公司代码")
    private String companyCd;
    /**
     * 保险公司性质
     */
    @ApiModelProperty(value = "保险公司性质")
    private String nature;
    /**
     * 保险公司规模
     */
    @ApiModelProperty(value = "保险公司规模")
    private String scale;
    /**
     * 保险公司地址
     */
    @ApiModelProperty(value = "保险公司地址")
    private String address;
    /**
     * 保险公司电话
     */
    @ApiModelProperty(value = "保险公司电话")
    private String telephone;
    /**
     * 保险公司简介
     */
    @ApiModelProperty(value = "保险公司简介")
    private String briefIntroduction;
    /**
     * 公司LOGO
     */
    @ApiModelProperty(value = "公司LOGO")
    private String companyLogo;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTm;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Integer creUser;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private Integer mdfUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date mdfTm;
    /**
     * 删除标志(0 删除,1正常)
     */
    @ApiModelProperty(value = "删除标志(0 删除,1正常)")
    @TableLogic(value = "0",delval = "1")
    private String delFlag;
    /**
     * 启用状态(0 未启用,1启用  2停用)
     */
    @ApiModelProperty(value = "启用状态(0 未启用,1启用  2停用)")
    private String startSts;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String companyFullName;
    /**
     * 是否展示  1是  0否
     */
    @ApiModelProperty(value = "是否展示  1是  0否")
    private String isShow;
    /**
     * 理赔报案信息标题
     */
    @ApiModelProperty(value = "理赔报案信息标题")
    private String claimsTitle;
    /**
     * 保险公司理赔说明
     */
    @ApiModelProperty(value = "保险公司理赔说明")
    private String claimsThat;
    /**
     * 客服电话
     */
    @ApiModelProperty(value = "客服电话")
    private String serviceTel;
    /**
     * 是否开启比对配置 0关闭 1 开启
     */
    @ApiModelProperty(value = "是否开启比对配置 0关闭 1 开启")
    private String compareFlag;
    /**
     * 排序
     */
    private Integer sort;

}
