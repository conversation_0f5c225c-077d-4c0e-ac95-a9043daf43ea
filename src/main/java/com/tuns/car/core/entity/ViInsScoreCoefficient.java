package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.InsuredTypeEnum;
import com.tuns.car.core.constant.SecondaryNewCarEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 保单评分及系数表
 *
 * <AUTHOR>
 * @date 2022-05-13 13:50:40
 */
@Data
@TableName("vi_ins_score_coefficient")
public class ViInsScoreCoefficient extends BaseEntity {

    /**
     * 保单批次ID   ----关联vi_ins_plcy_inf的批次id字段
     */
    @ApiModelProperty(value = "保单批次ID   ----关联vi_ins_plcy_inf的批次id字段")
    private Long policyBatchId;

    /**
     * 车慧达分档
     */
    @ApiModelProperty(value = "车慧达分档")
    private String cheHuidaTranche;

    /**
     * 合作网点
     */
    @ApiModelProperty(value = "合作网点")
    private String cooperativeNetwork;

    /**
     * 风险等级
     */
    @ApiModelProperty(value = "风险等级")
    private String riskLevel;

    /**
     * 车险分
     */
    @ApiModelProperty(value = "车险分")
    private BigDecimal autoInsurancePoints;

    /**
     * 期望折扣
     */
    @ApiModelProperty(value = "期望折扣")
    private Double expectedDiscount;

    /**
     * 建议折扣
     */
    @ApiModelProperty(value = "建议折扣")
    private Double proposalDiscount;

    /**
     * 车险分
     */
    @ApiModelProperty(value = "期望折扣与建议折扣差额")
    private Double differenceDiscount;

    /**
     * 车损险北理新能源评分
     */
    @ApiModelProperty(value = "车损险北理新能源评分")
    private String bitDamageScore;

    /**
     * 商业人保分
     */
    private BigDecimal piccScoreBi;

    /**
     * 交强人保分
     */
    private BigDecimal piccScoreCi;

    /**
     * 交强险初始手续费比例
     */
    private BigDecimal handFeeRatioCi;

    /**
     * 商业险初始手续费比例
     */
    private BigDecimal handFeeRatioBi;


    /**
     * NCD标费预期赔付率
     */
    @ApiModelProperty(value = "NCD标费预期赔付率")
    private BigDecimal ncdEcompensationRate;
    /**
     * 交商合计含NCD标准保费预期赔付率
     */
    @ApiModelProperty(value = "交商合计含NCD标准保费预期赔付率")
    private BigDecimal ncdTotalEcompensationRate;

    /**
     * 连续承保期间出险次数(交强险)
     */
    @ApiModelProperty(value = "连续承保期间出险次数(交强险)")
    private BigDecimal numberOfUnderwritingCi;

    /**
     * 商业险NCD标费预期赔付率
     */
    @ApiModelProperty(value = "商业险NCD标费预期赔付率")
    private BigDecimal ncdEcompensationRateBi;

    /**
     * 客户是否注册中华保的小程序
     */
    @ApiModelProperty(value = "客户是否注册中华保的小程序")
    private String chineseApplet;
    /**
     * 次新车
     */
    @ApiModelProperty(value = "次新车")
    private SecondaryNewCarEnum secondaryNewCar;

    /**
     * 申能-车系分类
     */
    @ApiModelProperty(value = "车系分类")
    private String selfVehcType;
    /**
     * 上年出险次数（交商合计）
     */
    @ApiModelProperty(value = "上年出险次数（交商合计）")
    private Integer lastClaimCountTotal;
    /**
     * 合作方名称
     */
    @ApiModelProperty(value = "合作方名称")
    private String partnerName;
    /**
     * 业务分组名称
     */
    @ApiModelProperty(value = "业务分组名称")
    private String businessGroupName;
    /**
     * 精准使用年限
     */
    @ApiModelProperty(value = "精准使用年限")
    private BigDecimal preciseUseYear;

    /**
     * 是否真单交
     */
    @ApiModelProperty(value = "是否真单交")
    private String isDjq;

    /**
     * 交强  业务类型 1-新保 2-续保 3-转保
     */
    @ApiModelProperty(value = "业务类型 1-新保 2-续保 3-转保")
    private InsuredTypeEnum insuredTypeCi;

    /**
     * 商业 业务类型 1-新保 2-续保 3-转保
     */
    @ApiModelProperty(value = "业务类型 1-新保 2-续保 3-转保")
    private InsuredTypeEnum insuredTypeBi;

    /**
     * 建议自主定价系数
     */
    @ApiModelProperty(value = "建议自主定价系数")
    private BigDecimal proposeIndependentPriceRate;

    @ApiModelProperty(value = "亚太分")
    private BigDecimal asiaPacificScore;

    @ApiModelProperty(value = "团车码")
    private String teamCarCode;

    @ApiModelProperty(value = "交强险尊享分")
    private String trafficEnjoyScore;

    @ApiModelProperty(value = "商业险尊享分")
    private String commEnjoyScore;

    @ApiModelProperty(value = "尊享分（交商合计评分字段）")
    private String totalEnjoyScore;

    @ApiModelProperty(value = "单商业的交强险所属保司（当年）")
    private String compTrafficCompanyBi;

    @ApiModelProperty(value = "交强险不浮动原因")
    private String noFloatReasonCi;

    @ApiModelProperty(value = "商业险不浮动原因")
    private String noFloatReasonBi;

    @ApiModelProperty(value = "国任决策单元")
    private String xdcxDmu;
    /**
     * 交强新转续二级标识
     */
    @ApiModelProperty(value = "交强新转续二级标识")
    private String thisRenewalCi;
    /**
     * 商业新转续二级标识
     */
    @ApiModelProperty(value = "商业新转续二级标识")
    private String thisRenewalBi;

    /**
     * 人保归属机构
     */
    @ApiModelProperty(value = "人保归属机构")
    private String piccBelongDepartment;

    @ApiModelProperty(value = "业务类别")
    private String businessCategory;
    /**
     * 小货车综合评分等级
     */
    @ApiModelProperty(value = "小货车综合评分等级")
    private String comprehensiveRiskRating;
    /**
     * 货车评分
     */
    @ApiModelProperty(value = "货车评分")
    private String truckRating;

    @ApiModelProperty(value = "违章风险评级")
    private String violationRiskLevel;

    @ApiModelProperty(value = "业务类型(紫金)")
    private String businessType;

    @ApiModelProperty(value = "续保类型(交强险)")
    private String renewalTypeCi;

    @ApiModelProperty(value = "续保类型(商业险)")
    private String renewalTypeBi;

    @ApiModelProperty(value = "出单口")
    private String underwritingChannel;

    @ApiModelProperty(value = "中交兴路评分（商业险）")
    private BigDecimal zjxlBiScore;
}
