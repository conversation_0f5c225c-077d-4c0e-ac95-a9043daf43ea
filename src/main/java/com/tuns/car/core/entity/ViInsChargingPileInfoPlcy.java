package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-12 08:40:13
 */
@Data
@TableName("vi_ins_charging_pile_info_plcy")
public class ViInsChargingPileInfoPlcy extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 报价单ID
	 */
	@ApiModelProperty(value = "报价单ID")
	private Long policyBatchId;
	/**
	 * 充电桩型号
	 */
	@ApiModelProperty(value = "充电桩型号")
	private String chargingModel;
	/**
	 * 充电桩编码
	 */
	@ApiModelProperty(value = "充电桩编码")
	private String chargingCode;
	/**
	 * 充电桩类型
	 */
	@ApiModelProperty(value = "充电桩类型")
	private String chargingType;
	/**
	 * 充电桩安装地点类型
	 */
	@ApiModelProperty(value = "充电桩安装地点类型")
	private String chargingInstallAddrType;
	/**
	 * 安装时间
	 */
	private LocalDate chargingInstallDate;
	/**
	 * 充电桩使用年限
	 */
	@ApiModelProperty(value = "充电桩使用年限")
	private String chargingUseYears;
	/**
	 * 充电桩完整地址
	 */
	@ApiModelProperty(value = "充电桩完整地址")
	private String chargingAddrComplete;
	/**
	 * 充电桩详细地址
	 */
	@ApiModelProperty(value = "充电桩详细地址")
	private String chargingAddrDetail;
	/**
	 * 省编码
	 */
	@ApiModelProperty(value = "省编码")
	private String province;
	/**
	 * 省名称
	 */
	@ApiModelProperty(value = "省名称")
	private String provinceName;
	/**
	 * 市编码
	 */
	@ApiModelProperty(value = "市编码")
	private String city;
	/**
	 * 市名称
	 */
	@ApiModelProperty(value = "市名称")
	private String cityName;
	/**
	 * 区县编码
	 */
	@ApiModelProperty(value = "区县编码")
	private String county;
	/**
	 * 区县名称
	 */
	@ApiModelProperty(value = "区县名称")
	private String countyName;
	/**
	 * 1报价单 2保单
	 */
	@ApiModelProperty(value = "1报价单 2保单")
	private String busiType;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer carPlcyRelationId;
	/**
	 * 业务主键id
	 */
	@ApiModelProperty(value = "业务主键id")
	private Long chargingPileId;

}
