package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险附加机构政策表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_extral_depart_policy")
public class ViExtralDepartPolicy implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String extralPolicyId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String departCode;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer creUser;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date creTm;

}
