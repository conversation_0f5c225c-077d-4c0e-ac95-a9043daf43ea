package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_self_ratio_batch")
public class ViSelfRatioBatch implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@TableId
	private Integer id;
	/**
	 * 批次id
	 */
	@ApiModelProperty(value = "批次id")
	private String batchId;
	/**
	 * 批次代码
	 */
	@ApiModelProperty(value = "批次代码")
	private String batchCode;
	/**
	 * 批次名称
	 */
	@ApiModelProperty(value = "批次名称")
	private String batchName;
	/**
	 * 保险公司id
	 */
	@ApiModelProperty(value = "保险公司id")
	private String companyId;
	/**
	 * 保险公司名字
	 */
	@ApiModelProperty(value = "保险公司名字")
	private String companyName;
	/**
	 * 保险公司渠道id
	 */
	@ApiModelProperty(value = "保险公司渠道id")
	private String channelId;
	/**
	 * 保险公司渠道名称
	 */
	@ApiModelProperty(value = "保险公司渠道名称")
	private String channelName;
	/**
	 * 自主定价系数最小值
	 */
	@ApiModelProperty(value = "自主定价系数最小值")
	private BigDecimal ratioMin;
	/**
	 * 是否展示推荐系数  0否,1是
	 */
	@ApiModelProperty(value = "是否展示推荐系数  0否,1是")
	private String isShow;
	/**
	 * 系数状态
	 */
	@ApiModelProperty(value = "系数状态")
	private String status;
	/**
	 * 审核不通过的原因
	 */
	@ApiModelProperty(value = "审核不通过的原因")
	private String reason;
	/**
	 * 启用开始时间
	 */
	@ApiModelProperty(value = "启用开始时间")
	private LocalDateTime beginTm;
	/**
	 * 启用结束时间
	 */
	@ApiModelProperty(value = "启用结束时间")
	private Date endTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 创建人名字
	 */
	@ApiModelProperty(value = "创建人名字")
	private String creUserName;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private Integer mdfUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 更新人名字
	 */
	@ApiModelProperty(value = "更新人名字")
	private String mdfUserName;
	/**
	 * 是否删除  0否,1是
	 */
	@ApiModelProperty(value = "是否删除  0否,1是")
	private String delFlag;

}
