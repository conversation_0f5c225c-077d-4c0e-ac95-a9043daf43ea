package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_self_ratio_rule")
public class ViSelfRatioRule implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@TableId
	private Integer id;
	/**
	 * 批次id
	 */
	@ApiModelProperty(value = "批次id")
	private String batchId;
	/**
	 * 规则id
	 */
	@ApiModelProperty(value = "规则id")
	private String ruleId;
	/**
	 * 规则代码
	 */
	@ApiModelProperty(value = "规则代码")
	private String ruleCode;
	/**
	 * 规则名称
	 */
	@ApiModelProperty(value = "规则名称")
	private String ruleName;
	/**
	 * 规则内容详情
	 */
	@ApiModelProperty(value = "规则内容详情")
	private String ruleDetail;
	/**
	 * 自主定价系数
	 */
	@ApiModelProperty(value = "自主定价系数")
	private BigDecimal ruleRatio;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 创建人名字
	 */
	@ApiModelProperty(value = "创建人名字")
	private String creUserName;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private Integer mdfUser;
	/**
	 * 更新人名字
	 */
	@ApiModelProperty(value = "更新人名字")
	private String mdfUserName;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 是否删除  0否,1是
	 */
	@ApiModelProperty(value = "是否删除  0否,1是")
	private String delFlag;

}
