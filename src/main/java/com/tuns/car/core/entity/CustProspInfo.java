package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 准客户信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@TableName("cust_prosp_info")
public class CustProspInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 客户编号
     */
    private Integer custId;

    /**
     * 业务员工号
     */
    private Integer userId;

    /**
     * 客户类型：1-个人；2-团体
     */
    private String custType;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 联系电话
     */
    private String cellphone;

    /**
     * 省份编码
     */
    private String province;

    /**
     * 市级编码
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 注册来源：1-APP；2-WEB
     */
    private String regFrom;

    /**
     * 备注
     */
    private String remark;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 性别：1-男；2-女
     */
    private String sex;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 联系人电话
     */
    private String telphone;

    /**
     * 家庭住址
     */
    private String homeAddr;

    /**
     * 邮箱
     */
    private String email;

    /**
     * QQ号
     */
    private String qq;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 爱好
     */
    private String hobby;

    /**
     * 联系人备注
     */
    private String note;

    /**
     * 注册时间
     */
    private LocalDateTime regTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime creTm;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creUser;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime mdfTm;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer mdfUser;

    @Override
    public String toString() {
        return "CustProspInfo{" +
                "id=" + id +
                ", custId=" + custId +
                ", userId=" + userId +
                ", custType=" + custType +
                ", custName=" + custName +
                ", cellphone=" + cellphone +
                ", province=" + province +
                ", city=" + city +
                ", address=" + address +
                ", regFrom=" + regFrom +
                ", remark=" + remark +
                ", realName=" + realName +
                ", sex=" + sex +
                ", birthday=" + birthday +
                ", mobile=" + mobile +
                ", telphone=" + telphone +
                ", homeAddr=" + homeAddr +
                ", email=" + email +
                ", qq=" + qq +
                ", wechat=" + wechat +
                ", hobby=" + hobby +
                ", note=" + note +
                ", regTime=" + regTime +
                ", creUser=" + creUser +
                ", creTm=" + creTm +
                ", mdfUser=" + mdfUser +
                ", mdfTm=" + mdfTm +
                "}";
    }
}
