package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 保险公司配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-01 16:41:57
 */
@Data
@TableName("cmm_company_config")
public class CmmCompanyConfig implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String busType;
	/**
	 * 保险公司ID
	 */
	@ApiModelProperty(value = "保险公司ID")
	private String companyId;
	/**
	 * 保险公司名称
	 */
	@ApiModelProperty(value = "保险公司名称")
	private String companyName;
	/**
	 * 接口前缀地址 域名/ip+端口
	 */
	@ApiModelProperty(value = "接口前缀地址 域名/ip+端口")
	private String address;
	/**
	 * 车型查询路径
	 */
	@ApiModelProperty(value = "车型查询路径")
	private String vehicleModelPath;
	/**
	 * 精确车型查询标志 0-否 1-是
	 */
	@ApiModelProperty(value = "精确车型查询标志 0-否 1-是")
	private String preciseVehicleMark;
	/**
	 * 车辆实际价值计算路径
	 */
	@ApiModelProperty(value = "车辆实际价值计算路径")
	private String actualValuePath;
	/**
	 * 保费计算路径
	 */
	@ApiModelProperty(value = "保费计算路径")
	private String premiumCaculatePath;
	/**
	 * 投保单保存路径
	 */
	@ApiModelProperty(value = "投保单保存路径")
	private String proposalSavePath;
	/**
	 * 投保单取消路径
	 */
	@ApiModelProperty(value = "投保单取消路径")
	private String proposalCancelPath;
	/**
	 * 投保单详情路径
	 */
	@ApiModelProperty(value = "投保单详情路径")
	private String proposalDetialPath;
	/**
	 * 保单详情路径
	 */
	@ApiModelProperty(value = "保单详情路径")
	private String policyDetailPath;
	/**
	 * 订单状态路径
	 */
	@ApiModelProperty(value = "订单状态路径")
	private String orderStatusPath;
	/**
	 * 支付地址
	 */
	@ApiModelProperty(value = "支付地址")
	private String paymentPath;
	/**
	 * 第二支付地址
	 */
	@ApiModelProperty(value = "第二支付地址")
	private String twoPaymentPath;
	/**
	 * 续保查询路径
	 */
	@ApiModelProperty(value = "续保查询路径")
	private String renewalQueryPath;
	/**
	 * 附件上传路径
	 */
	@ApiModelProperty(value = "附件上传路径")
	private String accessoryUploadPath;
	/**
	 * 附件下载路径
	 */
	@ApiModelProperty(value = "附件下载路径")
	private String accessoryDownloadPath;
	/**
	 * 回调前端地址
	 */
	@ApiModelProperty(value = "回调前端地址")
	private String callBackPage;
	/**
	 * 回调后台地址
	 */
	@ApiModelProperty(value = "回调后台地址")
	private String callBackInsured;
	/**
	 * 核保回调地址
	 */
	@ApiModelProperty(value = "核保回调地址")
	private String callBackUnderwriting;
	/**
	 * 支付回调地址
	 */
	@ApiModelProperty(value = "支付回调地址")
	private String callBackPay;
	/**
	 * 类路径
	 */
	@ApiModelProperty(value = "类路径")
	private String classPath;
	/**
	 * 是否测试 0-否 1-是
	 */
	@ApiModelProperty(value = "是否测试 0-否 1-是")
	private String testMark;
	/**
	 * 是否启用
	 */
	@ApiModelProperty(value = "是否启用")
	private String isEnable;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date mdfTm;
	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	private Integer mdfUser;
	/**
	 * 删除标志
	 */
	@ApiModelProperty(value = "删除标志")
	private String delFlag;
	/**
	 * 车型数据新旧标志 0-否 1-是
	 */
	@ApiModelProperty(value = "车型数据新旧标志 0-否 1-是")
	private String modelCodeFlag;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String whetherDiscount;

}
