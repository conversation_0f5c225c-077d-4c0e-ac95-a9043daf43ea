package com.tuns.car.core.entity;

import com.tuns.car.core.constant.BooleanEnum;
import com.tuns.car.core.constant.ConditionTypeEnum;
import com.tuns.core.boot.entity.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * 车险系数规则明细表
 * @date 2024-5-6 16:20
 */
@Data
public class ViCoefficientRuleConfigDetail extends BaseEntity {
    /**
     * 规则代码
     */
    private Long ruleCode;
    /**
     * 因子编码(影响因素)
     */
    private String factorCode;
    /**
     * 条件类型 1指定 2除外 3区间
     */
    private ConditionTypeEnum conditionType;
    /**
     * 条件明细
     */
    private String conditionDetail;
    /**
     * 开始值
     */
    private String beginValue;
    /**
     * 结束值
     */
    private String endValue;
    /**
     * 开始值是否包含 1是 0否
     */
    private BooleanEnum beginContain;
    /**
     * 结束值是否包含 1是 0否
     */
    private BooleanEnum endContain;
    /**
     * 目前给前端用做回显
     */
    private String remark;
}
