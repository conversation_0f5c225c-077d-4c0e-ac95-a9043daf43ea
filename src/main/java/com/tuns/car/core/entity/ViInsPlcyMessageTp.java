package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.MsgTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 车险报价消息提示表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_ins_plcy_message_tp")
public class ViInsPlcyMessageTp implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@ApiModelProperty(value = "主键ID")
	@TableId
	private Long id;
	/**
	 * 流程流水号
	 */
	@ApiModelProperty(value = "流程流水号")
	private Long serialNumber;
	/**
	 * 保单批次ID
	 */
	@ApiModelProperty(value = "保单批次ID")
	private Long policyBatchId;
	/**
	 * 消息类型: 1-正常业务提示 , 2-重复投保 , 3-预核保 , 4-核保 
	 */
	@ApiModelProperty(value = "消息类型: 1-正常业务提示 , 2-重复投保 , 3-预核保 , 4-核保 ")
	private MsgTypeEnum msgType;
	/**
	 * 消息提示语
	 */
	@ApiModelProperty(value = "消息提示语")
	private String msgInfo;
	/**
	 * 删除标志 0-未删除 1-已删除
	 */
	@ApiModelProperty(value = "删除标志 0-未删除 1-已删除")
	@TableField(fill = FieldFill.INSERT)
	private String delFlag;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	@TableField(fill = FieldFill.INSERT)
	private LocalDateTime creTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	@TableField(fill = FieldFill.UPDATE)
	private LocalDateTime mdfTm;
	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	private Integer mdfUser;

}
