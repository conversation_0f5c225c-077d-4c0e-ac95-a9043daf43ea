package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险预约表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_car_appoint")
public class ViCarAppoint implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 业务id
	 */
	@ApiModelProperty(value = "业务id")
	private String businessId;
	/**
	 * 车牌号
	 */
	@ApiModelProperty(value = "车牌号")
	private String number;
	/**
	 * 手机号码
	 */
	@ApiModelProperty(value = "手机号码")
	private String phone;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 数据来源_code
	 */
	@ApiModelProperty(value = "数据来源_code")
	private String sourceId;
	/**
	 * 数据来源_名称
	 */
	@ApiModelProperty(value = "数据来源_名称")
	private String sourceName;
	/**
	 * 保险到期日
	 */
	@ApiModelProperty(value = "保险到期日")
	private Date endTm;
	/**
	 * 跟进状态 01待跟进 02已报价 03已出单 04已流失
	 */
	@ApiModelProperty(value = "跟进状态 01待跟进 02已报价 03已出单 04已流失")
	private String status;
	/**
	 * 报价时间
	 */
	@ApiModelProperty(value = "报价时间")
	private Date offerTm;
	/**
	 * 出单时间
	 */
	@ApiModelProperty(value = "出单时间")
	private Date issueTm;
	/**
	 * 流失时间
	 */
	@ApiModelProperty(value = "流失时间")
	private Date lossTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private Integer mdfUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 删除标志 1是 0否
	 */
	@ApiModelProperty(value = "删除标志 1是 0否")
	private String delFlag;
	/**
	 * 报价人
	 */
	@ApiModelProperty(value = "报价人")
	private Integer offerUser;
	/**
	 * 出单人
	 */
	@ApiModelProperty(value = "出单人")
	private Integer issueUser;
	/**
	 * 流失人
	 */
	@ApiModelProperty(value = "流失人")
	private Integer lossUser;
	/**
	 * 报价人
	 */
	@ApiModelProperty(value = "报价人")
	private String offerUserName;
	/**
	 * 出单人
	 */
	@ApiModelProperty(value = "出单人")
	private String issueUserName;
	/**
	 * 流失人
	 */
	@ApiModelProperty(value = "流失人")
	private String lossUserName;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer followUser;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String followUserName;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date followTime;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String followDescribe;

}
