package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
@TableName("vi_ins_addition_clause_element")
public class ViInsAdditionClauseElement implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@TableId
	private Integer id;
	/**
	 * 影响因素id
	 */
	@ApiModelProperty(value = "影响因素id")
	private String additionClauseElementId;
	/**
	 * 服务条款主键ID
	 */
	@ApiModelProperty(value = "服务条款主键ID")
	private String additionClauseId;
	/**
	 * 因素内容
	 */
	@ApiModelProperty(value = "因素内容")
	private String elementContent;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String mdfUser;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date mdfTm;

}
