package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.PolicyTypeEnum;
import com.tuns.core.boot.entity.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-5-19 8:52
 */
@TableName("coms_vi_ins_special_agreement")
@Data
public class ComsViInsSpecialAgreement extends BaseEntity {
    /**
     * 批次ID
     */
    private Long policyBatchId;
    /**
     * 保单记录ID
     */
    private Long policyId;
    /**
     * 特约代码
     */
    private String engageCode;
    /**
     * 特约内容
     */
    private String engageContent;
    /**
     * 特约标题
     */
    private String engageTitle;
    /**
     * 交强险1/商业险2/交商同保3
     */
    private PolicyTypeEnum recordType;
    /**
     * 备注
     */
    private String remark;
}
