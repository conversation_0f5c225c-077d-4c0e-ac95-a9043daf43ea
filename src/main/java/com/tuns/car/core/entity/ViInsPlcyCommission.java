package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_ins_plcy_commission")
public class ViInsPlcyCommission implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 保单记录ID
	 */
	@ApiModelProperty(value = "保单记录ID")
	private Long policyId;
	/**
	 * 保单类型 1 交强 2商业
	 */
	@ApiModelProperty(value = "保单类型 1 交强 2商业")
	private String policyType;
	/**
	 * 险种名称
	 */
	@ApiModelProperty(value = "规则名称")
	private String ruleName;
	/**
	 * 佣金率
	 */
	@ApiModelProperty(value = "佣金率")
	private BigDecimal commRate;
	/**
	 * 佣金类型 1交强(1),2商业(1), 3车船税(1), 4单件 5,附加,6调整佣金 ,7协作费,8交强(2),9商业(2),10车船税(2)
	 */
	@ApiModelProperty(value = "佣金类型 1交强(1),2商业(1), 3车船税(1), 4单件 5,附加,6调整佣金 ,7协作费,8交强(2),9商业(2),10车船税(2)")
	private String commType;
	/**
	 * 佣金金额
	 */
	@ApiModelProperty(value = "佣金金额")
	private BigDecimal commAmt;
	/**
	 * 任务流水
	 */
	@ApiModelProperty(value = "任务流水")
	private Long jobJrnNo;
	/**
	 * 结算方式 0 不含税, 1含税
	 */
	@ApiModelProperty(value = "结算方式 0 不含税, 1含税")
	private String isIncludeTax;
	/**
	 * 政策代码
	 */
	@ApiModelProperty(value = "政策代码")
	private String policyCode;
	/**
	 * 政策级别:01-总部 02-分公司 03-营业部 04-业务部
	 */
	@ApiModelProperty(value = "政策级别:01-总部 02-分公司 03-营业部 04-业务部")
	private String policyLevel;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String backSinglePolicyId;
	/**
	 * 分润金额
	 */
	@ApiModelProperty(value = "分润金额")
	private BigDecimal splitAmt;
	/**
	 * 创建用户
	 */
	@ApiModelProperty(value = "创建用户")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 是否计入收益 1计入 0不计入
	 */
	@ApiModelProperty(value = "是否计入收益 1计入 0不计入")
	private String incomeFlag;
	/**
	 * 政策名称
	 */
	@ApiModelProperty(value = "政策名称")
	private String policyName;
	/**
	 * 分支机构编码
	 */
	@ApiModelProperty(value = "分支机构编码")
	private String departCode;
	/**
	 * 协助id（来自mysql中的 vi_policy_commission_cooperation 表的 cooperation_id 字段，只有协助佣金类型时才存在值，其他的默认空值）
	 */
	@ApiModelProperty(value = "协助id（来自mysql中的 vi_policy_commission_cooperation 表的 cooperation_id 字段，只有协助佣金类型时才存在值，其他的默认空值）")
	private String cooperationId;
	/**
	 * 分润状态: 1.分润 2.不分润
	 */
	@ApiModelProperty(value = "分润状态: 1.分润 2.不分润")
	private String shareStatus;
	/**
	 * 父级机构编码
	 */
	@ApiModelProperty(value = "父级机构编码")
	private String parentDepartCode;
	/**
	 * 分支机构名称
	 */
	@ApiModelProperty(value = "分支机构名称")
	private String departName;
	/**
	 * 原始佣金率（分润前佣金率）
	 */
	@ApiModelProperty(value = "原始佣金率（分润前佣金率）")
	private String originCommRate;
	/**
	 * 保单批次id
	 */
	@ApiModelProperty(value = "保单批次id")
	private String policyBatchId;
	/**
	 * 规则代码
	 */
	@ApiModelProperty(value = "规则代码")
	private String ruleCode;
}
