package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险政策险种明细
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_policy_plant_detail")
public class ViPolicyPlantDetail implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 险种id
	 */
	@ApiModelProperty(value = "险种id")
	private Integer plantId;
	/**
	 * 政策类型
	 */
	@ApiModelProperty(value = "政策类型")
	private String policyType;
	/**
	 * 条件类型 1指定 2除外 3区间
	 */
	@ApiModelProperty(value = "条件类型 1指定 2除外 3区间")
	private String conditionType;
	/**
	 * 条件明细
	 */
	@ApiModelProperty(value = "条件明细")
	private String conditionDetail;
	/**
	 * 开始值
	 */
	@ApiModelProperty(value = "开始值")
	private String beginValue;
	/**
	 * 结束值
	 */
	@ApiModelProperty(value = "结束值")
	private String endValue;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 删除标志 0否, 1是
	 */
	@ApiModelProperty(value = "删除标志 0否, 1是")
	private String delFlag;
	/**
	 * 创建人名字
	 */
	@ApiModelProperty(value = "创建人名字")
	private String creUserName;
	/**
	 * 更新人名字
	 */
	@ApiModelProperty(value = "更新人名字")
	private String mdfUserName;
	/**
	 * 开始值是否包含 1是 0否
	 */
	@ApiModelProperty(value = "开始值是否包含 1是 0否")
	private String beginContain;
	/**
	 * 结束值是否包含 1是 0否
	 */
	@ApiModelProperty(value = "结束值是否包含 1是 0否")
	private String endContain;

}
