package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/11/28 9:07
 */
@Data
@TableName("cmm_company_detail")
public class CmmCompanyDetail implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 详情ID
     */
    @NotNull(message = "详情ID不能为空")
    private String detailId;

    /**
     * 保险公司ID
     */
    private String companyId;

    /**
     * 模块Id
     */

    private String modularId;

    /**
     * 模块对应的内容
     */
    private String context;

    /**
     * 模块类型
     */
    private String modularType;
}
