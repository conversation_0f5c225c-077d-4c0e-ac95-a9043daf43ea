package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@TableName("vi_ins_plcy_commission_single")
@Data
public class ViInsPlcyCommissionSingle {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long policyBatchId;
    private Long policyId;
    private String riskType;
    private String departCode;
    private String policyLevel;
    private BigDecimal originSingleRate;
    private BigDecimal originSingleFee;
    private BigDecimal originConversionSingleRate;
    private BigDecimal originConversionSingleFee;
    private BigDecimal floorSingleRate;
    private BigDecimal floorSingleFee;
    private BigDecimal floorConversionSingleRate;
    private BigDecimal floorConversionSingleFee;
    private BigDecimal ceilingSingleRate;
    private BigDecimal ceilingSingleFee;
    private BigDecimal ceilingConversionSingleRate;
    private BigDecimal ceilingConversionSingleFee;
    private BigDecimal finalSingleRate;
    private BigDecimal finalSingleFee;
    private BigDecimal finalConversionSingleRate;
    private BigDecimal finalConversionSingleFee;
    private BigDecimal finalSplitSingleRate;
    private BigDecimal finalSplitSingleFee;
    private BigDecimal finalSplitConversionSingleRate;
    private BigDecimal finalSplitConversionSingleFee;
    private Integer creUser;
    private LocalDateTime creTm;
    private Integer mdfUser;
    private LocalDateTime mdfTm;
}
