package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_merge_data_conf")
public class ViMergeDataConf implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	@TableId
	private Integer id;
	/**
	 * 截止时间（大于等于这个时间且小于等于当前系统时间-延迟时间）
	 */
	@ApiModelProperty(value = "截止时间（大于等于这个时间且小于等于当前系统时间-延迟时间）")
	private Date cutOffTime;
	/**
	 * 延迟时间（分钟），即延迟分钟内的数据不迁移
	 */
	@ApiModelProperty(value = "延迟时间（分钟），即延迟分钟内的数据不迁移")
	private Integer delayMinute;
	/**
	 * 业务数据，存最后更新的批次id
	 */
	@ApiModelProperty(value = "业务数据，存最后更新的批次id")
	private String businessData;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date mdfTm;
	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	private Integer mdfUser;
	/**
	 * 删除标志[0-存储,1-已删除]
	 */
	@ApiModelProperty(value = "删除标志[0-存储,1-已删除]")
	private String delFlag;

}
