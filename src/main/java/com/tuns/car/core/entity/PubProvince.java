package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 省份表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-01 17:31:26
 */
@Data
@TableName("pub_province")
public class PubProvince implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 省份id
	 */
	@ApiModelProperty(value = "省份id")
	@TableId
	private String province;
	/**
	 * 省份名称
	 */
	@ApiModelProperty(value = "省份名称")
	private String provinceName;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
