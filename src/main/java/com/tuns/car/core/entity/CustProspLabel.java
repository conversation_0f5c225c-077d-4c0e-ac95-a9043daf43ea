package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 准客户标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@TableName("cust_prosp_label")
public class CustProspLabel implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 客户编号
     */
    private Integer custId;

    /**
     * 客户分类
     */
    private String clazzLabel;

    /**
     * 客户重要性
     */
    private String importantLabel;

    /**
     * 自定义标签
     */
    private String defineLabel;

    /**
     * 创建时间
     */
    private LocalDateTime creTm;

    /**
     * 创建人
     */
    private Integer creUser;

    /**
     * 更新时间
     */
    private LocalDateTime mdfTm;

    /**
     * 更新人
     */
    private Integer mdfUser;

    @Override
    public String toString() {
        return "CustProspLabel{" +
                "id=" + id +
                ", custId=" + custId +
                ", clazzLabel=" + clazzLabel +
                ", importantLabel=" + importantLabel +
                ", defineLabel=" + defineLabel +
                ", creTm=" + creTm +
                ", creUser=" + creUser +
                ", mdfTm=" + mdfTm +
                ", mdfUser=" + mdfUser +
                "}";
    }
}
