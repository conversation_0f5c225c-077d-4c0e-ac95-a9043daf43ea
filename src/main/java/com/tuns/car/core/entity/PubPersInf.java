package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.supalle.autotrim.AutoTrim;
import com.tuns.car.core.constant.PersonDataTypeEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 车险保单关系人表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-01 09:06:30
 */
@Data
@AutoTrim
@TableName("pub_pers_inf")
@Accessors(chain = true)
public class PubPersInf extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据类型 避免多业务数据冲突
	 */
	@ApiModelProperty(value = "数据类型 避免多业务数据冲突")
	private PersonDataTypeEnum dataType;
	/**
	 * 保单批次ID
	 */
	@ApiModelProperty(value = "保单批次ID")
	private Long policyBatchId;
	/**
	 * 类型  01车主,02被保人,03投保人,04受益人,05收件人
	 */
	@ApiModelProperty(value = "类型  01车主,02被保人,03投保人,04受益人,05收件人")
	private String personType;
	/**
	 * 姓名
	 */
	@ApiModelProperty(value = "姓名")
	private String personName;
	/**
	 * 证件类型
	 */
	@ApiModelProperty(value = "证件类型")
	private String identifyType;
	/**
	 * 证件号码
	 */
	@ApiModelProperty(value = "证件号码")
	private String identifyNumber;
	/**
	 * 省编号
	 */
	@ApiModelProperty(value = "省编号")
	private String provinceNumber;
	/**
	 * 市编号
	 */
	@ApiModelProperty(value = "市编号")
	private String cityNumber;
	/**
	 * 区/县编号
	 */
	@ApiModelProperty(value = "区/县编号")
	private String countyNumber;
	/**
	 * 邮政编码
	 */
	@ApiModelProperty(value = "邮政编码")
	private String postCode;
	/**
	 * 详细联系地址 xx镇/街xxxxxx
	 */
	@ApiModelProperty(value = "详细联系地址 xx镇/街xxxxxx")
	private String addressDetail;
	/**
	 * 完整联系地址 xx省xx市xx区/县xx镇/街xxxxxx
	 */
	@ApiModelProperty(value = "完整联系地址 xx省xx市xx区/县xx镇/街xxxxxx")
	private String addressComplete;
	/**
	 * 手机号码
	 */
	@ApiModelProperty(value = "手机号码")
	private String mobilePhone;
	/**
	 * 联系人姓名
	 */
	@ApiModelProperty(value = "联系人姓名")
	private String contactName;
	/**
	 * 联系人手机号码
	 */
	@ApiModelProperty(value = "联系人手机号码")
	private String contactMobilePhone;
	/**
	 * 性别 1-男 2-女
	 */
	@ApiModelProperty(value = "性别 1-男 2-女")
	private String personSex;
	/**
	 * 驾龄
	 */
	@ApiModelProperty(value = "驾龄")
	private Integer drivingYears;
	/**
	 * 出生日期
	 */
	@ApiModelProperty(value = "出生日期")
	private String birthDate;
	/**
	 * 当前年龄
	 */
	@ApiModelProperty(value = "当前年龄")
	private Integer personAge;
	/**
	 * 性质 1-个人 2-机关 3-企业
	 */
	@ApiModelProperty(value = "性质 1-个人 2-机关 3-企业")
	private String nature;
	/**
	 * 扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）
	 */
	@ApiModelProperty(value = "扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）")
	private String extend;
	/**
	 * 客户编号
	 */
	@ApiModelProperty(value = "客户编号")
	private String custNumber;
	/**
	 * 证件有效期
	 */
	@ApiModelProperty(value = "证件有效期")
	private String identityValidity;
	/**
	 * 电子邮箱
	 */
	@ApiModelProperty(value = "电子邮箱")
	private String email;
	/**
	 * 国籍
	 */
	@ApiModelProperty(value = "国籍")
	private String nationality;
	/**
	 * 婚姻状态
	 */
	@ApiModelProperty(value = "婚姻状态")
	private String maritalStatus;
	/**
	 * 与主要关系人所属关系
	 */
	@ApiModelProperty(value = "与主要关系人所属关系")
	private String mainPersRelation;
	/**
	 * 所在单位
	 */
	@ApiModelProperty(value = "所在单位")
	private String organization;
	/**
	 * 单位地址
	 */
	@ApiModelProperty(value = "单位地址")
	private String organizationAddr;
	/**
	 * 国定电话
	 */
	@ApiModelProperty(value = "国定电话")
	private String landlinePhone;
	/**
	 * 职业
	 */
	@ApiModelProperty(value = "职业")
	private String profession;
	/**
	 * 职务
	 */
	@ApiModelProperty(value = "职务")
	private String jobPosition;
	/**
	 * 微信号
	 */
	@ApiModelProperty(value = "微信号")
	private String wechat;
	/**
	 * 开户银行
	 */
	@ApiModelProperty(value = "开户银行")
	private String bankDetialName;
	/**
	 * 银行账号
	 */
	@ApiModelProperty(value = "银行账号")
	private String bankAccount;
	/**
	 * 学历
	 */
	@ApiModelProperty(value = "学历")
	private String education;
	/**
	 * 最高职业代码
	 */
	@ApiModelProperty(value = "最高职业代码")
	private String maxPrfsCode;
	/**
	 * 兼职
	 */
	@ApiModelProperty(value = "兼职")
	private String partTimeJob;
	/**
	 * 身高
	 */
	@ApiModelProperty(value = "身高")
	private Integer height;
	/**
	 * 体重
	 */
	@ApiModelProperty(value = "体重")
	private Integer weight;
	/**
	 * 年收入
	 */
	@ApiModelProperty(value = "年收入")
	private Integer annualIncome;
	/**
	 * 受益顺序
	 */
	@ApiModelProperty(value = "受益顺序")
	private Integer beneficialOrder;
	/**
	 * 受益份额
	 */
	@ApiModelProperty(value = "受益份额")
	private BigDecimal benefitShare;
	/**
	 * 客户ID
	 */
	@ApiModelProperty(value = "客户ID")
	private Integer custId;

	/**
	 * 身份证有效起期
	 */
	@ApiModelProperty(value = "身份证有效起期")
	private String identityValidityStart;

}
