package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.NoDeleteBaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 折扣系数审核记录附件表
 *
 * <AUTHOR>
 * @since 2024-05-06 11:39
 **/
@Data
@TableName("vi_discount_application_record_attachment")
public class ViDiscountApplicationRecordAttachment extends NoDeleteBaseEntity {

    /**
     * 折扣系数ID
     */
    @ApiModelProperty(value = "折扣系数ID")
    private String discountFactorId;

    /**
     * 附件类型
     */
    @ApiModelProperty(value = "附件类型")
    private String type;

    /**
     * 附件路径
     */
    @ApiModelProperty(value = "附件路径")
    @TableField("`key`")
    private String key;

    /**
     * 附件文件名
     */
    @ApiModelProperty(value = "附件文件名")
    private String fileName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String fileType;

}
