package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-05-23 10:48:53
 */
@Data
@TableName("pub_cert_info")
public class PubCertInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 客户编号
	 */
	@ApiModelProperty(value = "客户编号")
	private Integer custId;
	/**
	 * 证件类型
	 */
	@ApiModelProperty(value = "证件类型")
	private String certType;
	/**
	 * 证件号码
	 */
	@ApiModelProperty(value = "证件号码")
	private String certNo;
	/**
	 * 证件有效期
	 */
	@ApiModelProperty(value = "证件有效期")
	private String certValidDt;

}
