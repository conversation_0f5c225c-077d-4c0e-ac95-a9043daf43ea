package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * ${comments}
 * @Description车险订单信息表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_order_info")
@Accessors(chain = true)
public class ViOrderInfo extends BaseEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 订单UUID
	 */
	@ApiModelProperty(value = "订单UUID")
	private String orderUuid;
	/**
	 * 订单编号
	 */
	@ApiModelProperty(value = "订单编号")
	private String orderNo;
	/**
	 * 报价主表ID
	 */
	@ApiModelProperty(value = "报价主表ID")
	private Long quoteId;
	/**
	 * 保险公司ID
	 */
	@ApiModelProperty(value = "保险公司ID")
	private String companyId;
	/**
	 * 合作渠道ID
	 */
	@ApiModelProperty(value = "合作渠道ID")
	private String channelId;
	/**
	 * 业务员对应的分支机构代码
	 */
	@ApiModelProperty(value = "业务员对应的分支机构代码")
	private String departCode;
	/**
	 * 保费
	 */
	@ApiModelProperty(value = "保费")
	private BigDecimal prem;
	/**
	 * 订单状态
	 */
	@ApiModelProperty(value = "订单状态")
	private String status;
	/**
	 * 支付时间
	 */
	@ApiModelProperty(value = "支付时间")
	private LocalDateTime payTime;
	/**
	 * 支付到期时间
	 */
	@ApiModelProperty(value = "支付到期时间")
	private Date payExpireTime;
	/**
	 * 支付流水号
	 */
	@ApiModelProperty(value = "支付流水号")
	private String paySerialNumber;
	/**
	 * 支付链接
	 */
	@ApiModelProperty(value = "支付链接")
	private String payUrl;
	/**
	 * 支付二维码图片地址
	 */
	@ApiModelProperty(value = "支付二维码图片地址")
	private String payImgUrl;
	/**
	 * 支付方式，微信|支付宝等
	 */
	@ApiModelProperty(value = "支付方式，微信|支付宝等")
	private String payWay;
	/**
	 * 支付二维码超时时间
	 */
	@ApiModelProperty(value = "支付二维码超时时间")
	private Date payCodeExpireTime;
	/**
	 * 业务员对应的用户ID
	 */
	@ApiModelProperty(value = "业务员对应的用户ID")
	private Integer salesmanId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 支付二维码图片刷新时间
	 */
	@ApiModelProperty(value = "支付二维码图片刷新时间")
	private Date payRefreshTime;
	/**
	 * 已支付确认推送状态 0否,  1是
	 */
	@ApiModelProperty(value = "已支付确认推送状态 0否,  1是")
	private String payPushState;
	/**
	 * 支付二维码刷新状态 0未刷新,1刷新中,2已刷新
	 */
	@ApiModelProperty(value = "支付二维码刷新状态 0未刷新,1刷新中,2已刷新")
	private String flushType;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String unit;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String setTime;


	//缴费人姓名
	@ApiModelProperty(value = "缴费人姓名")
	private String payerName;
	//缴费人类型
	@ApiModelProperty(value = "缴费人类型")
	private String payerCertificateType;
	//缴费人证码
	@ApiModelProperty(value = "缴费人证件号")
	private String payerCertificateCode;

}
