package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.ChargingAddrTypeEnum;
import com.tuns.car.core.constant.ChargingTypeEnum;
import com.tuns.car.core.constant.ChargingUseYearsEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
@TableName("vi_ins_charging_pile_info_car")
public class ViInsChargingPileInfoCar extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;

	@ApiModelProperty(value = "业务主键id")
	private Long chargingPileCarId;
	/**
	 * 车牌号
	 */
	@ApiModelProperty(value = "车牌号")
	private String plateNumber;
	/**
	 * 车架号
	 */
	@ApiModelProperty(value = "车架号")
	private String frameNumber;
	/**
	 * 充电桩型号
	 */
	@ApiModelProperty(value = "充电桩型号")
	private String chargingModel;
	/**
	 * 充电桩编码
	 */
	@ApiModelProperty(value = "充电桩编码")
	private String chargingCode;
	/**
	 * 充电桩类型
	 */
	@ApiModelProperty(value = "充电桩类型")
	private ChargingTypeEnum chargingType;
	/**
	 * 充电桩安装地点类型
	 */
	@ApiModelProperty(value = "充电桩安装地点类型")
	private ChargingAddrTypeEnum chargingInstallAddrType;
	/**
	 * 充电桩使用年限
	 */
	@ApiModelProperty(value = "充电桩使用年限")
	private ChargingUseYearsEnum chargingUseYears;
	/**
	 * 安装时间
	 */
	private LocalDate chargingInstallDate;
	/**
	 * 充电桩完整地址
	 */
	@ApiModelProperty(value = "充电桩完整地址")
	private String chargingAddrComplete;
	/**
	 * 充电桩详细地址
	 */
	@ApiModelProperty(value = "充电桩详细地址")
	private String chargingAddrDetail;
	/**
	 * 省编码
	 */
	@ApiModelProperty(value = "省编码")
	private String province;
	/**
	 * 省名称
	 */
	@ApiModelProperty(value = "省名称")
	private String provinceName;
	/**
	 * 市编码
	 */
	@ApiModelProperty(value = "市编码")
	private String city;
	/**
	 * 市名称
	 */
	@ApiModelProperty(value = "市名称")
	private String cityName;
	/**
	 * 区县编码
	 */
	@ApiModelProperty(value = "区县编码")
	private String county;
	/**
	 * 区县名称
	 */
	@ApiModelProperty(value = "区县名称")
	private String countyName;


}
