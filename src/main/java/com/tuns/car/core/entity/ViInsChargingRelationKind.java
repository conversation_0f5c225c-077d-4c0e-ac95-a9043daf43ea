package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.constant.YesNoNumberEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 充电桩险种关联表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-12 08:40:13
 */
@Data
@TableName("vi_ins_charging_relation_kind")
public class ViInsChargingRelationKind extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 险种代码
	 */
	@ApiModelProperty(value = "险种代码")
	private String kindCode;
	/**
	 * 充电桩主键id
	 */
	@ApiModelProperty(value = "充电桩主键id")
	private Long chargingId;
	/**
	 * 单位保额
	 */
	@ApiModelProperty(value = "单位保额")
	private BigDecimal amount;
    /**
     * 是否勾选共享保额 0否 1是
     */
    @ApiModelProperty(value = "是否勾选共享保额 0否 1是")
    private YesNoNumberEnum isSharedAmount;

    /**
     * 是否展示共享保额 0否 1是
     */
    @ApiModelProperty(value = "是否展示共享保额 0否 1是")
    private YesNoNumberEnum isShowSharedAmount;
}
