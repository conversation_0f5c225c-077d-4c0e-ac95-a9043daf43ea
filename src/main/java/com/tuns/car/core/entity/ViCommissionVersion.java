package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_commission_version")
public class ViCommissionVersion implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 版本编号
	 */
	@ApiModelProperty(value = "版本编号")
	private String verId;
	/**
	 * 版本名称
	 */
	@ApiModelProperty(value = "版本名称")
	private String verName;
	/**
	 * 保险公司编号
	 */
	@ApiModelProperty(value = "保险公司编号")
	private String companyId;
	/**
	 * 保险公司名称
	 */
	@ApiModelProperty(value = "保险公司名称")
	private String companyName;
	/**
	 * 合作渠道编号
	 */
	@ApiModelProperty(value = "合作渠道编号")
	private String channelId;
	/**
	 * 合作渠道名称
	 */
	@ApiModelProperty(value = "合作渠道名称")
	private String channelName;
	/**
	 * 保险公司代码
	 */
	@ApiModelProperty(value = "保险公司代码")
	private String companyCd;
	/**
	 * 版本状态 U 编辑中 P待审核 I 审批中 S审核成功 F审核拒绝
	 */
	@ApiModelProperty(value = "版本状态 U 编辑中 P待审核 I 审批中 S审核成功 F审核拒绝")
	private String verSts;
	/**
	 * 启用标识 0未启用 1启用 2停用
	 */
	@ApiModelProperty(value = "启用标识 0未启用 1启用 2停用")
	private String startSts;
	/**
	 * 版本类型
	 */
	@ApiModelProperty(value = "版本类型")
	private String verTyp;
	/**
	 * 版本种类
	 */
	@ApiModelProperty(value = "版本种类")
	private String verKind;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private Integer mdfUser;
	/**
	 * 审核人
	 */
	@ApiModelProperty(value = "审核人")
	private Integer chkUser;
	/**
	 * 时间戳
	 */
	@ApiModelProperty(value = "时间戳")
	private Date tmSmp;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String creUserName;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String mdfUserName;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String chkUserName;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date chkTm;

}
