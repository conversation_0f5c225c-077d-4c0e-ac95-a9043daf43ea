package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 投保地与出单渠道账号绑定关系表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
@TableName("vi_ins_city_channel_binding")
public class ViInsCityChannelBinding implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 投保地省级代码
	 */
	@ApiModelProperty(value = "投保地省级代码")
	private String provinceNumber;
	/**
	 * 投保地省级
	 */
	@ApiModelProperty(value = "投保地省级")
	private String provinceName;
	/**
	 * 投保地市级代码
	 */
	@ApiModelProperty(value = "投保地市级代码")
	private String cityNumber;
	/**
	 * 投保地市级
	 */
	@ApiModelProperty(value = "投保地市级")
	private String cityName;
	/**
	 * 保险公司ID
	 */
	@ApiModelProperty(value = "保险公司ID")
	private String companyId;
	/**
	 * 保险公司名称
	 */
	@ApiModelProperty(value = "保险公司名称")
	private String companyName;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String channelId;
	/**
	 * 合作渠道名称
	 */
	@ApiModelProperty(value = "合作渠道名称")
	private String channelName;
	/**
	 * 是否默认标志
	 */
	@ApiModelProperty(value = "是否默认标志")
	private String defaultMark;
	/**
	 * 公司ID+城市ID唯一值（确保一个所选投保地只有一条记录）
	 */
	@ApiModelProperty(value = "公司ID+城市ID唯一值（确保一个所选投保地只有一条记录）")
	private String companyCityUnique;
	/**
	 * 公司ID+省ID+默认唯一值（确保一个省只有一个默认值，当前是默认时不能为空，否则必须为空）
	 */
	@ApiModelProperty(value = "公司ID+省ID+默认唯一值（确保一个省只有一个默认值，当前是默认时不能为空，否则必须为空）")
	private String companyProvinceDefaultUnique;
	/**
	 * 实际出单市级代码
	 */
	@ApiModelProperty(value = "实际出单市级代码")
	private String actualCityNumber;
	/**
	 * 车牌号码
	 */
	@ApiModelProperty(value = "车牌号码")
	private String plateNumber;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date mdfTm;
	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	private Integer mdfUser;

}
