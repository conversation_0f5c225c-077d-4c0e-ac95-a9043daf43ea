package com.tuns.car.core.entity;

import com.tuns.car.core.constant.BooleanEnum;
import com.tuns.core.boot.entity.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * 车险系数规则表
 * @date 2024-5-6 16:08
 */
@Data
public class ViCoefficientRuleConfig extends BaseEntity {
    /**
     * 保险公司
     */
    private String companyId;
    /**
     * 是否兜底配置 1是 0否
     */
    private BooleanEnum lastConfiguration;
    /**
     * 规则代码
     */
    private Long ruleCode;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 规则内容详情
     */
    private String ruleDetail;
    /**
     * 批次编号
     */
    private String batchNumber;
    /**
     * 最低系数
     */
    private Double minCoefficient;
    /**
     * 生效状态 0未生效 1已生效 2 已停用
     */
    private BooleanEnum effectiveStatus;

    /**
     * 序号
     */
    private Integer sort;
}
