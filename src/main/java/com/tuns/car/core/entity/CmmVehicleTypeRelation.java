package com.tuns.car.core.entity;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.UsingNatureEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@TableName("cmm_vehicle_type_relation")
public class CmmVehicleTypeRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据库id
     */
    private Integer id;

    /**
     * 滕顺车辆类型-大类
     */
    private String typeCode;

    /**
     * 滕顺车辆类型-小类
     */
    private UsingNatureEnum usingNature;

    /**
     * 营运性质 0-非营运 1-营运
     */
    private String operationNature;

    /**
     * 保险公司编码
     */
    private String companyCd;

    /**
     * 保司车辆性质名称
     */
    private String thirdVehiclePropertiesName;

    /**
     * 保司车辆性质
     */
    private String thirdVehicleProperties;

    @ApiModelProperty("保司车辆类型名称")
    private String thirdVehicleTypeName;

    @ApiModelProperty("保司车辆类型")
    private String thirdVehicleType;

    @ApiModelProperty("腾顺所属性质 1-个人 2-机关 3-企业")
    private String ownershipNature;

    @ApiModelProperty("保司车辆类型上级(国任)")
    private String thirdVehicleSuperiorType;

    /**
     * 创建人id
     */
    private Integer creUser;

    /**
     * 创建时间
     */
    private LocalDateTime creTm;

    /**
     * 修改人id
     */
    private Integer mdfUser;

    /**
     * 修改时间
     */
    private LocalDateTime mdfTm;

    /**
     * 滕顺车辆种类
     */
    private String carTypeCode;
    /**
     * 第三方字段json
     */
    private String thirdFieldJson;

    @TableField(exist = false)
    private JSONObject thirdFieldObj;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    /**
     *  获取第三方字段值
     * @param thirdField 第三方字段
     * @return
     */
    public String getThirdFieldValue(String thirdField){
        Object value = thirdFieldObj.get(thirdField);
        return StrUtil.toString(value);
    }
}
