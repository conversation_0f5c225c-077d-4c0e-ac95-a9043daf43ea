package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险收益记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
@TableName("vi_ins_earnings_record")
public class ViInsEarningsRecord implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@TableId
	private Integer id;
	/**
	 * 业务ID(订单ins_order_id,保单ins_policy_id)
	 */
	@ApiModelProperty(value = "业务ID(订单ins_order_id,保单ins_policy_id)")
	private String businessId;
	/**
	 * 收益类型 1常规、2续保、3冲账、4保全、5增员奖、6失效、7收益调整
	 */
	@ApiModelProperty(value = "收益类型 1常规、2续保、3冲账、4保全、5增员奖、6失效、7收益调整")
	private String profitType;
	/**
	 * 合作伙伴ID
	 */
	@ApiModelProperty(value = "合作伙伴ID")
	private Integer partnerId;
	/**
	 * 推广收益(基础佣金1)
	 */
	@ApiModelProperty(value = "推广收益(基础佣金1)")
	private BigDecimal viSpreadProfit;
	/**
	 * 活动收益(基础佣金2)
	 */
	@ApiModelProperty(value = "活动收益(基础佣金2)")
	private BigDecimal viActivityProfit;
	/**
	 * 调整收益
	 */
	@ApiModelProperty(value = "调整收益")
	private BigDecimal viAdjustProfit;
	/**
	 * 协作收益
	 */
	@ApiModelProperty(value = "协作收益")
	private BigDecimal viCooperationProfit;
	/**
	 * 业务推动津贴1(车船税1)
	 */
	@ApiModelProperty(value = "业务推动津贴1(车船税1)")
	private BigDecimal viAllowanceOne;
	/**
	 * 业务推动津贴2(车船税2)
	 */
	@ApiModelProperty(value = "业务推动津贴2(车船税2)")
	private BigDecimal viAllowanceTwo;
	/**
	 * 已结佣金金额
	 */
	@ApiModelProperty(value = "已结佣金金额")
	private BigDecimal incomePaid;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 结算状态 1已结算 0未结算 2结算中
	 */
	@ApiModelProperty(value = "结算状态 1已结算 0未结算 2结算中")
	private String settlementFlag;
	/**
	 * 是否删除 0:否,1:是
	 */
	@ApiModelProperty(value = "是否删除 0:否,1:是")
	private String isDel;

}
