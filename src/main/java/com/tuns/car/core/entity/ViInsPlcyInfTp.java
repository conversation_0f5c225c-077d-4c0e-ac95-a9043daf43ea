package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.InsuredTypeEnum;
import com.tuns.car.core.constant.SecondaryNewCarEnum;
import com.tuns.core.boot.constant.YesNoEnum;
import com.tuns.core.boot.constant.YesNoNumberEnum;
import com.tuns.core.boot.vo.page.LockKeyProvider;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-02 11:37:27
 */
@Data
@TableName("vi_ins_plcy_inf_tp")
public class ViInsPlcyInfTp implements LockKeyProvider, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    @TableId
    private Integer id;
    /**
     * 流程流水号/报价批次号
     */
    @ApiModelProperty(value = "流程流水号/报价批次号")
    private Long serialNumber;
    /**
     * 保单记录ID
     */
    @ApiModelProperty(value = "保单记录ID")
    private Long policyId;
    /**
     * 保单批次ID
     */
    @ApiModelProperty(value = "保单批次ID")
    private Long policyBatchId;
    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNumber;
    /**
     * 主保单号
     */
    @ApiModelProperty(value = "主保单号")
    private String mainPolicyNumber;
    /**
     * 投保单号
     */
    @ApiModelProperty(value = "投保单号")
    private String proposalNumber;
    /**
     * 主投保单号
     */
    @ApiModelProperty(value = "主投保单号")
    private String mainProposalNumber;
    /**
     * 上年保单号
     */
    @ApiModelProperty(value = "上年保单号")
    private String lastPolicy;
    /**
     * 上年承保公司ID
     */
    @ApiModelProperty(value = "上年承保公司ID")
    private String lastCompanyId;
    /**
     * 上年承保公司名称
     */
    @ApiModelProperty(value = "上年承保公司名称")
    private String lastCompanyName;
    /**
     * 出单类型 1-单交强 2-单商业 3-交商同保
     */
    @ApiModelProperty(value = "出单类型 1-单交强 2-单商业 3-交商同保")
    private String recordType;
    /**
     * 保单类型 1-交强单 2-商业单
     */
    @ApiModelProperty(value = "保单类型 1-交强单 2-商业单")
    private String policyType;
    /**
     * 自动报价标志
     */
    @ApiModelProperty(value = "自动报价标志")
    private String autoMark;
    /**
     * 保单记录来源 1-客户端 2-内勤 3-录单
     */
    @ApiModelProperty(value = "保单记录来源 1-客户端 2-内勤 3-录单")
    private String recordSource;
    /**
     * 保险起期
     */
    @ApiModelProperty(value = "保险起期")
    private String insBegin;
    /**
     * 保险止期
     */
    @ApiModelProperty(value = "保险止期")
    private String insEnd;
    /**
     * 保费
     */
    @ApiModelProperty(value = "保费")
    private BigDecimal insuredPremium;
    /**
     * 附加保费/车船税
     */
    @ApiModelProperty(value = "附加保费/车船税")
    private BigDecimal attachPremium;
    /**
     * 总保费=保费+附加保费
     */
    @ApiModelProperty(value = "总保费=保费+附加保费")
    private BigDecimal totalPremium;
    /**
     * 保险金额
     */
    @ApiModelProperty(value = "保险金额")
    private BigDecimal insAmount;
    /**
     * 奖励
     */
    @ApiModelProperty(value = "奖励")
    private BigDecimal insuredReward;
    /**
     * 附加奖励
     */
    @ApiModelProperty(value = "附加奖励")
    private BigDecimal attachReward;
    /**
     * 总奖励=佣金+附加奖励
     */
    @ApiModelProperty(value = "总奖励=佣金+附加奖励")
    private BigDecimal totalReward;
    /**
     * 折扣 0~2
     */
    @ApiModelProperty(value = "折扣 0~2")
    private BigDecimal discount;
    /**
     * 承保公司ID
     */
    @ApiModelProperty(value = "承保公司ID")
    private String companyId;
    /**
     * 承保公司名称
     */
    @ApiModelProperty(value = "承保公司名称")
    private String companyName;
    /**
     * 行政区域代码 省
     */
    @ApiModelProperty(value = "行政区域代码 省")
    private String provinceNumber;
    /**
     * 行政区域 省
     */
    @ApiModelProperty(value = "行政区域 省")
    private String provinceName;
    /**
     * 行政区域代码 市
     */
    @ApiModelProperty(value = "行政区域代码 市")
    private String cityNumber;
    /**
     * 行政区域 市
     */
    @ApiModelProperty(value = "行政区域 市")
    private String cityName;
    /**
     * 保费计算识别码/保险公司订单号码
     */
    @ApiModelProperty(value = "保费计算识别码/保险公司订单号码")
    private String insPremiumNumber;
    /**
     * 特别约定 已迁移至 vi_quote_special_agreement
     */
    @Deprecated
    @ApiModelProperty(value = "特别约定")
    private String specialAgreement;
    /**
     * 报价审核人ID 自动报价时为保险公司ID/人工报价时为内勤人员ID
     */
    @ApiModelProperty(value = "报价审核人ID 自动报价时为保险公司ID/人工报价时为内勤人员ID")
    private String reviewerId;
    /**
     * 报价审核人姓名  自动报价时为保险公司/人工报价时为内勤人员
     */
    @ApiModelProperty(value = "报价审核人姓名  自动报价时为保险公司/人工报价时为内勤人员")
    private String reviewerName;
    /**
     * 报价审核时间
     */
    @ApiModelProperty(value = "报价审核时间")
    private LocalDateTime reviewTime;
    /**
     * 报价状态 0-报价失败 1-报价成功 3-报价等待
     */
    @ApiModelProperty(value = "报价状态 0-报价失败 1-报价成功 3-报价等待")
    private String recordStatus;
    /**
     * 报价备注信息
     */
    @ApiModelProperty(value = "报价备注信息")
    private String reviewMsg;
    /**
     * 上年理赔次数
     */
    @ApiModelProperty(value = "上年理赔次数")
    private Integer lastClaimCount;
    /**
     * 自动报价时为保险公司ID/人工报价时为内勤人员ID
     */
    @ApiModelProperty(value = "自动报价时为保险公司ID/人工报价时为内勤人员ID")
    private String insuredReviewerId;
    /**
     * 投保审核人姓名 自动报价时为保险公司/人工报价时为内勤人员
     */
    @ApiModelProperty(value = "投保审核人姓名 自动报价时为保险公司/人工报价时为内勤人员")
    private String insuredReviewerName;
    /**
     * 投保审核时间
     */
    @ApiModelProperty(value = "投保审核时间")
    private LocalDateTime insuredReviewTime;
    /**
     * 投保备注信息
     */
    @ApiModelProperty(value = "投保备注信息")
    private String insuredMsg;
    /**
     * 投保状态 附件码表
     */
    @ApiModelProperty(value = "投保状态 附件码表")
    private String insuredStatus;
    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    private LocalDateTime payTime;
    /**
     * 保单配送方式 1-快递配送 2-门店自提
     */
    @ApiModelProperty(value = "保单配送方式 1-快递配送 2-门店自提")
    private String receiverType;
    /**
     * 业务员ID/出单员
     */
    @ApiModelProperty(value = "业务员ID/出单员")
    private String salesmanId;
    /**
     * 询价人ID
     */
    @ApiModelProperty(value = "询价人ID")
    private String inquirerId;
    /**
     * 本公司组织机构代码
     */
    @ApiModelProperty(value = "本公司组织机构代码")
    private String departCode;
    /**
     * 本公司组织机构名称
     */
    @ApiModelProperty(value = "本公司组织机构名称")
    private String departName;
    /**
     * 保险公司合作渠道代码
     */
    @ApiModelProperty(value = "保险公司合作渠道代码")
    private String channelId;
    /**
     * 报价评分
     */
    @ApiModelProperty(value = "报价评分")
    private String recordScore;
    /**
     * 业务类型 1-新保 2-续保 3-转保
     */
    @ApiModelProperty(value = "业务类型 1-新保 2-续保 3-转保")
    private InsuredTypeEnum insuredType;
    /**
     * 本渠道续保标志
     */
    @ApiModelProperty(value = "本渠道续保标志")
    private String thisRenewal;
    /**
     * 交强新转续二级标识
     */
    @ApiModelProperty(value = "交强新转续二级标识")
    private String thisRenewalCi;
    /**
     * 商业新转续二级标识
     */
    @ApiModelProperty(value = "商业新转续二级标识")
    private String thisRenewalBi;
    /**
     * 佣金手续费比例 0~1
     */
    @ApiModelProperty(value = "佣金手续费比例 0~1")
    private BigDecimal rewardPercent;
    /**
     * 承保时间
     */
    @ApiModelProperty(value = "承保时间")
    private LocalDateTime insuredTime;
    /**
     * 保单审核人ID
     */
    @ApiModelProperty(value = "保单审核人ID")
    private String policyReviewerId;
    /**
     * 保单审核人名称
     */
    @ApiModelProperty(value = "保单审核人名称")
    private String policyReviewerName;
    /**
     * 保单审核时间
     */
    @ApiModelProperty(value = "保单审核时间")
    private LocalDateTime policyReviewTime;
    /**
     * 保单审核备注信息
     */
    @ApiModelProperty(value = "保单审核备注信息")
    private String policyReviewMsg;
    /**
     * 保单审核状态 0-未审核 1-已审核 2-审核失败
     */
    @ApiModelProperty(value = "保单审核状态 0-未审核 1-已审核 2-审核失败")
    private String policyReviewMark;
    /**
     * 保单是否打印
     */
    @ApiModelProperty(value = "保单是否打印")
    private String policyPrintMark;
    /**
     * 保单打印人ID
     */
    @ApiModelProperty(value = "保单打印人ID")
    private String policyPrinterId;
    /**
     * 保单打印人名称
     */
    @ApiModelProperty(value = "保单打印人名称")
    private String policyPrinterName;
    /**
     * 保单打印时间
     */
    @ApiModelProperty(value = "保单打印时间")
    private LocalDateTime policyPrintTime;
    /**
     * 是否新保单 原始数据
     */
    @ApiModelProperty(value = "是否新保单 原始数据")
    private String policyNewMark;
    /**
     * 单证编号
     */
    @ApiModelProperty(value = "单证编号")
    private String documentNumber;
    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    private String invoiceNumber;
    /**
     * 交强险标志编号
     */
    @ApiModelProperty(value = "交强险标志编号")
    private String ciMarkNumber;
    /**
     * 税额
     */
    @ApiModelProperty(value = "税额")
    private BigDecimal tax;
    /**
     * 标准保费
     */
    @ApiModelProperty(value = "标准保费")
    private BigDecimal standardPremium;
    /**
     * 原保单记录ID
     */
    @ApiModelProperty(value = "原保单记录ID")
    private Long lastPolicyId;
    /**
     * 批单号
     */
    @ApiModelProperty(value = "批单号")
    private String endorsementNumber;
/** 该字段已经不再使用，转到【vi_ins_plcy_json_tp】存储
 * 扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）

 @ApiModelProperty(value = "扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）")
 private String extend;

 */
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creTm;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人/出单员")
    private Integer creUser;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime mdfTm;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private Integer mdfUser;
    /**
     * 删除标志 0-已删除 1-存储
     */
    @ApiModelProperty(value = "删除标志 0-已删除 1-存储")
    private String delFlag;
    /**
     * 投保提交时间
     */
    @ApiModelProperty(value = "投保提交时间")
    private LocalDateTime insuredSubmitTime;
    /**
     * 报价提交时间
     */
    @ApiModelProperty(value = "报价提交时间")
    private LocalDateTime inquirerTime;
    /**
     * 交商同保评分
     */
    @ApiModelProperty(value = "交商同保评分")
    private String totalRecordScore;
    /**
     * 询价人所在部门
     */
    @ApiModelProperty(value = "询价人所在部门")
    private String inquirerDepartCode;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private BigDecimal netPremium;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private BigDecimal taxRate;
    /**
     * 从车型查询开始全流程流水号 用于发送给保险公司
     */
    @ApiModelProperty(value = "从车型查询开始全流程流水号 用于发送给保险公司")
    private Long carSerialNumber;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private String invoiceMark;
    /**
     * 即时起保
     */
    @ApiModelProperty(value = "即时起保")
    private String effectiveImmediately;
    /**
     * 询价人备注类型
     */
    @ApiModelProperty(value = "询价人备注类型")
    private String inquirerMsgType;
    /**
     * 询价人备注
     */
    @ApiModelProperty(value = "询价人备注")
    private String inquirerMsg;
    /**
     * 免验车原因
     */
    @ApiModelProperty(value = "免验车原因")
    private String exemptVerifyReason;
    /**
     * 车船税缴税类型
     */
    @ApiModelProperty(value = "车船税缴税类型")
    private String carTaxType;
    /**
     * 投保子状态
     */
    @ApiModelProperty(value = "投保子状态")
    private String insuredSubStatus;
    /**
     * 最近操作人 指内勤 不同于修改人
     */
    @ApiModelProperty(value = "最近操作人 指内勤 不同于修改人")
    private Integer lastMdfUser;
    /**
     * 投保查询码
     */
    @ApiModelProperty(value = "投保查询码")
    private String demandNo;
    /**
     * 是否需要短信验证码：0-否，1-是
     */
    @ApiModelProperty(value = "是否需要短信验证码：0-否，1-是")
    private String needIssueCode;
    /**
     * 验证码生成时间
     */
    @ApiModelProperty(value = "验证码生成时间")
    private LocalDateTime issueCodeTime;
    /**
     * 是否验证通过：0-否，1-是
     */
    @ApiModelProperty(value = "是否验证通过：0-否，1-是")
    private String verifyStatus;
    /**
     * 报价图片url地址
     */
    @ApiModelProperty(value = "报价图片url地址")
    private String offerUrl;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private String needInfomation;
    /**
     * 人工报价经办人手机号
     */
    @ApiModelProperty(value = "人工报价经办人手机号")
    private String salesmanPhone;
    /**
     * 取消类型 1:用户自主取消, 2:后台操作取消, 3:其他
     */
    @ApiModelProperty(value = "取消类型 1:用户自主取消, 2:后台操作取消, 3:其他")
    private String cancelType;
    /**
     * 取消原因
     */
    @ApiModelProperty(value = "取消原因")
    private String cancelReason;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private String cancelUserName;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private LocalDateTime cancelTime;
    /**
     * 交强险上张保单保险起期
     */
    @ApiModelProperty(value = "交强险上张保单保险起期")
    private String lastYearInsBegin;
    /**
     * 交强险上张保单保险止期
     */
    @ApiModelProperty(value = "交强险上张保单保险止期")
    private String lastYearInsEnd;
    /**
     * 交通事故记录
     */
    @ApiModelProperty(value = "交通事故记录")
    private String trafficAccidentRecord;
    /**
     * 无赔款折扣系数
     */
    @ApiModelProperty(value = "无赔款折扣系数")
    private BigDecimal noClaimDiscount;
    /**
     * 自主定价系数
     */
    @ApiModelProperty(value = "自主定价系数")
    private BigDecimal independentPriceRate;
    /**
     * 连续承保年数
     */
    @ApiModelProperty(value = "连续承保年数")
    private String insureYears;
    /**
     * 连续承保出险次数
     */
    @ApiModelProperty(value = "连续承保出险次数")
    private String claimTimes;
    /**
     * 无赔优系数级别
     */
    @ApiModelProperty(value = "无赔优系数级别")
    private String claimAdjustLevel;
    /**
     * 业务分组
     */
    @ApiModelProperty(value = "业务分组")
    private String businessGroup;
    /**
     * 签单折扣预期赔付率
     */
    @ApiModelProperty(value = "签单折扣预期赔付率")
    private BigDecimal billElr;
    /**
     * 重复投保起保时间
     */
    @ApiModelProperty(value = "重复投保起保时间")
    private String repeatInsBegin;
    /**
     * 重复投保终保时间
     */
    @ApiModelProperty(value = "重复投保终保时间")
    private String repeatInsEnd;
    /**
     * 保费是否改变
     */
    @ApiModelProperty(value = "保费是否改变")
    private String isChange;
    /**
     * 录屏ID
     */
    @ApiModelProperty(value = "录屏ID")
    private String snapshotUniqueId;
    /**
     * 客户评分等级
     */
    @ApiModelProperty(value = "客户评分等级")
    private BigDecimal customerRiskRating;
    /**
     * 私家车车联网分档
     */
    @ApiModelProperty(value = "私家车车联网分档")
    private String carSpreading;
    /**
     * 网约车分级
     */
    @ApiModelProperty(value = "网约车分级")
    private BigDecimal wyCarType;
    /**
     * 商业险预期赔付率
     */
    @ApiModelProperty(value = "商业险预期赔付率")
    private BigDecimal lossRatioBi;
    /**
     * 交强险预期赔付率
     */
    @ApiModelProperty(value = "交强险预期赔付率")
    private BigDecimal lossRatioCi;
    /**
     * 整单预期赔付率
     */
    @ApiModelProperty(value = "整单预期赔付率")
    private BigDecimal totalRatio;

    /**
     * 整单预期赔付率
     */
    @ApiModelProperty(value = "整单预期赔付率2")
    private BigDecimal totalRatio2;

    /**
     * ncd系数级别
     */
    @ApiModelProperty(value = "ncd系数级别")
    private BigDecimal noClaimLevel;
    /**
     * 保单成本率
     */
    @ApiModelProperty(value = "保单成本率")
    private BigDecimal policyCostRate;
    /**
     * 大家分
     */
    @ApiModelProperty(value = "大家分")
    private String allScore;
    /**
     * 天马指数-核保
     */
    @ApiModelProperty(value = "天马指数-核保")
    private BigDecimal tianMaIndexFloorPrice;
    /**
     * 天马指数-标费
     */
    @ApiModelProperty(value = "天马指数-标费")
    private BigDecimal tianMaIndexBasicPrice;
    /**
     * 非车险套餐信息
     */
    @ApiModelProperty(value = "非车险套餐信息")
    private String carProductName;
    /**
     * 非车险保全费
     */
    @ApiModelProperty(value = "非车险保全费")
    private String carSaveCost;
    /**
     * 三者+车损分档
     */
    @ApiModelProperty(value = "三者+车损分档")
    private String thirdCarRank;
    /**
     * 商业险手续费
     */
    @ApiModelProperty(value = "商业险手续费")
    private BigDecimal handFeeBi;
    /**
     * 期望折扣系数
     */
    @ApiModelProperty(value = "$column.comments")
    private BigDecimal expectedDiscount;
    /**
     * 交强险手续费
     */
    @ApiModelProperty(value = "交强险手续费")
    private BigDecimal handFeeCi;
    /**
     * 送修代码
     */
    @ApiModelProperty(value = "送修代码")
    private String repairCode;
    /**
     * 项目代码
     */
    @ApiModelProperty(value = "项目代码")
    private String projectCode;
    /**
     * 上年度出单渠道类型
     */
    @ApiModelProperty(value = "上年度出单渠道类型")
    private String lastChannelType;
    /**
     * 交强险续保提前天数
     */
    @ApiModelProperty(value = "交强险续保提前天数")
    private Integer daysInAdvanceOfRenewalCi;
    /**
     * 商业险续保提前天数
     */
    @ApiModelProperty(value = "商业险续保提前天数")
    private Integer daysInAdvanceOfRenewalBi;
    /**
     * 是否送修 1是 0否
     */
    @ApiModelProperty(value = "是否送修 1是 0否")
    private String isRepair;
    /**
     * 自主评分
     */
    @ApiModelProperty(value = "自主评分")
    private BigDecimal selfScore;
    /**
     * 连续承保年限
     */
    @ApiModelProperty(value = "连续承保年限")
    private BigDecimal consecutiveYearsOfCoverage;
    /**
     * 连续承保期间出险次数
     */
    @ApiModelProperty(value = "连续承保期间出险次数")
    private BigDecimal numberOfUnderwriting;
    /**
     * 交强险精算纯风险保费
     */
    @ApiModelProperty(value = "交强险精算纯风险保费")
    private BigDecimal actuarialPureRiskPremiumCi;
    /**
     * 商业险精算纯风险保费
     */
    @ApiModelProperty(value = "商业险精算纯风险保费")
    private BigDecimal actuarialPureRiskPremiumBi;
    /**
     * 承保年限
     */
    @ApiModelProperty(value = "承保年限")
    private BigDecimal underwritingPeriod;
    /**
     * 交通违法系数
     */
    @ApiModelProperty(value = "交通违法系数")
    private BigDecimal trafficViolationCoefficient;
    /**
     * 车辆的评分
     */
    @ApiModelProperty(value = "车辆的评分")
    private BigDecimal vehicleRating;
    /**
     * 合作伙伴用户名
     */
    @ApiModelProperty(value = "合作伙伴用户名")
    private String realName;
    /**
     * 合作伙伴身份证号码
     */
    @ApiModelProperty(value = "合作伙伴身份证号码")
    private String idCardNo;
    /**
     * 冲账对应原保单id
     */
    @ApiModelProperty(value = "冲账对应原保单id")
    private String commRemark;
    /**
     * 保险公司编码
     */
    @ApiModelProperty(value = "保险公司编码")
    private String companyCode;
    /**
     * 批单类型
     */
    @ApiModelProperty(value = "批单类型")
    private String endorsementType;
    /**
     * 页面输入折扣
     */
    @ApiModelProperty(value = "页面输入折扣")
    private BigDecimal enterDiscount;
    /**
     * 登录渠道名称
     */
    @ApiModelProperty(value = "登录渠道名称")
    private String linkName;
    /**
     * 出单工号
     */
    @ApiModelProperty(value = "出单工号")
    private String linkNo;
    /**
     * 是否匹配到基础政策 0 未匹配到，1 匹配1个，2 匹配多个
     */
    @ApiModelProperty(value = "是否匹配到基础政策 0 未匹配到，1 匹配1个，2 匹配多个")
    private String mateBasePolicy;
    /**
     * 预核保信息
     */
    @ApiModelProperty(value = "预核保信息")
    private String preUnderwritingMsg;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private String querySequNoValidDate;
    /**
     * 查询码
     */
    @ApiModelProperty(value = "查询码")
    private String querySequenceNo;
    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    private Long reversePolicyId;
    /**
     * 冲账状态 0未冲,1被冲,2对冲,3已冲
     */
    @ApiModelProperty(value = "冲账状态 0未冲,1被冲,2对冲,3已冲")
    private String reverseState;
    /**
     * 转保标识
     */
    @ApiModelProperty(value = "转保标识")
    private String trunMrk;
    /**
     * 出单渠道id
     */
    @ApiModelProperty(value = "出单渠道id")
    private Integer chanDetailId;

    @ApiModelProperty(value = "建议折扣")
    private BigDecimal proposalDiscount;
    /**
     * 投保日期
     */
    private LocalDateTime issueTime;
    /**
     * 报价日期
     */
    private LocalDateTime offerTime;
    /**
     * 车慧达评级
     */
    @ApiModelProperty(value = "车慧达评级")
    private String cheHuidaTranche;
    /**
     * 车损险北理新能源评分
     */
    @ApiModelProperty(value = "车损险北理新能源评分")
    private String bitDamageScore;
    /**
     * 人保分
     */
    @ApiModelProperty(value = "人保分")
    private BigDecimal piccScore;
    /**
     * 交强险手续费比例
     */
    @ApiModelProperty(value = "交强险手续费比例")
    private BigDecimal handFeeRatioCi;
    /**
     * 商业险手续费比例
     */
    @ApiModelProperty(value = "商业险手续费比例")
    private BigDecimal handFeeRatioBi;
    /**
     * 初始手续费比例默认上限
     */
    @ApiModelProperty(value = "初始手续费比例默认上限")
    private BigDecimal upperLimit;


    /**
     * NCD标费预期赔付率
     */
    @ApiModelProperty(value = "NCD标费预期赔付率")
    private BigDecimal ncdEcompensationRate;
    /**
     * 商业险NCD标费预期赔付率
     */
    @ApiModelProperty(value = "商业险NCD标费预期赔付率")
    private BigDecimal ncdEcompensationRateBi;
    /**
     * 交商合计含NCD标准保费预期赔付率
     */
    @ApiModelProperty(value = "交商合计含NCD标准保费预期赔付率")
    private BigDecimal ncdTotalEcompensationRate;

    /**
     * 车系分类
     */
    @ApiModelProperty(value = "车系分类")
    private String selfVehcType;

    /**
     * 上年出险次数（交商合计）
     */
    @ApiModelProperty(value = "上年出险次数（交商合计）")
    private Integer lastClaimCountTotal;

    /**
     * 交强险净保费预期赔付率
     */
    @ApiModelProperty(value = "交强险净保费预期赔付率")
    private BigDecimal netEcompensationRateCi;

    /**
     * 商业险净保费预期赔付率
     */
    @ApiModelProperty(value = "商业险净保费预期赔付率")
    private BigDecimal netEcompensationRateBi;

    /**
     * 交商合计净保费预期赔付率
     */
    @ApiModelProperty(value = "交商合计净保费预期赔付率")
    private BigDecimal totalNetEcompensationRate;

    /**
     * 合作方名称
     */
    @ApiModelProperty(value = "合作方名称")
    private String partnerName;

    /**
     * 业务分组名称
     */
    @ApiModelProperty(value = "业务分组名称")
    private String businessGroupName;
    /**
     * 精准使用年限
     */
    @ApiModelProperty(value = "精准使用年限")
    private BigDecimal preciseUseYear;

    /**
     * 是否真单交
     */
    @ApiModelProperty(value = "是否真单交")
    private String isDjq;

    @ApiModelProperty(value = "建议自主定价系数")
    private BigDecimal proposeIndependentPriceRate;

    /**
     * 交强  业务类型 1-新保 2-续保 3-转保
     */
    @ApiModelProperty(value = "业务类型 1-新保 2-续保 3-转保")
    private InsuredTypeEnum insuredTypeCi;

    /**
     * 商业 业务类型 1-新保 2-续保 3-转保
     */
    @ApiModelProperty(value = "业务类型 1-新保 2-续保 3-转保")
    private InsuredTypeEnum insuredTypeBi;

    /**
     * 亚太分
     */
    @ApiModelProperty(value = "亚太分")
    private BigDecimal asiaPacificScore;
    /**
     * 不浮动原因
     */
    @ApiModelProperty(value = "不浮动原因")
    private String noFloatReason;
    /**
     * 团车码/团车渠道
     */
    @ApiModelProperty(value = "团车码/团车渠道")
    private String teamCarCode;
    /**
     * 次新车
     */
    @ApiModelProperty(value = "次新车")
    private SecondaryNewCarEnum secondaryNewCar;
    /**
     * 风险等级
     */
    @ApiModelProperty(value = "风险等级")
    private String riskLevel;
    /**
     * 车险分
     */
    @ApiModelProperty(value = "车险分")
    private BigDecimal autoInsurancePoints;

    /**
     * 业务类别
     */
    @ApiModelProperty(value = "业务类别")
    private String businessCategory;

    /**
     * 活动码
     */
    @ApiModelProperty(value = "活动码")
    private String promoteSalePlanId;

    /**
     * 国任决策单元
     */
    @ApiModelProperty(value = "国任决策单元")
    private String xdcxDmu;
    /**
     * 合作网点
     */
    @ApiModelProperty(value = "合作网点")
    private String cooperativeNetwork;

    /**
     * 单商业的交强险所属保司（当年）
     */
    @ApiModelProperty(value = "单商业的交强险所属保司（当年）")
    private String compTrafficCompanyBi;
    /**
     * 客户是否注册中华保的小程序
     */
    @ApiModelProperty(value = "客户是否注册中华保的小程序")
    private String chineseApplet;

    @ApiModelProperty(value = "人保归属机构")
    private String piccBelongDepartment;

    @ApiModelProperty(value = "高风险业务标识")
    private String highRiskServiceFlag;

    @ApiModelProperty(value = "尊享分（交商合计评分字段）")
    private BigDecimal totalEnjoyScore;
    /**
     * 小货车综合评分等级
     */
    @ApiModelProperty(value = "小货车综合评分等级")
    private String comprehensiveRiskRating;
    /**
     * 货车评分
     */
    @ApiModelProperty(value = "货车评分")
    private String truckRating;

    @ApiModelProperty(value = "交强险尊享分")
    private BigDecimal trafficEnjoyScore;

    @ApiModelProperty(value = "商业险尊享分")
    private BigDecimal commEnjoyScore;

    @ApiModelProperty(value = "违章风险评级")
    private String violationRiskLevel;

    @ApiModelProperty(value = "业务类型(紫金)")
    private String businessType;

    @ApiModelProperty(value = "商业险维度编码(渤海)")
    private String dimensionalityCodeBi;

    @ApiModelProperty(value = "交强险维度编码(渤海)")
    private String dimensionalityCodeCi;

    @ApiModelProperty(value = "续保类型(交强险)")
    private String renewalTypeCi;

    @ApiModelProperty(value = "续保类型(商业险)")
    private String renewalTypeBi;

    @ApiModelProperty(value = "出单口")
    private String underwritingChannel;

    @ApiModelProperty(value = "是否在APP展示")
    private YesNoNumberEnum showInApp;

    @Override
    public String getLockKey() {
        return policyNumber;
    }
}
