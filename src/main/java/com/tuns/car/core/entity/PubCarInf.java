package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 车辆数据库表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@Data
@TableName("pub_car_inf")
public class PubCarInf implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private Integer custId;

    /**
     * 车主
     */
    private String carOwner;

    /**
     * 车主证件号码
     */
    private String ownerIdentify;

    /**
     * 车主性质 1-个人 2-机关 3-企业
     */
    private String ownerNature;

    /**
     * 车主证件类型
     */
    private String ownerIdentifyType;

    /**
     * 车牌号码
     */
    private String plateNumber;

    /**
     * 车架号
     */
    private String frameNumber;

    /**
     * 发动机号码
     */
    private String engineNumber;

    /**
     * 注册日期 yyyy-MM-dd
     */
    private String regDate;

    /**
     * 发证日期 yyyy-MM-dd
     */
    private String certDate;

    /**
     * 过户标志
     */
    private String transferMark;

    /**
     * 过户日期 yyyy-MM-dd
     */
    private String transferDate;

    /**
     * 新车标致 0-非新车 1-新车
     */
    private String newCarMark;

    /**
     * 品牌名称
     */
    private String modelName;

    /**
     * 车型代码
     */
    private String modelCode;

    /**
     * 行驶里程 (公里/千米)
     */
    private Integer runMiles;

    /**
     * 核定载客
     */
    private Integer seatCount;

    /**
     * 能源类型
     */
    private String fuelType;

    /**
     * 排量(ML)
     */
    private Integer exhaustScale;

    /**
     * 功率(瓦)
     */
    private Integer power;

    /**
     * 新车购置价
     */
    private BigDecimal purchasePrice;

    /**
     * 贷款车辆标志 0-非贷款车 1-贷款车
     */
    private String loanCarMark;

    /**
     * 整备质量
     */
    private Integer wholeWeight;

    /**
     * 年款 yyyyMM/yyyy
     */
    private String marketDate;

    /**
     * 车辆类型描述
     */
    private String carStyleNote;

    /**
     * 车款名称
     */
    private String carName;

    /**
     * 车辆产地 1-进口 2-国产 3-合资
     */
    private String carOrigin;

    /**
     * 车辆吨位/核定载质量(吨)
     */
    private BigDecimal carTonnage;

    /**
     * 行业车型编码
     */
    private String prfsModelCode;

    /**
     * 是否安装GPS 0-未安装 1-安装
     */
    private String gpsMark;

    /**
     * 家庭车辆台数
     */
    private Integer familyCarCount;

    /**
     * 减税车型标志 0-正常 04-减免税 05-减税
     */
    private String taxCutsMark;

    /**
     * 减税比例 0~1
     */
    private BigDecimal taxCutProportion;

    /**
     * 车系名称
     */
    private String familyName;

    /**
     * 公告型号
     */
    private String noticeType;

    /**
     * 所属性质 1-个人 2-机关 3-企业
     */
    private String ownershipNature;

    /**
     * 营运性质 0-非营运 1-营运
     */
    private String operationNature;

    /**
     * 使用性质 附件码表C1.使用性质
     */
    private String usingNature;

    /**
     * 行政区域代码 省
     */
    private String provinceNumber;

    /**
     * 行政区域代码 市
     */
    private String cityNumber;

    /**
     * 行驶区域
     */
    private String runAreaCode;

    /**
     * 使用年限
     */
    private Integer useYears;

    /**
     * 车身颜色
     */
    private String bodyColor;

    /**
     * 备注
     */
    private String note;

    /**
     * 车型来源公司ID
     */
    private String carInfoCompany;

    /**
     * 创建时间
     */
    private LocalDateTime creTm;

    /**
     * 创建人
     */
    private Integer creUser;

    /**
     * 更新时间
     */
    private LocalDateTime mdfTm;

    /**
     * 更新人
     */
    private Integer mdfUser;

    /**
     * 删除标志 0-已删除 1-存储
     */
    private String delFlag;



    @Override
    public String toString() {
        return "PubCarInf{" +
                "id=" + id +
                ", custId=" + custId +
                ", carOwner=" + carOwner +
                ", ownerIdentify=" + ownerIdentify +
                ", ownerNature=" + ownerNature +
                ", ownerIdentifyType=" + ownerIdentifyType +
                ", plateNumber=" + plateNumber +
                ", frameNumber=" + frameNumber +
                ", engineNumber=" + engineNumber +
                ", regDate=" + regDate +
                ", certDate=" + certDate +
                ", transferMark=" + transferMark +
                ", transferDate=" + transferDate +
                ", newCarMark=" + newCarMark +
                ", modelName=" + modelName +
                ", modelCode=" + modelCode +
                ", runMiles=" + runMiles +
                ", seatCount=" + seatCount +
                ", fuelType=" + fuelType +
                ", exhaustScale=" + exhaustScale +
                ", power=" + power +
                ", purchasePrice=" + purchasePrice +
                ", loanCarMark=" + loanCarMark +
                ", wholeWeight=" + wholeWeight +
                ", marketDate=" + marketDate +
                ", carStyleNote=" + carStyleNote +
                ", carName=" + carName +
                ", carOrigin=" + carOrigin +
                ", carTonnage=" + carTonnage +
                ", prfsModelCode=" + prfsModelCode +
                ", gpsMark=" + gpsMark +
                ", familyCarCount=" + familyCarCount +
                ", taxCutsMark=" + taxCutsMark +
                ", taxCutProportion=" + taxCutProportion +
                ", familyName=" + familyName +
                ", noticeType=" + noticeType +
                ", ownershipNature=" + ownershipNature +
                ", operationNature=" + operationNature +
                ", usingNature=" + usingNature +
                ", provinceNumber=" + provinceNumber +
                ", cityNumber=" + cityNumber +
                ", runAreaCode=" + runAreaCode +
                ", useYears=" + useYears +
                ", bodyColor=" + bodyColor +
                ", note=" + note +
                ", carInfoCompany=" + carInfoCompany +
                ", creTm=" + creTm +
                ", creUser=" + creUser +
                ", mdfTm=" + mdfTm +
                ", mdfUser=" + mdfUser +
                ", delFlag=" + delFlag +
                "}";
    }
}
