package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.ImageDataEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 车险自助投保影像资料配置子表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-17 15:51:48
 */
@Data
@TableName("vi_image_data_conf_detail")
public class ViImageDataConfDetail implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 影像资料配置ID
	 */
	@ApiModelProperty(value = "影像资料配置ID")
	private String imageDataConfId;
	/**
	 * 影像资料
	 */
	@ApiModelProperty(value = "影像资料")
	private ImageDataEnum imageData;
	/**
	 * 影像资料
	 */
	@ApiModelProperty(value = "影像资料")
	private String attachType;

}
