package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_commission_temp")
public class ViCommissionTemp implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String verId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String tempId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String tempName;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String tempSts;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String remark;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date creTm;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date mdfTm;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer creUser;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer mdfUser;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer chkUser;
	/**
	 * 时间戳
	 */
	@ApiModelProperty(value = "时间戳")
	private Date tmSmp;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String oldTempId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String creUserName;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String mdfUserName;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String chkUserName;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date chkTm;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String rebutReason;

}
