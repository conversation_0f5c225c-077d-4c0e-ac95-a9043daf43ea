package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_policy_depart_info")
public class ViPolicyDepartInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 险种id
	 */
	@ApiModelProperty(value = "险种id")
	private Integer plantId;
	/**
	 * 批次id
	 */
	@ApiModelProperty(value = "批次id")
	private Integer batchId;
	/**
	 * 政策代码
	 */
	@ApiModelProperty(value = "政策代码")
	private String policyCode;
	/**
	 * 政策名称
	 */
	@ApiModelProperty(value = "政策名称")
	private String policyName;
	/**
	 * 公司编码
	 */
	@ApiModelProperty(value = "公司编码")
	private String companyId;
	/**
	 * 渠道编号
	 */
	@ApiModelProperty(value = "渠道编号")
	private String channelId;
	/**
	 * 机构代码
	 */
	@ApiModelProperty(value = "机构代码")
	private String departCode;
	/**
	 * 结算类型 1结回,2结出
	 */
	@ApiModelProperty(value = "结算类型 1结回,2结出")
	private String settleType;
	/**
	 * 机构类型  01 总部 02分公司 03营业部 04业务部
	 */
	@ApiModelProperty(value = "机构类型  01 总部 02分公司 03营业部 04业务部")
	private String policyLevel;
	/**
	 * 是否含税 0 否 1是
	 */
	@ApiModelProperty(value = "是否含税 0 否 1是")
	private String isIncludeTax;
	/**
	 * 交强险佣金率
	 */
	@ApiModelProperty(value = "交强险佣金率")
	private BigDecimal ciRate1;
	/**
	 * 交强险佣金率(附加)
	 */
	@ApiModelProperty(value = "交强险佣金率(附加)")
	private BigDecimal ciRate2;
	/**
	 * 商业险佣金率
	 */
	@ApiModelProperty(value = "商业险佣金率")
	private BigDecimal biRate1;
	/**
	 * 商业险佣金率(附加)
	 */
	@ApiModelProperty(value = "商业险佣金率(附加)")
	private BigDecimal biRate2;
	/**
	 * 奖励佣金率
	 */
	@ApiModelProperty(value = "奖励佣金率")
	private BigDecimal awardRate1;
	/**
	 * 奖励佣金率(附加)
	 */
	@ApiModelProperty(value = "奖励佣金率(附加)")
	private BigDecimal awardRate2;
	/**
	 * 创建用户
	 */
	@ApiModelProperty(value = "创建用户")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改用户
	 */
	@ApiModelProperty(value = "修改用户")
	private Integer mdfUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 是否删除  0否,1是
	 */
	@ApiModelProperty(value = "是否删除  0否,1是")
	private String delFlag;
	/**
	 * 创建人名字
	 */
	@ApiModelProperty(value = "创建人名字")
	private String creUserName;
	/**
	 * 更新人名字
	 */
	@ApiModelProperty(value = "更新人名字")
	private String mdfUserName;
	/**
	 * 佣金分配比例
	 */
	@ApiModelProperty(value = "佣金分配比例")
	private BigDecimal caRate;
	/**
	 * 下政策机构
	 */
	@ApiModelProperty(value = "下政策机构")
	private String nextPolicyAgency;
	/**
	 * 机构类型 1 直属机构 2 非直属机构
	 */
	@ApiModelProperty(value = "机构类型 1 直属机构 2 非直属机构")
	private String agencyType;
	/**
	 * 生效状态 1 生效 0 不生效
	 */
	@ApiModelProperty(value = "生效状态 1 生效 0 不生效")
	private String effectiveStatus;

}
