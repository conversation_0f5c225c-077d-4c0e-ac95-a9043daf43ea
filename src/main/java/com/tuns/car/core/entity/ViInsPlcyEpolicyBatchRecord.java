package com.tuns.car.core.entity;

import cn.hutool.json.JSONUtil;
import com.tuns.car.core.constant.EpolicyBatchDownloadStateEnum;
import com.tuns.core.boot.entity.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName ViInsPlcyEpolicyBatchRecord
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/5/15 14:49
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class ViInsPlcyEpolicyBatchRecord extends BaseEntity {

    /**
     * 批次id
     */
    private String downloadBatchId;

    /**
     * 状态
     */
    private EpolicyBatchDownloadStateEnum downloadState;

    /**
     * 下载文件地址
     */
    private String fileUrl;

    /**
     * 失败原因
     */
    private String failedReason;

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }

}
