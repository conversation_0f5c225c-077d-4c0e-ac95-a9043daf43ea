package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险单件政策表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_single_policy")
public class ViSinglePolicy implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 单件政策代码
	 */
	@ApiModelProperty(value = "单件政策代码")
	private String singlePolicyId;
	/**
	 * 单件政策名称
	 */
	@ApiModelProperty(value = "单件政策名称")
	private String singlePolicyName;
	/**
	 * 单件政策类型
	 */
	@ApiModelProperty(value = "单件政策类型")
	private String singlePolicyType;
	/**
	 * 单件政策json值
	 */
	@ApiModelProperty(value = "单件政策json值")
	private String singlePolicyJson;
	/**
	 * 公司编号
	 */
	@ApiModelProperty(value = "公司编号")
	private String companyId;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 渠道编号
	 */
	@ApiModelProperty(value = "渠道编号")
	private String channelId;
	/**
	 * 渠道名称
	 */
	@ApiModelProperty(value = "渠道名称")
	private String channelName;
	/**
	 * 基础政策代码
	 */
	@ApiModelProperty(value = "基础政策代码")
	private String policyId;
	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private Date beginTm;
	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private Date endTm;
	/**
	 * 结算类型 1结回 2结出
	 */
	@ApiModelProperty(value = "结算类型 1结回 2结出")
	private String sttType;
	/**
	 * 01 总部 02分公司 03营业部 04业务部
	 */
	@ApiModelProperty(value = "01 总部 02分公司 03营业部 04业务部")
	private String policyLevel;
	/**
	 * 是否含税 0 否 1是
	 */
	@ApiModelProperty(value = "是否含税 0 否 1是")
	private String isIncludeTax;
	/**
	 * 保费类型  1交强险保费, 2商业险保费, 3车险总保费, 4非车险总保费, 5车险总保费+非车险总保费
	 */
	@ApiModelProperty(value = "保费类型  1交强险保费, 2商业险保费, 3车险总保费, 4非车险总保费, 5车险总保费+非车险总保费")
	private String premType;
	/**
	 * 状态  0待提交 1 未审核  2审核拒绝 3审核通过 4启用 5停用 6作废
	 */
	@ApiModelProperty(value = "状态  0待提交 1 未审核  2审核拒绝 3审核通过 4启用 5停用 6作废")
	private String status;
	/**
	 * 是否按区间结算 0无  1区间
	 */
	@ApiModelProperty(value = "是否按区间结算 0无  1区间")
	private String conditionType;
	/**
	 * 奖励方式1佣金率, 2固定金额 
	 */
	@ApiModelProperty(value = "奖励方式1佣金率, 2固定金额 ")
	private String rewardMode;
	/**
	 * 奖励固定金额
	 */
	@ApiModelProperty(value = "奖励固定金额")
	private BigDecimal rewardAmt;
	/**
	 * 奖励比例
	 */
	@ApiModelProperty(value = "奖励比例")
	private BigDecimal rewardRate;
	/**
	 * 结出政策对应结回政策代码
	 */
	@ApiModelProperty(value = "结出政策对应结回政策代码")
	private String backSinglePolicyId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 创建用户
	 */
	@ApiModelProperty(value = "创建用户")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改用户
	 */
	@ApiModelProperty(value = "修改用户")
	private Integer mdfUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;

}
