package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 导入日志
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_import_log")
public class ViImportLog implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主健
	 */
	@ApiModelProperty(value = "主健")
	@TableId
	private Integer id;
	/**
	 * 导入日志业务ID
	 */
	@ApiModelProperty(value = "导入日志业务ID")
	private String importLogId;
	/**
	 * 导入批次号
	 */
	@ApiModelProperty(value = "导入批次号")
	private String batchNum;
	/**
	 * 导入结果 0 失败 1 成功
	 */
	@ApiModelProperty(value = "导入结果 0 失败 1 成功")
	private String state;
	/**
	 * 原文件
	 */
	@ApiModelProperty(value = "原文件")
	private String fileUrl;
	/**
	 * 创建人id
	 */
	@ApiModelProperty(value = "创建人id")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 导入类型，1为总部政策
	 */
	@ApiModelProperty(value = "导入类型，1为总部政策")
	private String importType;
	/**
	 * 创建人名字
	 */
	@ApiModelProperty(value = "创建人名字")
	private String creUserName;

}
