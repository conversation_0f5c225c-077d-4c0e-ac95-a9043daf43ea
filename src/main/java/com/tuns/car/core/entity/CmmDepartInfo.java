package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 组织结构表
 * 
 * <AUTHOR>
 * @date 2022-05-23 10:31:34
 */
@Data
@TableName("cmm_depart_info")
public class CmmDepartInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@ApiModelProperty(value = "id")
	@TableId
	private Integer id;
	/**
	 * 组织代码
	 */
	@ApiModelProperty(value = "组织代码")
	private String departCode;
	/**
	 * 组织名称
	 */
	@ApiModelProperty(value = "组织名称")
	private String departName;
	/**
	 * 组织类型
	 */
	@ApiModelProperty(value = "组织类型")
	private String departType;
	/**
	 * 父级组织代码
	 */
	@ApiModelProperty(value = "父级组织代码")
	private String parentDepartCode;
	/**
	 * 删除标记，1删除 0未删除（有效）
	 */
	@ApiModelProperty(value = "删除标记，1删除 0未删除（有效）")
	private String delFlag;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 顺序号
	 */
	@ApiModelProperty(value = "顺序号")
	private Integer orderSeq;
	/**
	 * 所在省份
	 */
	@ApiModelProperty(value = "所在省份")
	private String province;
	/**
	 * 所在城市
	 */
	@ApiModelProperty(value = "所在城市")
	private String city;
	/**
	 * 所在区县
	 */
	@ApiModelProperty(value = "所在区县")
	private String county;
	/**
	 * 启用状态，0停用，1启用
	 */
	@ApiModelProperty(value = "启用状态，0停用，1启用")
	private String isEnable;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private Integer mdfUser;
	/**
	 * 旧的部门ID
	 */
	@ApiModelProperty(value = "旧的部门ID")
	private Long oldBmid;
	/**
	 * 政策级别，1：分支机构政策，2：合作伙伴政策
	 */
	@ApiModelProperty(value = "政策级别，1：分支机构政策，2：合作伙伴政策")
	private String policyLevel;
	/**
	 * 组织全称
	 */
	@ApiModelProperty(value = "组织全称")
	private String departFullName;
	/**
	 * 机构邀请码
	 */
	@ApiModelProperty(value = "机构邀请码")
	private String investCode;
	/**
	 * 归属映射机构代码
	 */
	@ApiModelProperty(value = "归属映射机构代码")
	private String mappingDepartCode;
	/**
	 * 机构负责人
	 */
	@ApiModelProperty(value = "机构负责人")
	private Integer departDutyUserId;
	/**
	 * 机构负责人姓名
	 */
	@ApiModelProperty(value = "机构负责人姓名")
	private String departDutyUserName;
	/**
	 * 关联银保机构代码
	 */
	@ApiModelProperty(value = "关联银保机构代码")
	private String cbrcDepartCode;
	/**
	 * 关联银保机构名称
	 */
	@ApiModelProperty(value = "关联银保机构名称")
	private String cbrcDepartName;
	/**
	 * 机构性质 1：直属机构 2：非直属机构
	 */
	@ApiModelProperty(value = "机构性质 1：直属机构 2：非直属机构")
	private String agencyType;

}
