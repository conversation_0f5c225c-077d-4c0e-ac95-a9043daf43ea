package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.ChanTypeEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * 保单信息快照表
 * @date 2022/6/9 16:59
 */
@Data
@TableName("vi_ins_plcy_inf_snapshot")
public class ViInsPlcyInfSnapshot extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "批次id")
    private Long policyBatchId;

    @ApiModelProperty(value = "渠道类型")
    private ChanTypeEnum chanType;
}
