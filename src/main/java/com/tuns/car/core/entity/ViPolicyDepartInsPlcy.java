package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 一单一议保单政策信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_policy_depart_ins_plcy")
public class ViPolicyDepartInsPlcy implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 保单记录ID
	 */
	@ApiModelProperty(value = "保单记录ID")
	private Long policyId;
	/**
	 * 部门编码
	 */
	@ApiModelProperty(value = "部门编码")
	private String departCode;
	/**
	 * 结算类型 1结回,2结出
	 */
	@ApiModelProperty(value = "结算类型 1结回,2结出")
	private String settleType;
	/**
	 * 机构类型  01 总部 02分公司 03营业部 04业务部
	 */
	@ApiModelProperty(value = "机构类型  01 总部 02分公司 03营业部 04业务部")
	private String policyLevel;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String isIncludeTax;
	/**
	 * 交强险佣金率
	 */
	@ApiModelProperty(value = "交强险佣金率")
	private BigDecimal ciRate1;
	/**
	 * 交强险佣金率(附加)
	 */
	@ApiModelProperty(value = "交强险佣金率(附加)")
	private BigDecimal ciRate2;
	/**
	 * 商业险佣金率
	 */
	@ApiModelProperty(value = "商业险佣金率")
	private BigDecimal biRate1;
	/**
	 * 商业险佣金率(附加)
	 */
	@ApiModelProperty(value = "商业险佣金率(附加)")
	private BigDecimal biRate2;
	/**
	 * 奖励佣金率
	 */
	@ApiModelProperty(value = "奖励佣金率")
	private BigDecimal awardRate1;
	/**
	 * 奖励佣金率(附加)
	 */
	@ApiModelProperty(value = "奖励佣金率(附加)")
	private BigDecimal awardRate2;
	/**
	 * 创建用户
	 */
	@ApiModelProperty(value = "创建用户")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改用户
	 */
	@ApiModelProperty(value = "修改用户")
	private Integer mdfUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 是否删除  0否,1是
	 */
	@ApiModelProperty(value = "是否删除  0否,1是")
	private String delFlag;

}
