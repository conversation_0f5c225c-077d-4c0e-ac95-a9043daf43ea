package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.supalle.autotrim.AutoTrim;
import com.tuns.car.core.constant.SpecialInputEnum;
import com.tuns.car.core.constant.TractorTypeEnum;
import com.tuns.car.core.constant.TransferMarkTypeEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
@AutoTrim
@TableName("vi_ins_car_inf")
public class ViInsCarInf extends BaseEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 保单批次ID
	 */
	@ApiModelProperty(value = "保单批次ID")
	private Long policyBatchId;
	/**
	 * 车主
	 */
	@ApiModelProperty(value = "车主")
	private String carOwner;
	/**
	 * 车主证件号码
	 */
	@ApiModelProperty(value = "车主证件号码")
	private String ownerIdentify;
	/**
	 * 车主证件类型
	 */
	@ApiModelProperty(value = "车主证件类型")
	private String ownerIdentifyType;
	/**
	 * 车牌号码
	 */
	@ApiModelProperty(value = "车牌号码")
	private String plateNumber;
	/**
	 * 车架号
	 */
	@ApiModelProperty(value = "车架号")
	private String frameNumber;
	/**
	 * 发动机号码
	 */
	@ApiModelProperty(value = "发动机号码")
	private String engineNumber;
	/**
	 * 注册日期 yyyy-MM-dd
	 */
	@ApiModelProperty(value = "注册日期 yyyy-MM-dd")
	private String regDate;
	/**
	 * 发证日期 yyyy-MM-dd
	 */
	@ApiModelProperty(value = "发证日期 yyyy-MM-dd")
	private String certDate;
	/**
	 * 过户标志
	 */
	@ApiModelProperty(value = "过户标志")
	private String transferMark;
	/**
	 * 过户日期 yyyy-MM-dd
	 */
	@ApiModelProperty(value = "过户日期 yyyy-MM-dd")
	private String transferDate;
	/**
	 * 新车标致 0-非新车 1-新车
	 */
	@ApiModelProperty(value = "新车标致 0-非新车 1-新车")
	private String newCarMark;
	/**
	 * 品牌名称
	 */
	@ApiModelProperty(value = "品牌名称")
	private String modelName;
	/**
	 * 车型代码
	 */
	@ApiModelProperty(value = "车型代码")
	private String modelCode;
	/**
	 * 行驶里程 (公里/千米)
	 */
	@ApiModelProperty(value = "行驶里程 (公里/千米)")
	private Integer runMiles;
	/**
	 * 核定载客
	 */
	@ApiModelProperty(value = "核定载客")
	private Integer seatCount;
	/**
	 * 能源类型
	 */
	@ApiModelProperty(value = "能源类型")
	private String fuelType;
	/**
	 * 排量(ML)
	 */
	@ApiModelProperty(value = "排量(ML)")
	private Integer exhaustScale;
	/**
	 * 功率(瓦)
	 */
	@ApiModelProperty(value = "功率(瓦)")
	private Integer power;
	/**
	 * 新车购置价
	 */
	@ApiModelProperty(value = "新车购置价")
	private BigDecimal purchasePrice;
	/**
	 * 贷款车辆标志 0-非贷款车 1-贷款车
	 */
	@ApiModelProperty(value = "贷款车辆标志 0-非贷款车 1-贷款车")
	private String loanCarMark;
	/**
	 * 整备质量
	 */
	@ApiModelProperty(value = "整备质量")
	private Integer wholeWeight;
	/**
	 * 年款 yyyyMM/yyyy
	 */
	@ApiModelProperty(value = "年款 yyyyMM/yyyy")
	private String marketDate;
	/**
	 * 车辆类型描述
	 */
	@ApiModelProperty(value = "车辆类型描述")
	private String carStyleNote;
	/**
	 * 车款名称
	 */
	@ApiModelProperty(value = "车款名称")
	private String carName;
	/**
	 * 车辆产地 1-进口 2-国产 3-合资
	 */
	@ApiModelProperty(value = "车辆产地 1-进口 2-国产 3-合资")
	private String carOrigin;
	/**
	 * 车辆吨位/核定载质量(吨)
	 */
	@ApiModelProperty(value = "车辆吨位/核定载质量(吨)")
	private BigDecimal carTonnage;
	/**
	 * 行业车型编码
	 */
	@ApiModelProperty(value = "行业车型编码")
	private String prfsModelCode;
	/**
	 * 是否安装GPS 0-未安装 1-安装
	 */
	@ApiModelProperty(value = "是否安装GPS 0-未安装 1-安装")
	private String gpsMark;
	/**
	 * 家庭车辆台数
	 */
	@ApiModelProperty(value = "家庭车辆台数")
	private Integer familyCarCount;
	/**
	 * 减税车型标志 0-正常 04-减免税 05-减税
	 */
	@ApiModelProperty(value = "减税车型标志 0-正常 04-减免税 05-减税")
	private String taxCutsMark;
	/**
	 * 减税比例 0~1
	 */
	@ApiModelProperty(value = "减税比例 0~1")
	private BigDecimal taxCutProportion;
	/**
	 * 车系名称
	 */
	@ApiModelProperty(value = "车系名称")
	private String familyName;
	/**
	 * 公告型号
	 */
	@ApiModelProperty(value = "公告型号")
	private String noticeType;
	/**
	 * 所属性质 1-个人 2-机关 3-企业
	 */
	@ApiModelProperty(value = "所属性质 1-个人 2-机关 3-企业")
	private String ownershipNature;
	/**
	 * 营运性质 0-非营运 1-营运
	 */
	@ApiModelProperty(value = "营运性质 0-非营运 1-营运")
	private String operationNature;
	/**
	 * 使用性质 附件码表C1.使用性质
	 */
	@ApiModelProperty(value = "使用性质 附件码表C1.使用性质")
	private String usingNature;
	/**
	 * 行政区域代码 省级
	 */
	@ApiModelProperty(value = "行政区域代码 省级")
	private String provinceNumber;
	/**
	 * 行政区域代码 市级
	 */
	@ApiModelProperty(value = "行政区域代码 市级")
	private String cityNumber;
	/**
	 * 行驶区域
	 */
	@ApiModelProperty(value = "行驶区域")
	private String runAreaCode;
	/**
	 * 使用年限
	 */
	@ApiModelProperty(value = "使用年限")
	private Integer useYears;
	/**
	 * 车身颜色
	 */
	@ApiModelProperty(value = "车身颜色")
	private String bodyColor;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String note;
	/**
	 * 车型来源公司ID
	 */
	@ApiModelProperty(value = "车型来源公司ID")
	private String carInfoCompany;
	/**
	 * 扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）
	 */
	@ApiModelProperty(value = "扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）")
	private String extend;

	/**
	 * 车主性质
	 */
	@ApiModelProperty(value = "车主性质")
	private String ownerNature;
	/**
	 * 车辆实际价
	 */
	@ApiModelProperty(value = "车辆实际价")
	private BigDecimal actualPrice;
	/**
	 * 验车状态  1已验 2免验
	 */
	@ApiModelProperty(value = "验车状态  1已验 2免验")
	private String checkCarStatus;
	/**
	 * 免验车原因 1单保责任险 2按期续保，且未加保损失类险种 3新车 4团车业务 5符合免验规定
	 */
	@ApiModelProperty(value = "免验车原因 1单保责任险 2按期续保，且未加保损失类险种 3新车 4团车业务 5符合免验规定")
	private String exemptionReason;
	/**
	 * 验车人
	 */
	@ApiModelProperty(value = "验车人")
	private String carInspector;

	/**
	 * 破损位置
	 */
	@ApiModelProperty("破损位置")
	private String damageLocation;
	/**
	 * 验车时间
	 */
	@ApiModelProperty(value = "验车时间")
	private String inspectionTime;
	/**
	 * 验车结果 1 全车完好无损，证车相符，年审合格 9 其他
	 */
	@ApiModelProperty(value = "验车结果 1 全车完好无损，证车相符，年审合格 9 其他")
	private String inspectionResult;
	/**
	 * 其他能源类型（燃油）种类描述
	 */
	@ApiModelProperty(value = "其他能源类型（燃油）种类描述")
	private String fuelTypeRemark;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String pureRange;
	/**
	 * 行驶证车辆类型
	 */
	@ApiModelProperty(value = "行驶证车辆类型")
	private String licenseVehicleType;

    /**
     *车辆类型编码
     * */
    @ApiModelProperty(value = "车辆类型编码")
    private String carTypeCode;

	/**
	 * {@link SpecialInputEnum}
	 */
	@ApiModelProperty(value = "特殊录入情况")
	private String specialInput;

    @ApiModelProperty(value = "过户类型")
    private TransferMarkTypeEnum transferMarkType;

    @ApiModelProperty(value = "行驶证厂牌型号")
    private String licenseBrand;

    @ApiModelProperty(value = "行驶证地址")
    private String licenseAddress;

    @ApiModelProperty(value = "号牌底色")
    private String licenseColor;

    /**
     * 车辆来历凭证编码
     */
    @ApiModelProperty(value = "车辆来历凭证编码")
    private String vehicleCertNo;
    /**
     * 车辆来历凭证种类
     */
    @ApiModelProperty(value = "车辆来历凭证种类")
    private String vehicleCertType;

    /**
     * 开具车辆来历凭证所载日期
     */
    @ApiModelProperty(value = "开具车辆来历凭证所载日期")
    private LocalDateTime vehicleCertDate;
	 /**
     * 牵引总质量
     */
    @ApiModelProperty("牵引总质量")
    private BigDecimal totalTractionMass;

    /**
     * 是否牵引车 0:否,1:是
     */
    @ApiModelProperty("是否牵引车 0:否,1:是")
    private String tractorMark;

    @ApiModelProperty("牵引车类型")
    private TractorTypeEnum tractorType;

	@ApiModelProperty(value = "验车具体原因文本")
	private String carCheckReasonStr;

}
