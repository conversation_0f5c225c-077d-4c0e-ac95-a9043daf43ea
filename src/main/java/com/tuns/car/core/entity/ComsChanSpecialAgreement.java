package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.InputTypeEnum;
import com.tuns.car.core.constant.PageTypeEnum;
import com.tuns.car.core.constant.PolicyTypeEnum;
import com.tuns.core.boot.constant.SwitchFlagEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 渠道特约配置表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("coms_chan_special_agreement")
public class ComsChanSpecialAgreement extends BaseEntity {
    /**
     * 出单渠道ID
     */
    @ApiModelProperty(value = "出单渠道ID")
    private Integer chanDetailId;
    /**
     * 特约类型（对应保单业务中是出单类型）
     */
    @ApiModelProperty(value = "特约类型（对应保单业务中是出单类型）")
    private PolicyTypeEnum engageType;
    /**
     * 特约代码
     */
    @ApiModelProperty(value = "特约代码")
    private String engageCode;

    /**
     * 特约名称
     */
    @ApiModelProperty(value = "特约名称")
    private String engageName;

    /**
     * 特约内容
     */
    @ApiModelProperty(value = "特约内容")
    private String engageContent;
    /**
     * 页面字段输入类型
     */
    @ApiModelProperty(value = "页面字段输入类型")
    private InputTypeEnum inputType;
    /**
     * 特约所属页面
     */
    @ApiModelProperty(value = "特约所属页面")
    private PageTypeEnum pageType;
    /**
     * 特约状态
     */
    @ApiModelProperty(value = "特约状态")
    private SwitchFlagEnum engageStatus;
}
