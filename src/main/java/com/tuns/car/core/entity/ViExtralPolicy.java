package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险附加政策表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_extral_policy")
public class ViExtralPolicy implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 附加政策代码
	 */
	@ApiModelProperty(value = "附加政策代码")
	private String extralPolicyId;
	/**
	 * 附加政策名称
	 */
	@ApiModelProperty(value = "附加政策名称")
	private String extralPolicyName;
	/**
	 * 附加政策类型 1规模平台 2达标进度 3赔付率+手续费
	 */
	@ApiModelProperty(value = "附加政策类型 1规模平台 2达标进度 3赔付率+手续费")
	private String extralPolicyType;
	/**
	 * 附加政策json值
	 */
	@ApiModelProperty(value = "附加政策json值")
	private String extralPolicyJson;
	/**
	 * 公司编号
	 */
	@ApiModelProperty(value = "公司编号")
	private String companyId;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 渠道编号
	 */
	@ApiModelProperty(value = "渠道编号")
	private String channelId;
	/**
	 * 渠道名称
	 */
	@ApiModelProperty(value = "渠道名称")
	private String channelName;
	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private Date beginTm;
	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private Date endTm;
	/**
	 * 状态  0待提交 1 未审核  2审核拒绝 3审核通过 4启用 5停用 6作废
	 */
	@ApiModelProperty(value = "状态  0待提交 1 未审核  2审核拒绝 3审核通过 4启用 5停用 6作废")
	private String status;
	/**
	 * 是否含税 0 否 1是
	 */
	@ApiModelProperty(value = "是否含税 0 否 1是")
	private String isIncludeTax;
	/**
	 * 结算类型 1结回 2结出
	 */
	@ApiModelProperty(value = "结算类型 1结回 2结出")
	private String sttType;
	/**
	 * 政策级别 01 总部 02分公司 03营业部 04业务部
	 */
	@ApiModelProperty(value = "政策级别 01 总部 02分公司 03营业部 04业务部")
	private String policyLevel;
	/**
	 * 创建用户
	 */
	@ApiModelProperty(value = "创建用户")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改用户
	 */
	@ApiModelProperty(value = "修改用户")
	private Integer mdfUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
