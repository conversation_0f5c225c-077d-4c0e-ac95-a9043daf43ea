package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险报价批处理表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_quote_bat")
public class ViQuoteBat implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ID序列
	 */
	@ApiModelProperty(value = "ID序列")
	@TableId
	private Integer id;
	/**
	 * 请求ID
	 */
	@ApiModelProperty(value = "请求ID")
	private String requestId;
	/**
	 * 批处理service类
	 */
	@ApiModelProperty(value = "批处理service类")
	private String batService;
	/**
	 * 批处理类型
	 */
	@ApiModelProperty(value = "批处理类型")
	private String batType;
	/**
	 * 记录数
	 */
	@ApiModelProperty(value = "记录数")
	private Integer recordCount;
	/**
	 * 执行开始时间
	 */
	@ApiModelProperty(value = "执行开始时间")
	private Date beginTime;
	/**
	 * 执行结束时间
	 */
	@ApiModelProperty(value = "执行结束时间")
	private Date endTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private Integer mdfUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;

}
