package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险数据合并异常记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_merge_data_error")
public class ViMergeDataError implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	@TableId
	private Integer id;
	/**
	 * 是否处理完成[1-已处理，9-未处理]
	 */
	@ApiModelProperty(value = "是否处理完成[1-已处理，9-未处理]")
	private Integer errorStatus;
	/**
	 * 业务类型[01-报价，02-订单]
	 */
	@ApiModelProperty(value = "业务类型[01-报价，02-订单]")
	private String businessType;
	/**
	 * 业务数据，存批次id
	 */
	@ApiModelProperty(value = "业务数据，存批次id")
	private String businessData;
	/**
	 * 错误消息
	 */
	@ApiModelProperty(value = "错误消息")
	private String errorMsg;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date mdfTm;
	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	private Integer mdfUser;
	/**
	 * 删除标志[0-存储,1-已删除]
	 */
	@ApiModelProperty(value = "删除标志[0-存储,1-已删除]")
	private String delFlag;

}
