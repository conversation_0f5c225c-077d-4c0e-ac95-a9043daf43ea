package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险机构政策表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:25
 */
@Data
@TableName("vi_depart_policy")
public class ViDepartPolicy implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 车险政策代码
	 */
	@ApiModelProperty(value = "车险政策代码")
	private String viPolicyId;
	/**
	 * 政策代码
	 */
	@ApiModelProperty(value = "政策代码")
	private String policyId;
	/**
	 * 公司编码
	 */
	@ApiModelProperty(value = "公司编码")
	private String companyId;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 渠道编号
	 */
	@ApiModelProperty(value = "渠道编号")
	private String channelId;
	/**
	 * 渠道名称
	 */
	@ApiModelProperty(value = "渠道名称")
	private String channelName;
	/**
	 * 上级车险政策代码
	 */
	@ApiModelProperty(value = "上级车险政策代码")
	private String parentViPolicyId;
	/**
	 * 机构代码
	 */
	@ApiModelProperty(value = "机构代码")
	private String departCode;
	/**
	 * 机构类型  01 总部 02分公司 03营业部 04业务部
	 */
	@ApiModelProperty(value = "机构类型  01 总部 02分公司 03营业部 04业务部")
	private String policyLevel;
	/**
	 * 商业佣金费率
	 */
	@ApiModelProperty(value = "商业佣金费率")
	private BigDecimal commercialFeeRate;
	/**
	 * 交强佣金费率
	 */
	@ApiModelProperty(value = "交强佣金费率")
	private BigDecimal compulsoryFeeRate;
	/**
	 * 车船税佣金费率
	 */
	@ApiModelProperty(value = "车船税佣金费率")
	private BigDecimal vvtaxFeeRate;
	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private Date beginTm;
	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private Date endTm;
	/**
	 * 是否含税 0 否 1是
	 */
	@ApiModelProperty(value = "是否含税 0 否 1是")
	private String isIncludeTax;
	/**
	 * 状态  0待提交 1 未审核  2审核拒绝 3审核通过 4启用 5停用 6作废
	 */
	@ApiModelProperty(value = "状态  0待提交 1 未审核  2审核拒绝 3审核通过 4启用 5停用 6作废")
	private String status;
	/**
	 * 创建用户
	 */
	@ApiModelProperty(value = "创建用户")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改用户
	 */
	@ApiModelProperty(value = "修改用户")
	private Integer mdfUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
