package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 车险保单续保拓展表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-22 16:51:14
 */
@Data
@TableName("vi_ins_plcy_inf_expand")
public class ViInsPlcyInfExpand extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键字段
     */
    @ApiModelProperty(value = "业务主键字段")
    private String renewalId;
    /**
     * 保单记录ID
     */
    @ApiModelProperty(value = "保单记录ID")
    private Long policyId;
    /**
     * 展业人员身份证
     */
    @ApiModelProperty(value = "展业人员身份证")
    private String expandCard;
    /**
     * 展业人员手机号
     */
    @ApiModelProperty(value = "展业人员手机号")
    private String expandPhone;
    /**
     * 展业人员名字
     */
    @ApiModelProperty(value = "展业人员名字")
    private String expandUserName;

}
