package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_commission_policy")
public class ViCommissionPolicy implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 模板编号
	 */
	@ApiModelProperty(value = "模板编号")
	private String tempId;
	/**
	 * 政策类型
	 */
	@ApiModelProperty(value = "政策类型")
	private String policyTyp;
	/**
	 * 条件类型 1指定 2除外 3区间
	 */
	@ApiModelProperty(value = "条件类型 1指定 2除外 3区间")
	private String conditionTyp;
	/**
	 * 条件明细
	 */
	@ApiModelProperty(value = "条件明细")
	private String conditionDetail;
	/**
	 * 开始值
	 */
	@ApiModelProperty(value = "开始值")
	private String beginValue;
	/**
	 * 开始值是否包含 0 否 1是
	 */
	@ApiModelProperty(value = "开始值是否包含 0 否 1是")
	private String beginInclude;
	/**
	 * 结束值
	 */
	@ApiModelProperty(value = "结束值")
	private String endValue;
	/**
	 * 结束值是否包含 0否 1是
	 */
	@ApiModelProperty(value = "结束值是否包含 0否 1是")
	private String endInclude;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private Integer mdfUser;
	/**
	 * 审核人
	 */
	@ApiModelProperty(value = "审核人")
	private Integer chkUser;
	/**
	 * 时间戳
	 */
	@ApiModelProperty(value = "时间戳")
	private Date tmSmp;
	/**
	 * 版本号
	 */
	@ApiModelProperty(value = "版本号")
	private String verId;

}
