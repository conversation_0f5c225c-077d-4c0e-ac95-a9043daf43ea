package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险自助投保影像资料配置子表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_ins_image_data_conf_detail")
public class ViInsImageDataConfDetail implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 影像资料配置ID
	 */
	@ApiModelProperty(value = "影像资料配置ID")
	private String imageDataConfId;
	/**
	 * 影像资料
	 */
	@ApiModelProperty(value = "影像资料")
	private String imageData;
	/**
	 * 影像资料
	 */
	@ApiModelProperty(value = "影像资料")
	private String attachType;

}
