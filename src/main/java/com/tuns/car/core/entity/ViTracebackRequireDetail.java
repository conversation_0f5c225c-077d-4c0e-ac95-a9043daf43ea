package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险投保回溯要求配置子表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-14 14:37:10
 */
@Data
@TableName("vi_traceback_require_detail")
public class ViTracebackRequireDetail implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@TableId
	private Integer id;
	/**
	 * 回溯主键ID
	 */
	@ApiModelProperty(value = "回溯主键ID")
	private String tracebackRequireId;
	/**
	 * 文件名称
	 */
	@ApiModelProperty(value = "文件名称")
	private String fileUrl;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String fileName;
	/**
	 * pdf路径
	 */
	@ApiModelProperty(value = "pdf路径")
	private String pdfUrl;

}
