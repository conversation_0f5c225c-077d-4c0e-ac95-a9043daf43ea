package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.compare.BusinessSceneEnum;
import com.tuns.car.core.constant.compare.CompareResultPassEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车险数据比对结果表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-23 15:37:23
 */
@Data
@TableName("vi_ins_plcy_compare_result")
public class ViInsPlcyCompareResult extends BaseEntity {
    /**
     * 比对结果(0 一致 1 不一致)
     */
    @ApiModelProperty(value = "比对结果(0 一致 1 不一致)")
    private String compareResult;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private Long policyBatchId;
    /**
     * 不一致字段json
     */
    @ApiModelProperty(value = "不一致字段json")
    private String inconformityField;
    /**
     * 对比场景
     */
    @ApiModelProperty(value = "对比场景")
    private BusinessSceneEnum compareScene;

    /**
     * 放开限制
     */
    @ApiModelProperty(value = "放开限制")
    private CompareResultPassEnum pass;

    /**
     * 放开的配置项对应的字典表id
     */
    @ApiModelProperty(value = "放开的配置项对应的字典表id")
    private Long passFieldDictId;
}
