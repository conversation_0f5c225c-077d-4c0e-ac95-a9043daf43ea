package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.InsuredTypeEnum;
import com.tuns.car.core.constant.SecondaryNewCarEnum;
import com.tuns.core.boot.constant.YesNoNumberEnum;
import com.tuns.core.boot.vo.page.LockKeyProvider;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 报价单政策匹配佣金计算表
 */
@Data
@TableName("vi_ins_plcy_inf_tp_plan")
public class ViInsPlcyInfTpPlan implements LockKeyProvider, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @ApiModelProperty(value = "$column.comments")
    @TableId
    private Integer id;
    /**
     * 保单批次ID
     */
    @ApiModelProperty(value = "保单批次ID")
    private Long policyBatchId;

    /**
     * 承保公司ID
     */
    @ApiModelProperty(value = "承保公司ID")
    private String companyId;

    /**
     * 保险公司合作渠道代码
     */
    @ApiModelProperty(value = "保险公司合作渠道代码")
    private String channelId;

    /**
     * 报价日期
     */
    @ApiModelProperty(value = "报价日期")
    private LocalDateTime offerTime;

    /**
     * 本公司组织机构代码
     */
    @ApiModelProperty(value = "本公司组织机构代码")
    private String departCode;

    /**
     * 本公司组织机构名称
     */
    @ApiModelProperty(value = "本公司组织机构名称")
    private String departName;




    @Override
    public String getLockKey() {
        return policyBatchId+"";
    }
}
