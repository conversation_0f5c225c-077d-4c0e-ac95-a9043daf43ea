package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.SimpleBaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-08-08 19:08
 **/
@Data
@TableName("vi_quote_apply_record_company_permission")
public class ViQuoteApplyRecordCompanyPermission extends SimpleBaseEntity {

    /**
     * 保险公司ID
     */
    @ApiModelProperty(value = "保险公司ID")
    private Integer companyId;

}
