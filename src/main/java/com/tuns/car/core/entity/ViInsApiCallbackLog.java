package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-16 10:31:13
 */
@Data
@TableName("vi_ins_api_callback_log")
public class ViInsApiCallbackLog extends BaseEntity {

    /**
     * 回调处理业务ID 据类型而定
     */
    @ApiModelProperty(value = "回调处理业务ID 据类型而定")
    private String businessId;
    /**
     * 回调处理业务类型 1承保 2投保 3支付
     */
    @ApiModelProperty(value = "回调处理业务类型 1承保 2投保 3支付")
    private String businessType;
    /**
     * 处理结果 1成功 0失败
     */
    @ApiModelProperty(value = "处理结果 1成功 0失败")
    private String processResult;
    /**
     * 异常信息 处理结果失败时不能为空
     */
    @ApiModelProperty(value = "异常信息 处理结果失败时不能为空")
    private String errorMessage;
    /**
     * 保司请求回调原始json报文
     */
    @ApiModelProperty(value = "保司请求回调原始json报文")
    private String jsonContent;
    /**
     * 保险公司代码
     */
    @ApiModelProperty(value = "保险公司代码")
    private String companyId;

}
