package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_extral_policy_stt_inf")
public class ViExtralPolicySttInf implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 附加政策
	 */
	@ApiModelProperty(value = "附加政策")
	private String extralPolicyId;
	/**
	 * 结算日期
	 */
	@ApiModelProperty(value = "结算日期")
	private Date sttDate;
	/**
	 * 结算开始时间
	 */
	@ApiModelProperty(value = "结算开始时间")
	private Date sttBeginTm;
	/**
	 * 结算结束时间
	 */
	@ApiModelProperty(value = "结算结束时间")
	private Date sttEndTm;
	/**
	 * 结算金额
	 */
	@ApiModelProperty(value = "结算金额")
	private BigDecimal sttAmt;
	/**
	 * 状态 U 待处理 P处理中 S成功 F失败
	 */
	@ApiModelProperty(value = "状态 U 待处理 P处理中 S成功 F失败")
	private String status;
	/**
	 * 任务流水号
	 */
	@ApiModelProperty(value = "任务流水号")
	private Long jobJrnNo;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;

}
