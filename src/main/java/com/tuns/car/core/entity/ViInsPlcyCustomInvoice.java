package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.InvoicePayerTypeEnum;
import com.tuns.car.core.constant.InvoiceTypeEnum;
import com.tuns.car.core.constant.ViPersonTypeEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName ViInsPlcyCustomInvoice
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/6 14:27
 * @Version 1.0
 */
@Data
@TableName("vi_ins_plcy_custom_invoice")
public class ViInsPlcyCustomInvoice extends BaseEntity {


    /**
     * 保单批次id
     */
    @ApiModelProperty("保单批次id")
    private Long policyBatchId;

    /**
     * 发票类型
     */
    @ApiModelProperty("发票类型")
    private InvoiceTypeEnum invoiceType;

    /**
     * 发票类型
     */
    @ApiModelProperty("发票类型")
    private InvoicePayerTypeEnum invoicePayerType;

    /**
     * 开票对象
     */
    @ApiModelProperty("开票对象 01车主,02被保人,03投保人")
    private ViPersonTypeEnum invoiceTarget;

    /**
     * 纳税人识别号/统一社会信用代码
     */
    @ApiModelProperty("纳税人识别号/统一社会信用代码")
    private String identifyNumber;

    /**
     * 电子发票接收手机号
     */
    @ApiModelProperty("电子发票接收手机号")
    private String mobile;

    /**
     * 发票（电话）
     */
    @ApiModelProperty("发票（电话）")
    private String invoiceMobile;

    /**
     * 税务登记地址
     */
    @ApiModelProperty("税务登记地址")
    private String addressComplete;

    /**
     * 开户行名称
     */
    @ApiModelProperty("开户行名称")
    private String bankDeposit;

    /**
     * 银行账号
     */
    @ApiModelProperty("银行账号")
    private String bankAccount;


}
