package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 折扣系数审核记录表
 *
 * <AUTHOR>
 * @since 2024-05-06 11:18
 **/
@Data
@TableName("vi_discount_application_record")
public class ViDiscountApplicationRecord extends BaseEntity {

    /**
     * 折扣系数ID
     */
    @ApiModelProperty(value = "折扣系数ID")
    private String discountFactorId;

    /**
     * 保险公司ID
     */
    @ApiModelProperty(value = "保险公司ID")
    private String companyId;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称")
    private String companyName;

    /**
     * 渠道ID
     */
    @ApiModelProperty(value = "渠道ID")
    private String channelId;

    /**
     * 审核状态：0：待审核；1：已审核
     */
    @ApiModelProperty(value = "审核状态：0：待审核；1：已审核")
    private Integer auditType;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditDate;

    /**
     * 审核结果：0：已通过；1：已拒接
     */
    @ApiModelProperty(value = "审核结果：0：已通过；1：已拒绝")
    private Integer auditResult;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因")
    private String applicationReason;

    /**
     * 申请系数
     */
    @ApiModelProperty(value = "申请系数")
    private BigDecimal applicationFactor;

    /**
     * 配置的最低系数
     */
    @ApiModelProperty(value = "配置的最低系数")
    private BigDecimal configMinFactor;

    /**
     * 作废标识：0：正常使用，1：作废 2：初始值
     */
    @ApiModelProperty(value = "作废标识：0：正常使用，1：作废")
    private Integer cancelMark;

    /**
     * 拒绝原因
     */
    @ApiModelProperty(value = "拒绝原因")
    private String refuseReason;
}
