package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险单件机构政策表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_single_depart_policy")
public class ViSingleDepartPolicy implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String singlePolicyId;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String departCode;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer creUser;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date creTm;

}
