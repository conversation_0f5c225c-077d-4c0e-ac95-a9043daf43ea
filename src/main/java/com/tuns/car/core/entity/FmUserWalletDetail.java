package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@Data
@TableName("fm_user_wallet_detail")
public class FmUserWalletDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 交易流水号
     */
    private String transNumber;

    /**
     * 保单id
     */
    private String policyId;

    /**
     * 保单号码
     */
    private String policyNumber;

    /**
     * 批单号码
     */
    private String endorsementNumber;

    /**
     * 车牌号码
     */
    private String plateNumber;

    /**
     * 业务类型:1-车险，2-非车险，3-寿险
     */
    private String insuranceType;

    /**
     * 政策类型：1-基础政策，2-单件奖励，3-附加奖励，4-调整佣金 5-协作费
     */
    private String policyType;

    /**
     * 交易类型：1-收入，2-提现，3-冲账
     */
    private String transType;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 流水状态：1-冻结中，2-可提现，3-提现申请，4-提现成功，5-提现失败，6-提现退回，7-冲账，8-已撤销佣金
     */
    private String status;

    /**
     * 是否删除：0-否，1-是
     */
    private String delFlag;

    /**
     * 创建用户
     */
    private Integer creUser;

    /**
     * 创建时间
     */
    private LocalDateTime creTm;

    /**
     * 修改用户
     */
    private Integer mdfUser;

    /**
     * 修改时间
     */
    private LocalDateTime mdfTm;

    /**
     * 批次号（车险批次号，非车保单id，提现交易流水号）
     */
    private String policyBatchId;

    /**
     * 保单类型 1-交强单 2-商业单
     */
    private String insType;

    @Override
    public String toString() {
        return "FmUserWalletDetail{" +
                "id=" + id +
                ", transNumber=" + transNumber +
                ", policyId=" + policyId +
                ", policyNumber=" + policyNumber +
                ", endorsementNumber=" + endorsementNumber +
                ", plateNumber=" + plateNumber +
                ", insuranceType=" + insuranceType +
                ", policyType=" + policyType +
                ", transType=" + transType +
                ", userId=" + userId +
                ", amount=" + amount +
                ", status=" + status +
                ", delFlag=" + delFlag +
                ", creUser=" + creUser +
                ", creTm=" + creTm +
                ", mdfUser=" + mdfUser +
                ", mdfTm=" + mdfTm +
                ", policyBatchId=" + policyBatchId +
                ", insType=" + insType +
                "}";
    }
}
