package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险基础政策明细
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_base_policy_detail")
public class ViBasePolicyDetail implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 基础政策代码
	 */
	@ApiModelProperty(value = "基础政策代码")
	private String policyId;
	/**
	 * 政策类型
	 */
	@ApiModelProperty(value = "政策类型")
	private String policyType;
	/**
	 * 条件类型 1指定 2除外 3区间
	 */
	@ApiModelProperty(value = "条件类型 1指定 2除外 3区间")
	private String conditionType;
	/**
	 * 条件明细
	 */
	@ApiModelProperty(value = "条件明细")
	private String conditionDetail;
	/**
	 * 开始值
	 */
	@ApiModelProperty(value = "开始值")
	private String beginValue;
	/**
	 * 结束值
	 */
	@ApiModelProperty(value = "结束值")
	private String endValue;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;

}
