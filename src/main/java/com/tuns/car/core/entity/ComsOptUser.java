package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 出单平台_操作员信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@TableName("coms_opt_user")
public class ComsOptUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作员信息主键
     */
    private Integer id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 渠道角色编号
     */
    private Long roleId;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    /**
     * 状态(1-未启用,2-已启用,3-已停用)
     */
    private String states;


}
