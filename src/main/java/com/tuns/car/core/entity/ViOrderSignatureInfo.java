package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ${comments}
 * @Description车险订单签名信息表
 * <AUTHOR>
 * @date 2023-02-17
 */
@Data
@TableName("vi_order_signature_info")
@Accessors(chain = true)
public class ViOrderSignatureInfo extends BaseEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 订单UUID
	 */
	@ApiModelProperty(value = "订单UUID")
	private String orderUuid;

    /**
     * 是否已发送短信：Y-是,N-否
     */
    @ApiModelProperty(value = "是否已发送短信：Y-是,N-否")
    private String isMessage;

    /**
     * 是否已签名：Y-是,N-否
     */
    @ApiModelProperty(value = "是否已签名：Y-是,N-否")
    private String isSign;

    /**
     * 签名人手机号
     */
    @ApiModelProperty(value = "签名人手机号")
    private String signPhone;

}
