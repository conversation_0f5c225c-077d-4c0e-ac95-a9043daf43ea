package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 *
 */
@Data
@TableName("cmm_user_info")
public class CmmUserInfo implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 5359257232337640587L;

    /**
     * id
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 所属组织代码
     */
    private String departCode;

    /**
     * 所属组织名称
     */
    private String departName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 用户密码
     */
    private String userLoginPwd;

    /**
     * 身份证号码
     */
    private String idCardNo;

    /**
     * 手机号
     */
    private String cellphone;

    /**
     * 注册来源，1：app，2：pc后台
     */
    private String registFrom;

    /**
     * 注册时间
     */
    private LocalDateTime registTime;

    /**
     * 是否黑名单，0否，1是
     */
    private String blackFlag;

    /**
     * 实名认证状态，N未提交 P待审核 S审核成功 F审核拒绝
     */
    private String nameAuthStatus;

    /**
     * 系统账号开通标识（0未开通，1开通）
     */
    private String openWebFlag;

    /**
     * APP账号开通标识（0未开通，1开通）
     */
    private String openAppFlag;

    /**
     * 所在省份
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区县
     */
    private String county;

    /**
     * 性别,F女，M男
     */
    private String sex;

    /**
     * 出生日期
     */
    private LocalDateTime birthday;

    /**
     * 提现密码
     */
    private String cashPwd;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 微信号
     */
    private String weixin;

    /**
     * 账号等级
     */
    private String acctLevel;

    /**
     * 入职日期
     */
    private LocalDateTime hireDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime creTm;

    /**
     * 创建人
     */
    private Integer creUser;

    /**
     * 修改时间
     */
    private LocalDateTime mdfTm;

    /**
     * 修改人
     */
    private Integer mdfUser;

    /**
     * 是否业务员，0否，1是
     */
    private String salesmanFlag;

    /**
     * 是否内勤，0否，1是
     */
    private String staffFlag;

    /**
     * 是否启用，0否，1是
     */
    private String isEnable;

    /**
     * 职务等级（1合作伙伴、2团队长、3内勤、4、部门主管、5机构负责人、6总经理）
     */
    private String jobLevel;

    /**
     * 在职状态，1在职，2离职
     */
    private String hireStatus;

    /**
     * 寿险推荐人
     */
    private Integer lifeRecommander;

    /**
     * 产品推荐人
     */
    private Integer prodRecommander;

    /**
     * App推荐人
     */
    private Integer appRecommander;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 邀请码
     */
    private String investCode;

    /**
     * 是否接收短信，0否，1是
     */
    private String isReceiveSms;

    /**
     * 审核人
     */
    private Integer auditUser;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 推荐类型，1个人推荐， 2部门推荐，3总公司推荐
     */
    private String recomandType;

    /**
     * 员工编号
     */
    private String userNo;

    /**
     * 删除标志 0未删除, 1删除
     */
    private String delFlag;

    /**
     * 职业认证状态 0未认证,1待审核,2已认证3待修改
     */
    private String professionState;

    /**
     * 证件类型：身份证01
     */
    private String certificateType;

    /**
     * 佣金可见 0否,1是
     */
    private String seeFlag;

    /**
     * 头像
     *
     * @return
     */
    @TableField(exist = false)
    private String headImg;

    /**
     * 拓展人员姓名
     */
    @ApiModelProperty(value = "拓展人员姓名")
    private String expandUserName;
    /**
     * 拓展人员身份证
     */
    @ApiModelProperty(value = "拓展人员身份证")
    private String expandCard;
    /**
     * 拓展人员手机号
     */
    @ApiModelProperty(value = "拓展人员手机号")
    private String expandPhone;

    /**
     * 账户权限规则(A:仅能看到创建人为自己的数据;B:查看所有人创建的数据;)
     */
    @ApiModelProperty(value = "账户权限规则(A:仅能看到创建人为自己的数据;B:查看所有人创建的数据;)")
    private String acctAuthRule;
}
