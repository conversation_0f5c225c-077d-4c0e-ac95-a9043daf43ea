package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_policy_matching_stop")
public class ViPolicyMatchingStop implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@TableId
	private Integer id;
	/**
	 * 保险公司id
	 */
	@ApiModelProperty(value = "保险公司id")
	private String companyId;
	/**
	 * 渠道id
	 */
	@ApiModelProperty(value = "渠道id")
	private String channelId;
	/**
	 * 停止时间
	 */
	@ApiModelProperty(value = "停止时间")
	private Date stopTime;
	/**
	 * 启用时间
	 */
	@ApiModelProperty(value = "启用时间")
	private Date enableTime;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private Integer mdfUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 删除标识 1是 0否
	 */
	@ApiModelProperty(value = "删除标识 1是 0否")
	private String delFlag;

}
