package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 真实客户信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@TableName("cust_real_info")
public class CustRealInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 客户编号
     */
    private Integer custId;

    /**
     * 客户类型：1-个人；2-团体
     */
    private String custType;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 证件类型：type_code='Certificate_type'
     * 01-身份证
     * 02-户口本
     * 03-出生证
     * 04-出生日期（新生婴儿）
     * 05-护照
     * 06-军官证
     * 07-驾驶执照
     * 08-回乡证
     * 09-组织机构代码
     * 10-士兵证
     * 11-临时身份证
     * 12-警官证
     * 13-学生证
     * 14-军官离退休证
     * 15-港澳通行证
     * 16-台湾通行证
     * 17-旅行证
     * 18-外国人永久居留身份证
     * 19-统一社会信用代码
     * 99-其他
     */
    private String certType;

    /**
     * 证件号码
     */
    private String certCode;

    /**
     * 证件到期时间
     */
    private LocalDate expireDate;

    /**
     * 联系电话
     */
    private String cellphone;

    /**
     * 省份编码
     */
    private String province;

    /**
     * 市级编码
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 注册来源：1-APP；2-WEB
     */
    private String regFrom;

    /**
     * 备注
     */
    private String remark;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 性别：1-男；2-女
     */
    private String sex;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 联系人电话
     */
    private String telphone;

    /**
     * 家庭住址
     */
    private String homeAddr;

    /**
     * 邮箱
     */
    private String email;

    /**
     * QQ号
     */
    private String qq;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 爱好
     */
    private String hobby;

    /**
     * 联系人备注
     */
    private String note;

    /**
     * 注册时间
     */
    private LocalDateTime regTime;

    private Integer creUser;

    private LocalDateTime creTm;

    private Integer mdfUser;

    private LocalDateTime mdfTm;



    @Override
    public String toString() {
        return "CustRealInfo{" +
                "id=" + id +
                ", custId=" + custId +
                ", custType=" + custType +
                ", custName=" + custName +
                ", certType=" + certType +
                ", certCode=" + certCode +
                ", expireDate=" + expireDate +
                ", cellphone=" + cellphone +
                ", province=" + province +
                ", city=" + city +
                ", address=" + address +
                ", regFrom=" + regFrom +
                ", remark=" + remark +
                ", realName=" + realName +
                ", sex=" + sex +
                ", birthday=" + birthday +
                ", mobile=" + mobile +
                ", telphone=" + telphone +
                ", homeAddr=" + homeAddr +
                ", email=" + email +
                ", qq=" + qq +
                ", wechat=" + wechat +
                ", hobby=" + hobby +
                ", note=" + note +
                ", regTime=" + regTime +
                ", creUser=" + creUser +
                ", creTm=" + creTm +
                ", mdfUser=" + mdfUser +
                ", mdfTm=" + mdfTm +
                "}";
    }
}
