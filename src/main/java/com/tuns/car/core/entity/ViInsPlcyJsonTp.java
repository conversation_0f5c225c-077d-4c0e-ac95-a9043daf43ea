package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车险报价原始json信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_ins_plcy_json_tp")
public class ViInsPlcyJsonTp extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 流程流水号
     */
    @ApiModelProperty(value = "流程流水号")
    private Long serialNumber;
    /**
     * 保单批次ID
     */
    @ApiModelProperty(value = "保单批次ID")
    private Long policyBatchId;
    /**
     * 保司接口返回参数
     */
    @ApiModelProperty(value = "保司接口返回参数")
    private String extendJson;

}
