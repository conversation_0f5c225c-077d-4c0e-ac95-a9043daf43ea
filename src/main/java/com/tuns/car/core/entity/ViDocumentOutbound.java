package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 单证出库
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_document_outbound")
public class ViDocumentOutbound implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 单证出库id
	 */
	@ApiModelProperty(value = "单证出库id")
	@TableId
	private Integer docOutId;
	/**
	 * 批次号
	 */
	@ApiModelProperty(value = "批次号")
	private String batchNumber;
	/**
	 * 保险公司id
	 */
	@ApiModelProperty(value = "保险公司id")
	private String companyId;
	/**
	 * 保险公司名称
	 */
	@ApiModelProperty(value = "保险公司名称")
	private String companyName;
	/**
	 * 保险公司合作渠道id
	 */
	@ApiModelProperty(value = "保险公司合作渠道id")
	private String channelId;
	/**
	 * 保险公司合作渠道名称
	 */
	@ApiModelProperty(value = "保险公司合作渠道名称")
	private String channelName;
	/**
	 * 单证入库id
	 */
	@ApiModelProperty(value = "单证入库id")
	private Integer docPutId;
	/**
	 * 单证类型id    1交强(保单), 2交强(标志), 3商业(保单), 4商业(保卡), 5机动车(发票), 6交强险(批单) ,7商业(批单)
	 */
	@ApiModelProperty(value = "单证类型id    1交强(保单), 2交强(标志), 3商业(保单), 4商业(保卡), 5机动车(发票), 6交强险(批单) ,7商业(批单)")
	private Integer docType;
	/**
	 * 单证类型名称
	 */
	@ApiModelProperty(value = "单证类型名称")
	private String docName;
	/**
	 * 单证号码
	 */
	@ApiModelProperty(value = "单证号码")
	private String docNumber;
	/**
	 * 初始领用部门代码
	 */
	@ApiModelProperty(value = "初始领用部门代码")
	private String initDeptCode;
	/**
	 * 初始领用部门名称
	 */
	@ApiModelProperty(value = "初始领用部门名称")
	private String initDeptName;
	/**
	 * 领用部门代码
	 */
	@ApiModelProperty(value = "领用部门代码")
	private String receiveDeptCode;
	/**
	 * 领用部门名称
	 */
	@ApiModelProperty(value = "领用部门名称")
	private String receiveDeptName;
	/**
	 * 领用人id
	 */
	@ApiModelProperty(value = "领用人id")
	private Integer receiveUser;
	/**
	 * 领用人名称
	 */
	@ApiModelProperty(value = "领用人名称")
	private String receiveName;
	/**
	 * 出库起始段号
	 */
	@ApiModelProperty(value = "出库起始段号")
	private String docMark;
	/**
	 * 出库数量
	 */
	@ApiModelProperty(value = "出库数量")
	private Integer outNumber;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 填充备注
	 */
	@ApiModelProperty(value = "填充备注")
	private String fillRemark;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 出库人id
	 */
	@ApiModelProperty(value = "出库人id")
	private Integer creUser;
	/**
	 * 创建人名称
	 */
	@ApiModelProperty(value = "创建人名称")
	private String creName;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 修改人名称
	 */
	@ApiModelProperty(value = "修改人名称")
	private String mdfName;

}
