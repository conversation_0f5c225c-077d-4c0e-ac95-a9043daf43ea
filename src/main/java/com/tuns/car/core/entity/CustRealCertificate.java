package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 真实客户证件表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@TableName("cust_real_certificate")
public class CustRealCertificate implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 客户编号
     */
    private Integer custId;

    /**
     * 证件类型：type_code='Certificate_type'
     * 01-身份证
     * 02-户口本
     * 03-出生证
     * 04-出生日期（新生婴儿）
     * 05-护照
     * 06-军官证
     * 07-驾驶执照
     * 08-回乡证
     * 09-组织机构代码
     * 10-士兵证
     * 11-临时身份证
     * 12-警官证
     * 13-学生证
     * 14-军官离退休证
     * 15-港澳通行证
     * 16-台湾通行证
     * 17-旅行证
     * 18-外国人永久居留身份证
     * 19-统一社会信用代码
     * 99-其他
     */
    private String certType;

    /**
     * 证件号码
     */
    private String certCode;

    /**
     * 证件有效开始时间
     */
    private LocalDate validStartdate;

    /**
     * 证件有效结束时间
     */
    private LocalDate validEnddate;

    /**
     * 证件正面URL
     */
    private String frontUrl;

    /**
     * 证件反面URL
     */
    private String backUrl;

    private LocalDateTime creTm;

    private Integer creUser;

    private LocalDateTime mdfTm;

    private Integer mdfUser;



    @Override
    public String toString() {
        return "CustRealCertificate{" +
                "id=" + id +
                ", custId=" + custId +
                ", certType=" + certType +
                ", certCode=" + certCode +
                ", validStartdate=" + validStartdate +
                ", validEnddate=" + validEnddate +
                ", frontUrl=" + frontUrl +
                ", backUrl=" + backUrl +
                ", creTm=" + creTm +
                ", creUser=" + creUser +
                ", mdfTm=" + mdfTm +
                ", mdfUser=" + mdfUser +
                "}";
    }
}
