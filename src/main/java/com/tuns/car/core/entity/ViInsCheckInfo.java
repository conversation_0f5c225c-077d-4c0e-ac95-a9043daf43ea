package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险保单审核流水记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
@TableName("vi_ins_check_info")
public class ViInsCheckInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 自增序列
	 */
	@ApiModelProperty(value = "自增序列")
	@TableId
	private Integer id;
	/**
	 * 批次号
	 */
	@ApiModelProperty(value = "批次号")
	private Long policyBatchId;
	/**
	 * 改动内容
	 */
	@ApiModelProperty(value = "改动内容")
	private String changeContent;
	/**
	 * 政策内容
	 */
	@ApiModelProperty(value = "政策内容")
	private String policyContent;
	/**
	 * 审核时间
	 */
	@ApiModelProperty(value = "审核时间")
	private Date checkTime;
	/**
	 * 审核状态 0不通过, 1通过
	 */
	@ApiModelProperty(value = "审核状态 0不通过, 1通过")
	private String checkState;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 删除标志 0未删. 1删除
	 */
	@ApiModelProperty(value = "删除标志 0未删. 1删除")
	private String delFlag;

}
