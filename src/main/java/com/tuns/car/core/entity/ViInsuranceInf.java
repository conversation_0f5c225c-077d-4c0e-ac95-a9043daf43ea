package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.InsuranceMappingTypeEnum;
import com.tuns.core.boot.constant.YesNoNumberEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 保险公司险种基础表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_insurance_inf")
public class ViInsuranceInf extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 险种id
     */
    @ApiModelProperty(value = "险种id")
    private Long kindId;
    /**
     * 险种代码
     */
    @ApiModelProperty(value = "险种代码")
    private String kindCode;
    /**
     * 险种名称
     */
    @ApiModelProperty(value = "险种名称")
    private String kindName;
    /**
     * 保险公司编号
     */
    @ApiModelProperty(value = "保险公司编号")
    private String companyId;
    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称")
    private String companyName;
    /**
     * 主附险标志 1 主险 0 附加险
     */
    @ApiModelProperty(value = "主附险标志 1 主险 0 附加险")
    private String insFlag;
    /**
     * 是否商业险 1是 0否
     */
    @ApiModelProperty(value = "是否商业险 1是 0否")
    private String comFlag;
    /**
     * 是否不计免赔 1 是 0 否
     */
    @ApiModelProperty(value = "是否不计免赔 1 是 0 否")
    private String addlMark;
    /**
     * 附加险所在主险id
     */
    @ApiModelProperty(value = "附加险所在主险id")
    private Long mainKindId;
    /**
     * 车险险种id
     */
    @ApiModelProperty(value = "车险险种id")
    private Long insuranceId;
    /**
     * 车险险种险种代码
     */
    @ApiModelProperty(value = "车险险种险种代码")
    private String insuranceCode;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 附加险所在主险代码
     */
    @ApiModelProperty(value = "附加险所在主险代码")
    private String mainKindCode;
    /**
     * 映射类型
     */
    @ApiModelProperty(value = "映射类型")
    private InsuranceMappingTypeEnum mappingType;

    @ApiModelProperty(value = "保额模式单选 0固定保额 1主险比例保额 2以保司报价结果为准' ")
    private String insAmountType;

    @ApiModelProperty(value = "主险保额比例（百分比）")
    private BigDecimal mainInsAmountRatio;

    /**
     * 保额，JSON数组
     */
    @ApiModelProperty(value = "保额，JSON数组格式")
    private String insuredAmountArr;
    /**
     * 是否共享保额 0否 1是
     */
    @ApiModelProperty(value = "是否共享保额 0否 1是")
    private YesNoNumberEnum isSharedAmount;


}
