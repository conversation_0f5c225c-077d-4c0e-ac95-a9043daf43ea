package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.constant.YesNoNumberEnum;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_ins_kind_detial_tp")
public class ViInsKindDetialTp extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/**
	 * 保单记录ID
	 */
	@ApiModelProperty(value = "保单记录ID")
	private Long policyId;
	/**
	 * 保单批次ID
	 */
	@ApiModelProperty(value = "保单批次ID")
	private Long policyBatchId;
	/**
	 * 险别代码
	 */
	@ApiModelProperty(value = "险别代码")
	private String kindCode;
	/**
	 * 险别名称
	 */
	@ApiModelProperty(value = "险别名称")
	private String kindName;
	/**
	 * 数量
	 */
	@ApiModelProperty(value = "数量")
	private Integer quantity;
	/**
	 * 单位保额
	 */
	@ApiModelProperty(value = "单位保额")
	private BigDecimal unitAmount;
	/**
	 * 总保额
	 */
	@ApiModelProperty(value = "总保额")
	private BigDecimal amount;
	/**
	 * 保费
	 */
	@ApiModelProperty(value = "保费")
	private BigDecimal premium;
	/**
	 * 折扣
	 */
	@ApiModelProperty(value = "折扣")
	private BigDecimal discount;
	/**
	 * 费率
	 */
	@ApiModelProperty(value = "费率")
	private BigDecimal rate;
	/**
	 * 是否不计面免赔
	 */
	@ApiModelProperty(value = "是否不计面免赔")
	private String addlMark;
	/**
	 * 是否购买不计免赔险
	 */
	@ApiModelProperty(value = "是否购买不计免赔险")
	private String buyAddl;
	/**
	 * 附加类型 的值  比如 A挡中的'A' 20公里中的‘20’
	 */
	@ApiModelProperty(value = "附加类型")
	private String valueType;
	/**
	 * 是否商业险
	 */
	@ApiModelProperty(value = "是否商业险")
	private String businessMark;
	/**
	 * 扩展字段字段 附加类型 的单位  比如 A挡中的'挡' 20公里中的‘公里’
	 */
	@ApiModelProperty(value = "扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）")
	private String extend;
	/**
	 * 序号
	 */
	@ApiModelProperty(value = "序号")
	private Integer orderNo;
	/**
	 * 险种名称-别名
	 */
	@ApiModelProperty(value = "险种名称-别名")
	private String insuranceAlias;

	@ApiModelProperty(value = "是否共享保额")
	private YesNoNumberEnum isSharedAmount;
	/**
	 * 滞纳金
	 */
	@ApiModelProperty(value = "滞纳金")
	private BigDecimal lateFee;
	/**
	 * 往年补缴
	 */
	@ApiModelProperty(value = "往年补缴")
	private BigDecimal previousPay;
	/**
	 * 税款所属始期
	 */
	@ApiModelProperty(value = "税款所属始期")
	private String payStartDate;
	/**
	 * 税款所属止期
	 */
	@ApiModelProperty(value = "税款所属止期")
	private String payEndDate;

}
