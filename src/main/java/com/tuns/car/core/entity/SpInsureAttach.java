package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
@TableName("sp_insure_attach")
public class SpInsureAttach implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private Integer id;

    /**
     * 报价单号
     */
    private String quotationNo;

    /**
     * 附件类型
     */
    private String attachType;

    /**
     * 附件地址
     */
    private String attachUrl;

    /**
     * 创建时间
     */
    private LocalDateTime creTm;

    /**
     * 创建人
     */
    private Integer creUser;


}
