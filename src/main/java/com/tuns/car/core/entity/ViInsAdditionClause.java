package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险投保附加服务条款
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_ins_addition_clause")
public class ViInsAdditionClause implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@TableId
	private Integer id;
	/**
	 * 报价公司配置主键ID
	 */
	@ApiModelProperty(value = "报价公司配置主键ID")
	private Integer viInsCompanySupportId;
	/**
	 * 服务条款主键ID
	 */
	@ApiModelProperty(value = "服务条款主键ID")
	private String additionClauseId;
	/**
	 * 条款名称
	 */
	@ApiModelProperty(value = "条款名称")
	private String clauseName;
	/**
	 * 条款代码
	 */
	@ApiModelProperty(value = "条款代码")
	private String clauseCode;
	/**
	 * 是否支持选择
	 */
	@ApiModelProperty(value = "是否支持选择")
	private String isSupportChoice;
	/**
	 * 是否固定次数
	 */
	@ApiModelProperty(value = "是否固定次数")
	private String isFixedService;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date creTm;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer creUser;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date mdfTm;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer mdfUser;

}
