package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_ins_offer_operation")
public class ViInsOfferOperation implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据id
	 */
	@ApiModelProperty(value = "数据id")
	@TableId
	private Integer id;
	/**
	 * 保单批次id
	 */
	@ApiModelProperty(value = "保单批次id")
	private Long serialNumber;
	/**
	 * 操作前节点状态
	 */
	@ApiModelProperty(value = "操作前节点状态")
	private String stateBefore;
	/**
	 * 保险公司反馈意见
	 */
	@ApiModelProperty(value = "保险公司反馈意见")
	private String compOpinion;
	/**
	 * 操作员意见
	 */
	@ApiModelProperty(value = "操作员意见")
	private String operOpinion;
	/**
	 * 操作员id
	 */
	@ApiModelProperty(value = "操作员id")
	private Integer operUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private LocalDateTime creTm;
	/**
	 * 删除标志 0 未删 ,1删除
	 */
	@ApiModelProperty(value = "删除标志 0 未删 ,1删除")
	@TableLogic(value = "0",delval = "1")
	private String delFlag;

}
