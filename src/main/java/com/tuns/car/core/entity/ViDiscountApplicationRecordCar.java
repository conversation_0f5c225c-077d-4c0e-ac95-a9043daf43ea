package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 折扣审核车辆信息表
 *
 * <AUTHOR>
 * @since 2024-05-06 11:32
 **/
@Data
@TableName("vi_discount_application_record_car")
public class ViDiscountApplicationRecordCar extends BaseEntity {

    /**
     * 折扣系数ID
     */
    @ApiModelProperty(value = "折扣系数ID")
    private String discountFactorId;

    /**
     * 车牌号码
     */
    @ApiModelProperty(value = "车牌号码")
    private String plateNumber;

    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号")
    private String frameNumber;

    /**
     * 发动机号码
     */
    @ApiModelProperty(value = "发动机号码")
    private String engineNumber;

    /**
     * 注册日期 yyyy-MM-dd
     */
    @ApiModelProperty(value = "注册日期 yyyy-MM-dd")
    private String regDate;

    /**
     * 发证日期 yyyy-MM-dd
     */
    @ApiModelProperty(value = "发证日期 yyyy-MM-dd")
    private String certDate;

    /**
     * 品牌名称
     */
    @ApiModelProperty(value = "品牌名称")
    private String modelName;

    /**
     * 车型代码
     */
    @ApiModelProperty(value = "车型代码")
    private String modelCode;

    /**
     * 核定载客
     */
    @ApiModelProperty(value = "核定载客")
    private Integer seatCount;

    /**
     * 能源类型
     */
    @ApiModelProperty(value = "能源类型")
    private String fuelType;

    /**
     * 排量(ML)
     */
    @ApiModelProperty(value = "排量(ML)")
    private Integer exhaustScale;

    /**
     * 功率(瓦)
     */
    @ApiModelProperty(value = "功率(瓦)")
    private Integer power;

    /**
     * 整备质量
     */
    @ApiModelProperty(value = "整备质量")
    private Integer wholeWeight;

    /**
     * 车辆吨位/核定载质量(吨)
     */
    @ApiModelProperty(value = "车辆吨位/核定载质量(吨)")
    private BigDecimal carTonnage;

    /**
     * 营运性质 0-非营运 1-营运
     */
    @ApiModelProperty(value = "营运性质 0-非营运 1-营运")
    private Integer operationNature;

    /**
     * 使用性质
     */
    @ApiModelProperty(value = "使用性质")
    private String usingNature;

    /**
     * 过户标志
     */
    @ApiModelProperty(value = "过户标志")
    private Integer transferMark;

    /**
     * 过户日期 yyyy-MM-dd
     */
    @ApiModelProperty(value = "过户日期 yyyy-MM-dd")
    private String transferDate;

    /**
     * 车主姓名
     */
    @ApiModelProperty(value = "车主姓名")
    private String ownerName;

    /**
     * 投保人姓名
     */
    @ApiModelProperty(value = "投保人姓名")
    private String holderName;

    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名")
    private String insuredName;

    /**
     * 行驶证车辆类型
     */
    @ApiModelProperty(value = "行驶证车辆类型")
    private String licenseVehicleType;

    /**
     * 车辆种类
     */
    @ApiModelProperty(value = "车辆种类")
    private String carTypeCode;

    /**
     * 车损+三者分档
     */
    @ApiModelProperty(value = "车损+三者分档")
    private String thirdCarRank;

    /**
     * 交强新转续标识
     */
    @ApiModelProperty(value = "交强新转续标识")
    private String thisRenewalCi;

    /**
     * 商业新转续标识
     */
    @ApiModelProperty(value = "商业新转续标识")
    private String thisRenewalBi;

    /**
     * 公共新转续标识
     */
    @ApiModelProperty(value = "公共新转续标识")
    private String thisRenewal;

    /**
     * 投保类型
     */
    @ApiModelProperty(value = "投保类型")
    private String recordType;
}
