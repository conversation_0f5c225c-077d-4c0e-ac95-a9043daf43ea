package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.supalle.autotrim.AutoTrim;
import com.tuns.core.boot.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
@AutoTrim
@TableName("vi_app_config_special_field")
public class ViAppConfigSpecialField extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 保单批次ID
     */
    @ApiModelProperty(value = "保单批次ID")
    private Long policyBatchId;

    /**
     * 保险公司id
     */
    @ApiModelProperty(value = "保险公司id")
    private String companyId;

    /**
     * 保单批次ID
     */
    @ApiModelProperty(value = "字段code")
    private String typeCode;

    /**
     * 保单批次ID
     */
    @ApiModelProperty(value = "字段值")
    private String typeValue;

}
