package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车险投保回溯要求配置
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
@TableName("vi_ins_traceback_require")
public class ViInsTracebackRequire implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@TableId
	private Integer id;
	/**
	 * 报价公司配置主键ID
	 */
	@ApiModelProperty(value = "报价公司配置主键ID")
	private Integer viInsCompanySupportId;
	/**
	 * 回溯主键ID
	 */
	@ApiModelProperty(value = "回溯主键ID")
	private String tracebackRequireId;
	/**
	 * 是否需要阅读1是0否
	 */
	@ApiModelProperty(value = "是否需要阅读1是0否")
	private String whetherNeed;
	/**
	 * 投保类型
	 */
	@ApiModelProperty(value = "投保类型")
	private String insureType;
	/**
	 * 是否有阅读时长1是0否
	 */
	@ApiModelProperty(value = "是否有阅读时长1是0否")
	private String isReadTime;
	/**
	 * 阅读时长
	 */
	@ApiModelProperty(value = "阅读时长")
	private Integer timeLength;
	/**
	 * 时间单位
	 */
	@ApiModelProperty(value = "时间单位")
	private String unit;
	/**
	 * 是否强制滑动1是0否
	 */
	@ApiModelProperty(value = "是否强制滑动1是0否")
	private String isForcedToSlide;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date creTm;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer creUser;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Date mdfTm;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer mdfUser;

}
