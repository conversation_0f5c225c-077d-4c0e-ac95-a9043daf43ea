package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 单证入库
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 18:56:26
 */
@Data
@TableName("vi_document_storage")
public class ViDocumentStorage implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 单证入库id
	 */
	@ApiModelProperty(value = "单证入库id")
	@TableId
	private Integer docPutId;
	/**
	 * 保险公司id
	 */
	@ApiModelProperty(value = "保险公司id")
	private String companyId;
	/**
	 * 保险公司名称
	 */
	@ApiModelProperty(value = "保险公司名称")
	private String companyName;
	/**
	 * 保险公司合作渠道id
	 */
	@ApiModelProperty(value = "保险公司合作渠道id")
	private String channelId;
	/**
	 * 保险公司合作渠道名称
	 */
	@ApiModelProperty(value = "保险公司合作渠道名称")
	private String channelName;
	/**
	 * 单证类型ID 1交强(保单), 2交强(标志), 3商业(保单), 4商业(保卡), 5机动车(发票), 6交强险(批单) ,7商业(批单)
	 */
	@ApiModelProperty(value = "单证类型ID 1交强(保单), 2交强(标志), 3商业(保单), 4商业(保卡), 5机动车(发票), 6交强险(批单) ,7商业(批单)")
	private Integer docType;
	/**
	 * 批次号
	 */
	@ApiModelProperty(value = "批次号")
	private String batchNumber;
	/**
	 * 单证类型名称
	 */
	@ApiModelProperty(value = "单证类型名称")
	private String docName;
	/**
	 * 单证领用状态 A未领用 ,B已领用
	 */
	@ApiModelProperty(value = "单证领用状态 A未领用 ,B已领用")
	private String receiveState;
	/**
	 * 单证号段
	 */
	@ApiModelProperty(value = "单证号段")
	private String docMark;
	/**
	 * 单证号码
	 */
	@ApiModelProperty(value = "单证号码")
	private String docNumber;
	/**
	 * 使用部门代码
	 */
	@ApiModelProperty(value = "使用部门代码")
	private String useDeptCode;
	/**
	 * 使用部门名称
	 */
	@ApiModelProperty(value = "使用部门名称")
	private String useDeptName;
	/**
	 * 单证领取使用时间
	 */
	@ApiModelProperty(value = "单证领取使用时间")
	private Date useDate;
	/**
	 * 单正使用状态  A未使用 ,B已使用 ,C作废
	 */
	@ApiModelProperty(value = "单正使用状态  A未使用 ,B已使用 ,C作废")
	private String useState;
	/**
	 * 入库部门代码
	 */
	@ApiModelProperty(value = "入库部门代码")
	private String putDeptCode;
	/**
	 * 入库部门名称
	 */
	@ApiModelProperty(value = "入库部门名称")
	private String putDeptName;
	/**
	 * 结束(失效)时间
	 */
	@ApiModelProperty(value = "结束(失效)时间")
	private Date loseDate;
	/**
	 * 平台收单审核人id
	 */
	@ApiModelProperty(value = "平台收单审核人id")
	private Integer terCheckId;
	/**
	 * 平台收单审核人名称
	 */
	@ApiModelProperty(value = "平台收单审核人名称")
	private String terCheckName;
	/**
	 * 平台收单审核状态  A未审核 , B已审核
	 */
	@ApiModelProperty(value = "平台收单审核状态  A未审核 , B已审核")
	private String terCheck;
	/**
	 * 平台收单审核日期
	 */
	@ApiModelProperty(value = "平台收单审核日期")
	private Date terCheckDate;
	/**
	 * 平台收单状态 A未收单,B已收单
	 */
	@ApiModelProperty(value = "平台收单状态 A未收单,B已收单")
	private String terState;
	/**
	 * 平台收单人id
	 */
	@ApiModelProperty(value = "平台收单人id")
	private Integer terCollectId;
	/**
	 * 平台收单人名称
	 */
	@ApiModelProperty(value = "平台收单人名称")
	private String terCollectName;
	/**
	 * 平台收单日期
	 */
	@ApiModelProperty(value = "平台收单日期")
	private Date terCollectDate;
	/**
	 * 平台审核取消人id
	 */
	@ApiModelProperty(value = "平台审核取消人id")
	private Integer terCancelId;
	/**
	 * 平台审核取消人名称
	 */
	@ApiModelProperty(value = "平台审核取消人名称")
	private String terCancelName;
	/**
	 * 平台审核取消日期
	 */
	@ApiModelProperty(value = "平台审核取消日期")
	private Date terCancelDate;
	/**
	 * 保险公司收单人名称
	 */
	@ApiModelProperty(value = "保险公司收单人名称")
	private String compyCollectName;
	/**
	 * 保险公司收单日期
	 */
	@ApiModelProperty(value = "保险公司收单日期")
	private Date compyCollectDate;
	/**
	 * 保险公司收单状态 A未收单 B已收单
	 */
	@ApiModelProperty(value = "保险公司收单状态 A未收单 B已收单")
	private String compyCollectState;
	/**
	 * 保单号
	 */
	@ApiModelProperty(value = "保单号")
	private String policyNumber;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 创建人id
	 */
	@ApiModelProperty(value = "创建人id")
	private Integer creUser;
	/**
	 * 创建人名称
	 */
	@ApiModelProperty(value = "创建人名称")
	private String creName;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Date mdfTm;
	/**
	 * 修改人名称
	 */
	@ApiModelProperty(value = "修改人名称")
	private String mdfName;

}
