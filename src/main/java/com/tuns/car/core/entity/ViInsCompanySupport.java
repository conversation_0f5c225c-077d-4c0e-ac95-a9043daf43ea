package com.tuns.car.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:58
 */
@Data
@TableName("vi_ins_company_support")
public class ViInsCompanySupport implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * 保险公司ID
	 */
	@ApiModelProperty(value = "保险公司ID")
	private String companyId;
	/**
	 * 报价类型 ["1"]
	 */
	@ApiModelProperty(value = "报价类型 [1]")
	private String recordType;
	/**
	 * 投保地市级 ["430100"]
	 */
	@ApiModelProperty(value = "投保地市级 [430100]")
	private String cityNumber;
	/**
	 * 是否自动报价
	 */
	@ApiModelProperty(value = "是否自动报价")
	private String autoMark;
	/**
	 * 页面报价
	 */
	@ApiModelProperty(value = "页面报价")
	private String pageAutoMark;
	/**
	 * 核定载客 ["1"]
	 */
	@ApiModelProperty(value = "核定载客 [1]")
	private String seatCount;
	/**
	 * 是否上牌 ["1"]
	 */
	@ApiModelProperty(value = "是否上牌 [1]")
	private String plateNumberMark;
	/**
	 * 能源类型 ["1"]
	 */
	@ApiModelProperty(value = "能源类型 [1]")
	private String fuelType;
	/**
	 * 贷款车辆标志 ["1"]
	 */
	@ApiModelProperty(value = "贷款车辆标志 [1]")
	private String loanCarMark;
	/**
	 * 所属性质 ["1"]
	 */
	@ApiModelProperty(value = "所属性质 [1]")
	private String ownershipNature;
	/**
	 * 营运性质 ["1"]
	 */
	@ApiModelProperty(value = "营运性质 [1]")
	private String operationNature;
	/**
	 * 使用性质 ["1"]
	 */
	@ApiModelProperty(value = "使用性质 [1]")
	private String usingNature;
	/**
	 * 车牌号码 ["湘A"]
	 */
	@ApiModelProperty(value = "车牌号码 [湘A]")
	private String plateNumber;
	/**
	 * 险种代码 ["1"]
	 */
	@ApiModelProperty(value = "险种代码 [1]")
	private String kindCode;
	/**
	 * 可用车型公司 ["1000"]
	 */
	@ApiModelProperty(value = "可用车型公司 [1000]")
	private String supportVehicle;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date creTm;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Integer creUser;
	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date mdfTm;
	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	private Integer mdfUser;
	/**
	 * 删除标志 0-已删除 1-存储
	 */
	@ApiModelProperty(value = "删除标志 0-已删除 1-存储")
	private String delFlag;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private Integer sort;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String tag;
	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	private String estimateMatchingFlag;
	/**
	 * 保险公司简介
	 */
	@ApiModelProperty(value = "保险公司简介")
	private String remake;

}
