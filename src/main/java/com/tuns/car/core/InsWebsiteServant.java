package com.tuns.car.core;

/**
 * 保险公司网站服务者
 * 针对保险公司某个网站
 *
 * <AUTHOR>
 * @date 2022/11/02
 **/
public interface InsWebsiteServant extends InsServant {

    /**
     * 服务的的网站url
     *
     * @return
     */
    String serviceSiteUrl();

    /**
     * 可以提供服务
     *
     * @param siteUrl 网站url
     * @return
     */
    default Boolean canService(String siteUrl) {
        return serviceSiteUrl().contains(siteUrl) || siteUrl.contains(serviceSiteUrl());
    }
}
