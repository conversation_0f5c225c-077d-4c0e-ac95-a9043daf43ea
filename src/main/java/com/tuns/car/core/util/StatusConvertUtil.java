package com.tuns.car.core.util;

import com.tuns.car.core.constant.InsuredStatusEnum;
import com.tuns.car.core.constant.InsuredSubStatusEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class StatusConvertUtil {
    //
    public static String insuredSubStatusConvert(String insuredSubStatus) {
        Map<String, String> map = new HashMap<>();
        map.put(InsuredSubStatusEnum.A1.getValue(), InsuredStatusEnum.WAIT.getValue());
        map.put(InsuredSubStatusEnum.A7.getValue(), InsuredStatusEnum.PREMIUMCACULATE_CANCEL.getValue());
        map.put(InsuredSubStatusEnum.B1.getValue(), InsuredStatusEnum.UNDERWRITING_WAITING.getValue());
        map.put(InsuredSubStatusEnum.B2.getValue(), InsuredStatusEnum.UNDERWRITING_THROUGH.getValue());
        map.put(InsuredSubStatusEnum.B4.getValue(), InsuredStatusEnum.UNDERWRITING_FAILURE.getValue());
        map.put(InsuredSubStatusEnum.B5.getValue(), InsuredStatusEnum.UNDERWRITING_FAILURE.getValue());
        map.put(InsuredSubStatusEnum.C1.getValue(), InsuredStatusEnum.WAITING_PAY.getValue());
        map.put(InsuredSubStatusEnum.C19.getValue(), InsuredStatusEnum.PAID.getValue());
        map.put(InsuredSubStatusEnum.C2.getValue(), InsuredStatusEnum.PAID.getValue());
        map.put(InsuredSubStatusEnum.C3.getValue(), InsuredStatusEnum.PAY_ERROR.getValue());
        map.put(InsuredSubStatusEnum.B6.getValue(), InsuredStatusEnum.UNDERWRITING_CANCEL.getValue());
        map.put(InsuredSubStatusEnum.C4.getValue(), InsuredStatusEnum.PAID_CANCEL.getValue());
        map.put(InsuredSubStatusEnum.C4_1.getValue(), InsuredStatusEnum.PAID_CANCEL.getValue());
        map.put(InsuredSubStatusEnum.C4_2.getValue(), InsuredStatusEnum.PAID_CANCEL.getValue());
        map.put(InsuredSubStatusEnum.C4_3.getValue(), InsuredStatusEnum.PAID_CANCEL.getValue());
        map.put(InsuredSubStatusEnum.C4_4.getValue(), InsuredStatusEnum.PAID_CANCEL.getValue());
        map.put(InsuredSubStatusEnum.C4_5.getValue(), InsuredStatusEnum.PAID_CANCEL.getValue());
        return map.get(insuredSubStatus);
    }

    /**
     * @throws @author: wang wei
     * @Title: statusConvert
     * @Description: 保单状态 转换
     * @param: @param  status
     * @param: @return
     * @return: String
     * @date: 2021年2月1日
     */
    public static String statusConvert(String status) {
        Map<String, String> statusMap = new HashMap<>();
        statusMap.put("2", InsuredSubStatusEnum.B1.getValue());
        statusMap.put("3", InsuredSubStatusEnum.B5.getValue());
        statusMap.put("1", InsuredSubStatusEnum.B5.getValue());
        statusMap.put("4", InsuredSubStatusEnum.A1.getValue());
        statusMap.put("5", InsuredSubStatusEnum.B2.getValue());
        statusMap.put("6", InsuredSubStatusEnum.B5.getValue());
        statusMap.put("7", InsuredSubStatusEnum.C2.getValue());
        statusMap.put("8", InsuredSubStatusEnum.B5.getValue());
        statusMap.put("9", InsuredSubStatusEnum.B5.getValue());
        return statusMap.get(status);
    }

    /**
     * @throws @author: wang wei
     * @Title: PayStatusConvert
     * @Description: 支付状态转换
     * @param: @param  payStatus
     * @param: @return
     * @return: String
     * @date: 2021年2月1日
     */
    public static String payStatusConvert(String payStatus) {
        Map<String, String> payStatusMap = new HashMap<>();
        payStatusMap.put("1", InsuredSubStatusEnum.C1.getValue());
        payStatusMap.put("2", InsuredSubStatusEnum.C1.getValue());
        payStatusMap.put("3", InsuredSubStatusEnum.C2.getValue());
        payStatusMap.put("4", InsuredSubStatusEnum.C3.getValue());

        String subStatus = payStatusMap.get(payStatus);
        if (StringUtils.isEmpty(subStatus)) {
            return payStatus;
        }
        return subStatus;
    }
}
