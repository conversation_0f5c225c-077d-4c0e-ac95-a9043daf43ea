package com.tuns.car.core.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @ClassName: DateUtil
 * @date 2019/7/16 14:17
 */
public class DateUtil extends cn.hutool.core.date.DateUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    /**
     * @desctiption 格式化时间 可将上面的字符串统一格式化
     * <AUTHOR>
     * @date 2019/7/16 14:19
     */
    public static String dateFormat(String endTime) {
        return dateFormat(endTime, "yyyy-MM-dd HH:mm");
    }

    public static String dateFormat(String endTime, String rule) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(rule, Locale.ENGLISH);
            endTime = endTime.replaceAll("([^0-9] )|([^0-9])", " ");
            endTime = endTime.replaceAll("\\s{1,}", " ");
            String[] numbers = endTime.split(" ");
            if (numbers.length == 1) {
                String time = numbers[0];
                SimpleDateFormat format;

                if (time.length() == 8)
                    format = new SimpleDateFormat("yyyyMMdd", Locale.ENGLISH);
                else if (time.length() == 14)
                    format = new SimpleDateFormat("yyyyMMddHHmmss", Locale.ENGLISH);
                else if (time.length() > 8) {
                    time = time.substring(0, 8);
                    format = new SimpleDateFormat("yyyyMMdd", Locale.ENGLISH);
                } else {
                    format = null;
                    return sdf.format(new Date(0));
                }
                Date date = null;
                try {
                    date = format.parse(time);
                } catch (ParseException e) {
                    e.printStackTrace();
                }

                return sdf.format(date);
            }
            Vector<Integer> nums = new Vector<Integer>();
            for (int i = 0; i < 6; i++)
                nums.add(0);

            if (numbers.length > 3) {
                if (Integer.parseInt(numbers[2]) > 31) {
                    String tmp = numbers[2].substring(0, 2);
                    if (Integer.parseInt(tmp) > 31)
                        tmp = numbers[2].substring(0, 1);
                    numbers[3] = numbers[2].substring(tmp.length());
                    numbers[2] = tmp;
                }
            }
            for (int i = 0; i < numbers.length; i++) {
                nums.setElementAt(Integer.parseInt(numbers[i]), i);
            }

            int year = nums.get(0);
            int month = nums.get(1);
            int day = nums.get(2);

            int hour = nums.get(3);
            if (hour > 24)
                hour = 0;
            int min = nums.get(4);
            if (min > 60)
                min = 0;
            int sec = nums.get(5);
            if (sec > 60)
                sec = 0;

            Calendar calendar = Calendar.getInstance();
            calendar.set(year, month - 1, day, hour, min, sec);
            return sdf.format(calendar.getTime());
        } catch (Exception e) {
        }
        return endTime;
    }

    /*
     * @Description: 获取两个时间的月份数
     *
     * @Author: 陈急舟
     *
     * @Date: 2021/01/30 09:10:58
     */
    public static int getMonth(String startDate, String endDate) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(sdf.parse(startDate));
        c2.setTime(sdf.parse(endDate));
        int year = c2.get(Calendar.YEAR) - c1.get(Calendar.YEAR);
        int month = c2.get(Calendar.MONTH) + year * 12 - c1.get(Calendar.MONTH);
        if (c1.getTime().getDate() > c2.getTime().getDate()) {
            month = month - 1;
        }
        return month;
    }

    /*
     * @Description: 获取第二天的0时
     *
     * @Author: 陈急舟
     *
     * @Date: 2021/01/26 15:18:56
     */
    public static LocalDateTime getNextDayFirstTime() {
        return LocalDateTime.of(LocalDateTime.now().plusDays(1).toLocalDate(), LocalTime.MIN);
    }

    /*
     * @Description: 获取第二天的0时
     *
     * @Author: 陈急舟
     *
     * @Date: 2021/01/26 15:18:56
     */
    public static LocalDateTime getNextYearFirstTime() {
        return LocalDateTime.of(LocalDateTime.now().plusDays(1).plusYears(1).toLocalDate(), LocalTime.MIN);
    }

    /*
     * @Description: 获取半个月前时间--由于每个月总天数不一样 因此获取14天以前的时间就行了
     *
     * @Author: 陈急舟
     *
     * @Date: 2021/01/28 11:35:19
     */
    public static LocalDate getHalfMonthDate() {
        return LocalDate.now().plusDays(-14);
    }

    /*
     * 加减对应时间后的日期
     *
     * @param date 需要加减时间的日期
     *
     * @param amount 加减的时间(毫秒)
     *
     * @return 加减对应时间后的日期
     */
    public static Date subtractTime(Date date, int amount) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String strTime = sdf.format(date.getTime() + amount);
            Date time = sdf.parse(strTime);
            return time;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Long dateStrToLong(String startTime) {

        DateFormat fmt = new SimpleDateFormat("EEE, d MMM yyyy HH:mm:ss 'GMT'", Locale.US);

        try {
            Date date = fmt.parse(startTime);
            return date.getTime();
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return 0l;
    }

    /**
     * 使用年限
     *
     * @param date
     * @return
     * @throws ParseException
     */
    public static Integer getUseYears(String date) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-ddd");
            Date parse = format.parse(date);
            int useYears = (int) betweenYear(parse, new Date(), false);
            return useYears;
        } catch (Exception e) {
            LOGGER.error("获取使用年限异常，传入参数：" + date + ",异常信息：", e);
            return null;
        }
    }

    /**
     * 时间格式化
     *
     * @param date
     * @return
     * @throws ParseException
     */
    public static String strToDateFormat(String date) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            formatter.setLenient(false);
            Date newDate = formatter.parse(date);
            formatter = new SimpleDateFormat("yyyy-MM-dd");
            return formatter.format(newDate);
        } catch (Exception e) {
            LOGGER.error("转换日期异常，传入参数" + date + "异常信息：", e);
            return "";
        }
    }

    /*
     * @Description: 获取多少天之前的时间，时间格式：yyyy-MM-dd
     *
     *
     */
    public static LocalDate getsubtractDay(int day) {
        return LocalDate.now().plusDays(-day);
    }

    /**
     * date转LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        try {
            Instant instant = date.toInstant();
            LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
            return localDateTime;
        } catch (Exception e) {
            LOGGER.error("date转LocalDateTime失败：", e);
        }
        return null;
    }

    /**
     * date转LocalDate
     *
     * @param date
     * @return
     */
    public static LocalDate dateToLocalDate(Date date) {
        LocalDateTime localDateTime = dateToLocalDateTime(date);
        return localDateTime.toLocalDate();
    }

    /**
     * LocalDateTime 转 Date
     *
     * @param date
     * @return
     */
    public static Date localDateTimeToDate(LocalDateTime date) {
        if (Objects.isNull(date)) {
            return null;
        }
        Instant instant = date.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * date转LocalDate
     *
     * @param date
     * @return
     */
    public static Date localDateToDate(LocalDate date) {
        Instant instant = date.atStartOfDay(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取调整后得日期
     *
     * @param date  原始日期
     * @param value 加减值
     * @param unit  单位
     * @return
     */
    public static Date getDateAfterAdjust(Date date, Integer value, Integer unit) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.add(unit, value);
        Date time = instance.getTime();
        return time;
    }

    /**
     * 判断字符串是否符合目标格式
     *
     * @param dateStr 日期字符串
     * @param pattern 日期格式
     * @return
     */
    public static Boolean match(String dateStr, String pattern) {
        if (StrUtil.isBlank(dateStr) || StrUtil.isBlank(pattern)) {
            return Boolean.FALSE;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        try {
            simpleDateFormat.parse(dateStr);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }


    /**
     * @param date  源日期
     * @param fmt   格式
     * @param field 字段(Y|M|D|H|MI|S)
     * @param value 正数为加, 负数为减
     * @return String
     * @Title: dateTimeCalc
     * @Description: 计算日期
     */
    public static String dateTimeCalc(String date, String fmt, String field, Integer value) {
        try {
            if (!date.contains("24:00:00")) {
                return date;
            }
            SimpleDateFormat sdf = null;
            if (StringUtils.isEmpty(fmt)) {
                sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            } else {
                sdf = new SimpleDateFormat(fmt);
            }
            Calendar cd = Calendar.getInstance();
            cd.setTime(sdf.parse(date));
            if ("Y".equals(field)) {
                cd.add(Calendar.YEAR, value);
            } else if ("M".equals(field)) {
                cd.add(Calendar.MONTH, value);
            } else if ("D".equals(field)) {
                cd.add(Calendar.DATE, value);
            } else if ("H".equals(field)) {
                cd.add(Calendar.HOUR, value);
            } else if ("MI".equals(field)) {
                cd.add(Calendar.MINUTE, value);
            } else if ("S".equals(field)) {
                cd.add(Calendar.SECOND, value);
            } else {
                throw new IllegalArgumentException("参数错误(支持Y,M,D,H,MI,S)");
            }
            return sdf.format(cd.getTime());
        } catch (ParseException ex) {
            return null;
        }
    }

    /**
     * 获取天数间隔
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    public static int betweenDas(LocalDate beginDate, LocalDate endDate) {
        Date end = localDateToDate(endDate);
        Date begin = localDateToDate(beginDate);
        long between = between(begin, end, DateUnit.DAY, true);
        return Long.valueOf(between).intValue();
    }

    /**
     * 获取当前时间戳
     *
     * @return
     */
    public static String getDateTime17() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return sdf.format(new Date());
    }

    /**
     * 时间增加
     *
     * @param date   时间
     * @param amount 数量
     * @return 增加后的时间
     * @throws ParseException
     */
    public static String calculateTime(Date date, int amount) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.YEAR, amount);
        Date time = c.getTime();
        String format = sdf.format(time);
        return format;

    }

    public static Date addDay(Date date, int day) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, day);
        date = calendar.getTime();   //这个时间就是日期往后推一天的结果
        return date;
    }

    /**
     * 添加天数
     *
     * @param date 字符串时间
     * @param days 天数
     * @return 加完之后的时间
     */
    public static String addDay(String date, int days) {
        String format = null;
        try {
            DateTime dateTime = parse(date);
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(dateTime);
            calendar.add(Calendar.DATE, days);
            Date time = calendar.getTime();
            format = DateFormatUtils.format(time, "yyyy-MM-dd HH:mm:ss");
        } catch (Exception e) {
            LOGGER.error("时间转换添加天数失败：", e);
        }
        return format;
    }

    /**
     * 添加天数
     *
     * @param date  字符串时间
     * @param years 天数
     * @return 加完之后的时间
     */
    public static String addYear(String date, int years) {
        String format = null;
        try {
            DateTime dateTime = parse(date);
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(dateTime);
            calendar.add(Calendar.YEAR, years);
            Date time = calendar.getTime();
            format = DateFormatUtils.format(time, "yyyy-MM-dd HH:mm:ss");
        } catch (Exception e) {
            LOGGER.error("时间转换添加天数失败：", e);
        }
        return format;
    }

    /**
     * 获取当天剩余时间（秒）
     *
     * @return 秒数
     */
    public static Long getTimeLeft() {
        Date currentDate = new Date();
        LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
        long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
        return seconds;
    }

    /**
     * 获取保险续保时间
     * 今年度保险起保日期-签单日期
     *
     * @param insBegin   起保时间
     * @param insureTime 签单时间
     * @return
     */
    public static Integer getDaysInAdvanceOfRenewal(String insBegin, String insureTime) {
        try {
            LOGGER.info("起保时间：{}，签单时间：{}", insBegin, insureTime);
            Integer betweenDay = (int) cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parseDate(insBegin), cn.hutool.core.date.DateUtil.parseDate(insureTime), true);
//            long start = parse(insBegin).getTime();
//            long end1 = parse(insureTime).getTime();
//            long sub = Math.subtractExact(start, end1);
//            long day = Math.floorDiv(sub, (24 * 3600 * 1000));
            LOGGER.info("保险续保时间:{}天", betweenDay);
            return Math.toIntExact(Math.max(betweenDay, 0));
        } catch (Exception e) {
            LOGGER.warn("获取保险续保时间失败：", e);
        }
        return null;
    }

    /**
     * 根据特定格式格式化日期
     *
     * @param localDateTime 被格式化的日期
     * @return 格式化后的字符串
     */
    public static String formatDate(LocalDate localDateTime) {
        if (null == localDateTime) {
            return null;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN);
        return localDateTime.format(df);
    }

    public static LocalDate parseLocalDate(String dateStr) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN);
        return LocalDate.parse(dateStr, df);
    }


    /**
     * 终保时间减去一秒 2023-06-07 00:00:00 -> 2023-06-06 23:59:59
     * @param endTime yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String subtractOneSecond(String endTime){
        return DateTime.of(endTime, DatePattern.NORM_DATETIME_PATTERN).offset(DateField.SECOND, -1).toString();
    }

    /**
     * 终保时间添加一秒 2023-06-06 23:59:59 -> 2023-06-07 00:00:00
     * @param endTime yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String addOneSecond(String endTime){
        return DateTime.of(endTime, DatePattern.NORM_DATETIME_PATTERN).offset(DateField.SECOND, 1).toString();
    }
}