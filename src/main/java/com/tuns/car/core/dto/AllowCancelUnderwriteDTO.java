package com.tuns.car.core.dto;

import com.tuns.car.core.constant.InsuredStatusEnum;
import com.tuns.car.core.constant.InsuredSubStatusEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023-10-30 10:19
 */
@Data
@NoArgsConstructor
public class AllowCancelUnderwriteDTO {

    private String companyId;

    private InsuredStatusEnum insuredStatusEnum;

    private InsuredSubStatusEnum insuredSubStatusEnum;

    private String proposalNumber;

    public AllowCancelUnderwriteDTO(String companyId, InsuredStatusEnum insuredStatusEnum) {
        this.companyId = companyId;
        this.insuredStatusEnum = insuredStatusEnum;
    }


    public AllowCancelUnderwriteDTO(String companyId, InsuredSubStatusEnum insuredSubStatusEnum, String proposalNumber) {
        this.companyId = companyId;
        this.insuredSubStatusEnum = insuredSubStatusEnum;
        this.proposalNumber = proposalNumber;
    }
}
