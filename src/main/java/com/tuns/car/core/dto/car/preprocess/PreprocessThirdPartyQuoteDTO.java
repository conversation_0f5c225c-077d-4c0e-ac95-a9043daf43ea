package com.tuns.car.core.dto.car.preprocess;

import com.tuns.car.core.dto.car.IssueOrderChanInfoDTO;
import com.tuns.car.core.dto.carprocess.premium.SpecialInfoDTO;
import com.tuns.car.core.dto.carprocess.premium.request.CarInfo;
import lombok.Data;

/**
 * 第三方报价前置处理所需参数，参数来源于保费试算封装的DTO
 *
 * <AUTHOR>
 * @since 2024-12-16 14:05
 */
@Data
public class PreprocessThirdPartyQuoteDTO extends IssueOrderChanInfoDTO {

    /**
     * 非标页面
     */
    private SpecialInfoDTO specialInfo;

    /**
     * 车辆信息
     */
    private CarInfo carInfo;
}
