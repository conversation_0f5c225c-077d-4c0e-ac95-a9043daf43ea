package com.tuns.car.core.dto.gi;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 个险 非车计划信息
 * <AUTHOR>
 * @since 2021/12/29
 */
public class GiPlanInfoVO {


    @JsonProperty("id")
    private String id;
    @JsonProperty("companyId")
    private String companyId;
    @JsonProperty("companyName")
    private String companyName;
    @JsonProperty("prodId")
    private Integer prodId;
    @JsonProperty("prodName")
    private String prodName;
    @JsonProperty("projectId")
    private Integer projectId;
    @JsonProperty("projectName")
    private String projectName;
    @JsonProperty("projectCode")
    private String projectCode;
    @JsonProperty("projLiabList")
    private List<ProjLiabListDTO> projLiabList;
    @JsonProperty("viewSetList")
    private List<ViewSetListDTO> viewSetList;

    public static class ProjLiabListDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("prodId")
        private Integer prodId;
        @JsonProperty("projectId")
        private Integer projectId;
        @JsonProperty("planId")
        private Integer planId;
        @JsonProperty("liabId")
        private Integer liabId;
        @JsonProperty("liabBusiCode")
        private Object liabBusiCode;
        @JsonProperty("liabSysCode")
        private String liabSysCode;
        @JsonProperty("sumIns")
        private Object sumIns;
        @JsonProperty("sumInsArr")
        private String sumInsArr;
        @JsonProperty("premMin")
        private Object premMin;
        @JsonProperty("premMax")
        private Object premMax;
        @JsonProperty("viewOrder")
        private String viewOrder;
        @JsonProperty("viewName")
        private String viewName;
        @JsonProperty("protectionDetail")
        private String protectionDetail;
        @JsonProperty("description")
        private String description;
        @JsonProperty("planName")
        private String planName;
        @JsonProperty("liabName")
        private String liabName;
        @JsonProperty("mrFlag")
        private String mrFlag;
        @JsonProperty("planCode")
        private String planCode;
        @JsonProperty("planShortName")
        private String planShortName;
        @JsonProperty("planAttachList")
        private List<PlanAttachListDTO> planAttachList;

        public static class PlanAttachListDTO {
            @JsonProperty("attachUrl")
            private String attachUrl;
            @JsonProperty("attachName")
            private String attachName;

            public String getAttachUrl() {
                return attachUrl;
            }

            public void setAttachUrl(String attachUrl) {
                this.attachUrl = attachUrl;
            }

            public String getAttachName() {
                return attachName;
            }

            public void setAttachName(String attachName) {
                this.attachName = attachName;
            }
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Integer getProdId() {
            return prodId;
        }

        public void setProdId(Integer prodId) {
            this.prodId = prodId;
        }

        public Integer getProjectId() {
            return projectId;
        }

        public void setProjectId(Integer projectId) {
            this.projectId = projectId;
        }

        public Integer getPlanId() {
            return planId;
        }

        public void setPlanId(Integer planId) {
            this.planId = planId;
        }

        public Integer getLiabId() {
            return liabId;
        }

        public void setLiabId(Integer liabId) {
            this.liabId = liabId;
        }

        public Object getLiabBusiCode() {
            return liabBusiCode;
        }

        public void setLiabBusiCode(Object liabBusiCode) {
            this.liabBusiCode = liabBusiCode;
        }

        public String getLiabSysCode() {
            return liabSysCode;
        }

        public void setLiabSysCode(String liabSysCode) {
            this.liabSysCode = liabSysCode;
        }

        public Object getSumIns() {
            return sumIns;
        }

        public void setSumIns(Object sumIns) {
            this.sumIns = sumIns;
        }

        public String getSumInsArr() {
            return sumInsArr;
        }

        public void setSumInsArr(String sumInsArr) {
            this.sumInsArr = sumInsArr;
        }

        public Object getPremMin() {
            return premMin;
        }

        public void setPremMin(Object premMin) {
            this.premMin = premMin;
        }

        public Object getPremMax() {
            return premMax;
        }

        public void setPremMax(Object premMax) {
            this.premMax = premMax;
        }

        public String getViewOrder() {
            return viewOrder;
        }

        public void setViewOrder(String viewOrder) {
            this.viewOrder = viewOrder;
        }

        public String getViewName() {
            return viewName;
        }

        public void setViewName(String viewName) {
            this.viewName = viewName;
        }

        public String getProtectionDetail() {
            return protectionDetail;
        }

        public void setProtectionDetail(String protectionDetail) {
            this.protectionDetail = protectionDetail;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getPlanName() {
            return planName;
        }

        public void setPlanName(String planName) {
            this.planName = planName;
        }

        public String getLiabName() {
            return liabName;
        }

        public void setLiabName(String liabName) {
            this.liabName = liabName;
        }

        public String getMrFlag() {
            return mrFlag;
        }

        public void setMrFlag(String mrFlag) {
            this.mrFlag = mrFlag;
        }

        public String getPlanCode() {
            return planCode;
        }

        public void setPlanCode(String planCode) {
            this.planCode = planCode;
        }

        public String getPlanShortName() {
            return planShortName;
        }

        public void setPlanShortName(String planShortName) {
            this.planShortName = planShortName;
        }

        public List<PlanAttachListDTO> getPlanAttachList() {
            return planAttachList;
        }

        public void setPlanAttachList(List<PlanAttachListDTO> planAttachList) {
            this.planAttachList = planAttachList;
        }
    }

    public static class ViewSetListDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("prodId")
        private Integer prodId;
        @JsonProperty("projectId")
        private Integer projectId;
        @JsonProperty("viewName")
        private String viewName;
        @JsonProperty("viewOrder")
        private Integer viewOrder;
        @JsonProperty("viewDetail")
        private String viewDetail;
        @JsonProperty("sumInsDesc")
        private String sumInsDesc;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Integer getProdId() {
            return prodId;
        }

        public void setProdId(Integer prodId) {
            this.prodId = prodId;
        }

        public Integer getProjectId() {
            return projectId;
        }

        public void setProjectId(Integer projectId) {
            this.projectId = projectId;
        }

        public String getViewName() {
            return viewName;
        }

        public void setViewName(String viewName) {
            this.viewName = viewName;
        }

        public Integer getViewOrder() {
            return viewOrder;
        }

        public void setViewOrder(Integer viewOrder) {
            this.viewOrder = viewOrder;
        }

        public String getViewDetail() {
            return viewDetail;
        }

        public void setViewDetail(String viewDetail) {
            this.viewDetail = viewDetail;
        }

        public String getSumInsDesc() {
            return sumInsDesc;
        }

        public void setSumInsDesc(String sumInsDesc) {
            this.sumInsDesc = sumInsDesc;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Integer getProdId() {
        return prodId;
    }

    public void setProdId(Integer prodId) {
        this.prodId = prodId;
    }

    public String getProdName() {
        return prodName;
    }

    public void setProdName(String prodName) {
        this.prodName = prodName;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public List<ProjLiabListDTO> getProjLiabList() {
        return projLiabList;
    }

    public void setProjLiabList(List<ProjLiabListDTO> projLiabList) {
        this.projLiabList = projLiabList;
    }

    public List<ViewSetListDTO> getViewSetList() {
        return viewSetList;
    }

    public void setViewSetList(List<ViewSetListDTO> viewSetList) {
        this.viewSetList = viewSetList;
    }
}
