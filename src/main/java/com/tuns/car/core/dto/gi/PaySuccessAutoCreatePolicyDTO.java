package com.tuns.car.core.dto.gi;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PaySuccessAutoCreatePolicyDTO implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = -4658207815424888941L;


    /**
     * 订单信息--不能为空
     */
    @Valid
    private List<PaySuccessOrderDetailDTO> paySuccessOrderDetailDTOS;



    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
