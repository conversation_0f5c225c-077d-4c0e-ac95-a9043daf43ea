package com.tuns.car.core.dto.finance;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CommissionBean implements Serializable {

    private static final long serialVersionUID = -3133426305221302701L;
    private Long policyId;// 保单记录ID
    private String commType;//佣金类型
    private String policyType;//保单类型
    private String isIncludeTax;//是否含税 0否,1是
    private BigDecimal commRate;// 佣金率
    private BigDecimal commAmt;// 佣金金额
    private String viPolicyId;// 车险政策代码
    private String viPolicyName;//政策名称
    private String rewardMode;//奖励方式 1佣金率, 2固定金额
    private String policyCode;//政策代码
    private String policyLevel;//政策级别（ 01 总部 02分公司 03营业部 04业务部）
    private BigDecimal netPremium; //净保费
    private BigDecimal insuredPremium; //保费（不含车船税）
    private String backSinglePolicyId; //结出政策对于结回政策代码
    private BigDecimal policyRate;
    private BigDecimal splitAmt; //分润金额
    private Integer viPlantId;// 险种id
    private String viPlantName;//险种名称
    private Integer creUser;
    private Date creTm;

    /** S add by yn */
    /**
     * 佣金率(新)
     */
    private String rateStr;
    /**
     * 佣金金额与分润金额(新)
     */
    private String amountStr;


}
