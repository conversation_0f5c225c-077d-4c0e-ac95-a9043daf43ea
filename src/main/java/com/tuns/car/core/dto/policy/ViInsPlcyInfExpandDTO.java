package com.tuns.car.core.dto.policy;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/***
 * 车险保单续保拓展表的dto对象
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/7 15:31
 */
@Data
public class ViInsPlcyInfExpandDTO implements Serializable {
    private static final long serialVersionUID = 2037666909897851293L;

    /**
     * 保单记录ID
     */
    @ApiModelProperty(value = "保单记录ID")
    private Long policyId;

    /**
     * 拓展人员姓名
     */
    @ApiModelProperty(value = "拓展人员姓名")
    private String expandUserName;
    /**
     * 拓展人员身份证
     */
    @ApiModelProperty(value = "拓展人员身份证")
    private String expandCard;
    /**
     * 拓展人员手机号
     */
    @ApiModelProperty(value = "拓展人员手机号")
    private String expandPhone;

}
