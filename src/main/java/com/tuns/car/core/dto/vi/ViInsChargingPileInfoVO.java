package com.tuns.car.core.dto.vi;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tuns.car.core.config.CustomLocalDateToStringSerializer;
import com.tuns.car.core.constant.ViInsContants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 车险充电桩信息
 *
 * <AUTHOR>
 * @since 2021/12/21
 */
@ApiModel
@Data
public class ViInsChargingPileInfoVO {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("充电桩型号")
    private String chargingModel;

    @ApiModelProperty("充电桩编码")
    private String chargingCode;

    @ApiModelProperty("充电桩类型")
    private ViInsContants.ChargingType chargingType;

    @ApiModelProperty("充电桩类型(描述)")
    private String chargingTypeDescription;

    @ApiModelProperty("充电桩安装地点类型")
    private ViInsContants.ChargingAddrType chargingInstallAddrType;

    @ApiModelProperty("充电桩安装地点类型(描述)")
    private String chargingInstallAddrTypeDescription;

    @ApiModelProperty("充电桩使用年限")
    private ViInsContants.ChargingUseYears chargingUseYears;

    @ApiModelProperty(value = "充电桩安装时间", required = true)
    @JsonSerialize(using = CustomLocalDateToStringSerializer.class)
    private LocalDate chargingInstallDate;

    @ApiModelProperty("充电桩使用年限(描述)")
    private String chargingUseYearsDescription;

    @ApiModelProperty("充电桩完整地址")
    private String chargingAddrComplete;

    @ApiModelProperty("充电桩详细地址")
    private String chargingAddrDetail;

    @ApiModelProperty("省代码")
    private String province;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市代码")
    private String city;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("区县代码")
    private String county;

    @ApiModelProperty("区县名称")
    private String countyName;

    @ApiModelProperty("保额")
    private BigDecimal amount;

    @ApiModelProperty(value = "险种代码", hidden = true)
    private String kindCode;

    @ApiModelProperty(value = "业务ID")
    private Long chargingPileTpId;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
