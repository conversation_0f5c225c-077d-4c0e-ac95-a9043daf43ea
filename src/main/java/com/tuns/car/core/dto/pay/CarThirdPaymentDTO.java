package com.tuns.car.core.dto.pay;

import com.tuns.car.core.dto.car.IssueOrderChanInfoDTO;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import com.tuns.car.core.entity.ViOrderInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName CarThirdPaymentDTO
 * @Description TODO
 * <AUTHOR> yi
 * @Date 2021/1/29 15:26
 * @Version 1.0
 */
@Data
public class CarThirdPaymentDTO extends IssueOrderChanInfoDTO implements Serializable {

    private static final long serialVersionUID = 2127726673997422315L;

    /**
     * 车险交商同保信息数据
     */
    private List<ViInsPlcyInfTp> plcyInfList;

    /**
     * 订单数据
     */
    private ViOrderInfo orderInfo;

    /**
     * 车牌号
     */
    private String plateNumber;
}
