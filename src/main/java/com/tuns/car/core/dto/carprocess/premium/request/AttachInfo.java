package com.tuns.car.core.dto.carprocess.premium.request;

import com.tuns.car.core.constant.ViAttachTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 附件信息
 *
 * <AUTHOR>
 * @since 2022/3/23
 */
@ApiModel
@Data
public class AttachInfo {
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @NotBlank
    @ApiModelProperty(value = "访问key")
    private String key;

    @NotNull
    @ApiModelProperty(value = "类型")
    private ViAttachTypeEnum type;
}
