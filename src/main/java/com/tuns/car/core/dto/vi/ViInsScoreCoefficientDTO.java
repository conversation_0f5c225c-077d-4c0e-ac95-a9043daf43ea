package com.tuns.car.core.dto.vi;


import com.tuns.car.core.constant.InsuredTypeEnum;
import com.tuns.car.core.constant.SecondaryNewCarEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 车险保单评分以及系数出参
 */
@ApiModel
@Data
public class ViInsScoreCoefficientDTO implements Serializable {
    private static final long serialVersionUID = 462525509737311428L;
    /**
     * 车慧达分档
     */
    @ApiModelProperty(value = "车慧达分档")
    private String cheHuidaTranche;
    /**
     * 合作网点
     */
    @ApiModelProperty(value = "合作网点")
    private String cooperativeNetwork;
    /**
     * 风险等级
     */
    @ApiModelProperty(value = "风险等级")
    private String riskLevel;
    /**
     * 车险分
     */
    @ApiModelProperty(value = "车险分")
    private BigDecimal autoInsurancePoints;
    /**
     * 车损险北理新能源评分
     */
    @ApiModelProperty(value = "车损险北理新能源评分")
    private String bitDamageScore;
    /**
     * 连续承保期间出险次数(交强险)
     */
    @ApiModelProperty(value = "连续承保期间出险次数(交强险)")
    private BigDecimal numberOfUnderwritingCi;
    /**
     * 商业险含NCD标准保费预期赔付率
     */
    @ApiModelProperty(value = "商业险含NCD标准保费预期赔付率")
    private BigDecimal ncdEcompensationRateBi;
    /**
     * 客户是否注册中华保的小程序
     */
    @ApiModelProperty(value = "客户是否注册中华保的小程序")
    private String chineseApplet;
    /**
     * 次新车
     */
    @ApiModelProperty(value = "次新车")
    private SecondaryNewCarEnum secondaryNewCar;
    /**
     * 车系分类
     */
    @ApiModelProperty(value = "车系分类")
    private String selfVehcType;

    /**
     * 上年出险次数（交商合计）
     */
    @ApiModelProperty(value = "上年出险次数（交商合计）")
    private Integer lastClaimCountTotal;
    /**
     * 合作方名称
     */
    @ApiModelProperty(value = "合作方名称")
    private String partnerName;

    /**
     * 业务分组名称
     */
    @ApiModelProperty(value = "业务分组名称")
    private String businessGroupName;

    @ApiModelProperty(value = "期望折扣与建议折扣差额")
    private String differenceDiscount;

    @ApiModelProperty(value = "建议折扣")
    private Double proposalDiscount;

    @ApiModelProperty(value = "期望折扣")
    private Double expectedDiscount;

    @ApiModelProperty(value = "尊享分（交商合计评分字段）")
    private String totalEnjoyScore;

    @ApiModelProperty(value = "商业险尊享分")
    private String commEnjoyScore;

    @ApiModelProperty(value = "交强险尊享分")
    private String trafficEnjoyScore;

    @ApiModelProperty(value = "亚太分")
    private BigDecimal asiaPacificScore;

    @ApiModelProperty(value = "团车码")
    private String teamCarCode;

    @ApiModelProperty(value = "交强人保分")
    private String piccScoreCi;

    @ApiModelProperty(value = "商业人保分")
    private String piccScoreBi;

    @ApiModelProperty(value = "商业团车码")
    private String businessTeamCarCode;

    @ApiModelProperty(value = "交强团车码")
    private String trafficTeamCarCode;

    @ApiModelProperty(value = "建议自主定价系数")
    private String proposeIndependentPriceRate;

    /**
     * 交强险手续费比例
     */
    @ApiModelProperty(value = "交强险手续费比例")
    private BigDecimal handFeeRatioCi;
    /**
     * 商业险手续费比例
     */
    @ApiModelProperty(value = "商业险手续费比例")
    private BigDecimal handFeeRatioBi;

    /**
     * NCD标费预期赔付率
     */
    @ApiModelProperty(value = "NCD标费预期赔付率")
    private BigDecimal ncdEcompensationRate;

    /**
     * 交商合计含NCD标准保费预期赔付率
     */
    @ApiModelProperty(value = "交商合计含NCD标准保费预期赔付率")
    private BigDecimal ncdTotalEcompensationRate;

    /**
     * 精准使用年限
     */
    @ApiModelProperty(value = "精准使用年限")
    private BigDecimal preciseUseYear;

    /**
     * 是否真单交
     */
    @ApiModelProperty(value = "是否真单交")
    private String isDjq;

    /**
     * 交强  业务类型 1-新保 2-续保 3-转保
     */
    @ApiModelProperty(value = "业务类型 1-新保 2-续保 3-转保")
    private InsuredTypeEnum insuredTypeCi;

    /**
     * 商业 业务类型 1-新保 2-续保 3-转保
     */
    @ApiModelProperty(value = "业务类型 1-新保 2-续保 3-转保")
    private InsuredTypeEnum insuredTypeBi;

    @ApiModelProperty(value = "交强险不浮动原因")
    private String noFloatReasonCi;

    @ApiModelProperty(value = "商业险不浮动原因")
    private String noFloatReasonBi;

    @ApiModelProperty(value = "大家分(交强)")
    private String allScoreCi;

    @ApiModelProperty(value = "大家分(商业)")
    private String allScoreBi;

    @ApiModelProperty(value = "国任决策单元")
    private String xdcxDmu;
    /**
     * 交强新转续二级标识
     */
    @ApiModelProperty(value = "交强新转续二级标识")
    private String thisRenewalCi;
    /**
     * 商业新转续二级标识
     */
    @ApiModelProperty(value = "商业新转续二级标识")
    private String thisRenewalBi;

    @ApiModelProperty(value = "人保归属机构")
    private String piccBelongDepartmanet;

    @ApiModelProperty(value = "业务类别")
    private String businessCategory;

    @ApiModelProperty(value = "出单口")
    private String underwritingChannel;

    @ApiModelProperty(value = "中交兴路评分（商业险）")
    private BigDecimal zjxlBiScore;

}
