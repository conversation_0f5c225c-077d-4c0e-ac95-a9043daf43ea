package com.tuns.car.core.dto.gi;

import com.tuns.car.core.dto.car.IssueOrderChanInfoDTO;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName SyncPaySuccessAutoCreatePolicyDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/9 16:32
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class SyncPaySuccessAutoCreatePolicyDTO extends IssueOrderChanInfoDTO {

    private Long policyBatchId;

    private List<ViInsPlcyInfTp> viInsPlcyInfTp;

    private List<CarSameSaleGiOrderInfoVO> giOrders;

}
