package com.tuns.car.core.dto.carprocess.premium;


import com.tuns.car.core.constant.ChanConfigFieldEnum;
import com.tuns.car.core.constant.InsEnum;
import com.tuns.car.core.constant.ViPersonTypeEnum;
import com.tuns.car.core.dto.ComsChanConfigDTO;
import com.tuns.car.core.dto.TsRenewalTimeDTO;
import com.tuns.car.core.dto.UserDTO;
import com.tuns.car.core.dto.car.AddNoCarDTO;
import com.tuns.car.core.dto.car.IssueOrderChanInfoDTO;
import com.tuns.car.core.dto.carprocess.premium.request.CarInfo;
import com.tuns.car.core.dto.carprocess.premium.request.CheckCarInfoDTO;
import com.tuns.car.core.dto.carprocess.premium.request.ItemKind;
import com.tuns.car.core.dto.carprocess.premium.request.PersonInfo;
import com.tuns.car.core.dto.charging.ChargingPileDTO;
import com.tuns.car.core.dto.nocar.GiGroupInsureDTO;
import com.tuns.car.core.entity.*;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class PremiumCaculateDTO extends IssueOrderChanInfoDTO {

    /**
     * 客户端传过来的原始报价请求参数
     */
    private InsCarFullInfoSaveDTO premiumCaculateRequest;

    /**
     * 请求用户id
     */
    private Integer userId;

    /**
     * 批次id
     */
    private String policyBatchId;

    /**
     * 流水号
     */
    private Long serialNumber;

    /**
     * 险种信息集合(key:我方险种代码)
     */
    private Map<String, ViInsuranceInf> tpViInsuranceInfMapSelf;
    /**
     * 险种信息集合（key:保险公司险种代码）
     */
    private Map<String, ViInsuranceInf> tpViInsuranceInfMapTP;

    /**
     * 本次报价购买的险种信息集合（key: 我方保险公司险种代码）
     * 包含主险 + 附加服务条款
     */
    private Map<String, ItemKind> buyInsuranceInfMap;

    /**
     * 保险公司信息
     */
    private CmmCompanyConfig cmmCompanyConfig;

    /**
     * 车辆信息
     */
    private CarInfo carInfo;
    /**
     * 关系人信息
     */
    private Map<String, PersonInfo> personInfoMap;

    /**
     * 车辆关系映射
     */
    private CmmVehicleTypeRelation vehicleTypeRelation;

    /**
     * 报价记录
     */
    private List<ViInsPlcyInfTp> viInsPlcyInfTps;

    /**
     * 报价
     */
    private Map<String, ViInsPlcyInfTp> viInsPlcyInfTpMap;

    /**
     * 太保报价时需要用到，其他保司如果需要可以setget
     */
    private Map<String, ItemKind> itemKindMap;

    /**
     * 验车信息
     */
    private CheckCarInfoDTO checkCarInfo;

    /**
     * 报价地区
     */
    private AreaDTO areaDTO;

    /**
     * 非车险
     */
    private GiGroupInsureDTO giInsureDTO;

    /**
     * 保险公司枚举
     */
    private InsEnum insEnum;

    /**
     * 非车校验结果
     */
    private String noCarCheckResult;

    /**
     * 添加非车相关信息
     */
    private AddNoCarDTO addNoCarDTO;

    /**
     * 投被保的地址信息
     */
    private Map<ViPersonTypeEnum, AreaDTO> addressMap;

    /**
     * 充电桩信息
     */
    private List<ChargingPileDTO> chargingPiles;

    /**
     * 是否新能源
     */
    private Boolean newEnergy = false;

    /**
     * 出单员信息
     */
    private UserDTO userInfo;

    /**
     * 续保时效表
     */
    private TsRenewalTimeDTO tsRenewalTimeDTO;

    /**
     * 是否是重复投保报价
     */
    private boolean repeatInsured;

    /**
     * 发票信息
     */
    private ViInsPlcyCustomInvoice invoice;

    /**
     * 非标页面
     */
    private SpecialInfoDTO specialInfo;

    /**
     * 报价单基本信息
     */
    private QuoteBaseDTO baseDTO;

    /**
     * 调整系数所需信息
     */
    private RatioAdjustDTO ratioAdjust;

    /**
     * 特约信息
     */
    private List<ViQuoteSpecialAgreement> specialAgreementDtoList;

    /**
     * 新车购置开票信息
     */
    private ViQuoteInvoiceInfoTp viQuoteInvoiceInfoTp;

    /**
     * 核保告知
     */
    private String underwritingInform;

    /**
     * 删除本次报价的保险公司订单
     */
    private Boolean deleteCurrentQuoteInsOrder = false;

    /**
     * 删除上次报价的保险公司订单
     */
    private Boolean deleteLastQuoteInsOrder = true;

    /**
     * 上一笔报价单
     */
    private List<ViInsPlcyInfTp> lastPlcyTpList;
    /**
     * 渠道配置信息
     */
    Map<ChanConfigFieldEnum, List<ComsChanConfigDTO>> chanConfigMap;

    /**
     * 报价预处理结果
     *
     */
    private String preQuoteResult;

    /**
     * 需要等待预处理结束
     *
     */
    private Boolean needWaitPreProcess;
    /**
     * 智能报价原保单ID
     */
    private Long intellectPolicyBatchId;

    /**
     * 请求来源-是否来自快捷修改
     */
    private Boolean isQuickUpdate;

}
