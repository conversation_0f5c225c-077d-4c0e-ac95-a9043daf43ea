package com.tuns.car.core.dto.gi;

import com.alibaba.fastjson.JSON;

import java.io.Serializable;

/**
 * @ClassName: OutCenterVO
 * @Description: 出单中心VO
 * <AUTHOR>
 * @String 2019年8月27日
 */
public class OutCenterVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1381260027895003403L;

	private String giSpiderSerialNumber; // 非车险报价业务关联id

	private String policyBatchId;

	public String getGiSpiderSerialNumber() {
		return giSpiderSerialNumber;
	}

	public void setGiSpiderSerialNumber(String giSpiderSerialNumber) {
		this.giSpiderSerialNumber = giSpiderSerialNumber;
	}

	public String getPolicyBatchId() {
		return policyBatchId;
	}

	public void setPolicyBatchId(String policyBatchId) {
		this.policyBatchId = policyBatchId;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

}
