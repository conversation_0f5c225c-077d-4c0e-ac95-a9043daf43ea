package com.tuns.car.core.dto.finance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PolicyCommissionBean implements Serializable {

    /**
     * 佣金信息记录
    */
    private List<CommissionBean> commissionList;

    /** 提交审核的“车险保单”(可能多个)是否包含未匹配上政策的“车险保单，未匹配政策*/
    private Boolean noMatchingPolicy = false;
    /**
     * 政策匹配的错误原因
     */
    private String errorReason;

    @ApiModelProperty(value = "单件政策匹配的错误信息")
    private String singleErrorReason;

}
