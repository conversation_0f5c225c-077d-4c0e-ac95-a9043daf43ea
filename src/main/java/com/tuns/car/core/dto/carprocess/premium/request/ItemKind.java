package com.tuns.car.core.dto.carprocess.premium.request;

import com.tuns.core.boot.constant.YesNoNumberEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/***
 * 险种信息
 * ClassName: ItemKind
 * Description:
 * Create Time: 2022/3/1 15:53
 *
 * <AUTHOR>
 */
@Data
public class ItemKind implements Serializable {

    private static final long serialVersionUID = -2348271283820838135L;

    @ApiModelProperty(value = "险种代码")
    private String kindCode;

    @ApiModelProperty(value = "险种名称")
    private String kindName;

    @ApiModelProperty(value = "数量")
    private Integer quantity;

    @ApiModelProperty(value = "单位保额")
    private BigDecimal unitAmount;

    @ApiModelProperty(value = "总保额")
    private BigDecimal amount;

    @ApiModelProperty(value = "是否不计免赔 0-否 1-是")
    private String addlMark;

    @ApiModelProperty(value = "是否购买不计免赔 0-否 1-是")
    private String buyAddl;

    @ApiModelProperty(value = "商业险标致 0-否 1-是")
    private String businessMark;

    @ApiModelProperty(value = "序号")
    private Integer orderNo;

    @ApiModelProperty(value = "附加类型 玻璃险专用 1-进口 2-国产")
    private String valueType;

    @ApiModelProperty(value = "费率0~1 指定专修厂专用")
    private Double rate;

    @ApiModelProperty(value = "险种名称-别名")
    private String insuranceAlias;

    @ApiModelProperty(value = "是服务条款")
    private Boolean beServiceTerm;

    @ApiModelProperty(value = "是否共享保额")
    private YesNoNumberEnum isSharedAmount;
}
