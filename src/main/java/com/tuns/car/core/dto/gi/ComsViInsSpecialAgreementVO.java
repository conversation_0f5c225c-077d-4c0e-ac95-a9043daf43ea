package com.tuns.car.core.dto.gi;

import com.tuns.car.core.constant.PolicyTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-11-7 10:03
 */
@Data
public class ComsViInsSpecialAgreementVO {
    @ApiModelProperty("批次ID")
    private Long policyBatchId;

    @ApiModelProperty("保单记录ID")
    private Long policyId;

    @ApiModelProperty("特约代码")
    private String engageCode;

    @ApiModelProperty("特约内容")
    private String engageContent;

    @ApiModelProperty("特约标题")
    private String engageTitle;

    @ApiModelProperty("交强险1/商业险2/交商同保3")
    private PolicyTypeEnum recordType;

    @ApiModelProperty("备注")
    private String remark;
}
