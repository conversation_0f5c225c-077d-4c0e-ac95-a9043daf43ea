package com.tuns.car.core.dto.car;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * app端车险订单列表分页查询请求数据类
 * </p>
 *
 * <AUTHOR> wei
 * @since 2021年1月25日
 */
@Data
public class ViOrderInfoPageDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 4386442631854197089L;

    /**
     * 订单类型 0全部,1待支付,2待确认,3已支付
     */
    @ApiModelProperty(value = "订单类型 0全部,1待支付,2待确认,3已支付", required = true)
    private String type;

    /**
     * 模糊搜索名称
     */
    @ApiModelProperty(value = "模糊搜索名称")
    private String keyword;

    /**
     * 当前用户id
     */
    private Integer userId;

}
