package com.tuns.car.core.dto.policy;

import com.tuns.car.core.constant.DelayTaskLogBusinessTypeEnum;
import com.tuns.car.core.constant.ExecuteFlagEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 车险延迟消息任务日志入参
 * @Author: <a href="http://www.loserzhao.com">JoMin</a>
 * @Create: 2024-11-28 10:41
 */
@Data
@Accessors(chain = true)
public class ViInsDelayTaskDTO implements Serializable {
    private static final long serialVersionUID = 6586565839963968332L;

    /**
     * 日志主键ID
     */
    @ApiModelProperty(value = "日志主键ID")
    private Integer logId;

    /**
     * 业务ID
     */
    @ApiModelProperty(value = "业务ID")
    private Long businessId;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private DelayTaskLogBusinessTypeEnum businessType;
    /**
     * 任务序号
     */
    @ApiModelProperty(value = "任务序号")
    private Integer taskSerial;
    /**
     * 执行标志 0待执行 1执行成功 2执行失败
     */
    @ApiModelProperty(value = "执行标志 0待执行 1执行成功 2执行失败")
    private ExecuteFlagEnum executeFlag;
    /**
     * 间隔时间（毫秒）
     */
    @ApiModelProperty(value = "间隔时间（毫秒）")
    private Long delayedIntervaTimeMillis;
    /**
     * 预计执行时间
     */
    @ApiModelProperty(value = "预计执行时间")
    private LocalDateTime predictExecuteDate;
}
