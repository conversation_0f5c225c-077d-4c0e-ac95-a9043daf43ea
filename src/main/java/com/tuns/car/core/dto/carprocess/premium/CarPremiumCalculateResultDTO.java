package com.tuns.car.core.dto.carprocess.premium;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 车险保费计算批量结果信息
 *
 * <AUTHOR>
 */
@ApiModel
public class CarPremiumCalculateResultDTO implements Serializable {

    private static final long serialVersionUID = 3424828270095981228L;

    @NotBlank
    @ApiModelProperty(value = "报价流水ID")
    private String serialNumber;

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }
}

