package com.tuns.car.core.dto.carprocess.premium;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/04/28
 **/
public enum SceneEnum implements IEnum<String>, SwaggerDisplayEnum {

    /**
     * 正常保费试算
     */
    NORMAL("normal", "正常保费试算"),

    /**
     * 获取自主定价系数
     */
    DISCOUNT("discount", "获取自主定价系数");

    private final String value;

    private String description;

    private static Map<String, SceneEnum> map = Stream.of(SceneEnum.values()).collect(Collectors.toMap(SceneEnum::getValue, scene1 -> scene1));

    SceneEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static SceneEnum getByValue(String value) {
        return map.get(value);
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getSwaggerDesc() {
        return getValue() + ":" + getDescription();
    }

    @Override
    public String toString() {
        return value;
    }
}
