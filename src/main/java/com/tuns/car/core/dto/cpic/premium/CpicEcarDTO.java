package com.tuns.car.core.dto.cpic.premium;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CpicEcarDTO
 * @Description 太平洋原始请求报文dto对象（ ）
 * <AUTHOR>
 * @Date 2022/02/14 09:36:58
 * @Version 1.0
 */
@Data
public class CpicEcarDTO implements Serializable {
    private String absFlag;
    private String actualValue;
    private String antiTheft;
    private String bodyType;
    private String brandCode;
    private String caliberOfNewChannels;
    private String carGroupName;
    private String carVIN;
    private String carryingCapacity;
    private String certNo;
    private String certType;
    private String deductionDueCodeJY;
    private String dumpTrailerFlag;
    private String emptyWeight;
    private String engineCapacity;
    private String engineDesc;
    private String engineNo;
    private String factoryCode;
    private String factoryName;
    private String factoryType;
    private String familyCode;
    private String familyName;
    private String fuelType;
    private String fullWeightMin;
    private String fullweightMax;
    private String gearboxtype;
    private String glassManufacturer;
    private String hfEndTime;
    private String hfStartTime;
    private String hkLicense;
    private Long id;
    private String inType;
    private String insuranceCode;
    private String jyFuelType;
    private String jyRiskFlagCode;
    private String kindPrice;
    private String loan;
    private String marketDate;
    private String modelType;
    private String moldCharacterCode;
    private String moldName;
    private String moldNameWithSuffix;
    private String negotiatedValue;
    private String newCarFlag;
    private String newFamilyGrade;
    private String newVehicleClass;
    private String newVehicleClassCode;
    private String oldCustomerFlag;
    private String oriCarryingCapacity;
    private String oriCurrentValue;
    private String oriEmptyWeight;
    private String oriEngineCapacity;
    private String oriMakerModel;
    private String oriMoldName;
    private String oriPurchasePrice;
    private String oriSeatCount;
    private String oriVehicleVariety1;
    private String oriVehicleVariety2;
    private String ownerName;
    private String ownerProp;
    private String plateColor;
    private String plateNo;
    private String plateType;
    private Boolean plateless;
    private String power;
    private String prevMoldChrCode;
    private String prevNegoValue;
    private String producingArea;
    private String purchasePrice;
    private String rVehicleName;
    private String realPurchasePrice;
    private String reductionType;
    private String registerDate;
    private String relationship;
    private String remark;
    private String seatCount;
    private String seatMax;
    private String seatMin;
    private String secretCertNo;
    private String secretHolderTelphone;
    private String shortcutCode;
    private String showDefaultTaxType;
    private String specialVehicleFlag;
    private String stRegisterDate;
    private String taxCustomerType;
    private String taxMark;
    private String taxTypeWz;
    private String tonnage;
    private String tpyRiskflagCode;
    private String tpyRiskflagName;
    private String usage;
    private String usageType;
    private String vehicleAlias;
    private String vehicleBrand;
    private String vehicleClassCode;
    private String vehicleClassName;
    private String vehicleGroupCode;
    private String vehicleHyCode;
    private String vehicleHyName;
    private String vehiclePowerJY;
    private String vehiclePurpose;
    private String vehicleType;
    private String vehicleUsage1;
    private String vehicleUsage2;
    private String vehicleVariety1;
    private String vehicleVariety2;
    private String vehiclefgwCode;
    private String vehiclefgwName;
    private String vehiclegroupName;
    private String yearPattern;
    private String specialVehicleIden;
    private String stChangeRegisterDate;
    private String certificateValidity;
    private String stCertificateValidity;

}
