/**
  * Copyright 2021 json.cn 
  */
package com.tuns.car.core.dto.gi.msb;

/**
 * Auto-generated: 2021-11-01 15:12:57
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class PaymentRecordVos {

    private String payNo;
    private String payStatus;
    private double paymentAmount;
    private String paymentType;
    private String policyHolder;
    private String policyNo;
    private String policyType;
    private String productType;
    private String quotationNo;
    private String status;
    public void setPayNo(String payNo) {
         this.payNo = payNo;
     }
     public String getPayNo() {
         return payNo;
     }

    public void setPayStatus(String payStatus) {
         this.payStatus = payStatus;
     }
     public String getPayStatus() {
         return payStatus;
     }

    public void setPaymentAmount(double paymentAmount) {
         this.paymentAmount = paymentAmount;
     }
     public double getPaymentAmount() {
         return paymentAmount;
     }

    public void setPaymentType(String paymentType) {
         this.paymentType = paymentType;
     }
     public String getPaymentType() {
         return paymentType;
     }

    public void setPolicyHolder(String policyHolder) {
         this.policyHolder = policyHolder;
     }
     public String getPolicyHolder() {
         return policyHolder;
     }

    public void setPolicyNo(String policyNo) {
         this.policyNo = policyNo;
     }
     public String getPolicyNo() {
         return policyNo;
     }

    public void setPolicyType(String policyType) {
         this.policyType = policyType;
     }
     public String getPolicyType() {
         return policyType;
     }

    public void setProductType(String productType) {
         this.productType = productType;
     }
     public String getProductType() {
         return productType;
     }

    public void setQuotationNo(String quotationNo) {
         this.quotationNo = quotationNo;
     }
     public String getQuotationNo() {
         return quotationNo;
     }

    public void setStatus(String status) {
         this.status = status;
     }
     public String getStatus() {
         return status;
     }

}