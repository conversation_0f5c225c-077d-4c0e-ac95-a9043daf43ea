package com.tuns.car.core.dto.nocar;

import com.tuns.car.core.dto.gi.CarSameSaleGiOrderInfoDetailVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 非车投保必传信息
 * 
 * <AUTHOR>
 *
 */
@Data
@Accessors(chain = true)
public class GiGroupInsureDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4310881904748056108L;

    // 定义个险投保业务编号(唯一)
	private String giGroupBizNo;

    /**
     * 购买了非车产品
     */
    private Boolean actualBuy;

    /**
     * 订单信息
     */
    private List<CarSameSaleGiOrderInfoDetailVO> giOrderList;
}
