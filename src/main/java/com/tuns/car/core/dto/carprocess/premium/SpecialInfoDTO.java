package com.tuns.car.core.dto.carprocess.premium;

import com.tuns.car.core.constant.PayTypeEnum;
import com.tuns.car.core.dto.carprocess.premium.request.CheckCarInfoDTO;
import com.tuns.car.core.dto.charging.ChargingPileDTO;
import com.tuns.car.core.entity.ViInsPlcyCustomInvoice;
import com.tuns.car.core.vo.car.ScoringFactorVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 非标信息
 *
 * <AUTHOR>
 * @date 2023/03/04
 **/
@Data
@ApiModel
public class SpecialInfoDTO {

    @ApiModelProperty(value = "非车报价核心业务编号")
    private String giGroupBizNo;


    @ApiModelProperty(value = "车损险保额")
    private BigDecimal damageInsAmount;

    @ApiModelProperty(value = "发票开票信息")
    private ViInsPlcyCustomInvoice invoice;

    @ApiModelProperty(value = "充电桩信息")
    private List<ChargingPileDTO> chargingPiles;

    @ApiModelProperty(value = "服务条款")
    private List<ServiceTermDTO> serviceTerms;

    @ApiModelProperty(value = "验车信息")
    private CheckCarInfoDTO checkCarInfo;

    @Valid
    @ApiModelProperty(value = "折扣系数信息")
    private RatioDTO ratioInfo;

    @ApiModelProperty(value = "车辆来历凭证")
    private VehicleVhlCertDTO vehicleVhlCertDTO;

    @ApiModelProperty(value = "特殊录入情况")
    private SpecialInputDTO specialInputInfo;

    /**
     * 由于中华API 点击修改折扣按钮进来没有经过车型查询拿到最新的报价ID导致报错，新增一个标识来判断是否调用保费试算前置接口
     * 该字段只有在报价详情点击修改折扣或者系数的时候才有值
     * 1:是 0否
     */
    @ApiModelProperty(value = "是否调用报价前置接口标识")
    private String isModifyDiscount;
    /**
     * 非标页面
     */
    @ApiModelProperty("评分及系数")
    private List<ScoringFactorVO> scoringFactors;

    /**
     * 团车码
     */
    @ApiModelProperty(value = "团车码")
    private String groupCarCode;

    @ApiModelProperty(value = "团车码具体值")
    private String groupCarValue;

    /**
     * 活动码
     *
     */
    @ApiModelProperty(value = "活动码")
    private String promoteSalePlanId;

    @ApiModelProperty(value = "单位信息")
    private ViQuotationEnterpriseInfoDTO quotationEnterpriseInfoDTO;

    @ApiModelProperty(value = "号牌底色")
    private String licenseColor;

    @ApiModelProperty(value = "支付方式")
    private PayTypeEnum payType;

    @ApiModelProperty(value = "非标页其他信息")
    private List<ViAppConfigSpecialFieldDTO> viAppConfigSpecialFieldDTOList;
}
