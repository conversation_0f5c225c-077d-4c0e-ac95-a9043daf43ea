package com.tuns.car.core.dto.car;

import com.tuns.car.core.vo.car.ViInsChargingPileMiniVO;
import com.tuns.core.boot.constant.YesNoNumberEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/19 11:07
 */
@Data
@Accessors(chain = true)
public class QuoteKindDTO implements Serializable {

    private static final long serialVersionUID = -4617772066757236033L;
    private long id;
    /**
     * 险别代码
     */
    private String kindCode;
    /**
     * 险别名称
     */
    private String kindName;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 单位保额
     */
    private Double unitAmount;
    /**
     * 总保额
     */
    private Double amount;
    /**
     * 保费
     */
    private Double premium;
    /**
     * 折扣
     */
    private Double discount;
    /**
     * 费率
     */
    private Double rate;
    /**
     * 是否不计面免赔
     */
    private String addlMark;
    /**
     * 是否购买不计免赔险
     */
    private String buyAddl;
    /**
     * 附加类型
     */
    private String valueType;
    /**
     * 是否商业险
     */
    private String businessMark;
    /**
     * 序号
     */
    private Integer orderNo;
    /**
     * 附加险所在主险代码
     */
    private String mainInsuranceCode;
    /**
     * 净保费
     */
    private Double netPremium;
    /**
     * 总税额
     */
    private Double tax;

    private List<ViInsChargingPileMiniVO> pileMiniList;

    /**
     * 是否共享保额
     */
    private YesNoNumberEnum isSharedAmount;
}
