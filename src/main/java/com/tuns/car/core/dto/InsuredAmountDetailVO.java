package com.tuns.car.core.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 基础险种查询
 *
 * <AUTHOR>
 **/
@Data
public class InsuredAmountDetailVO {
    @ApiModelProperty(value = "保额")
    private BigDecimal value;
    @ApiModelProperty(value = "保额text")
    private String label;
    @ApiModelProperty(value = "是否勾选")
    private Boolean selected;
    @ApiModelProperty(value = "是否打推荐标记")
    private Boolean recommend;
}
