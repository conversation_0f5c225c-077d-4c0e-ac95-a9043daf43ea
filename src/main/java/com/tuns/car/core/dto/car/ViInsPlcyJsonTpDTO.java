package com.tuns.car.core.dto.car;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 车险报价原始json信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-02-28 17:23:57
 */
@Data
public class ViInsPlcyJsonTpDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流程流水号
	 */
	@ApiModelProperty(value = "流程流水号")
	private Long serialNumber;
	/**
	 * 保单批次ID
	 */
	@ApiModelProperty(value = "保单批次ID")
	private Long policyBatchId;
	/**
	 * 保司接口返回参数
	 */
	@ApiModelProperty(value = "保司接口返回参数")
	private String extendJson;


}
