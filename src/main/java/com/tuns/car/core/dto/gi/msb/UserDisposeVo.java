/**
  * Copyright 2021 json.cn 
  */
package com.tuns.car.core.dto.gi.msb;

/**
 * Auto-generated: 2021-11-01 15:12:57
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class UserDisposeVo {

    private String isBusinessfee;
    private String isChangeDiscuteRate;
    private String isChangeFeeRate;
    private String isChangeableFeeRate;
    private String isCompAndCommPolicecost;
    private String isNetEcompensationRate;
    private String isPerformance;
    private String isPolicyCostRate;
    private String isPoudage;
    private String isPrintAuth;
    private String refundNoticePrint;
    public void setIsBusinessfee(String isBusinessfee) {
         this.isBusinessfee = isBusinessfee;
     }
     public String getIsBusinessfee() {
         return isBusinessfee;
     }

    public void setIsChangeDiscuteRate(String isChangeDiscuteRate) {
         this.isChangeDiscuteRate = isChangeDiscuteRate;
     }
     public String getIsChangeDiscuteRate() {
         return isChangeDiscuteRate;
     }

    public void setIsChangeFeeRate(String isChangeFeeRate) {
         this.isChangeFeeRate = isChangeFeeRate;
     }
     public String getIsChangeFeeRate() {
         return isChangeFeeRate;
     }

    public void setIsChangeableFeeRate(String isChangeableFeeRate) {
         this.isChangeableFeeRate = isChangeableFeeRate;
     }
     public String getIsChangeableFeeRate() {
         return isChangeableFeeRate;
     }

    public void setIsCompAndCommPolicecost(String isCompAndCommPolicecost) {
         this.isCompAndCommPolicecost = isCompAndCommPolicecost;
     }
     public String getIsCompAndCommPolicecost() {
         return isCompAndCommPolicecost;
     }

    public void setIsNetEcompensationRate(String isNetEcompensationRate) {
         this.isNetEcompensationRate = isNetEcompensationRate;
     }
     public String getIsNetEcompensationRate() {
         return isNetEcompensationRate;
     }

    public void setIsPerformance(String isPerformance) {
         this.isPerformance = isPerformance;
     }
     public String getIsPerformance() {
         return isPerformance;
     }

    public void setIsPolicyCostRate(String isPolicyCostRate) {
         this.isPolicyCostRate = isPolicyCostRate;
     }
     public String getIsPolicyCostRate() {
         return isPolicyCostRate;
     }

    public void setIsPoudage(String isPoudage) {
         this.isPoudage = isPoudage;
     }
     public String getIsPoudage() {
         return isPoudage;
     }

    public void setIsPrintAuth(String isPrintAuth) {
         this.isPrintAuth = isPrintAuth;
     }
     public String getIsPrintAuth() {
         return isPrintAuth;
     }

    public void setRefundNoticePrint(String refundNoticePrint) {
         this.refundNoticePrint = refundNoticePrint;
     }
     public String getRefundNoticePrint() {
         return refundNoticePrint;
     }

}