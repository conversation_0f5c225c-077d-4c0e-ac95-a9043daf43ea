package com.tuns.car.core.dto.carprocess.premium;

import com.tuns.car.core.constant.EpolicyDeletionEnum;
import lombok.Data;

import java.util.List;

/**
 * 新的电子保单返回类
 * 新增了3.3.97需求返回的字段
 *
 * <AUTHOR>
 * @since 2024-02-05 14:44
 **/
@Data
public class NewEpolicyDTO {

    private List<EpolicyDTO> epolicyDTOList;


    /**
     * * 1：电子保单存在；
     * * 2：电子保单缺失；
     * * 3：该单该电子保单本就不存在
     * 交强电子保单 默认3
     */
    private EpolicyDeletionEnum isEpolicyDeletionCI = EpolicyDeletionEnum.ABSENT;

    /**
     * * 1：电子保单存在；
     * * 2：电子保单缺失；
     * * 3：该单该电子保单本就不存在
     * 商业电子保单 默认3
     */
    private EpolicyDeletionEnum isEpolicyDeletionBI = EpolicyDeletionEnum.ABSENT;

    /**
     * * 1：电子保单存在；
     * * 2：电子保单缺失；
     * * 3：该单该电子保单本就不存在
     * 电子投保单 默认3
     */
    private EpolicyDeletionEnum isEpolicyDeletionApplication = EpolicyDeletionEnum.ABSENT;

    /**
     * * 1：电子保单存在；
     * * 2：电子保单缺失；
     * * 3：该单该电子保单本就不存在
     * 非车电子保单 默认3
     */
    private EpolicyDeletionEnum isEpolicyDeletionFC = EpolicyDeletionEnum.ABSENT;


}
