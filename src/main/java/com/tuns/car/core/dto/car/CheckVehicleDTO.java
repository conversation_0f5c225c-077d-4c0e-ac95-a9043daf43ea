package com.tuns.car.core.dto.car;

import com.tuns.car.core.dto.carprocess.premium.request.CheckCarInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 验车信息
 *
 * <AUTHOR>
 * @since 2023-12-7 10:23
 */
@Data
public class CheckVehicleDTO extends CheckCarInfoDTO {

    @ApiModelProperty(value = "期望验车时间")
    private String checkVehicleDate;

    @ApiModelProperty(value = "期望验车时间段（01-上午02-下午）")
    private String checkVehicleDateZone;

    @ApiModelProperty(value = "期望验车地点")
    private String checkVehicleAddress;
}
