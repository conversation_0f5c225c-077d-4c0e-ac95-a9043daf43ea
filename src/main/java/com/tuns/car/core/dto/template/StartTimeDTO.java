package com.tuns.car.core.dto.template;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/30 15:58
 */
@Data
@Accessors(chain = true)
public class StartTimeDTO implements Serializable {

    private static final long serialVersionUID = 2824217314962057110L;

    /**
     * 开始时间 格式: yyyy-MM-dd
     */
    private LocalDateTime startTime;

    /**
     * 截止时间 格式: yyyy-MM-dd
     */
    private LocalDateTime endTime;

    /**
     * 即时起保
     */
    private Boolean effectiveNow = false;

    public enum Kind {
        /**
         * 交强险
         */
        CI,

        /**
         * 商业险
         */
        BI
    }


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
