package com.tuns.car.core.dto.finance;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/3 16:16
 */
@Data
public class InsPolicy implements Serializable {

    private static final long serialVersionUID = -715174231586629181L;
    /**
     * 保单id
     */
    private String policyId;
    /**
     * 保单类型1-交强单 2-商业单
     */
    private String policyType;
    /**
     * 原保单id
     */
    private String oldPolicyId;
    /**
     * 保单号码
     */
    private String policyNumber;
    /**
     * 批单号码
     */
    private String endorsementNumber;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
}
