/**
  * Copyright 2021 json.cn 
  */
package com.tuns.car.core.dto.gi.msb;
import java.util.Date;

/**
 * Auto-generated: 2021-11-01 15:12:57
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class CarInfoVo {

    private String carGroupName;
    private String carModelRiskLevel;
    private String carOwnerName;
    private String carSpreading;
    private String carType;
    private String carUse;
    private String carVin;
    private String cerNo;
    private String cerType;
    private String changeRegisterDate;
    private String createEpolicy;
    private String createInvoice;
    private String customerLongTermValueScore;
    private String emptyWeight;
    private String energyType;
    private String engineCapacity;
    private String engineNo;
    private String holderTelphone;
    private String inType;
    private String industryActualPrice;
    private String invoiceType;
    private String isCarLoan;
    private String isEApplication;
    private String lawsuits;
    private String modelType;
    private String negotiatedValue;
    private String newCarPrice;
    private String newFamilyGrade;
    private String oldCustomerFlag;
    private String ownerNature;
    private String palteColor;
    private String plateNO;
    private String plateType;
    private Date quotationTime;
    private Date registDate;
    private String relationship;
    private String riskLevel;
    private String salesPerson;
    private String seatCount;
    private String secretCertNo;
    private String secretHolderTelphone;
    private String sendEpolicy;
    private String status;
    private String tpyRiskflagName;
    private String trafficModelType;
    private String uniformInsurance;
    private String useProperties;
    private String vehicleRegisterAddress;
    private String wyCarType;
    public void setCarGroupName(String carGroupName) {
         this.carGroupName = carGroupName;
     }
     public String getCarGroupName() {
         return carGroupName;
     }

    public void setCarModelRiskLevel(String carModelRiskLevel) {
         this.carModelRiskLevel = carModelRiskLevel;
     }
     public String getCarModelRiskLevel() {
         return carModelRiskLevel;
     }

    public void setCarOwnerName(String carOwnerName) {
         this.carOwnerName = carOwnerName;
     }
     public String getCarOwnerName() {
         return carOwnerName;
     }

    public void setCarSpreading(String carSpreading) {
         this.carSpreading = carSpreading;
     }
     public String getCarSpreading() {
         return carSpreading;
     }

    public void setCarType(String carType) {
         this.carType = carType;
     }
     public String getCarType() {
         return carType;
     }

    public void setCarUse(String carUse) {
         this.carUse = carUse;
     }
     public String getCarUse() {
         return carUse;
     }

    public void setCarVin(String carVin) {
         this.carVin = carVin;
     }
     public String getCarVin() {
         return carVin;
     }

    public void setCerNo(String cerNo) {
         this.cerNo = cerNo;
     }
     public String getCerNo() {
         return cerNo;
     }

    public void setCerType(String cerType) {
         this.cerType = cerType;
     }
     public String getCerType() {
         return cerType;
     }

    public void setChangeRegisterDate(String changeRegisterDate) {
         this.changeRegisterDate = changeRegisterDate;
     }
     public String getChangeRegisterDate() {
         return changeRegisterDate;
     }

    public void setCreateEpolicy(String createEpolicy) {
         this.createEpolicy = createEpolicy;
     }
     public String getCreateEpolicy() {
         return createEpolicy;
     }

    public void setCreateInvoice(String createInvoice) {
         this.createInvoice = createInvoice;
     }
     public String getCreateInvoice() {
         return createInvoice;
     }

    public void setCustomerLongTermValueScore(String customerLongTermValueScore) {
         this.customerLongTermValueScore = customerLongTermValueScore;
     }
     public String getCustomerLongTermValueScore() {
         return customerLongTermValueScore;
     }

    public void setEmptyWeight(String emptyWeight) {
         this.emptyWeight = emptyWeight;
     }
     public String getEmptyWeight() {
         return emptyWeight;
     }

    public void setEnergyType(String energyType) {
         this.energyType = energyType;
     }
     public String getEnergyType() {
         return energyType;
     }

    public void setEngineCapacity(String engineCapacity) {
         this.engineCapacity = engineCapacity;
     }
     public String getEngineCapacity() {
         return engineCapacity;
     }

    public void setEngineNo(String engineNo) {
         this.engineNo = engineNo;
     }
     public String getEngineNo() {
         return engineNo;
     }

    public void setHolderTelphone(String holderTelphone) {
         this.holderTelphone = holderTelphone;
     }
     public String getHolderTelphone() {
         return holderTelphone;
     }

    public void setInType(String inType) {
         this.inType = inType;
     }
     public String getInType() {
         return inType;
     }

    public void setIndustryActualPrice(String industryActualPrice) {
         this.industryActualPrice = industryActualPrice;
     }
     public String getIndustryActualPrice() {
         return industryActualPrice;
     }

    public void setInvoiceType(String invoiceType) {
         this.invoiceType = invoiceType;
     }
     public String getInvoiceType() {
         return invoiceType;
     }

    public void setIsCarLoan(String isCarLoan) {
         this.isCarLoan = isCarLoan;
     }
     public String getIsCarLoan() {
         return isCarLoan;
     }

    public void setIsEApplication(String isEApplication) {
         this.isEApplication = isEApplication;
     }
     public String getIsEApplication() {
         return isEApplication;
     }

    public void setLawsuits(String lawsuits) {
         this.lawsuits = lawsuits;
     }
     public String getLawsuits() {
         return lawsuits;
     }

    public void setModelType(String modelType) {
         this.modelType = modelType;
     }
     public String getModelType() {
         return modelType;
     }

    public void setNegotiatedValue(String negotiatedValue) {
         this.negotiatedValue = negotiatedValue;
     }
     public String getNegotiatedValue() {
         return negotiatedValue;
     }

    public void setNewCarPrice(String newCarPrice) {
         this.newCarPrice = newCarPrice;
     }
     public String getNewCarPrice() {
         return newCarPrice;
     }

    public void setNewFamilyGrade(String newFamilyGrade) {
         this.newFamilyGrade = newFamilyGrade;
     }
     public String getNewFamilyGrade() {
         return newFamilyGrade;
     }

    public void setOldCustomerFlag(String oldCustomerFlag) {
         this.oldCustomerFlag = oldCustomerFlag;
     }
     public String getOldCustomerFlag() {
         return oldCustomerFlag;
     }

    public void setOwnerNature(String ownerNature) {
         this.ownerNature = ownerNature;
     }
     public String getOwnerNature() {
         return ownerNature;
     }

    public void setPalteColor(String palteColor) {
         this.palteColor = palteColor;
     }
     public String getPalteColor() {
         return palteColor;
     }

    public void setPlateNO(String plateNO) {
         this.plateNO = plateNO;
     }
     public String getPlateNO() {
         return plateNO;
     }

    public void setPlateType(String plateType) {
         this.plateType = plateType;
     }
     public String getPlateType() {
         return plateType;
     }

    public void setQuotationTime(Date quotationTime) {
         this.quotationTime = quotationTime;
     }
     public Date getQuotationTime() {
         return quotationTime;
     }

    public void setRegistDate(Date registDate) {
         this.registDate = registDate;
     }
     public Date getRegistDate() {
         return registDate;
     }

    public void setRelationship(String relationship) {
         this.relationship = relationship;
     }
     public String getRelationship() {
         return relationship;
     }

    public void setRiskLevel(String riskLevel) {
         this.riskLevel = riskLevel;
     }
     public String getRiskLevel() {
         return riskLevel;
     }

    public void setSalesPerson(String salesPerson) {
         this.salesPerson = salesPerson;
     }
     public String getSalesPerson() {
         return salesPerson;
     }

    public void setSeatCount(String seatCount) {
         this.seatCount = seatCount;
     }
     public String getSeatCount() {
         return seatCount;
     }

    public void setSecretCertNo(String secretCertNo) {
         this.secretCertNo = secretCertNo;
     }
     public String getSecretCertNo() {
         return secretCertNo;
     }

    public void setSecretHolderTelphone(String secretHolderTelphone) {
         this.secretHolderTelphone = secretHolderTelphone;
     }
     public String getSecretHolderTelphone() {
         return secretHolderTelphone;
     }

    public void setSendEpolicy(String sendEpolicy) {
         this.sendEpolicy = sendEpolicy;
     }
     public String getSendEpolicy() {
         return sendEpolicy;
     }

    public void setStatus(String status) {
         this.status = status;
     }
     public String getStatus() {
         return status;
     }

    public void setTpyRiskflagName(String tpyRiskflagName) {
         this.tpyRiskflagName = tpyRiskflagName;
     }
     public String getTpyRiskflagName() {
         return tpyRiskflagName;
     }

    public void setTrafficModelType(String trafficModelType) {
         this.trafficModelType = trafficModelType;
     }
     public String getTrafficModelType() {
         return trafficModelType;
     }

    public void setUniformInsurance(String uniformInsurance) {
         this.uniformInsurance = uniformInsurance;
     }
     public String getUniformInsurance() {
         return uniformInsurance;
     }

    public void setUseProperties(String useProperties) {
         this.useProperties = useProperties;
     }
     public String getUseProperties() {
         return useProperties;
     }

    public void setVehicleRegisterAddress(String vehicleRegisterAddress) {
         this.vehicleRegisterAddress = vehicleRegisterAddress;
     }
     public String getVehicleRegisterAddress() {
         return vehicleRegisterAddress;
     }

    public void setWyCarType(String wyCarType) {
         this.wyCarType = wyCarType;
     }
     public String getWyCarType() {
         return wyCarType;
     }

}