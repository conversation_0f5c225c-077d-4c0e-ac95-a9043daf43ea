package com.tuns.car.core.dto.car;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.io.Serializable;

/**
 * 验车人
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/3 9:13
 */
@Data
@RequiredArgsConstructor
public class CarInspectorDTO implements Serializable {

    private static final long serialVersionUID = -771585477181420701L;
    @ApiModelProperty(value = "渠道ID")
    private Integer chanDetailId;

    @ApiModelProperty("验车人")
    private String carInspector;

    @ApiModelProperty("验车人编码")
    private String carInspectorCode;

    public CarInspectorDTO(String carInspector){
        this.carInspector = carInspector;
    }

}
