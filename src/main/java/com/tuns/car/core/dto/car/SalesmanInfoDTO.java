package com.tuns.car.core.dto.car;/**
 * <AUTHOR>
 * @date 2020/6/5
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName SalesmanInfoDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/5 18:10
 * @Version 1.0
 */
@Data
@ApiModel(value = "SalesmanInfoDTO", description = "业务员信息")
public class SalesmanInfoDTO implements Serializable {

    private static final long serialVersionUID = 1882045189620439533L;

    /**
     * 业务员ID
     */
    @ApiModelProperty(value = "业务员ID", required = true)
    private String salesmanId;

    /**
     * 本公司组织机构代码
     */
    @ApiModelProperty(value = "本公司组织机构代码", required = false)
    private String departCode;

    /**
     * 是否内勤
     */
    @ApiModelProperty(value = "是否内勤", required = false)
    private String staffMark;

    /**
     * 账号等级
     */
    @ApiModelProperty(value = "账号等级", required = false)
    private String acctLevel;

    /**
     * 是否业务员
     */
    @ApiModelProperty(value = "是否业务员", required = false)
    private String salesmanMark;

    @ApiModelProperty(value = "真实姓名", required = false)
    private String realName;

    /**
     * 模糊查询 车牌号、车主姓名
     */
    @ApiModelProperty(value = "模糊查询 车牌号、车主姓名", required = false)
    private String keyword;

}
