package com.tuns.car.core.dto.gi;

import java.io.Serializable;

public class GiOrderCustVO implements Serializable{

    /**
	 * 
	 */
	private static final long serialVersionUID = -2994617276664975628L;

	/**
     * 订单号
     */
    private String orderNo;

    /**
     * 客户ID，关联客户表
     */
    private Integer custId;

    /**
     * 客户类型，1个人，2团体
     */
    private String custType;

    /**
     * 被保人/投保人  02被保人, 03 投保人,  04受益人, 05收件人
     */
    private String insurantType;

    /**
     * 组织机构代码，针对投保人是团体客户
     */
    private String organCode;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 投保单位
     */
    private String singleApplicantOrgan; // 投保单位

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 证件类型
     */
    private String certType;

    /**
     * 证件号码
     */
    private String certNo;

    /**
     * 性别
     */
    private String sex;

    /**
     * 年龄
     */
    private Short age;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 通讯地址
     */
    private String address;

    /**
     * 与投保人关系 01 本人 40子女 50父母 10配偶 99其他
     * 与投保人关系
     */
    private String relationship;

    /**
     * 行业代码，针对投保人是团体客户
     */
    private String insdustryCode;

    /**
     * 社保标识，1有，0无
     */
    private String socialSecurity;

    /**
     * 职业代码
     */
    private String profCode;

    /**
     * 与被监护人关系
     */
    private String bjhrrelation;

    /**
     * 监护类型
     */
    private String pupillusType;

    /**
     * 职业名称
     */
    private String profName;

    /**
     * 职业类别
     */
    private String profLevel;

    /**
     * 投保分数
     */
    private Integer copies;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getCustId() {
        return custId;
    }

    public void setCustId(Integer custId) {
        this.custId = custId;
    }

    public String getCustType() {
        return custType;
    }

    public void setCustType(String custType) {
        this.custType = custType;
    }

    public String getInsurantType() {
        return insurantType;
    }

    public void setInsurantType(String insurantType) {
        this.insurantType = insurantType;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getSingleApplicantOrgan() {
        return singleApplicantOrgan;
    }

    public void setSingleApplicantOrgan(String singleApplicantOrgan) {
        this.singleApplicantOrgan = singleApplicantOrgan;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Short getAge() {
        return age;
    }

    public void setAge(Short age) {
        this.age = age;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRelationship() {
        return relationship;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship;
    }

    public String getInsdustryCode() {
        return insdustryCode;
    }

    public void setInsdustryCode(String insdustryCode) {
        this.insdustryCode = insdustryCode;
    }

    public String getSocialSecurity() {
        return socialSecurity;
    }

    public void setSocialSecurity(String socialSecurity) {
        this.socialSecurity = socialSecurity;
    }

    public String getProfCode() {
        return profCode;
    }

    public void setProfCode(String profCode) {
        this.profCode = profCode;
    }

    public String getBjhrrelation() {
        return bjhrrelation;
    }

    public void setBjhrrelation(String bjhrrelation) {
        this.bjhrrelation = bjhrrelation;
    }

    public String getPupillusType() {
        return pupillusType;
    }

    public void setPupillusType(String pupillusType) {
        this.pupillusType = pupillusType;
    }

    public String getProfName() {
        return profName;
    }

    public void setProfName(String profName) {
        this.profName = profName;
    }

    public String getProfLevel() {
        return profLevel;
    }

    public void setProfLevel(String profLevel) {
        this.profLevel = profLevel;
    }

    public Integer getCopies() {
        return copies;
    }

    public void setCopies(Integer copies) {
        this.copies = copies;
    }
}