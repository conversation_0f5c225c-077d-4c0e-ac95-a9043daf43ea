package com.tuns.car.core.dto.carprocess.premium.request;

import com.tuns.car.core.constant.CheckCarStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

/***
 * 验车情况
 * ClassName: CheckCarInfoDTO
 * Description:
 * Create Time: 2022/3/1 15:57
 *
 * <AUTHOR>
 */
@Data
public class CheckCarInfoDTO implements Serializable {

    private static final long serialVersionUID = -3081443408069761686L;

    @ApiModelProperty(value = "验车状态 1已验 2免验 申能（3未验车 4 待补充） 8补验车")
    private CheckCarStatusEnum checkCarStatus;
    @ApiModelProperty(value = "免验车原因 1单保责任险 2按期续保，且未加保损失类险种 3新车 4团车业务 5符合免验规定 申能（6低险车风险）7 机关车，团车 9 其他原因 ")
    private Integer exemptionReason;
    @ApiModelProperty(value = "验车人")
    private String carInspector;
    @ApiModelProperty(value = "验车时间 yyyy-MM-dd")
    private String inspectionTime;
    @ApiModelProperty("具体原因")
    @Size(max = 200, message = "【具体原因】长度限制不能超过200")
    private String carCheckReasonStr;
    @ApiModelProperty(value = "破损位置",example = "01,02")
    private String damageLocation;
    /**
     * 验车结果
     */
    @ApiModelProperty(value = "验车结果 1 全车完好无损，证车相符，年审合格 2不合格 3车辆带损投保，带照片 9 其他")
    private String inspectionResult;
}
