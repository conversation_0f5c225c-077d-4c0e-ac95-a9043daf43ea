package com.tuns.car.core.dto.cpic.session;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @ClassName CPICUser
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/02/15 12:13:44
 * @Version 1.0
 */
@Data
public class CPICUser {
    public AtomicBoolean success = new AtomicBoolean(false);

    private String jessionId;

    private String route;

    private PartnerSelect selectVO;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
