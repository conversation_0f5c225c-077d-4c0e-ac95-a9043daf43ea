package com.tuns.car.core.dto.car;

import com.tuns.car.core.constant.group.ChannelSelectGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;

/**
 * 核心出单 渠道信息数据结构
 *
 * <AUTHOR>
 * @since 2021/8/27
 */
@Data
public class IssueOrderChanInfoDTO implements Serializable {

    private static final long serialVersionUID = -122327539182996006L;
    @ApiModelProperty(value = "保险公司ID（第一次车型查询可无,报价的时候车型查询必传）")
    @NotBlank(groups = {Default.class, ChannelSelectGroup.MANUAL.class})
    private String companyCode;

    @ApiModelProperty(value = "保险公司ID")
    @NotBlank(groups = {Default.class, ChannelSelectGroup.MANUAL.class})
    private String companyId;

    @ApiModelProperty(value = "出单渠道id")
    @NotNull(groups = {Default.class, ChannelSelectGroup.MANUAL.class})
    private Integer chanDetailId;
}
