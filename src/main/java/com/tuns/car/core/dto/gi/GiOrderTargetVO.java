package com.tuns.car.core.dto.gi;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.constant.CarTypeEnum;
import com.tuns.car.core.constant.UsingNatureEnum;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class GiOrderTargetVO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -7672443022740567953L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 是否是新车:0 否 1是
     */
    private String newoldcar;

    /**
     * 车辆种类
     */
    private String carCategory;

    /**
     * 车牌号码
     */
    private String plateNumber;

    /**
     * 车架号
     */
    private String frameNumber;

    /**
     * 发动机号码
     */
    private String engineNumber;

    /**
     * 交通工具种类
     */
    private String transCategory;

    /**
     * 核定座位数
     */
    private Short seatCount;

    /**
     * 使用性质
     */
    private String useNature;

    /**
     * 营运性质 0-非营运 1-营运
     */
    private String operationNature;

    /**
     * 行驶证车主
     */
    private String driverName;

    /**
     * 厂牌型号
     */
    private String brandModel;

    /**
     * 团体证件类型
     */
    private String groupIdentifyType;

    /**
     * 证件号码
     */
    private String identifyNumber;

    /**
     * 单位地址
     */
    private String organizationAddr;

    /**
     * 卫生许可证号
     */
    private String heahlicno;

    /**
     * 所在地区
     */
    private String hunanProvince;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 车牌底色
     */
    private String plateNumberBaseColor;

    /**
     * 自定义销售编码
     */
    private String specificSalesCode;

    /**
     * 车商合作代码
     */
    private String carBusiCoopCode;

    /**
     * 车牌底色
     */
    private String carNature;
    /**
     * 车辆类型
     */
    private CarTypeEnum carTypeCode;

    /**
     * 车辆种类
     */
    private UsingNatureEnum usingNature;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}