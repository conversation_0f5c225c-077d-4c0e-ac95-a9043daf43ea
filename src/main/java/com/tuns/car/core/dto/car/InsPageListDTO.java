package com.tuns.car.core.dto.car;


import com.tuns.car.core.dto.gi.CarSameSaleGiOrderInfoVO;
import com.tuns.car.core.dto.gi.CarSameSaleGiPolicyInfoVO;
import com.tuns.car.core.dto.gi.ComsViInsSpecialAgreementVO;
import com.tuns.car.core.dto.grab.InsDetailDTO;
import com.tuns.car.core.dto.vi.ViInsChargingPileInfoVO;
import com.tuns.car.core.dto.vi.ViInsScoreCoefficientDTO;
import com.tuns.car.core.entity.PubPersInf;
import com.tuns.car.core.vo.car.ViInsAttachInfVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@ApiModel
@Data
public class InsPageListDTO {
    @ApiModelProperty("交强保单数据信息")
    private InsDetailDTO compulsoryIns;
    @ApiModelProperty("商业保单数据信息")
    private InsDetailDTO businessIns;
    @ApiModelProperty("车船税数据信息")
    private InsDetailDTO vehicleIns;
    @ApiModelProperty("车辆信息")
    private InsCarInfDTO insCarInf;
    @ApiModelProperty("关系人数据")
    private List<PubPersInf> personList;
    @ApiModelProperty("影像附件数据")
    private List<ViInsAttachInfVO> attachList;
    @ApiModelProperty("交强原保单数据信息")
    private InsFormerDTO compulsoryFormer;
    @ApiModelProperty("商业原保单数据信息")
    private InsFormerDTO businessFormer;
    @ApiModelProperty("审核操作信息")
    private List<InsCheckInfoDTO> checkInfo;
    @ApiModelProperty("变更关系人数据")
    private Map<String, Object[]> mdfPers;
    @ApiModelProperty("非车险信息")
    private List<CarSameSaleGiOrderInfoVO> noCarOrderList;
    @ApiModelProperty("非车险信息")
    private List<CarSameSaleGiPolicyInfoVO> noCarPolicyList;
    @ApiModelProperty("充电桩信息")
    private List<ViInsChargingPileInfoVO> chargingPileList;
    @ApiModelProperty("车险评分及系数(目前只有车慧达分档)")
    private ViInsScoreCoefficientDTO viInsScoreCoefficientDTO;
    @ApiModelProperty("特约信息")
    private List<ComsViInsSpecialAgreementVO> specialAgreementDtoList;
    @ApiModelProperty("开票信息")
    private ViInsPlcyCustomInvoiceDTO viInsPlcyCustomInvoiceDTO;
}
