package com.tuns.car.core.dto.gi;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class GiProjectLiab{
    /**
     * 产品ID
     */


    private Integer prodId;

    /**
     * 计划ID
     */

    private Integer projectId;

    /**
     * 险种ID
     */

    private Integer planId;

    /**
     * 责任ID
     */

    private Integer liabId;

    /**
     * 责任（业务）代码
     */
    private String liabBusiCode;

    /**
     * 责任（系统）代码
     */
    private String liabSysCode;

    /**
     *  保额，不保存到表
     */
    private BigDecimal sumIns;

    /**
     * 保额数组，JSON数组，定制计划用，格式：[{"value":"10","unit":"WAN","text":"10万元"}]
     */
    private String sumInsArr;

    /**
     * 最低保费（元）
     */
    private BigDecimal premMin;

    /**
     * 最高保费（元）
     */
    private BigDecimal premMax;
    /**
     * 显示顺序
     */
    private String viewOrder;

    /**
     * 展示名称
     */
    private String viewName;

    /**
     * 保障责任详情
     */
    private String protectionDetail;

    /**
     * 保额或文字说明
     */
    private String description;

    /**
     * 险种名称
     */
    private String planName;

    /**
     * 责任名称
     */
    private String liabName;

    /**
     * 主附险
     */
    private String mrFlag;

    /**
     * 险种代码
     */
    private String planCode;


    /**
     * 责任代码
     */
    private String planShortName;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}