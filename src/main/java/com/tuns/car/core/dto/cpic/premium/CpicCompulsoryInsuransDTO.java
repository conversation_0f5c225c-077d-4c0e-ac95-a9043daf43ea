package com.tuns.car.core.dto.cpic.premium;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CpicCompulsoryInsuransDTO
 * @Description 太平洋原始请求报文dto对象（交强险）
 * <AUTHOR>
 * @Date 2022/02/14 09:36:58
 * @Version 1.0
 */
@Data
public class CpicCompulsoryInsuransDTO implements Serializable {

    private Integer annualPremium;
    private Integer backAmount;
    private String businessfee;
    private String carSpreading;
    private Integer changeablefeeRate;
    private Integer cipremium;  //交强险保费
    private String compContinuedInsuredYears;
    private String compCpicScore;
    private String ecompensationRate;//交强险预期赔付率
    private Long endDate;
    private String exceedDaysCount;
    private String fleetEcompensationRate;
    private String fleetTotalEcompensationRate;
    private String insuranceQueryCode;
    private String insuredNo;
    private String jqNewCarFlag;
    private Integer lateFee;
    private Double localPureRiskPremium;
    private Integer payableAmount;
    private String performance;
    private Double policycostRate;
    private String registryNumber;
    private String secretTaxpayerNo;
    private String stBackAmount;
    private String stCipremium;
    private String stEndDate;
    private String stLateFee;
    private String stPayableAmount;
    private String stStartDate;
    private String stTaxAmount;
    private String stTaxEndDate;
    private String stTaxStartDate;
    private String stTotalPremium;
    private Integer standardPremium;
    private Long startDate;
    private Integer taxAmount;
    private String taxCustomerType;
    private String taxEndDate;
    private String taxNonlocalBureau;
    private String taxStartDate;
    private String taxTimePeriod;
    private String taxType;
    private String taxVehicleType;
    private String taxpayerName;
    private String taxpayerNo;
    private String taxpayerRecno;
    private String taxpayerType;
    private String telSalePerson;
    private String taxBureauName;
    private String deductionDueType;
    private String deductionDueCode;
    private String deductionDueProportion;
    private String reductionNo;
    private String terminationDate;
    private Integer totalPremium;
    private String trafficpoudage;
    private String vehicleClaimType;
    private String wyCarType;
    private String policyStatus;

}
