package com.tuns.car.core.dto.car;

import cn.hutool.json.JSONUtil;
import com.tuns.car.core.entity.PubPersInfTp;
import com.tuns.car.core.entity.ViInsCarInfTp;
import lombok.Data;

import java.util.List;

/**
 * User: liu.le
 * ProjectName: tuns-car
 * PackageName: com.tuns.car.core.dto.car
 * Date: 2025/6/10
 * Time: 09:57
 */
@Data
public class CarArtificialUnderwritingCodeDTO {

    private Integer id;
    /**
     * 流程流水号/报价批次号
     */
    private Long serialNumber;
    /**
     * 保单记录ID
     */
    private Long policyId;
    /**
     * 保单批次ID
     */
    private Long policyBatchId;
    /**
     * 保单号
     */
    private String policyNumber;
    /**
     * 主保单号
     */
    private String mainPolicyNumber;
    /**
     * 投保单号
     */
    private String proposalNumber;
    /**
     * 主投保单号
     */
    private String mainProposalNumber;
    /**
     * 上年保单号
     */
    private String lastPolicy;
    /**
     * 上年承保公司ID
     */
    private String lastCompanyId;
    /**
     * 上年承保公司名称
     */
    private String lastCompanyName;
    /**
     * 出单类型 1-单交强 2-单商业 3-交商同保
     */
    private String recordType;
    /**
     * 保单类型 1-交强单 2-商业单
     */
    private String policyType;
    /**
     * 保费计算识别码/保险公司订单号码
     */
    private String insPremiumNumber;

    private String companyId;

    private Integer chanDetailId;

    private String flowId;

    private String quoteId;
    /**
     * 影像资料
     */
    List<ViOfferImgDTO> viOfferImgList;
    /**
     * 查询报价关系人数据
     */
    List<PubPersInfTp> pubPerTpList;

    /**
     * 车险车辆信息数据
     */
    private ViInsCarInfTp carInfTp;
}
