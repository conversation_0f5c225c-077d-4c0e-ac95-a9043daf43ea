package com.tuns.car.core.dto.carprocess.premium;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 报价单基本信息
 *
 * <AUTHOR>
 * @date 2023/04/28
 **/
@Data
public class QuoteBaseDTO {

    @ApiModelProperty(value = "报价流水号", hidden = true)
    private Long serialNumber;

    @ApiModelProperty(value = "上一笔报价流水号")
    private String lastSerialNumber;

    @ApiModelProperty(value = "报价类型 1-单交强 2-单商业 3-交商同保")
    private String recordType;

    @ApiModelProperty(value = "场景")
    private SceneEnum scene;

    @ApiModelProperty("折扣系数模式类型,1:手动模式，2:智能模式")
    private String ratioModel;

    @ApiModelProperty(value = "期望折扣信息")
    private BigDecimal expectDiscount;

    @ApiModelProperty(value = "报价方案 1推荐方案 2续保方案")
    private String quoteScheme;

    @ApiModelProperty(value = "交强起保时间 yyyy-MM-dd HH:mm:ss")
    private String insBeginCi;

    @ApiModelProperty(value = "商业起保时间 yyyy-MM-dd HH:mm:ss")
    private String insBeginBi;

    @ApiModelProperty(value = "报价时间")
    private LocalDateTime offerTime;

    @ApiModelProperty("推荐方案id")
    private Integer recommendPlanId;
    /**
     * 核保告知
     * @return
     */
    @ApiModelProperty("核保告知")
    private String underwritingInform;

}
