package com.tuns.car.core.dto.vi;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @create 2022-04-15 16:23
 **/

public class ChargingKindDTO implements Serializable {

    private static final long serialVersionUID = 3962800220114086545L;

    @ApiModelProperty(value = "险种代码", required = true)
    private String kindCode;

    @ApiModelProperty(value = "险种保额", required = true)
    private BigDecimal amount;

    public String getKindCode() {
        return kindCode;
    }

    public void setKindCode(String kindCode) {
        this.kindCode = kindCode;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
