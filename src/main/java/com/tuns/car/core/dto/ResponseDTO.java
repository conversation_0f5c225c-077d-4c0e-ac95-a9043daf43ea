package com.tuns.car.core.dto;

import com.alibaba.fastjson.JSONObject;

/**
 * 通用返回对象类，当返回对象是数组或分页查询时，data传PageBean对象
 *
 * <AUTHOR>
 */
public class ResponseDTO<T> {

    private String code;

    private String message;

    private T data;

    public ResponseDTO(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public ResponseDTO(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public ResponseDTO(T data) {
        this.data = data;
        this.code = "0000";
        this.message = "操作成功！";
    }

    public ResponseDTO() {
        this.code = "0000";
        this.message = "操作成功！";
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <T> ResponseDTO<T> ok() {
        return new ResponseDTO<T>(null);
    }

    public static <T> ResponseDTO<T> ok(T body) {
        return new ResponseDTO<T>(body);
    }

    public static <T> ResponseDTO<T> build(String code, String message, T data) {
        return new ResponseDTO<T>(code, message, data);
    }

    @Override
    public String toString() {
        return "ResponseDTO{" + "code='" + code + '\'' + ", message='" + message + '\'' + ", data="
                + JSONObject.toJSONString(data) + '}';
    }
}
