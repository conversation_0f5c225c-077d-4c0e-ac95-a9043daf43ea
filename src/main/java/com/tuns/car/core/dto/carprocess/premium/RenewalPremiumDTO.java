package com.tuns.car.core.dto.carprocess.premium;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/2/19 17:13
 * 续保自动报价组装请求参数接口入参
 */
@Data
public class RenewalPremiumDTO implements Serializable {

    @ApiModelProperty(value = "消息ID")
    @NotBlank(message = "消息ID不能为空")
    private String messageId;

    /**
     * 保单批次ID
     */
    @NotBlank(message = "保单批次ID不能为空")
    @ApiModelProperty(value = "续保保单批次ID")
    private Long taskPolicyBatchId;

    @NotBlank(message = "保单批次ID不能为空")
    @ApiModelProperty(value = "保单批次ID")
    private Long policyBatchId;

    @NotBlank(message = "报价流水号不能为空")
    @ApiModelProperty(value = "报价流水号")
    private Long serialNumber;

    @ApiModelProperty(value = "上笔报价流水号")
    private String lastSerialNumber;

    /**
     * 出单员（续保）
     */
    @NotBlank(message = "出单员（续保）不能为空")
    @ApiModelProperty(value = "出单员（续保）")
    private Integer userId;

    /**
     * 合作伙伴（续保）
     */
    @NotBlank(message = "合作伙伴（续保）不能为空")
    @ApiModelProperty(value = "合作伙伴（续保）")
    private Integer salesmanId;

    /**
     * 保司ID
     */
    @NotBlank(message = "保司ID不能为空")
    @ApiModelProperty(value = "保司ID")
    private String companyId;

    /**
     * 出单渠道ID
     */
    @NotBlank(message = "出单渠道ID不能为空")
    @ApiModelProperty(value = "出单渠道ID")
    private Integer chanDetailId;
}
