package com.tuns.car.core.dto.gi;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.dto.NoCarPolicy;
import com.tuns.car.core.dto.car.IssueOrderChanInfoDTO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

public class QuotationCreateOrderDTO extends IssueOrderChanInfoDTO implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = -660200535101452336L;

	/**
     * 车险保单批次id
     */
    private Long carPolicyBatchId;

    /**
     * 操作人用户id
     */
    private Integer mdfUserId;

    /**
     * 车险订单状态  -4取消订单,-3核保取消,-2报价取消,-1报价失败,0报价等待,1报价成功,3核保失败,4等待核保,5核保通过,6等待支付,7支付成功,11支付失败,12保单自取,13保单配送,14订单完成
     */
    private String quotationStatus;

    /**
     * 非车订单号
     * */
    private String noCarOrderCode;

    /**
     * 车险投保单号 交商同保 交强优先
     * */
    private String proposalNo;


    @ApiModelProperty(value = "非车保单信息")
    private List<NoCarPolicy> noCarPolicyList;

    public List<NoCarPolicy> getNoCarPolicyList() {
        return noCarPolicyList;
    }

    public void setNoCarPolicyList(List<NoCarPolicy> noCarPolicyList) {
        this.noCarPolicyList = noCarPolicyList;
    }

    public String getProposalNo() {
        return proposalNo;
    }

    public void setProposalNo(String proposalNo) {
        this.proposalNo = proposalNo;
    }

    public String getNoCarOrderCode() {
        return noCarOrderCode;
    }

    public void setNoCarOrderCode(String noCarOrderCode) {
        this.noCarOrderCode = noCarOrderCode;
    }

    public Long getCarPolicyBatchId() {
        return carPolicyBatchId;
    }

    public QuotationCreateOrderDTO setCarPolicyBatchId(Long carPolicyBatchId) {
        this.carPolicyBatchId = carPolicyBatchId;
        return this;
    }

    public String getQuotationStatus() {
        return quotationStatus;
    }

    public QuotationCreateOrderDTO setQuotationStatus(String quotationStatus) {
        this.quotationStatus = quotationStatus;
        return this;
    }

    public Integer getMdfUserId() {
        return mdfUserId;
    }

    public QuotationCreateOrderDTO setMdfUserId(Integer mdfUserId) {
        this.mdfUserId = mdfUserId;
        return this;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
