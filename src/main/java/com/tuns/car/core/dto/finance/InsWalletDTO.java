package com.tuns.car.core.dto.finance;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/19 11:20
 */
@Data
public class InsWalletDTO implements Serializable {
    private static final long serialVersionUID = 6940874744930154701L;

    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 批次id
     */
    private String policyBatchId;
    /**
     * 对冲保单id
     */
    private String hedgePolicyId;
    /**
     * 车牌号码
     */
    private String plateNumber;
    /**
     * 投保人
     */
    private String personName;
    /**
     * 投保公司
     */
    private String companyName;
    /**
     * 流水状态：1-冻结中，2-可提现，7-冲账，8-已撤销佣金
     */
    private String status;
    /**
     * 状态：1-冻结中，2-可提现，7-冲账，8-已撤销佣金
     */
    private String walletStatus;
    /**
     * 交易类型：1-收入，2-提现，3-冲账
     */
    private String transType;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 操作类型：1-保单提交，2-保单审核通过,3-保单冲账
     */
    private String operType;

    private InsPolicy insPolicyCi;

    private InsPolicy insPolicyBus;



}
