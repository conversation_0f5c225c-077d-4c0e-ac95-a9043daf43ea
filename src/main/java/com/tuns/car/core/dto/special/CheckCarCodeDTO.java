package com.tuns.car.core.dto.special;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.constant.InsEnum;
import com.tuns.core.boot.constant.YesNoEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/1 17:12
 */
@Data
public class CheckCarCodeDTO implements Serializable {

    private static final long serialVersionUID = 4579684969821966928L;
    @ApiModelProperty(value = "验车码")
    private String checkCarCode;
    @ApiModelProperty(value = "保险公司代码")
    private String companyCode;
    @ApiModelProperty("出单渠道id")
    private Integer chanDetailId;
    @ApiModelProperty("保险公司")
    private InsEnum company;
    @ApiModelProperty("失效时长(秒)")
    private Long expireSeconds;
    @ApiModelProperty(value = "是否智能验车")
    private YesNoEnum autoCheckCar;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
