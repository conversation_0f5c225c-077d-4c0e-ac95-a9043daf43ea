package com.tuns.car.core.dto.carprocess.premium;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.constant.PolicyTypeEnum;
import com.tuns.car.core.constant.ViAttachTypeEnum;
import com.tuns.car.core.entity.ViInsAttachInf;
import com.tuns.car.core.entity.ViInsPlcyInf;
import feign.Request;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.activation.MimeType;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/19 17:33
 */
@Data
@Accessors(chain = true)
public class EpolicyDTO implements Serializable {

    private static final long serialVersionUID = 4336689307411510043L;

    /**
     * 保险公司电子保单下载路径
     */
    private String insUrl;
    /**
     * 某些公司可能返回base64图片例众诚
     */
    private String base64;

    /**
     * 大家保险直接返回的文件流
     */
    private byte[] inputStream;
    /**
     * 请求方法
     */
    private Request.HttpMethod method = Request.HttpMethod.GET;

    /**
     * 请求体
     */
    private Map<String,Object> body;

    /**
     * 我方文件服务器的下载路径
     */
    private String ownUrl;

    /**
     * 我方文件服务器的文件key
     */
    private String ownOssKey;

    /**
     * 电子保单类型
     */
    private ViAttachTypeEnum type;

    /**
     * 保单号
     */
    private String policyNumber;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 渠道id
     */
    private Integer chanDetailId;

    /**
     * 批次id
     */
    private Long policyBatchId;

    /**
     * 文件格式
     */
    @ApiModelProperty(hidden = true)
    private MimeType mimeType;

    /**
     * 附件文件名
     */
    @ApiModelProperty(value = "附件文件名")
    private String fileName;

    public EpolicyDTO() {
    }

    public EpolicyDTO(ViInsAttachInf attachInf) {
        this.setPolicyBatchId(attachInf.getSerialNumber());
        this.setType(attachInf.getType());
        this.setFileName(attachInf.getFileName());
    }

    public EpolicyDTO(ViInsPlcyInf viInsPlcyInfTp, String url) {
        this.setInsUrl(url);
        this.setCompanyId(viInsPlcyInfTp.getCompanyId());
        this.setPolicyBatchId(viInsPlcyInfTp.getPolicyBatchId());
        this.setChanDetailId(viInsPlcyInfTp.getChanDetailId());
        if (PolicyTypeEnum.CI.getValue().equals(viInsPlcyInfTp.getPolicyType())) {
            this.setType(ViAttachTypeEnum.CIINSURANCE);
        } else {
            this.setType(ViAttachTypeEnum.BIINSURANCE);
        }
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
