package com.tuns.car.core.dto.renewal.premium;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @authon xupengcheng
 * @Date 2025/2/24 14:38
 */
@Data
public class RenewalAutoPremiumDTO implements Serializable {
    @ApiModelProperty(value = "车架号")
    @NotBlank(message = "车架号不能为空")
    private String frameNumber;

    @ApiModelProperty(value = "出单员ID")
    @NotNull(message = "用户Id不能为空")
    private Integer userId;
}
