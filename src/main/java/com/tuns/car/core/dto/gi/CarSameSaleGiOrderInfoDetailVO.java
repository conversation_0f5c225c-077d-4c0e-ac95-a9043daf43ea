package com.tuns.car.core.dto.gi;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class CarSameSaleGiOrderInfoDetailVO extends CarSameSaleGiProductInfoDetailVO {

    /**
     *
     */
    private static final long serialVersionUID = 8250527083249485513L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 保费
     */
    private BigDecimal prem;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付截止时间
     */
    private Date payExpireTime;

    /**
     * 佣金费率
     */
    private BigDecimal commRate;

    /**
     * 佣金
     */
    private BigDecimal commFee;

    /**
     * 业务员对应的用户ID
     */
    private Integer salesmanId;

    /**
     * 起保时间
     */
    private Date startTime;

    /**
     * 终保时间
     */
    private Date endTime;

    /**
     * 投保时间
     */
    private Date insureTime;

    /**
     * 份数
     */
    private Short copies;

    /**
     * 保险公司ID
     */
    private String companyId;

    /**
     * 合作渠道ID
     */
    private String channelId;

    /**
     * 业务员对应的分支机构代码
     */
    private String departCode;

    /**
     * 投保单号
     */
    private String proposalNo;

    /**
     * 保险公司提供的订单号
     */
    private String insOrderNo;

    /**
     * 支付链接
     */
    private String payUrl;

    /**
     * 保障期限
     */
    private String period;
    /**
     * 净保费
     */
    private BigDecimal premium;

    /**
     * 支付相关参数
     */
    private String payRequest;

    /**
     * 0单人 1多人
     */
    private String insuredOptions;

    /**
     * 回溯业务编号
     */
    private String snapshotUniqueid;

    /**
     * 是否车险兼售 1是 0否
     */
    private String isCarSameSale;

    /**
     * 保单号--车险兼售填写的保单号
     */
    private String policyNumber;
    /**
     * 车险保单批次id
     */
    private Long carPolicyBatchId;

    /**
     * 报价之前前端生成的业务id
     */
    private String serialNumber;

    /**
     * 车险订单状态  -4取消订单,-3核保取消,-2报价取消,-1报价失败,0报价等待,1报价成功,3核保失败,4等待核保,5核保通过,6等待支付,7支付成功,11支付失败,12保单自取,13保单配送,14订单完成
     */
    private String quotationStatus;

    /**
     * 订单 投/被保险人
     */
    private List<GiOrderCustVO> orderCustVOS;

    /**
     * 非车险订单标的信息
     */
    private GiOrderTargetVO orderTargetVO;

    /**
     * 非车险计划对应的保障利益表（app展示设置）
     */
    private List<GiProjectViewSetVO> viewSetVOS;

    /**
     * 非车计划责任信息（计划基础信息）
     */
    private List<GiProjectLiab> giProjectLiabs;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
