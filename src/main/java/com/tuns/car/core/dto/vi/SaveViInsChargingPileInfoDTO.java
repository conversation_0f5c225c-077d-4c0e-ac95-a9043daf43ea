package com.tuns.car.core.dto.vi;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.tuns.car.core.config.CustomStringToLocalDateDeserializer;
import com.tuns.car.core.constant.ChargingAddrTypeEnum;
import com.tuns.car.core.constant.ChargingTypeEnum;
import com.tuns.car.core.constant.ChargingUseYearsEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

/**
 * 新增车险充电桩，请求数据结构
 *
 * <AUTHOR>
 * @since 2021/12/22
 */
@ApiModel
@Data
public class SaveViInsChargingPileInfoDTO {

    @ApiModelProperty(value = "数据id,传入则修改该id对应的数据，不进行删除新增。", required = false)
    private Integer id;

    @NotBlank(message = "充电桩型号不能为空")
    @Size(max = 32, message = "充电桩型号最大长度不能超过32个字符")
    @ApiModelProperty(value = "充电桩型号", required = true)
    private String chargingModel;

    @NotBlank
    @Size(max = 32, message = "充电桩编码最大长度不能超过32个字符")
    @ApiModelProperty(value = "充电桩编码", required = true)
    private String chargingCode;

    @NotNull
    @ApiModelProperty(value = "充电桩类型", required = true)
    private ChargingTypeEnum chargingType;

    @NotNull
    @ApiModelProperty(value = "充电桩安装地点类型", required = true)
    private ChargingAddrTypeEnum chargingInstallAddrType;

    @NotNull
    @ApiModelProperty(value = "充电桩使用年限", required = true)
    private ChargingUseYearsEnum chargingUseYears;

    @NotBlank
    @Size(max = 64)
    @ApiModelProperty(value = "充电桩详细地址", required = true)
    private String chargingAddrDetail;
    /**
     * 安装时间
     */
    @ApiModelProperty(value = "充电桩安装时间", required = true)
    @JsonDeserialize(using = CustomStringToLocalDateDeserializer.class)
    private LocalDate chargingInstallDate;

    @NotBlank
    @ApiModelProperty(value = "省代码", required = true)
    private String province;

    @NotBlank
    @ApiModelProperty(value = "市代码", required = true)
    private String city;

    @NotBlank
    @ApiModelProperty(value = "区县代码", required = true)
    private String county;

    @ApiModelProperty("充电桩险种信息")
    private List<ChargingKindDTO> chargingKindDTOList;



}
