package com.tuns.car.core.dto.template;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模版自检所需信息
 *
 * <AUTHOR>
 * @since 2023-9-8
 */
@Data
@NoArgsConstructor
public class TemplateFunctionTestDTO {

    /**
     * 保险公司ID
     */
    private String companyId;
    /**
     * 待修改的特征值ID
     */
    private Integer regexId;
    /**
     * 本次修改的特征值内容
     */
    private String updateContent;

    public TemplateFunctionTestDTO(String companyId, Integer regexId, String updateContent) {
        this.companyId = companyId;
        this.regexId = regexId;
        this.updateContent = updateContent;
    }
}
