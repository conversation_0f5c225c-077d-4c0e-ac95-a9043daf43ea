package com.tuns.car.core.dto.car;

import cn.hutool.core.bean.BeanUtil;
import com.tuns.car.core.constant.RatioConfigFactorEnum;
import com.tuns.car.core.dto.carprocess.premium.PremiumCaculateDTO;
import com.tuns.car.core.entity.ViInsKindDetialTp;
import com.tuns.core.boot.constant.YesNoEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 保费试算结果dto
 *
 * <AUTHOR>
 * @since 2021/11/22
 */
@Data
@NoArgsConstructor
public class PremiumCalculateResultDTO extends PremiumCaculateDTO {

    private static final long serialVersionUID = 8339589021910563778L;

    /**
     * 保费过程产生的需要复查的异常信息
     */
    private String reviewMsg;

    /***
     * 保费计算原始响应报文
     */
    private String extendJson;

    /**
     * 非车信息
     */
    private AddNoCarDTO addNoCarDTO;

    /**
     * 总保费
     */
    private BigDecimal totalPremium;

    /**
     * 报价明细
     */
    private List<ViInsKindDetialTp> viInsKindDetialTps;

    /**
     * 系数调节失败
     */
    private Boolean ratioAdjustFail;

    /**
     * 调节系数
     */
    private RatioConfigFactorEnum ratio;

    /**
     * 是否是智能验车
     */
    private YesNoEnum autoCheckCar;

    /**
     * 方案id
     */
    private Integer planId;

    /**
     * 系数调整是否达标
     * 0：不达标，1：达标
     */
    private String scoreStandard;

    public PremiumCalculateResultDTO(PremiumCaculateDTO premiumCaculateDTO) {
        BeanUtil.copyProperties(premiumCaculateDTO, this);
    }
}
