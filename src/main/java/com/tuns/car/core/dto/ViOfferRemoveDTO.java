package com.tuns.car.core.dto;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.dto.car.ViOfferImgDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ViOfferAppendDTO
 * @Description APP车险报价单补传资料删除
 * <AUTHOR> yi
 * @Date 2020/6/8 19:09
 * @Version 1.0
 */
@Data
public class ViOfferRemoveDTO implements Serializable {

  private static final long serialVersionUID = 5475609607404318191L;

  /**
   * 报价批次id
   */
  @NotBlank(message = "报价批次id不能为空")
  @ApiModelProperty(value = "报价批次id", required = true)
  private Long policyBatchId;

  /**
   * 附件图片集合
   */
  @NotNull(message = "删除文件类型不能为空")
  @ApiModelProperty(value = "删除文件类型集合", required = true)
  private List<String> typeList;

  @Override
  public String toString() {
    return JSON.toJSONString(this);
  }

}
