package com.tuns.car.core.dto;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * @ClassName BaseDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/4 11:12
 * @Version 1.0
 */
public class BaseDTO<T> {

    /**
     * 显示数目
     */
    @ApiModelProperty(value = "显示条数", required = true, example = "10")
    @NotNull(message = "显示条数不能为空")
    private int pageSize;
    /**
     * 当前页数
     */
    @ApiModelProperty(value = "当前页数", required = true, example = "1")
    @NotNull(message = "当前页数不能为空")
    private int page;
    @ApiModelProperty(value = "最大页数", hidden = true)
    private Integer maxPageSize;

    public int getPageSize() {
        if (maxPageSize != null && pageSize > maxPageSize) {
            return maxPageSize;
        }
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public void setMaxPageSize(Integer maxPageSize) {
        this.maxPageSize = maxPageSize;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
