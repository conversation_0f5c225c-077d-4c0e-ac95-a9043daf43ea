package com.tuns.car.core.dto.gi;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CarSameSaleGiOrderInfoVO implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 9107962923258731536L;

	/**
     * 保单id
     */
    private String orderNo;

    /**
     * 保单编号
     */
    private String policyNumber;

    /**
     * 保险公司ID
     */
    private String companyId;

    /**
     * 保险公司ID
     */
    private String companyName;

    /**
     * 合作渠道ID
     */
    private String channelId;

    /**
     * 合作渠道ID
     */
    private String channelName;

    /**
     * 产品ID
     */
    private Integer prodId;

    /**
     * 产品code
     */
    private String prodCode;

    /**
     * 产品名称
     */
    private String prodName;

    /**
     * 计划ID
     */
    private Integer projectId;

    /**
     * 计划code
     */
    private String projectCode;

    /**
     * 计划名称
     */
    private String projectName;

    /**
     * 保费
     */
    private BigDecimal prem;

    /**
     * 净保费
     */
    private BigDecimal premium;


    /**
     * 起保时间
     */
    private Date startTime;

    /**
     * 终保时间
     */
    private Date endTime;

    /**
     * 份数
     */
    private Short copies;

    /**
     * （审核）状态，N未提交 P待审核  S审核成功 F审核拒绝
     */
    private String auditStatus;

    /**
     * 佣金费率
     */
    private BigDecimal commRate;

    /**
     * 佣金
     */
    private BigDecimal commFee;


    /**
     * 保险公司非车订单号
     * */
    private String insOrderNo;
    /**
     * 投保单号
     */
    private String proposalNo;

    /**
     * 保额
     */
    private BigDecimal amount;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
