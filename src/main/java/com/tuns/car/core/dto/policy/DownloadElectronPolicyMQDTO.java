package com.tuns.car.core.dto.policy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 下载电子保单信息（MQ）
 * @Author: <a href="http://www.loserzhao.com">Jo<PERSON><PERSON></a>
 * @Create: 2024-08-05 22:28
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "DownloadElectronPolicyMQDTO", description = "下载电子保单信息（MQ）")
public class DownloadElectronPolicyMQDTO implements Serializable {
    private static final long serialVersionUID = 1704949089407417755L;

    @ApiModelProperty(value = "延迟队列日志使用的主键ID")
    private Integer logId;

    @ApiModelProperty(value = "下载电子保单的保单批次号ID")
    private String policyBatchId;

}
