package com.tuns.car.core.dto.car;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 车险保单基础信息
 *
 * <AUTHOR>
 * @since 2022/4/29
 */
@Data
public class ViInsPlcyCommissionDTO {

    /**
     * 保单记录ID
     */
    @ApiModelProperty(value = "保单批次ID")
    private Long policyId;

    /**
     * 政策级别:01-总部 02-分公司 03-营业部 04-业务部
     */
    @ApiModelProperty(value = "政策级别:01-总部 02-分公司 03-营业部 04-业务部")
    private String policyLevel;

    /**
     * 佣金类型 1交强(1),2商业(1), 3车船税(1), 4单件 5,附加,6调整佣金 ,7协作费,8交强(2),9商业(2),10车船税(2)
     */
    @ApiModelProperty(value = "佣金类型 1交强(1),2商业(1), 3车船税(1), 4单件 5,附加,6调整佣金 ,7协作费,8交强(2),9商业(2),10车船税(2)")
    private String commType;

    /**
     * 不等于此类型，包含其它所有类型：佣金类型 1交强(1),2商业(1), 3车船税(1), 4单件 5,附加,6调整佣金 ,7协作费,8交强(2),9商业(2),10车船税(2)
     */
    @ApiModelProperty(value = "不等于此类型，包含其它所有类型：佣金类型 1交强(1),2商业(1), 3车船税(1), 4单件 5,附加,6调整佣金 ,7协作费,8交强(2),9商业(2),10车船税(2)")
    private String notEqualCommType;

    /**
     * 保单记录ID列表
     */
    @ApiModelProperty(value = "保单记录ID列表")
    private List<Long> policyIdList;
}
