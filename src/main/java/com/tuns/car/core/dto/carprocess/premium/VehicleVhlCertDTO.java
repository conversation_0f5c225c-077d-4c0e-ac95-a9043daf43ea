package com.tuns.car.core.dto.carprocess.premium;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 车辆来历凭证
 *
 * <AUTHOR>
 * @since 2024-9-2 16:15
 */
@Data
public class VehicleVhlCertDTO {

    /**
     * 车辆来历凭证编码
     */
    @ApiModelProperty(value = "车辆来历凭证编码")
    private String vehicleCertNo;
    /**
     * 车辆来历凭证种类
     */
    @ApiModelProperty(value = "车辆来历凭证种类")
    private String vehicleCertType;

    /**
     * 开具车辆来历凭证所载日期
     */
    @ApiModelProperty(value = "开具车辆来历凭证所载日期")
    private LocalDateTime vehicleCertDate;
}
