package com.tuns.car.core.dto.gi;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 保单数据
 * <AUTHOR>
 * @since 2022/5/6
 */
@Data
public class CarSameSalesPolicyInfoDTO {
    /**
     * 保单批次ID
     */
    private Long carPolicyBatchId;

    /**
     * 保单基本信息
     */
    private List<CarSameSaleBasePolicyInfoDTO> carSameSaleBaseInfoDTOS;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 业务员对应的用户ID
     */
    private Integer salesmanId;

    /**
     * 投保时间
     */
    private Date insureTime;

    /**
     * 业务来源不能为空 业务来源，1 app， 2内勤代出，3线下录单
     */
    private String busiFrom;

    /**
     * 机构代码
     */
    private String departCode;
    /**
     * 机构名称
     */
    private String departName;

    /**
     * 审核状态 0编辑中、1待审核、2已审核、3待修改
     */
    private String auditStatus;

    /**
     * 操作人用户id
     */
    private Integer mdfUserId;

    /**
     * 投/被保人
     */
    private List<CarSameSaleGiPolicyCustDTO> insurer;

    /**
     * 保单标的信息
     */
    private CarSameGiPolicyTargetDTO policyTargetDTO;

    /**
     * 是否为车险冲账相关操作
     */
    private boolean isReverseState;
}
