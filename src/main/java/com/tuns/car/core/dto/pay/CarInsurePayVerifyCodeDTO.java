package com.tuns.car.core.dto.pay;

import com.tuns.car.core.entity.PubPersInfTp;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @project: tuns-business
 * @description: 短信验证码获取请求DTO
 * author: Gk
 * @time: 2022-6-10 15:29
 */
@Data
@Accessors(chain = true)
public class CarInsurePayVerifyCodeDTO {
    /**
     * 关系人数据
     */
    private List<PubPersInfTp> pubPerList;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 保险公司ID
     */
    private String companyId;
}
