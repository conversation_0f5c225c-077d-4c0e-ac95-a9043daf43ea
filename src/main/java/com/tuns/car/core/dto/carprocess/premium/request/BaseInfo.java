package com.tuns.car.core.dto.carprocess.premium.request;

import com.tuns.car.core.constant.AutoMarkEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/***
 * 基础信息
 * ClassName: BaseInfo
 * Description:
 * Create Time: 2022/3/1 15:54
 *
 * <AUTHOR>
 */
@Data
public class BaseInfo implements Serializable {
    private static final long serialVersionUID = -3356927867719030191L;

    @ApiModelProperty(value = "报价类型 1-单交强 2-单商业 3-交商同保")
    private String recordType;
    @ApiModelProperty(value = "保单记录来源 1-客户端 2-内勤 3-录单 4抓取数据 5移动集成出单")
    private String recordSource;
    @ApiModelProperty(value = "省编号")
    private String provinceNumber;
    @ApiModelProperty(value = "市编号")
    private String cityNumber;
    @ApiModelProperty(value = "交强起保时间 yyyy-MM-dd HH:mm:ss")
    private String insBeginCi;
    @ApiModelProperty(value = "商业起保时间 yyyy-MM-dd HH:mm:ss")
    private String insBeginBi;
    @ApiModelProperty(value = "发票类型")
    private String invoiceMark;
    @ApiModelProperty(value = "保险公司报价单号")
    private String quotationNo;
    @ApiModelProperty(value = "报价批次id")
    private Long policyBatchId;

    @ApiModelProperty(value = "推荐方案ID")
    private String planId;

    @ApiModelProperty(value = "推荐方案ID")
    private AutoMarkEnum autoMark;
}
