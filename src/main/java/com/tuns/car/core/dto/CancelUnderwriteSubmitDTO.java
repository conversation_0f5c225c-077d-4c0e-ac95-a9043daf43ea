package com.tuns.car.core.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 供web使用的撤销核保提交dto
 *
 * <AUTHOR>
 * @since 2023-10-26 17:06
 */
@Data
public class CancelUnderwriteSubmitDTO {

    @ApiModelProperty(value = "报价批次id")
    private String policyBatchId;

    @ApiModelProperty(value = "订单UUID")
    private String orderUuid;

    @ApiModelProperty(value = "报单价OR保单标识不能为空")
    private String isOrderAndQuotation;
}
