package com.tuns.car.core.dto.car;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 车险报价政策匹配方案信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-10 15:49:41
 */
@Data
public class ViOrderQuoteMatchPlanDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private String status;
    /**
     * 保险公司ID
     */
    @ApiModelProperty(value = "保险公司ID")
    private String companyId;
    /**
     * 合作渠道ID
     */
    @ApiModelProperty(value = "合作渠道ID")
    private String channelId;

    @ApiModelProperty(value = "机构编码集合")
    private List<String> departCodeList;

    @ApiModelProperty(value = "保险公司ID")
    private List<String> companyIds;

    @ApiModelProperty(value = "合作渠道ID")
    private List<String> channelIds;

    @ApiModelProperty(value = "有效时间类型 01-支付日期 02-投保日期 03-报价日期 04-签单日期")
    private String validTimeType;

    @ApiModelProperty(value = "机构留点模板最小的启用时间")
    private LocalDateTime templateMinBeginTm;
}
