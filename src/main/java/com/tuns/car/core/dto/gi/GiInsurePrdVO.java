package com.tuns.car.core.dto.gi;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tuns.core.boot.serializer.BigDecimalTwoScaleSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class GiInsurePrdVO implements Serializable {

	private String prodName; // 产品名称

    private String prodCode;//产品编码

	private String planName; // 计划名称

    private String planCode; // 计划编码

    @JsonSerialize(using = BigDecimalTwoScaleSerializer.class)
	private BigDecimal premium; // 保费

    private String insuredSum;//保额

	private String orderNo; //订单号--非车相关

    private Short copies; //份数

    /**
     * 非车保障权益
     * */
    private GiPlanInfoVO noCarGuarantee;

}
