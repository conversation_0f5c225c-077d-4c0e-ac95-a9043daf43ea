package com.tuns.car.core.dto;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 保险公司是否存在自主定价系数
 * @ClassName RatioRuleExistsDTO
 * @Description 保险公司是否存在自主定价系数 
 * <AUTHOR>
 * @Date 2021/10/20 11:32:41 
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class RatioRuleExistsDTO implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 567463713479055463L;

    /**
     * 保险公司id
     */
    @NotBlank
    @ApiModelProperty(value = "保险公司id")
    private String companyId;

    @Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}