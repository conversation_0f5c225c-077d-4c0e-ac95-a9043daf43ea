package com.tuns.car.core.dto.carprocess.premium;

import com.tuns.car.core.dto.car.IssueOrderChanInfoDTO;
import com.tuns.car.core.dto.gi.NoCarPolicyDTO;
import com.tuns.car.core.entity.PubPersInf;
import com.tuns.car.core.entity.ViInsCarInf;
import com.tuns.car.core.entity.ViInsPlcyInf;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName CarEpolicyDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/8 16:41
 * @Version 1.0
 */
@Data
@ApiModel
@Accessors(chain = true)
public class CarEPolicyDTO extends IssueOrderChanInfoDTO {

    /**
     * 保单信息
     */
    private List<ViInsPlcyInf> viInsPlcyInfs;

    /**
     * 关系人信息
     */
    private List<PubPersInf> pubPersInfs;

    /**
     * 车辆信息
     */
    private ViInsCarInf carInf;

    /**
     * 非车保单
     */
    @ApiModelProperty(value = "非车保单信息")
    private List<NoCarPolicyDTO> noCarPolicyList;
}
