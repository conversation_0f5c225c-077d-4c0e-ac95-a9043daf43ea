package com.tuns.car.core.dto.car;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class GiOrderDetailDTO implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 6274418180606765729L;

	/**
     * 订单号
     */
    private String orderNo;

    /**
     * 保费
     */
    private BigDecimal prem;

    /**
     * 起保日期 yyyy-MM-dd HH:mm:ss
     */
    private String startTime;

    /**
     * 终保日期 yyyy-MM-dd HH:mm:ss
     */
    private String endTime;

    /***
     * 保险公司页面非车时间是否显示 默认1 显示 其他都为不显示
     *
     * */

    private String carDisplay = "1";

    /**
     * 投保单号
     */
    private String proposalNo;
    /**
     * 保额
     */
    private BigDecimal amount;

    /**
     * 非车类型 (申能需要用到)
     */
    private String type;
    /**
     * 产品名称
     */
    private String prodName;
    /**
     * 计划代码
     */
    private String projectCode;
    /**
     * 渠道id
     */
    private String channelId;
    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
