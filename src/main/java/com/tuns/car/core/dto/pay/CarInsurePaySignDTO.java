package com.tuns.car.core.dto.pay;

import com.tuns.car.core.constant.PayTypeEnum;
import com.tuns.car.core.dto.car.IssueOrderChanInfoDTO;
import com.tuns.car.core.dto.gi.CarSameSaleGiOrderInfoVO;
import com.tuns.car.core.entity.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName CarInsurePaySignDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/2 16:49
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class CarInsurePaySignDTO extends IssueOrderChanInfoDTO {

    private static final long serialVersionUID = 6529310236576537715L;
    /**
     * 投保人信息
     */
    private PubPersInfTp app;
    /**
     * 被保人信息
     */
    private PubPersInfTp insured;
    /**
     * 车主信息
     */
    private PubPersInfTp owner;

    private ViInsCarInfTp carInfo;

    private ViInsPlcyInfTp insPlcyInfPub;

    private ViInsPlcyInfTp insPlcyInfBi;

    private ViInsPlcyInfTp insPlcyInfCi;

    /**
     * 快照信息
     */
    private ViInsPlcyInfSnapshotTp snapshotTp;

    /**
     * 商业险特约(国任)
     */
    private String biPrptenages;

    /**
     * 交强险特约(国任)
     */
    private String ciPrptenages;

    /**
     * 非车订单号（国任）
     */
    private String order;
    /**
     * 支付总费用
     */
    private BigDecimal sumFee;

    /**
     * 短信验证码
     */
    private String issueCode;

    /**
     * 是否发送验证码（众诚）
     */
    private String sendIssueCode;

    /**
     * 支付方式 {@link com.tuns.car.core.constant.PayTypeEnum}
     */
    private PayTypeEnum paymentType;
    /**
     * 非车订单
     * */
    private List<CarSameSaleGiOrderInfoVO> noCarList;

    /**
     * 发票信息
     */
    private ViInsPlcyCustomInvoice viInsPlcyCustomInvoice;

    private List<ViInsPlcyInfTp> plcyInfList;

    /**
     * 订单保费（包含非车，交商保费）
     */
    private BigDecimal premium;

    private String orderUuid;

    private ViOrderInfo orderInfo;
}
