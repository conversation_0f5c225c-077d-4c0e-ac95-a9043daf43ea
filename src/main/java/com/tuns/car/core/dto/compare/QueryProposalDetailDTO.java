package com.tuns.car.core.dto.compare;

import com.tuns.car.core.entity.ViInsCarInfTp;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 查询投保单详情dto
 *
 * <AUTHOR>
 * @since 2022/8/31
 */
@Data
@Accessors(chain = true)
public class QueryProposalDetailDTO implements Serializable {
    private static final long serialVersionUID = -2555856475741914081L;
    /**
     * 公司id
     */
    private String companyId;

    /**
     * 渠道id
     */
    private Integer chanDetailId;

    /**
     * 商业险信息
     */
    private ViInsPlcyInfTp bi;

    /**
     * 交强险信息
     */
    private ViInsPlcyInfTp ci;
    /**
     * 保司报价识别码
     */
    private String insPremiumNumber;
    /**
     * 车辆信息
     */
    private ViInsCarInfTp car;

    /**
     * 车主证件号
     */
    private String ownerCerNo;

    /**
     * 被保人证件号码
     */
    private String insureCerNo;

    /**
     * 投保人证件号码
     */
    private String holderCerNo;
}
