package com.tuns.car.core.dto.carprocess.premium.request;

import com.tuns.car.core.constant.CarTypeEnum;
import com.tuns.car.core.constant.PlateNumberMarkEnum;
import com.tuns.car.core.constant.TractorTypeEnum;
import com.tuns.car.core.constant.TransferMarkTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/***
 * 车辆信息
 * ClassName: CarInfo
 * Description:
 * Create Time: 2022/3/1 15:56
 *
 * <AUTHOR>
 */
@Data
public class CarInfo implements Serializable {
    private static final long serialVersionUID = -8714200311126658758L;

    @ApiModelProperty(value = "车主性质")
    private String ownerNature;

    @ApiModelProperty(value = "车主证件类型")
    private String ownerIdentifyType;

    @ApiModelProperty(value = "车型来源公司ID")
    private String carInfoCompany;

    @ApiModelProperty(value = "验车情况")
    private String carCheckStatus;

    @ApiModelProperty(value = "营运性质 0-非营运 1-营运")
    private String operationNature;

    @ApiModelProperty(value = "使用性质 附件码表C1.使用性质/滕顺车辆种类-小类")
    private String usingNature;

    @ApiModelProperty(value = "行驶证车辆")
    private String licenseVehicleType;

    @ApiModelProperty(value = "所属性质 1-个人 2-机关 3-企业")
    private String ownershipNature;

    @ApiModelProperty(value = "外地车标致 0-本地车 1-外地车")
    private String nonlocalCarMark;

    @ApiModelProperty(value = "车辆吨位/核定载质量(吨)")
    private BigDecimal carTonnage;

    @ApiModelProperty(value = "行业车型编码")
    private String prfsModelCode;

    @ApiModelProperty(value = "是否安装GPS 0-未安装 1-安装")
    private String gpsMark;

    @ApiModelProperty(value = "家庭车辆台数")
    private Integer familyCarCount;

    @ApiModelProperty(value = "减税车型标志 0-正常 04-减免税 05-减税")
    private String taxCutsMark;

    @ApiModelProperty(value = "减税比例 0~1")
    private BigDecimal taxCutProportion;

    @ApiModelProperty(value = "车系名称")
    private String familyName;

    @ApiModelProperty(value = "公告型号")
    private String noticeType;

    @ApiModelProperty(value = "车款名称")
    private String carName;

    @ApiModelProperty(value = "车辆产地 1-进口 2-国产 3-合资")
    private String carOrigin;

    @ApiModelProperty(value = "年款 yyyyMM/yyyy")
    private String marketDate;

    @ApiModelProperty(value = "车辆类型描述")
    private String carStyleNote;

    @ApiModelProperty(value = "整备质量")
    private Integer wholeWeight;

    @ApiModelProperty(value = "贷款车辆标志 0-非贷款车 1-贷款车")
    private String loanCarMark;

    @ApiModelProperty(value = "新车购置价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "功率(瓦)")
    private Integer power;

    @ApiModelProperty(value = "排量(ML)")
    private Integer exhaustScale;

    @ApiModelProperty(value = "能源类型")
    private String fuelType;

    @ApiModelProperty(value = "核定载客")
    private Integer seatCount;

    @ApiModelProperty(value = "行驶里程 (公里/千米)")
    private Integer runMiles;

    @ApiModelProperty(value = "车型编码")
    private String modelCode;

    @ApiModelProperty(value = "车型编码Old")
    private String modelCodeOld;

    @ApiModelProperty(value = "是否上牌 0-未上牌 1-上牌")
    private PlateNumberMarkEnum plateNumberMark;

    @ApiModelProperty(value = "发动机号码")
    private String engineNumber;

    @ApiModelProperty(value = "车架号")
    private String frameNumber;

    @ApiModelProperty(value = "过户日期 yyyy-MM-dd")
    private String transferDate;

    @ApiModelProperty(value = "品牌名称", required = true)
    private String modelName;

    @ApiModelProperty(value = "车主证件号码")
    private String ownerIdentify;

    @ApiModelProperty(value = "车主")
    private String carOwner;

    @ApiModelProperty(value = "车牌号码")
    private String plateNumber;

    @ApiModelProperty(value = "注册日期 yyyy-MM-dd")
    private String regDate;

    @ApiModelProperty(value = "发证日期 yyyy-MM-dd")
    private String certDate;

    @NotBlank
    @ApiModelProperty(value = "过户标志, 1:过户 0：非过户")
    private String transferMark;

    @ApiModelProperty(value = "行政区域代码 市级")
    private String cityNumber;

    @ApiModelProperty(value = "人工报价经办人手机号")
    private String salesmanPhone;

    @ApiModelProperty(value = "扩展字段")
    private String extendField;

    @ApiModelProperty("车辆实际价值")
    private BigDecimal actualPrice;

    @ApiModelProperty("使用年限")
    private Integer useYears;

    @ApiModelProperty("新车标致 0-非新车 1-新车")
    private String newCarMark;

    @ApiModelProperty("续航里程")
    private String pureRange;

    @ApiModelProperty("其他能源描述")
    private String fuelTypeRemark;

    @ApiModelProperty("车辆种类")
    private CarTypeEnum carTypeCode;

    @ApiModelProperty(value = "精准查询")
    private Boolean exactQuery;

    @ApiModelProperty(value = "过户类型")
    private TransferMarkTypeEnum transferMarkType;

    @ApiModelProperty(value = "行驶证厂牌型号")
    private String licenseBrand;

    @ApiModelProperty(value = "是否提供上年商业险保单")
    private String provideLastYearBiPolicyImage;

    @ApiModelProperty(value = "行驶证地址")
    private LicenseAddressInfo licenseAddressInfo;

    @ApiModelProperty(value = "是否最高排量 1：是 0：否")
    private String highestExhaustScale;

    @ApiModelProperty(value = "同车主")
    private String sameCarOwnerMark;

    @ApiModelProperty("牵引总质量")
    private BigDecimal totalTractionMass;

    @ApiModelProperty("是否牵引车 0:否,1:是")
    private String tractorMark;

    @ApiModelProperty("牵引车类型")
    private TractorTypeEnum tractorType;

}
