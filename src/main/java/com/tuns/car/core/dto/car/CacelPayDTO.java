package com.tuns.car.core.dto.car;

import com.tuns.car.core.entity.ViInsPlcyInfTp;
import com.tuns.car.core.entity.ViOrderInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName CacelPayDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/2 16:15
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class CacelPayDTO {

    private List<ViInsPlcyInfTp> viInsPlcyInfTpList;

    private ViOrderInfo viOrderInfo;

}
