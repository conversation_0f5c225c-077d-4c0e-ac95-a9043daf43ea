package com.tuns.car.core.dto.template;

import com.tuns.car.core.feign.template.InsureRepeatTemplateFeign;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提供给{@link InsureRepeatTemplateFeign}的generateTemplate方法,以生成一个全新的准模版
 *
 * <AUTHOR>
 * @since 2023-6-12
 */
@Data
@NoArgsConstructor
public class RepeatTemplateGenerateDTO {

    @ApiModelProperty("投保类型")
    private String recordType;

    @ApiModelProperty("公司ID")
    private String companyId;

    @ApiModelProperty("重复投保消息")
    private String message;

    @ApiModelProperty("模版类型 1时间修复模版 2业务处理模版")
    private String templateType;

    public RepeatTemplateGenerateDTO(String recordType, String companyId, String message, String templateType) {
        this.recordType = recordType;
        this.companyId = companyId;
        this.message = message;
        this.templateType = templateType;
    }
}
