package com.tuns.car.core.dto.gi;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "非车险保单信息")
public class CarSameSaleGiPolicyInfoVO implements Serializable {

    private static final long serialVersionUID = -4014619916154133738L;
    /**
     * 保单id
     */
    @ApiModelProperty(value = "保单ID")
    private Long policyId;

    /**
     * 车险保单批次id
     */
    @ApiModelProperty(value = "车险保单批次id")
    private Long carPolicyBatchId;

    /**
     * 保单编号
     */
    @ApiModelProperty(value = "保单编号")
    private String policyNumber;

    /**
     * 批单号
     */
    @ApiModelProperty(value = "批单号")
    private String endorseNo;

    /**
     * 保险公司ID
     */
    @ApiModelProperty(value = "保险公司ID")
    private String companyId;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称")
    private String companyName;

    /**
     * 合作渠道ID
     */
    @ApiModelProperty(value = "合作渠道ID")
    private String channelId;

    /**
     * 合作渠道名称
     */
    @ApiModelProperty(value = "合作渠道名称")
    private String channelName;

    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品ID")
    private Integer prodId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String prodName;

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID")
    private Integer projectId;

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String projectName;

    /**
     * 保费
     */
    @ApiModelProperty(value = "保费")
    private BigDecimal prem;

    /**
     * 净保费
     */
    @ApiModelProperty(value = "净保费")
    private BigDecimal premium;


    /**
     * 起保时间
     */
    @ApiModelProperty(value = "起保时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 终保时间
     */
    @ApiModelProperty(value = "终保时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 份数
     */
    @ApiModelProperty(value = "份数")
    private Short copies;

    /**
     * 审核状态：N未提交 P待审核  S审核成功 F审核拒绝
     */
    @ApiModelProperty(value = "审核状态：N未提交 P待审核  S审核成功 F审核拒绝")
    private String auditStatus;

    /**
     * 佣金费率
     */
    @ApiModelProperty(value = "佣金费率")
    private BigDecimal commRate;

    /**
     * 佣金
     */
    @ApiModelProperty(value = "佣金")
    private BigDecimal commFee;

    /**
     * 投保人姓名
     */
    @ApiModelProperty(value = "投保人姓名")
    private String holderName;

    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名")
    private String insureName;

    /**
     * 车牌号码
     */
    @ApiModelProperty(value = "车牌号码")
    private String plateNumber;

    /**
     * 是否车险兼售 1是 0否
     */
    @ApiModelProperty(value = "是否车险兼售 1是 0否")
    private String isCarSameSale;

    /**
     * 是否驾意险 1是 0否
     */
    @ApiModelProperty(value = "是否驾意险 1是 0否")
    private String isDrivingMeansRisk;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 保单记录来源 1-客户端 2-内勤 3-录单 4-抓单 5-app出单
     */
    private String recordSource;

    /**
     * 业务来源说明
     */
    private String recordSourceDesc;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
