package com.tuns.car.core.dto.carprocess.premium;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Size;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-10-12 15:34
 */
@Data
public class ViQuoteInvoiceInfoTpDTO {
    /**
     * 开票类型 1.销售发票
     */
    private String invoicePurchaseType;
    /**
     * 开票号码
     */
    @Size(max = 20)
    private String invoicePurchaseNo;
    /**
     * 开票日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date invoicePurchaseDate;
}
