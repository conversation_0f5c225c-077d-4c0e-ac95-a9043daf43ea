package com.tuns.car.core.dto.gi;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 个险 非车产品信息
 * <AUTHOR>
 * @since 2021/12/29
 */
public class GiProdInfoVO {

    @JsonProperty("id")
    private String id;
    @JsonProperty("companyId")
    private String companyId;
    @JsonProperty("companyName")
    private String companyName;
    @JsonProperty("channelId")
    private String channelId;
    @JsonProperty("channelName")
    private String channelName;
    @JsonProperty("prodId")
    private Integer prodId;
    @JsonProperty("prodCode")
    private String prodCode;
    @JsonProperty("prodName")
    private String prodName;
    @JsonProperty("prodShortName")
    private String prodShortName;
    @JsonProperty("fitCrowd")
    private String fitCrowd;
    @JsonProperty("period")
    private String period;
    @JsonProperty("leastPerson")
    private String leastPerson;
    @JsonProperty("profAccept")
    private String profAccept;
    @JsonProperty("supervisionCate")
    private String supervisionCate;
    @JsonProperty("statisticCate")
    private String statisticCate;
    @JsonProperty("busiCate")
    private String busiCate;
    @JsonProperty("shelfSts")
    private String shelfSts;
    @JsonProperty("isEnable")
    private String isEnable;
    @JsonProperty("onlineSaleFlag")
    private String onlineSaleFlag;
    @JsonProperty("orderNum")
    private Object orderNum;
    @JsonProperty("premMin")
    private Double premMin;
    @JsonProperty("feature")
    private String feature;
    @JsonProperty("brief")
    private Object brief;
    @JsonProperty("promoteInfo")
    private String promoteInfo;
    @JsonProperty("prodMark")
    private String prodMark;
    @JsonProperty("creTm")
    private String creTm;
    @JsonProperty("creUser")
    private Integer creUser;
    @JsonProperty("mdfTm")
    private String mdfTm;
    @JsonProperty("mdfUser")
    private Integer mdfUser;
    @JsonProperty("clauseCode")
    private Object clauseCode;
    @JsonProperty("prodUrl")
    private String prodUrl;
    @JsonProperty("signFlag")
    private String signFlag;
    @JsonProperty("isChecked")
    private String isChecked;
    @JsonProperty("numberOfInsured")
    private String numberOfInsured;
    @JsonProperty("prodType")
    private String prodType;
    @JsonProperty("maxValue")
    private String maxValue;
    @JsonProperty("minValue")
    private String minValue;
    @JsonProperty("keyword")
    private String keyword;
    @JsonProperty("isConfig")
    private String isConfig;
    @JsonProperty("depict")
    private String depict;
    @JsonProperty("isCompute")
    private String isCompute;
    @JsonProperty("isCarSameSale")
    private String isCarSameSale;
    @JsonProperty("isDrivingMeansRisk")
    private String isDrivingMeansRisk;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public Integer getProdId() {
        return prodId;
    }

    public void setProdId(Integer prodId) {
        this.prodId = prodId;
    }

    public String getProdCode() {
        return prodCode;
    }

    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }

    public String getProdName() {
        return prodName;
    }

    public void setProdName(String prodName) {
        this.prodName = prodName;
    }

    public String getProdShortName() {
        return prodShortName;
    }

    public void setProdShortName(String prodShortName) {
        this.prodShortName = prodShortName;
    }

    public String getFitCrowd() {
        return fitCrowd;
    }

    public void setFitCrowd(String fitCrowd) {
        this.fitCrowd = fitCrowd;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getLeastPerson() {
        return leastPerson;
    }

    public void setLeastPerson(String leastPerson) {
        this.leastPerson = leastPerson;
    }

    public String getProfAccept() {
        return profAccept;
    }

    public void setProfAccept(String profAccept) {
        this.profAccept = profAccept;
    }

    public String getSupervisionCate() {
        return supervisionCate;
    }

    public void setSupervisionCate(String supervisionCate) {
        this.supervisionCate = supervisionCate;
    }

    public String getStatisticCate() {
        return statisticCate;
    }

    public void setStatisticCate(String statisticCate) {
        this.statisticCate = statisticCate;
    }

    public String getBusiCate() {
        return busiCate;
    }

    public void setBusiCate(String busiCate) {
        this.busiCate = busiCate;
    }

    public String getShelfSts() {
        return shelfSts;
    }

    public void setShelfSts(String shelfSts) {
        this.shelfSts = shelfSts;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getOnlineSaleFlag() {
        return onlineSaleFlag;
    }

    public void setOnlineSaleFlag(String onlineSaleFlag) {
        this.onlineSaleFlag = onlineSaleFlag;
    }

    public Object getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Object orderNum) {
        this.orderNum = orderNum;
    }

    public Double getPremMin() {
        return premMin;
    }

    public void setPremMin(Double premMin) {
        this.premMin = premMin;
    }

    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }

    public Object getBrief() {
        return brief;
    }

    public void setBrief(Object brief) {
        this.brief = brief;
    }

    public String getPromoteInfo() {
        return promoteInfo;
    }

    public void setPromoteInfo(String promoteInfo) {
        this.promoteInfo = promoteInfo;
    }

    public String getProdMark() {
        return prodMark;
    }

    public void setProdMark(String prodMark) {
        this.prodMark = prodMark;
    }

    public String getCreTm() {
        return creTm;
    }

    public void setCreTm(String creTm) {
        this.creTm = creTm;
    }

    public Integer getCreUser() {
        return creUser;
    }

    public void setCreUser(Integer creUser) {
        this.creUser = creUser;
    }

    public String getMdfTm() {
        return mdfTm;
    }

    public void setMdfTm(String mdfTm) {
        this.mdfTm = mdfTm;
    }

    public Integer getMdfUser() {
        return mdfUser;
    }

    public void setMdfUser(Integer mdfUser) {
        this.mdfUser = mdfUser;
    }

    public Object getClauseCode() {
        return clauseCode;
    }

    public void setClauseCode(Object clauseCode) {
        this.clauseCode = clauseCode;
    }

    public String getProdUrl() {
        return prodUrl;
    }

    public void setProdUrl(String prodUrl) {
        this.prodUrl = prodUrl;
    }

    public String getSignFlag() {
        return signFlag;
    }

    public void setSignFlag(String signFlag) {
        this.signFlag = signFlag;
    }

    public String getIsChecked() {
        return isChecked;
    }

    public void setIsChecked(String isChecked) {
        this.isChecked = isChecked;
    }

    public String getNumberOfInsured() {
        return numberOfInsured;
    }

    public void setNumberOfInsured(String numberOfInsured) {
        this.numberOfInsured = numberOfInsured;
    }

    public String getProdType() {
        return prodType;
    }

    public void setProdType(String prodType) {
        this.prodType = prodType;
    }

    public String getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(String maxValue) {
        this.maxValue = maxValue;
    }

    public String getMinValue() {
        return minValue;
    }

    public void setMinValue(String minValue) {
        this.minValue = minValue;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getIsConfig() {
        return isConfig;
    }

    public void setIsConfig(String isConfig) {
        this.isConfig = isConfig;
    }

    public String getDepict() {
        return depict;
    }

    public void setDepict(String depict) {
        this.depict = depict;
    }

    public String getIsCompute() {
        return isCompute;
    }

    public void setIsCompute(String isCompute) {
        this.isCompute = isCompute;
    }

    public String getIsCarSameSale() {
        return isCarSameSale;
    }

    public void setIsCarSameSale(String isCarSameSale) {
        this.isCarSameSale = isCarSameSale;
    }

    public String getIsDrivingMeansRisk() {
        return isDrivingMeansRisk;
    }

    public void setIsDrivingMeansRisk(String isDrivingMeansRisk) {
        this.isDrivingMeansRisk = isDrivingMeansRisk;
    }
}
