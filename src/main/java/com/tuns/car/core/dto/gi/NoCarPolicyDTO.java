package com.tuns.car.core.dto.gi;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 非车保单数据
 *
 * <AUTHOR>
 * @since 2022/4/27
 */
@Data
public class NoCarPolicyDTO {


    /**
     * 批次id
     */
    @ApiModelProperty(value = "批次id")
    private Long policyBatchId;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String prodName;
    /**
     * 产品代码
     */
    @ApiModelProperty(value = "产品代码")
    private String prodCode;
    /**
     * 个险产品id
     */
    @ApiModelProperty(value = "个险产品id")
    private Integer prodId;
    /**
     * 产品类型
     */
    private String prodType;
    /**
     * 套餐名称
     */
    @ApiModelProperty(value = "套餐名称")
    private String projectName;
    /**
     * 计划代码
     */
    @ApiModelProperty(value = "计划代码")
    private String projectCode;
    /**
     * 个险计划id
     */
    @ApiModelProperty(value = "个险计划id")
    private Integer projectId;
    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNumber;
    /**
     * 份数
     */
    @ApiModelProperty(value = "份数")
    private Integer copies;
    /**
     * 保费
     */
    @ApiModelProperty(value = "净保费")
    private BigDecimal premium;
    /**
     * 起保时间
     */
    @ApiModelProperty(value = "起保时间")
    private LocalDateTime startTime;
    /**
     * 终保时间
     */
    @ApiModelProperty(value = "终保时间")
    private LocalDateTime endTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 保险公司非车订单号
     * */
    private String insOrderNo;
    /**
     * 投保单号
     */
    private String proposalNo;
}
