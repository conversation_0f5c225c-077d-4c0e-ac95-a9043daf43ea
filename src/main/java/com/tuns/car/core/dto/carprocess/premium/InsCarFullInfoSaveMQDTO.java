package com.tuns.car.core.dto.carprocess.premium;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName InsCarFullInfoSaveMQDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/12/21
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@ApiModel(value = "InsCarFullInfoSaveMQDTO", description = "自助报价完整保存信息（MQ）")
public class InsCarFullInfoSaveMQDTO implements Serializable {

    private static final long serialVersionUID = 6967687863480816910L;

    @ApiModelProperty(value = "用户id", hidden = true)
    private Integer userId;
    @ApiModelProperty(value = "自助报价完整保存信息", hidden = true)
    private InsCarFullInfoSaveDTO insCarFullInfoSaveDTO;
    @ApiModelProperty(value = "自助报价完整保存信息v2.0", hidden = true)
    private PremiumCaculateDTO data;

}
