package com.tuns.car.core.dto.car;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 影像资料类型配置表
 *
 * <AUTHOR>
 * @date 2023-12-25 14:17:51
 */
@NoArgsConstructor
@Data
public class ViCompanyImageMappingDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 排序
     */
    private Integer sort;
    /**
     * 出单平台_保险公司地址表 主键id
     */
    @NotNull
    private Integer comsCompDetailId;
    /**
     * 我司字段代码
     */
    @NotBlank
    @ApiModelProperty("我司字段代码")
    private String tunsFieldCode;
    /**
     * 我司字段名称
     */
    @NotBlank
    @ApiModelProperty("我司字段名称")
    private String tunsFieldName;
    /**
     * 保司字段一级代码
     */
    @NotBlank
    @ApiModelProperty("保司字段一级代码")
    private String insFieldFirstLevelCode;
    /**
     * 保司字段一级名称
     */
    @NotBlank
    @ApiModelProperty("保司字段一级名称")
    private String insFieldFirstLevelName;
    /**
     * 保司字段二级代码
     */
    @ApiModelProperty("保司字段二级代码")
    private String insFieldSecondLevelCode;
    /**
     * 保司字段二级名称
     */
    @ApiModelProperty("保司字段二级名称")
    private String insFieldSecondLevelName;

    public ViCompanyImageMappingDTO(String insFieldFirstLevelCode, String insFieldFirstLevelName) {
        this.insFieldFirstLevelCode = insFieldFirstLevelCode;
        this.insFieldFirstLevelName = insFieldFirstLevelName;
    }
}
