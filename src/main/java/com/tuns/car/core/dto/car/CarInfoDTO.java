package com.tuns.car.core.dto.car;

import com.tuns.car.core.constant.CarTypeEnum;
import com.tuns.car.core.dto.carprocess.premium.request.PersonInfo;
import com.tuns.car.core.entity.CmmVehicleTypeRelation;
import com.tuns.core.boot.annotation.RepeatInvokeLockKey;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * 车辆信息
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CarInfoDTO extends IssueOrderChanInfoDTO {

    private static final long serialVersionUID = 6695022107152801561L;
    /**
     * 当前登录用户id
     */
    @RepeatInvokeLockKey
    private Integer userId;

    @ApiModelProperty(value = "发动机号码")
    @NotBlank(message = "发动机号码不能为空")
    private String engineNumber;

    @RepeatInvokeLockKey
    @ApiModelProperty(value = "车架号")
    @NotBlank(message = "车架号不能为空")
    private String frameNumber;

    @ApiModelProperty(value = "过户日期 yyyy-MM-dd")
    private String transferDate;

    @ApiModelProperty(value = "车型代码")
    private String modelCode;

    @ApiModelProperty(value = "品牌名称", required = true)
    private String modelName;

    @ApiModelProperty(value = "车主证件号码")
    private String ownerIdentify;

    @ApiModelProperty(value = "车主")
    private String carOwner;

    @ApiModelProperty(value = "车牌号码")
    private String plateNumber;

    @ApiModelProperty(value = "注册日期 yyyy-MM-dd")
    @NotBlank(message = "注册日期不能为空")
    private String regDate;

    @ApiModelProperty(value = "发证日期 yyyy-MM-dd")
    private String certDate;

    @ApiModelProperty(value = "过户标志")
    private String transferMark;

    @ApiModelProperty(value = "行政区域代码 市级")
    private String cityNumber;

    @ApiModelProperty(value = "行业车型编码")
    private String prfsModelCode;

    @ApiModelProperty(value = "查询次数")
    private String queryModel;

    @ApiModelProperty(value = "座位数")
    private Integer seatCount;

    @ApiModelProperty(value = "新车标识")
    private String plateNumberMark;

    @ApiModelProperty(value = "扩展字段")
    private String extendField;

    @ApiModelProperty(value = "车辆映射关系", hidden = true)
    private CmmVehicleTypeRelation relation;

    @ApiModelProperty(value = "营运性质 0-非营运 1-营运")
    private String operationNature;

    @ApiModelProperty(value = "使用性质 附件码表C1.使用性质/滕顺车辆种类-小类")
    private String usingNature;

    @ApiModelProperty(value = "所属性质 1-个人 2-机关 3-企业")
    private String ownershipNature;

    @ApiModelProperty("车辆种类")
    private CarTypeEnum carTypeCode;

    @ApiModelProperty(value = "报价类型 1-单交强 2-单商业 3-交商同保")
    private String recordType;

    @ApiModelProperty(value = "交强起保时间 yyyy-MM-dd HH:mm:ss")
    private String insBeginCi;

    @ApiModelProperty(value = "商业起保时间 yyyy-MM-dd HH:mm:ss")
    private String insBeginBi;

    @ApiModelProperty(value = "精准查询")
    private Boolean exactQuery;

    @ApiModelProperty(value = "车型代码Old")
    private String modelCodeOld;

    @ApiModelProperty(value = "能源种类")
    private String fuelType;

    @ApiModelProperty(value = "行驶证车辆")
    private String licenseVehicleType;

    @ApiModelProperty(value = "贷款车辆标志 0-非贷款车 1-贷款车")
    private String loanCarMark;

    @ApiModelProperty(value = "外地车标致 0-本地车 1-外地车")
    private String nonlocalCarMark;

    @ApiModelProperty("新车标致 0-非新车 1-新车")
    private String newCarMark;

    @ApiModelProperty(value = "排量(ML)")
    private Integer exhaustScale;

    @ApiModelProperty(value = "新车购置价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "车辆吨位/核定载质量(吨)")
    private BigDecimal carTonnage;

    @ApiModelProperty(value = "整备质量")
    private Integer wholeWeight;

    @ApiModelProperty(value = "功率(瓦)")
    private Integer power;

    private String brandName;

    private String deptName;

    private String tradeName;

    private String noticeType;

    @ApiModelProperty("车辆实际价值")
    private BigDecimal actualPrice;

    private String searchCode;

    @ApiModelProperty(value = "关系人信息")
    private List<PersonInfo> personInfos;

    @ApiModelProperty(value = "行业唯一车型ID")
    private String vehicleStyleUniqueId;

    @ApiModelProperty(value = "车型确认开关", hidden = true)
    private Boolean carAutoConfirmSwitch;

    @ApiModelProperty(value = "自动车型确认")
    private Boolean carAutoConfirm;

    @ApiModelProperty(value = "首页查询", hidden = true)
    private Boolean homePageQuery;

    @ApiModelProperty(value = "车型修复", hidden = true)
    private Boolean carModelFix;
}
