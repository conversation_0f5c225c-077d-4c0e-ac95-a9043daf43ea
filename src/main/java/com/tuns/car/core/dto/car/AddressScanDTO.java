package com.tuns.car.core.dto.car;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-10-17 9:12
 */
@Data
public class AddressScanDTO implements Serializable {
    private static final long serialVersionUID = 4857053016264258129L;
    @JSONField(name = "province")
    @ApiModelProperty("省")
    private String province;

    @JSONField(name = "province_code")
    @ApiModelProperty("省code")
    private String provinceCode;

    @JSONField(name = "city")
    @ApiModelProperty("市")
    private String city;

    @JSONField(name = "city_code")
    @ApiModelProperty("市code")
    private String cityCode;

    @JSONField(name = "county")
    @ApiModelProperty("区")
    private String county;

    @JSONField(name = "county_code")
    @ApiModelProperty("区code")
    private String countyCode;

    @JSONField(name = "town")
    @ApiModelProperty("镇")
    private String town;

    @JSONField(name = "detail")
    @ApiModelProperty("详细地址")
    private String detail;
}
