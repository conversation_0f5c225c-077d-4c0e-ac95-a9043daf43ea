/**
  * Copyright 2021 json.cn 
  */
package com.tuns.car.core.dto.gi.msb;

/**
 * Auto-generated: 2021-11-01 15:12:57
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class HoldVo {

    private String holderAddress;
    private String holderCerNo;
    private String holderCerType;
    private String holderContactor;
    private String holderEmail;
    private String holderName;
    private String holderRelationship;
    private String holderTelephone;
    private String isVerifyTruthHolder;
    private String secretCertNo;
    private String secretHolderTelphone;
    private String secretMail;
    private String secretMobile;
    public void setHolderAddress(String holderAddress) {
         this.holderAddress = holderAddress;
     }
     public String getHolderAddress() {
         return holderAddress;
     }

    public void setHolderCerNo(String holderCerNo) {
         this.holderCerNo = holderCerNo;
     }
     public String getHolderCerNo() {
         return holderCerNo;
     }

    public void setHolderCerType(String holderCerType) {
         this.holderCerType = holderCerType;
     }
     public String getHolderCerType() {
         return holderCerType;
     }

    public void setHolderContactor(String holderContactor) {
         this.holderContactor = holderContactor;
     }
     public String getHolderContactor() {
         return holderContactor;
     }

    public void setHolderEmail(String holderEmail) {
         this.holderEmail = holderEmail;
     }
     public String getHolderEmail() {
         return holderEmail;
     }

    public void setHolderName(String holderName) {
         this.holderName = holderName;
     }
     public String getHolderName() {
         return holderName;
     }

    public void setHolderRelationship(String holderRelationship) {
         this.holderRelationship = holderRelationship;
     }
     public String getHolderRelationship() {
         return holderRelationship;
     }

    public void setHolderTelephone(String holderTelephone) {
         this.holderTelephone = holderTelephone;
     }
     public String getHolderTelephone() {
         return holderTelephone;
     }

    public void setIsVerifyTruthHolder(String isVerifyTruthHolder) {
         this.isVerifyTruthHolder = isVerifyTruthHolder;
     }
     public String getIsVerifyTruthHolder() {
         return isVerifyTruthHolder;
     }

    public void setSecretCertNo(String secretCertNo) {
         this.secretCertNo = secretCertNo;
     }
     public String getSecretCertNo() {
         return secretCertNo;
     }

    public void setSecretHolderTelphone(String secretHolderTelphone) {
         this.secretHolderTelphone = secretHolderTelphone;
     }
     public String getSecretHolderTelphone() {
         return secretHolderTelphone;
     }

    public void setSecretMail(String secretMail) {
         this.secretMail = secretMail;
     }
     public String getSecretMail() {
         return secretMail;
     }

    public void setSecretMobile(String secretMobile) {
         this.secretMobile = secretMobile;
     }
     public String getSecretMobile() {
         return secretMobile;
     }

}