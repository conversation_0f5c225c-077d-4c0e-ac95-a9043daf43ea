package com.tuns.car.core.dto.car;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.constant.CarRegexpConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 车五项查询(首页获取报价)
 * @ClassName InsCarAutoCarBasicInfoDTO
 * @Description 车五项查询(首页获取报价) 
 * <AUTHOR>
 * @Date 2020/06/10 08:48:43 
 * @Version 1.0
 */
@Data
public class InsCarAutoCarBasicInfoDTO implements Serializable {

    private static final long serialVersionUID = 6652370343344984811L;
    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号", required = true)
    @Pattern(regexp = CarRegexpConstants.PLATE_NUMBER)
    private String plateNumber;

    @ApiModelProperty(value = "车架号")
    @Pattern(regexp = CarRegexpConstants.FRAME_NUMBER)
    private String frameNumber;

    @ApiModelProperty(value = "车主")
    @NotBlank(message = "车主不能为空")
    private String carOwner;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
