package com.tuns.car.core.dto.car;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/12 15:28
 */
@Data
public class GrabPolicyPageDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 7482308574835590147L;
    /**
     * 保单号码
     */
    @ApiModelProperty(value = "保单号码")
    private String policyNumber;
    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String plateNumber;
    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号")
    private String frameNumber;

    /**
     * 抓单人
     */
    @ApiModelProperty(value = "抓单人")
    private String operatorUserName;

    /**
     * 抓单人ID
     */
    @ApiModelProperty(value = "抓单人ID")
    private Integer operatorUserId;

    /**
     * 领用状态
     */
    @ApiModelProperty(value = "领用状态")
    private String pickUpFlag;

    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    private Long serialNumber;

    @ApiModelProperty(value = "保单是否存在")
    private Boolean policyExists;

    @ApiModelProperty(value = "保险公司ID")
    private String companyId;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
