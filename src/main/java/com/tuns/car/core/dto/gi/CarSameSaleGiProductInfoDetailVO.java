package com.tuns.car.core.dto.gi;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CarSameSaleGiProductInfoDetailVO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 8250527083249485513L;

    /**
     * 产品ID
     */
    private Integer prodId;

    /**
     * 产品代码
     */
    private String prodCode;

    /**
     * 产品名称
     */
    private String prodName;

    /**
     * 计划ID
     */
    private Integer projectId;

    /**
     * 计划名称
     */
    private String projectName;

    /**
     * 计划code
     */
    private String projectCode;

    /**
     * 原始金额--用于比价
     */
    private BigDecimal parityPrem;
    /**
     * 产品类型
     */
    private String prodType;

}
