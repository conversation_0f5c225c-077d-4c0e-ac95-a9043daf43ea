package com.tuns.car.core.dto.car;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/28 16:36
 */
@Data
public class CarDataTreasureDTO implements Serializable {

    private static final long serialVersionUID = -2150974332311068411L;

    /**
     * GuidancePrice	string	厂商指导价
     */
    private String GuidancePrice;
    /**
     * MarketDate	string	上市时间
     */
    private String MarketDate;
    /**
     * cph	string	车牌号
     */
    private String cph;
    /**
     * WheelBase	string	轴距
     */
    private String wheelBase;
    /**
     * shaft	string	轴数
     */
    private String shaft;
    /**
     * height	string	高
     */
    private String height;
    /**
     * type	string	车型类别
     */
    private String type;
    /**
     * width	string	宽
     */
    private String width;
    /**
     * Engine	string	发动机号
     */
    private String Engine;
    /**
     * InsuranceDate	string	承保年月
     */
    private String InsuranceDate;
    /**
     * cc	string	排量
     */
    private String cc;
    /**
     * properties	string	使用性质
     */
    private String properties;
    /**
     * Carriagenumber	string	厢数
     */
    private String Carriagenumber;
    /**
     * ManufacturingDate	string	生产日期
     */
    private String ManufacturingDate;
    /**
     * retirementDate	string	强制报废期止
     */
    private String retirementDate;
    /**
     * validityDayEnd	string	检验有效期止
     */
    private String validityDayEnd;
    /**
     * vin	string	车辆识别代号
     */
    private String vin;
    /**
     * color	string	车身颜色
     */
    private String color;
    /**
     * Frontwheelbase	string	前轮距
     */
    private String Frontwheelbase;
    /**
     * RegisterDate	string	车辆注册日期
     */
    private String RegisterDate;
    /**
     * EngineModel	string	发动机型号
     */
    private String EngineModel;
    /**
     * Ratedquality	string	额定载质量
     */
    private String Ratedquality;
    /**
     * Emissions	string	排放标准
     */
    private String Emissions;
    /**
     * brand	string	品牌
     */
    private String brand;
    /**
     * CarModel	string	车型
     */
    private String CarModel;
    /**
     * vehiclemodel	string	车辆型号
     */
    private String vehiclemodel;
    /**
     * Totalquality	string	总质量
     */
    private String Totalquality;
    /**
     * passengers	string	载客数
     */
    private String passengers;
    /**
     * StyleName	string	款型名称
     */
    private String StyleName;
    /**
     * length	string	长
     */
    private String length;
    /**
     * Curbweight	string	整备质量
     */
    private String curbweight;
    /**
     * TransmissionType	string	变速箱类型
     */
    private String TransmissionType;
    /**
     * Fueltypes	string	燃料种类
     */
    private String Fueltypes;
    /**
     * vehiclename	string	车辆类别
     */
    private String vehiclename;
    /**
     * ModelYear	string	车型年款
     */
    private String ModelYear;
    /**
     * Rearwheelbase	string	后轮距
     */
    private String Rearwheelbase;
    /**
     * power	string	功率
     */
    private String power;
    /**
     * Demio	string	系别
     */
    private String Demio;
    /**
     * vehicleStatus	string	车辆状态(0:正常1:不正常)
     */
    private String vehicleStatus;
    /**
     * seqNo	string	序列号（每次调用接口返回的唯一值，如有接口问题，请提供此值）
     */
    private String seqNo;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
