package com.tuns.car.core.dto.pay;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName OrderPayStatusVO
 * @Description 车险订单刷新支付状态返回数据类
 * <AUTHOR> yi
 * @Date 2020/6/8 15:09
 * @Version 1.0
 */
@Data
public class OrderPayStatusDTO implements Serializable {

  private static final long serialVersionUID = -5802345639297086166L;

  /**
   * 订单业务id
   */
  @ApiModelProperty(value = "订单业务id", required = true)
  private String orderUuid;

  /**
   * 订单号码
   */
  @ApiModelProperty(value = "订单号码", required = true)
  private String orderNo;

  /**
   * 订单类型：0人工, 1自助
   */
  @ApiModelProperty(value = "订单类型：0人工, 1自助")
  private String autoMark;

  /**
   * 支付时间
   */
  @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty(value = "支付时间", required = true)
  private Date payTime;

  /**
   * 支付金额
   */
  @ApiModelProperty(value = "支付金额", required = true)
  private BigDecimal prem;

  /**
   * 订单状态 6等待支付,7支付成功,11支付失败,12保单自取,13保单配送,14订单完成
   */
  @ApiModelProperty(value = "支付状态 6等待支付,7支付成功,11支付失败", required = true)
  private String status;

  /**
   * 支付失败原因
   */
  @ApiModelProperty(value = "支付失败原因", required = true)
  private String failedMsg;

  /**
   * 批次id
   */
  private Long policyBatchId;


  @Override
  public String toString() {
    return JSON.toJSONString(this);
  }
}
