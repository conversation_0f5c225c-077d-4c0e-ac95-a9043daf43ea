package com.tuns.car.core.dto.carprocess.premium.request;

import com.tuns.car.core.constant.PersonNatureEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/***
 * 关系人信息
 * ClassName: PersonInfo
 * Description:
 * Create Time: 2022/3/1 15:51
 *
 * <AUTHOR>
 */
@Data
public class PersonInfo implements Serializable {
    private static final long serialVersionUID = -8216800160910912790L;

    @ApiModelProperty(value = "类型-01车主,02被保人,03投保人,04受益人,05收件人")
    private String personType;

    @ApiModelProperty(value = "关系人性质")
    private PersonNatureEnum nature;

    @ApiModelProperty(value = "姓名")
    private String personName;

    @ApiModelProperty(value = "证件类型")
    private String identifyType;

    @ApiModelProperty(value = "证件号码")
    private String identifyNumber;

    @ApiModelProperty(value = "省编号")
    private String provinceNumber;

    @ApiModelProperty(value = "省名称")
    private String provinceNumberName;

    @ApiModelProperty(value = "市编号")
    private String cityNumber;

    @ApiModelProperty(value = "市名称")
    private String cityNumberName;

    @ApiModelProperty(value = "区/县编号")
    private String countyNumber;

    @ApiModelProperty(value = "区/县名称")
    private String countyNumberName;

    @ApiModelProperty(value = "详细地址，注意：这里不需要传完整地址")
    private String addressComplete;

    @ApiModelProperty(value = "完整地址")
    private String addressDetail;

    @ApiModelProperty(value = "手机号码")
    private String mobilePhone;

    @ApiModelProperty(value = "性别 1-男 2-女")
    private String personSex;

    @ApiModelProperty(value = "当前年龄")
    private String personAge;

    @ApiModelProperty(value = "出生日期 yyyy-MM-dd")
    private String birthDate;

    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @ApiModelProperty(value = "与主要关系人所属关系")
    private String mainPersRelation;

    @ApiModelProperty(value = "身份证有效起期")
    private String identityValidityStart;

    @ApiModelProperty(value = "身份证有效止期")
    private String identityValidity;
}
