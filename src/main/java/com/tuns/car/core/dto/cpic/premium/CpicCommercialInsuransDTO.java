package com.tuns.car.core.dto.cpic.premium;

/**
 * @ClassName CpicCommercialInsuransDTO
 * @Description 太平洋原始请求报文dto对象（商业险）
 * <AUTHOR>
 * @Date 2022/02/14 09:36:58
 * @Version 1.0
 */

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CpicCommercialInsuransDTO implements Serializable {

    private BigDecimal annualPremium;
    private String businessfee;
    private String businesspoudage;
    private String carSpreading;
    private BigDecimal changeableFeeRate;
    private BigDecimal channelRate;
    private String commContinuedInsuredYears;
    private String cpicScore;
    private String deferSubjectAcount;
    private String deferSubjectRate;
    private String ecompensationRate;
    private BigDecimal endDate;
    private String espcompensationRate;
    private String feemanagematchflag;
    private String fleetEcompensationRate;
    private String fleetTotalEcompensationRate;
    private BigDecimal independentPriceRate;//自主定价系数
    private String insuranceQueryCode;
    private String insuredNo;
    private String isLinkage;
    private String lastAdviceDiscuteMinRate;
    private String lastBusinessFee;
    private String lastLocalDiscuteRate;
    private String lastLocalPureriskPremium;
    private String lastOriginalChangeableFeeRate;
    private String lastPageChangeableFeeRate;
    private String lastPerformance;
    private String lastPoudage;
    private String lastPremiumCalculationScheme;
    private BigDecimal lastPremiumRatio;
    private String lastQuotationInfo;
    private String lastStandardPremium;
    private BigDecimal lastchangeableFeeRate;
    private String lasttrafficsubsidyflag;
    private BigDecimal localDiscuteRate;
    private BigDecimal localPureRiskPremium;
    private String newCarFlag;
    private String nonAutoAmountOfProfit;
    private BigDecimal nonClaimDiscountRate;//无赔款折扣系数
    private String originalAddedservicerate;
    private Integer originalBusinessFee;
    private BigDecimal originalChangeableFeeRate;
    private String originalDeferSubjectRate;
    private String originalDeferSubjectRcount;
    private Integer originalPerFormance;
    private BigDecimal originalPoudage;
    private String originalSubjectAcount;
    private String originalSubjectRate;
    private String performance;
    private BigDecimal policyCostRate;
    private BigDecimal poudage;
    private BigDecimal premium; //折后保费
    private String premiumCalculationScheme;
    private BigDecimal premiumRatio;
    private String ruleNo;
    private String serviceValueAddRate;

    private String stEndDate;//商业险终保日期
    private String stPremium;
    private String stPremiumRatio;
    private String stStandardPremium;
    private String stStartDate;//商业险起保日期
    private BigDecimal standardPremium;
    private Long startDate;
    private String subjectAcount;
    private String subjectRate;
    private Integer targetPolicyCostRate;
    private String telSalePerson;
    private String totalCpicScore;
    private String totalEcompensationRate;
    private Integer trafficTransgressRate;
    private String vehicleClaimType;
    private String vehicleRiskLevel;
    private String wyCarType;
    private String policyStatus;


}
