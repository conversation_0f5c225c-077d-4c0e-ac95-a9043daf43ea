package com.tuns.car.core.dto.car;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.io.Serializable;

/**
 * 查询影像资料配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025年3月10日09:12:21
 */
@Data
@RequiredArgsConstructor
public class QueryImageConfigurationDTO implements Serializable {

    private static final long serialVersionUID = -771585477181420701L;

    @ApiModelProperty("保司id")
    private String companyId;

    @ApiModelProperty(value = "渠道ID")
    private Integer chanDetailId;

    @ApiModelProperty("车险批次id")
    private String policyBatchId;

}
