package com.tuns.car.core.dto.gi;

import com.tuns.car.core.dto.car.GiOrderDetailDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
@Data
public class GiOrderCompleteDTO implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = -2832776919361833964L;

	/**
     * 修改人用户id
     */
    private Integer mdfUserId;

    /**
     * 车险保单批次id--不能为空
     */
    private Long carPolicyBatchId;

    /**
     * 车险订单状态  -1报价失败 1报价成功--不能为空
     */
    private String quotationStatus;


    private List<GiOrderDetailDTO> orderDetailDTOS;

    /**
     * 业务来源
     */
    private String recordSource;
    /**
     * 合作伙伴id
     */
    private Integer partnerId;
    /**
     * 合作伙伴真实姓名
     */
    private String realName;

    /**
     * 合作伙伴身份证号码
     */
    private String idCardNo;
    /**
     * 业务员对应的分支机构代码
     */
    private String departCode;
    /**
     * 合作伙伴分支机构代码
     */
    private String departName;

}
