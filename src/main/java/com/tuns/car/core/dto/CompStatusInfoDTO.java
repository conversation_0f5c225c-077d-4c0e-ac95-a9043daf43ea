package com.tuns.car.core.dto;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/4 15:31
 */
@Getter
@Setter
@Data
public class CompStatusInfoDTO implements Serializable {


    private static final long serialVersionUID = 2037666909897851293L;
    private String proposalNumber;
    /**
     * 报价状态
     */
    private String offerStatus;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 商业险保单号
     */
    private String commPlyNo;
    /**
     * 交强险保单号
     */
    private String compPlyNo;
    /**
     * 交强险投保单号
     */
    private String proposalCiNo;
    /**
     * 商业险投保单号
     */
    private String proposalBiNo;
    /**
     * 签单时间/承保时间 yyyy-MM-dd HH:mm:ss
     */
    private String insuredTime;
    /**
     * 审核意见
     */
    private String insuredMsg;

    //提交核保时间
    private LocalDateTime insuredSubmitTime;

    /**
     * 投保日期
     */
    private String issueTime;

    /**
     * 精准使用年限
     */
    @ApiModelProperty(value = "精准使用年限")
    private BigDecimal preciseUseYear;

    @ApiModelProperty(value = "非车保单信息")
    private List<NoCarPolicy> noCarPolicyList;

    /**
     * 是否发送通知
     */
    private boolean sendNotice = false;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
