package com.tuns.car.core.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tuns.car.core.constant.ChanConfigFieldEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-26 11:33:28
 */
@Data
public class ComsChanConfigDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@ApiModelProperty(value = "$column.comments")
	@TableId
	private Integer id;
	/**
	 * coms_chan_detail_id
	 */
	@ApiModelProperty(value = "coms_chan_detail_id")
	private Integer detailId;
	/**
	 * 配置字段
	 */
	@ApiModelProperty(value = "配置字段")
	private ChanConfigFieldEnum configField;
	/**
	 * 配置值
	 */
	@ApiModelProperty(value = "配置值")
	private String configValue;
	/**
	 * 配置类型
	 */
	@ApiModelProperty(value = "配置类型")
	private String configType;
	/**
	 * 配置名称
	 */
	@ApiModelProperty(value = "配置名称")
	private String configName;
	/**
	 * 配置说明项
	 */
	@ApiModelProperty(value = "配置说明项")
	private String configTips;

}
