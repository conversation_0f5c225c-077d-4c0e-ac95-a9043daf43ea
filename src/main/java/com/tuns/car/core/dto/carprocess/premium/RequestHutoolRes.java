package com.tuns.car.core.dto.carprocess.premium;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-5-6 15:43
 */
@Data
public class RequestHutoolRes {
    private byte[] bodyStream;
    private Map<String, List<String>> headers = new HashMap<>();

    public void setHeaders(Map<String, List<String>> headers) {
        for (Map.Entry<String, List<String>> stringListEntry : headers.entrySet()) {
            if (StrUtil.isNotBlank(stringListEntry.getKey())) {
                this.headers.put(stringListEntry.getKey(), stringListEntry.getValue());
            }
        }
    }
}
