package com.tuns.car.core.dto.car;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.constant.ViAttachTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName ViOfferImgDTO
 * @Description APP车险报价单补传资料(图片)请求数据类
 * <AUTHOR> yi
 * @Date 2020/6/9 11:13
 * @Version 1.0
 */
@Data
public class ViOfferImgDTO implements Serializable {

    private static final long serialVersionUID = 5414998027479575635L;

    @ApiModelProperty(value = "主键id,修改图片必传")
    private Integer id;

    /**
     * 图片类型 01被保人身份证正面,02被保人身份证反面,03行驶证正本,04行驶证副本,05营业执照/组织机构代码证,
     * 06机动车左前,07机动车左后,08机动车右前,09机动车右后,10机动车车架号,11机动车辆登记证书,12机动车销售统一发票,
     * 13机动车整车出产合格证,14驾驶证,15车船税证明,16交强险电子保单,17商业险电子保单,18交强险电子发票,
     * 19商业险电子发票,20交强险电子标签,21车主身份证正面,22车主身份证反面,23投保人身份证正面,24投保人身份证反面,99其它
     */
    @NotNull(message = "图片类型不能为空")
    @ApiModelProperty(value = "附件图片类型 01被保人身份证正面,02被保人身份证反面,03行驶证正本,04行驶证副本,05营业执照/组织机构代码证,06机动车左前,"
            + "07机动车左后,08机动车右前,09机动车右后,10机动车车架号,11机动车辆登记证书,12机动车销售统一发票,13机动车整车出产合格证,14驾驶证,15车船税证明,"
            + "16交强险电子保单,17商业险电子保单,18交强险电子发票,19商业险电子发票,20交强险电子标签,21车主身份证正面,22车主身份证反面,23投保人身份证正面,24投保人身份证反面,99其它", required = true)
    private ViAttachTypeEnum type;

    /**
     * 备用type （现太平使用，区别 身份证与营业执照）
     */
    private ViAttachTypeEnum standbyType;

    /**
     * 附件文件名
     */
//    @NotBlank(message = "附件文件名不能为空")
    @ApiModelProperty(value = "附件文件名", required = true)
    private String fileName;

    /**
     * 附件路径
     */
    @NotBlank(message = "附件路径不能为空")
    @ApiModelProperty(value = "附件路径", required = true)
    private String key;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    private String fileType;

    /**
     * 文件类型(中华)
     */
    @ApiModelProperty(value = "文件路径", required = true)
    private String path;

    /**
     * 验车图片类型(提供验车图片使用)
     */
    @ApiModelProperty(value = "验车图片类型附件类型")
    private ViAttachTypeEnum carImgType;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
