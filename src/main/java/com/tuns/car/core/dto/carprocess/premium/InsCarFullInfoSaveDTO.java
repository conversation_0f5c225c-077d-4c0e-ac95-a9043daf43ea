package com.tuns.car.core.dto.carprocess.premium;

import com.tuns.car.core.dto.ViQuoteSpecialAgreementDTO;
import com.tuns.car.core.dto.car.IssueOrderChanInfoDTO;
import com.tuns.car.core.dto.carprocess.premium.request.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/***
 * 自助报价完整信息数据对象
 *
 * @ClassName InsCarFullInfoSaveDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/03/01
 * @Version 1.0
 */
@Data
@ApiModel(value = "InsCarFullInfoSaveDTO", description = "自助报价完整信息数据对象")
public class InsCarFullInfoSaveDTO extends IssueOrderChanInfoDTO {

    private static final long serialVersionUID = 5967687863480816911L;

    @ApiModelProperty(value = "报价流水号", hidden = true)
    private Long serialNumber;

    @ApiModelProperty(value = "报价批次号", hidden = true)
    private Long policyBatchId;

    @ApiModelProperty(value = "上一笔报价流水号")
    private String lastSerialNumber;

    @ApiModelProperty("推荐方案id")
    private Integer recommendPlanId;

    @ApiModelProperty(value = "业务员信息")
    private SalesmanInfo salesmanInfo;

    @NotNull
    @ApiModelProperty(value = "基础信息")
    private BaseInfo baseInfo;

    @Valid
    @NotNull
    @ApiModelProperty(value = "车辆信息")
    private CarInfo carInfo;

    @ApiModelProperty(value = "险种信息")
    private List<ItemKind> itemKinds;

    @ApiModelProperty(value = "关系人信息")
    private List<PersonInfo> personInfos;

    @Valid
    @ApiModelProperty(value = "影像资料")
    private List<AttachInfo> attachmentInfos;

    @ApiModelProperty(value = "报价方案 1推荐方案 2续保方案")
    private String quoteScheme;

    @ApiModelProperty(value = "场景")
    @NotNull
    private SceneEnum scene;

    @NotNull
    @ApiModelProperty(value = "非标信息")
    @Valid
    private SpecialInfoDTO specialInfo;

    @ApiModelProperty(value = "特约信息")
    private List<ViQuoteSpecialAgreementDTO> specialAgreementDtoList;

    @ApiModelProperty(value = "新车购置开票信息")
    private ViQuoteInvoiceInfoTpDTO viQuoteInvoiceInfoTpDTO;

    @ApiModelProperty(value = "核保告知")
    private String underwritingInform;

    @ApiModelProperty(value = "报价方式 AutoMarkEnum 0:人工报价,1:自动报价,3:移动集成出单,4:系统自动报价(续保自动报价) 默认1自动报价")
    private String  autoMark="1";

    /**
     * 请求来源-是否来自快捷修改
     */
    private Boolean isQuickUpdate;

}
