package com.tuns.car.core.dto.car;

import com.tuns.car.core.dto.gi.CarSameSaleGiOrderInfoVO;
import com.tuns.car.core.entity.*;
import com.tuns.car.core.vo.car.OrderQueryVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/11
 */
@Data
public class InsOrderDTO {

    /**
     * 订单状态 6等待支付,7支付成功,11支付失败
     */
    @ApiModelProperty(value = "订单状态 6等待支付,7支付成功,11支付失败", required = true)
    private String orderStatus;

    /**
     * 订单标题
     */
    @ApiModelProperty(value = "订单标题", required = true)
    private String orderTitle;

    /**
     * 支付方式 client 客户端, weChat直接微信, weChatOnly仅支持微信打开, aliPay直接支付宝,
     * aliPayOnly仅支持支付宝打开, unionPay银联, kuaiQian快钱, yeepay易宝支付, noneType立即支付,
     * other其它方式
     */
    @ApiModelProperty(value = "支付方式 client 客户端, weChat直接微信, weChatOnly仅支持微信打开, aliPay直接支付宝, aliPayOnly仅支持支付宝打开, unionPay银联, kuaiQian快钱, yeepay易宝支付, noneType立即支付, other其它方式", required = true)
    private List<String> payList;

    /**
     * 当前的支付方式
     */
    @ApiModelProperty("当前的支付方式")
    private String currentPayType;

    /**
     * 支付转账数据
     */
    @ApiModelProperty(value = "支付转账数据")
    private List<OrderQueryVO.PayeeData> payeeInfo;

    /**
     * 业务类型标志
     */
    @ApiModelProperty(value = "业务类型标志 VI车险,PI个险,GI团险", required = true)
    private String mark;

    /**
     * 是否支付
     */
    @ApiModelProperty(value = "是否支付 0否,1是", required = true)
    private String payState;

    /**
     * 订单信息
     */
    private ViOrderInfo orderInfo;

    /**
     * 险种信息
     */
    private List<ViInsPlcyInfTp> insList;

    /**
     * 关系人信息
     */
    private List<PubPersInfTp> persInfTp;

    /**
     * 车辆信息
     */
    private ViInsCarInfTp insCar;

    /**
     * 支付url
     */
    private String payUrl;

    /**
     * 非车订单
     */
    private List<CarSameSaleGiOrderInfoVO> noCarOrderDTOS;

    /**
     * 需要发送验证码
     */
    private String needIssueCode;

    /**
     * 渠道类型 core ： api
     */
    private String chanType;
}
