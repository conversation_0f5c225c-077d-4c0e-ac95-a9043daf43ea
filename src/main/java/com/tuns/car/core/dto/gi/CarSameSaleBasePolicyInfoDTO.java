package com.tuns.car.core.dto.gi;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 非车险同步个险请求数据
 *
 * <AUTHOR>
 * @since 2022/5/6
 */
@Data
public class CarSameSaleBasePolicyInfoDTO {

    /**
     * 保单id
     */
    private Long policyId;

    /**
     * 保单编号
     */
    @NotBlank(message = "非车保单号不能为空")
    private String policyNumber;

    /**
     * 保险公司ID
     */
    private String companyId;

    /**
     * 合作渠道ID
     */
    private String channelId;

    /**
     * 产品ID
     */
    @NotNull
    private Integer prodId;

    /**
     * 产品名称
     */
    private String prodName;

    /**
     * 计划ID
     */
    @NotNull
    private Integer projectId;

    /**
     * 计划名称
     */
    private String projectName;

    /**
     * 保费
     */
    @NotNull(message = "非车保费不能为空")
    private BigDecimal prem;


    /**
     * 起保时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "非车起保日期不能为空")
    private LocalDateTime startTime;

    /**
     * 终保时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "非车终保日期不能为空")
    private LocalDateTime endTime;

    /**
     * 份数
     */
    private Short copies;

    /**
     * 是否同步关系人信息
     */
    private Boolean isSynchronization;

    /**
     * 备注
     */
    private String remark;
}
