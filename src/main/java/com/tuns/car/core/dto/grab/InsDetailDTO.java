package com.tuns.car.core.dto.grab;

import com.tuns.car.core.constant.SecondaryNewCarEnum;
import com.tuns.car.core.dto.car.QuoteKindDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/19 11:02
 */
@Data
@Accessors(chain = true)
public class InsDetailDTO implements Serializable {

    private static final long serialVersionUID = -696160595581748348L;
    private String policyId;
    private String policyBatchId;
    private String serialNumber;
    private String policyNumber;
    private String insBegin;
    private String insEnd;
    private Double insuredPremium;
    private Double attachPremium;
    private Double totalPremium;
    private Double insAmount;
    private Double insuredReward;
    private Double attachReward;
    private Integer lastClaimCount;
    private String recordScore;
    private Double rewardPercent;
    private Double totalReward;
    private Double discount;
    private String documentNumber;
    private String invoiceNumber;
    private String ciMarkNumber;
    private Double tax;
    private Double standardPremium;
    private String lastPolicyId;
    private String endorsementNumber;
    private Double netPremium;
    private Double taxRate;
    private String policyType;
    private String policyNewMark;
    private String endorsementType;
    private String carTaxType;
    private String specialAgreement;
    /**
     * 车慧达评级
     * */
    private String cheHuidaTranche;
    /**
     * 车损险北理新能源评分
     * */
    private String bitDamageScore;
    /**
     * 投保状态
     */
    private String insuredStatus;
    /**
     * 投保子状态
     */
    private String insuredSubStatus;
    /**
     * 询价人备注类型
     */
    private String inquirerMsgType;
    /**
     * 询价人备注
     */
    private String inquirerMsg;
    /**
     * 冲账状态 0未冲,1被冲,2对冲,3已冲
     */
    private String reverseState;
    private String reversePolicyId;
    /**
     * 签单时间
     */
    private LocalDateTime insuredTime;
    /**
     * 保费计算识别码/保险公司订单号码
     */
    private String insPremiumNumber;
    /**
     * 佣金率(全保费口径)
     */
    private Double sumRate;
    /**
     * 佣金费
     */
    private Double sumAmt;
    /**
     * 是否匹配到基础政策 0 未匹配到，1 匹配1个，2 匹配多个
     */
    private String mateBasePolicy;
    private Object[] mdfColumn;//变更过的字段
    private Date creTm;

    /**
     * 交强险预期赔付率
     */
    private BigDecimal lossRatioCi;

    /**
     * 商业险预期赔付率
     */
    private BigDecimal lossRatioBi;

    /**
     * 保单成本率
     */
    private BigDecimal policyCostRate;

    /**
     * 业务分组
     */
    private String businessGroup;

    /**
     * 客户评分等级
     */
    private BigDecimal customerRiskRating;

    /**
     * 自主定价系数
     */
    private BigDecimal independentPriceRate;

    /**
     * ncd系数级别
     */
    private BigDecimal noClaimLevel;

    /**
     * 整单预期赔付率
     */
    private BigDecimal totalRatio;

    /**
     * 整单预期赔付率2
     */

    private BigDecimal totalRatio2;

    /**
     * 私家车车联网分档
     */
    private String carSpreading;

    /**
     * 网约车分级
     */
    private BigDecimal wyCarType;

    /**
     * 无赔款折扣系数(NCD系数)
     */
    private BigDecimal noClaimDiscount;

    /**
     * 期望折扣双系数
     */
    private String expectedDiscount;

    /**
     * 大家分
     */
    private String allScore;

    /**
     * 商业险手续费
     */
    private BigDecimal handFeeBi;

    /**
     * 交强险手续费
     */
    private BigDecimal handFeeCi;

    /**
     * 天马指数-核保
     */
    private BigDecimal tianMaIndexFloorPrice;
    /**
     * 天马指数-标费
     */
    private BigDecimal tianMaIndexBasicPrice;
    /**
     * 三者+车损分档
     */
    private String thirdCarRank;
    /**
     * 交强险续保提前天数
     */
    private Integer daysInAdvanceOfRenewalCi;
    /**
     * 商业险续保提前天数
     */
    private Integer daysInAdvanceOfRenewalBi;
    /**
     * 险种数据
     */
    private List<QuoteKindDTO> kindList;
    /**
     * 佣金信息数据
     */
    private List<InsPlcyCommDTO> insPlcyComms;
    /**
     * 删除标志 1-已删除 0-存储'
     */
    private String delFlag;
    /**
     * 保单审核时间
     */
    private LocalDateTime policyReviewTime;

    /**
     * 交强险精算纯风险保费
     */
    private BigDecimal actuarialPureRiskPremiumCi;
    /**
     * 商业险精算纯风险保费
     */
    private BigDecimal actuarialPureRiskPremiumBi;

    /**
     * 连续承保年数
     */
    private BigDecimal underwritingPeriod;

    /**
     * 连续承保期间出险次数
     */
    private BigDecimal numberOfUnderwriting;

    /**
     * 车辆的评分
     */
    private BigDecimal vehicleRating;

    /**
     * 渠道id
     */
    private String chanDetailId;
    /**
     * 项目代码
     */
    private String projectCode;
    /**
     * 上年度出单渠道类型
     */
    private String lastChannelType;

    /**
     * 交通违法系数
     */
    private BigDecimal trafficViolationCoefficient;

    /**
     * NCD标费预期赔付率
     */
    private BigDecimal ncdEcompensationRate;
    /**
     * 交商合计含NCD标准保费预期赔付率
     */
    private BigDecimal ncdTotalEcompensationRate;
    /**
     * 商业险含NCD标准预期赔付率
     */
    private BigDecimal ncdEcompensationRateBi;

    /**
     * 自主评分(长安)
     */
    private BigDecimal selfScore;

    /**
     * 业务类型 1-新保 2-续保 3-转保
     */
    @ApiModelProperty(value = "业务类型 1-新保 2-续保 3-转保")
    private String insuredType;
    /**
     * 本渠道续保标志
     */
    @ApiModelProperty(value = "本渠道续保标志")
    private String thisRenewal;

    /**
     * 无赔优系数级别
     */
    private String claimAdjustLevel;
    
    /**
     * 上年出险次数（交商合计）
     */
    @ApiModelProperty(value = "上年出险次数（交商合计）")
    private Integer lastClaimCountTotal;

    /**
     * 是否真单交（紫金）
     */
    private String isDjq;

    /**
     * 建议自主定价系数
     */
    private BigDecimal proposeIndependentPriceRate;

    /**
     * 交强  业务类型 1-新保 2-续保 3-转保
     */
    private String insuredTypeCi;
    /**
     * 商业 业务类型 1-新保 2-续保 3-转保
     */
    private String insuredTypeBi;

    /**
     * 次新车
     */
    @ApiModelProperty(value = "次新车")
    private SecondaryNewCarEnum secondaryNewCar;

    /**
     * 交强新转续二级标识
     */
    private String thisRenewalCi;
    /**
     * 商业新转续二级标识
     */
    private String thisRenewalBi;

    @ApiModelProperty(value = "人保归属机构")
    private String piccBelongDepartment;
}
