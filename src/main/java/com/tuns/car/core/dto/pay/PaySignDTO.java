package com.tuns.car.core.dto.pay;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/6/20
 */
@Data
public class PaySignDTO {

    /**
     * 支付链接地址/支付二维码图片链接
     */
    private String url;

    /**
     * 失效时间 例：06月20日14:51
     */
    private Date expireTime;

    /**
     * 类型
     */
    private PaySignTypeEnum type;

    /**
     * 是否需要从oss下载
     */
    private boolean needDownload = false;

    public PaySignDTO() {
    }

    public PaySignDTO(String url, PaySignTypeEnum type) {
        this.url = url;
        this.type = type;
    }

    public PaySignDTO(String url, Date expireTime, PaySignTypeEnum type) {
        this.url = url;
        this.expireTime = expireTime;
        this.type = type;
    }

    public enum PaySignTypeEnum {

        /**
         * 支付页面链接
         */
        LINK,

        /**
         * 二维码图片链接
         */
        QR_CODE_URL

    }
}
