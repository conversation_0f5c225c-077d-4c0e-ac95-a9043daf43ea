package com.tuns.car.core.dto;

import com.tuns.car.core.entity.ViInsCarInfTp;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import com.tuns.car.core.entity.ViOrderInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CancelInsuredDTO implements Serializable {

    /**
     * 报价单号
     */
    private String quotationNo;

    /**
     * 渠道ID
     */
    private Integer chanDetailId;


    /**
     * 保险公司ID
     */
    private String companyId;

    /**
     * 投保单号（国任需要用）
     */
    private String proposalNumber;

    private String phone;

    private String recordType;

    private List<ViInsPlcyInfTp> viInsPlcyInfTps;

    /**
     * 订单信息
     */
    private ViOrderInfo viOrderInfo;
    /**
     * 0：报价单 1：保单
     */
    private String isOrderAndQuotation;

    /**
     * 车辆信息
     */
    private ViInsCarInfTp carInfo;
}
