package com.tuns.car.core.dto.cpic.premium;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName CpicPremiumDTO
 * @Description 太平洋原始请求报文dto对象（ 主体）
 * <AUTHOR>
 * @Date 2022/02/14 09:36:58
 * @Version 1.0
 */
@Data
public class CpicPremiumDTO implements Serializable {

    private String quotationNo;
    private Boolean commercial;
    private Boolean compulsory;
    private CpicCompulsoryInsuransDTO compulsoryInsuransVo;
    private CpicCommercialInsuransDTO commercialInsuransVo;
    private List<CpicQuoteInsuranceDTO> quoteInsuranceVos;
    private Boolean accident;
    private Integer isHolidaysDouble;
    private Integer ifAllroundClause;
    private String schemeId;
    private CpicPlatformDTO platformVo;
    private String productFlag;
    private String isRenew;
    private BigDecimal amount;
    private CpicEcarDTO ecarvo;

}
