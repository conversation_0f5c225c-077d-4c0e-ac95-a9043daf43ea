package com.tuns.car.core.dto.template;


import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/1/18
 */
@Data
@Accessors(chain = true)
public class RepeatProposalMsgDTO {

    /**
     * 是否已经修复
     */
    private boolean hasFixed;

    /**
     * 商业/交强
     */
    private InsType type;

    /**
     * 消息中的上张保单 起保日期、终保日期
     */
    private StartTimeDTO insDate;

    /**
     * 不包含日期
     */
    private Boolean noDate = false;

    public enum InsType {
        /**
         * 交强
         */
        CI,

        /**
         * 商业
         */
        BI
    }
}
