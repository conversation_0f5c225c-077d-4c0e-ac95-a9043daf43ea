package com.tuns.car.core.dto.carprocess.premium;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 车险报价单位企业信息
 *
 * <AUTHOR>
 * @since 2024-06-27 11:32
 **/
@Data
@Accessors(chain = true)
public class ViQuotationEnterpriseInfoDTO implements Serializable {
    private static final long serialVersionUID = 185957636416814092L;

    /**
     * 报价批次ID
     */
    private Long policyBatchId;
    /**
     * 单位组织机构类型
     */
    private String organizationType;

    /**
     * 单位电话总机
     */
    private String phone;

    /**
     * 单位联系人姓名
     */
    private String contactName;

    /**
     * 单位联系人方式 手机号码
     */
    private String phoneNumber;

    /**
     * 单位经办人姓名
     */
    private String operatorName;

    /**
     * 单位经办人证件号
     */
    private String credentialNo;

    /**
     * 单位经办人邮箱
     */
    private String operatorEmail;
}
