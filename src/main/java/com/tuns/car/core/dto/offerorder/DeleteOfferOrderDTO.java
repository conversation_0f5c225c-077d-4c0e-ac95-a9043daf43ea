package com.tuns.car.core.dto.offerorder;

import com.tuns.car.core.constant.InsEnum;
import com.tuns.car.core.dto.car.IssueOrderChanInfoDTO;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/***
 * 删除报价单dto对象
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/7 15:31
 */
@Getter
@Setter
@Data
public class DeleteOfferOrderDTO extends IssueOrderChanInfoDTO implements Serializable {


    private static final long serialVersionUID = 2037666909897851293L;

    /**
     * 保险公司枚举
     */
    private InsEnum insEnum;

    /**
     * 报价单信息
     */
    private List<ViInsPlcyInfTp> plcyInfs;

    /**
     * 车牌号
     */
    private String plateNumber;
}
