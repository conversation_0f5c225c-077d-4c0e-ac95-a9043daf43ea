/**
  * Copyright 2021 json.cn 
  */
package com.tuns.car.core.dto.gi.msb;
import java.util.Date;

/**
 * Auto-generated: 2021-11-01 15:12:57
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class MsbList {

    private String agentCode;
    private String amount;
    private String appEmail;
    private String brchCode;
    private String cardType;
    private String channelCode;
    private String classes_code;
    private String classes_name;
    private long createTime;
    private String groupOrderId;
    private String hdlrCode;
    private String idNumber;
    private Date insuranceEndDate;
    private Date insuranceStartDate;
    private String isCombineProduct;
    private String orderAmount;
    private String orderNo;
    private String otherSource;
    private String planCode;
    private String planName;
    private String policyNo;
    private String productCode;
    private String productName;
    private String quotationNo;
    private String status;
    private String subChannelCode;
    private String telphone;
    private String userName;
    public void setAgentCode(String agentCode) {
         this.agentCode = agentCode;
     }
     public String getAgentCode() {
         return agentCode;
     }

    public void setAmount(String amount) {
         this.amount = amount;
     }
     public String getAmount() {
         return amount;
     }

    public void setAppEmail(String appEmail) {
         this.appEmail = appEmail;
     }
     public String getAppEmail() {
         return appEmail;
     }

    public void setBrchCode(String brchCode) {
         this.brchCode = brchCode;
     }
     public String getBrchCode() {
         return brchCode;
     }

    public void setCardType(String cardType) {
         this.cardType = cardType;
     }
     public String getCardType() {
         return cardType;
     }

    public void setChannelCode(String channelCode) {
         this.channelCode = channelCode;
     }
     public String getChannelCode() {
         return channelCode;
     }

    public void setClasses_code(String classes_code) {
         this.classes_code = classes_code;
     }
     public String getClasses_code() {
         return classes_code;
     }

    public void setClasses_name(String classes_name) {
         this.classes_name = classes_name;
     }
     public String getClasses_name() {
         return classes_name;
     }

    public void setCreateTime(long createTime) {
         this.createTime = createTime;
     }
     public long getCreateTime() {
         return createTime;
     }

    public void setGroupOrderId(String groupOrderId) {
         this.groupOrderId = groupOrderId;
     }
     public String getGroupOrderId() {
         return groupOrderId;
     }

    public void setHdlrCode(String hdlrCode) {
         this.hdlrCode = hdlrCode;
     }
     public String getHdlrCode() {
         return hdlrCode;
     }

    public void setIdNumber(String idNumber) {
         this.idNumber = idNumber;
     }
     public String getIdNumber() {
         return idNumber;
     }

    public void setInsuranceEndDate(Date insuranceEndDate) {
         this.insuranceEndDate = insuranceEndDate;
     }
     public Date getInsuranceEndDate() {
         return insuranceEndDate;
     }

    public void setInsuranceStartDate(Date insuranceStartDate) {
         this.insuranceStartDate = insuranceStartDate;
     }
     public Date getInsuranceStartDate() {
         return insuranceStartDate;
     }

    public void setIsCombineProduct(String isCombineProduct) {
         this.isCombineProduct = isCombineProduct;
     }
     public String getIsCombineProduct() {
         return isCombineProduct;
     }

    public void setOrderAmount(String orderAmount) {
         this.orderAmount = orderAmount;
     }
     public String getOrderAmount() {
         return orderAmount;
     }

    public void setOrderNo(String orderNo) {
         this.orderNo = orderNo;
     }
     public String getOrderNo() {
         return orderNo;
     }

    public void setOtherSource(String otherSource) {
         this.otherSource = otherSource;
     }
     public String getOtherSource() {
         return otherSource;
     }

    public void setPlanCode(String planCode) {
         this.planCode = planCode;
     }
     public String getPlanCode() {
         return planCode;
     }

    public void setPlanName(String planName) {
         this.planName = planName;
     }
     public String getPlanName() {
         return planName;
     }

    public void setPolicyNo(String policyNo) {
         this.policyNo = policyNo;
     }
     public String getPolicyNo() {
         return policyNo;
     }

    public void setProductCode(String productCode) {
         this.productCode = productCode;
     }
     public String getProductCode() {
         return productCode;
     }

    public void setProductName(String productName) {
         this.productName = productName;
     }
     public String getProductName() {
         return productName;
     }

    public void setQuotationNo(String quotationNo) {
         this.quotationNo = quotationNo;
     }
     public String getQuotationNo() {
         return quotationNo;
     }

    public void setStatus(String status) {
         this.status = status;
     }
     public String getStatus() {
         return status;
     }

    public void setSubChannelCode(String subChannelCode) {
         this.subChannelCode = subChannelCode;
     }
     public String getSubChannelCode() {
         return subChannelCode;
     }

    public void setTelphone(String telphone) {
         this.telphone = telphone;
     }
     public String getTelphone() {
         return telphone;
     }

    public void setUserName(String userName) {
         this.userName = userName;
     }
     public String getUserName() {
         return userName;
     }

}