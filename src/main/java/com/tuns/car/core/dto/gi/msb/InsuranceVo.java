/**
  * Copyright 2021 json.cn 
  */
package com.tuns.car.core.dto.gi.msb;
import java.util.Date;

/**
 * Auto-generated: 2021-11-01 15:12:57
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class InsuranceVo {

    private String accidentInfo;
    private String accidentPremium;
    private String backAmount;
    private String businesspoudage;
    private double changeableFeeRate;
    private double channelRate;
    private String commContinuedInsuredYears;
    private String commercialAmount;
    private String commercialInsuranceConfirmCode;
    private String commercialInsuranceQueryCode;
    private String commercialPeriod;
    private String commercialRate;
    private String compContinuedInsuredYears;
    private String compCpicScore;
    private String compNetEcompensationRate;
    private String compulsoryAmount;
    private String compulsoryInsuranceConfirmCode;
    private String compulsoryInsuranceQueryCode;
    private String compulsoryPeriod;
    private String cpicScore;
    private String deductionDueProportion;
    private String exceedDaysCount;
    private String fleetTotalEcompensationRate;
    private String independentPriceRate;
    private String jqFleetEcompensationRate;
    private String jqbusinessfee;
    private String jqecompensationRate;
    private String jqendDate;
    private String jqperformance;
    private String jqstartDate;
    private String lateFee;
    private String localCalTax;
    private String msbPremium;
    private String ncdEcompensationRate;
    private String ncdTotalEcompensationRate;
    private String netEcompensationRate;
    private double nonClaimDiscountRate;
    private String payableAmount;
    private double policyCostRate;
    private String poudage;
    private String premiumCalculationScheme;
    private String reductionAmount;
    private String registryNumber;
    private String secretTaxpayerNo;
    private String secretTaxpayerRecno;
    private String serviceValueAddRate;
    private String stChannelRate;
    private String stNonClaimDiscountRate;
    private Date stTaxEndDate;
    private Date stTaxStartDate;
    private String stTrafficTransgressRate;
    private String syFleetEcompensationRate;
    private String sybusinessfee;
    private String syecompensationRate;
    private String syespcompensationRate;
    private String syperformance;
    private String tax;
    private String taxCustomerType;
    private Date taxEndDate;
    private Date taxStartDate;
    private String taxTimePeriod;
    private String taxType;
    private String taxTypeCode;
    private String taxVehicleType;
    private String taxpayerName;
    private String taxpayerNo;
    private String taxpayerRecno;
    private String taxpayerSex;
    private String taxpayerType;
    private String totalCpicScore;
    private String totalEcompensationRate;
    private String totalNetEcompensationRate;
    private String totalPolicyCostRate;
    private String totalPremium;
    private int trafficTransgressRate;
    private String trafficpoudage;
    public void setAccidentInfo(String accidentInfo) {
         this.accidentInfo = accidentInfo;
     }
     public String getAccidentInfo() {
         return accidentInfo;
     }

    public void setAccidentPremium(String accidentPremium) {
         this.accidentPremium = accidentPremium;
     }
     public String getAccidentPremium() {
         return accidentPremium;
     }

    public void setBackAmount(String backAmount) {
         this.backAmount = backAmount;
     }
     public String getBackAmount() {
         return backAmount;
     }

    public void setBusinesspoudage(String businesspoudage) {
         this.businesspoudage = businesspoudage;
     }
     public String getBusinesspoudage() {
         return businesspoudage;
     }

    public void setChangeableFeeRate(double changeableFeeRate) {
         this.changeableFeeRate = changeableFeeRate;
     }
     public double getChangeableFeeRate() {
         return changeableFeeRate;
     }

    public void setChannelRate(double channelRate) {
         this.channelRate = channelRate;
     }
     public double getChannelRate() {
         return channelRate;
     }

    public void setCommContinuedInsuredYears(String commContinuedInsuredYears) {
         this.commContinuedInsuredYears = commContinuedInsuredYears;
     }
     public String getCommContinuedInsuredYears() {
         return commContinuedInsuredYears;
     }

    public void setCommercialAmount(String commercialAmount) {
         this.commercialAmount = commercialAmount;
     }
     public String getCommercialAmount() {
         return commercialAmount;
     }

    public void setCommercialInsuranceConfirmCode(String commercialInsuranceConfirmCode) {
         this.commercialInsuranceConfirmCode = commercialInsuranceConfirmCode;
     }
     public String getCommercialInsuranceConfirmCode() {
         return commercialInsuranceConfirmCode;
     }

    public void setCommercialInsuranceQueryCode(String commercialInsuranceQueryCode) {
         this.commercialInsuranceQueryCode = commercialInsuranceQueryCode;
     }
     public String getCommercialInsuranceQueryCode() {
         return commercialInsuranceQueryCode;
     }

    public void setCommercialPeriod(String commercialPeriod) {
         this.commercialPeriod = commercialPeriod;
     }
     public String getCommercialPeriod() {
         return commercialPeriod;
     }

    public void setCommercialRate(String commercialRate) {
         this.commercialRate = commercialRate;
     }
     public String getCommercialRate() {
         return commercialRate;
     }

    public void setCompContinuedInsuredYears(String compContinuedInsuredYears) {
         this.compContinuedInsuredYears = compContinuedInsuredYears;
     }
     public String getCompContinuedInsuredYears() {
         return compContinuedInsuredYears;
     }

    public void setCompCpicScore(String compCpicScore) {
         this.compCpicScore = compCpicScore;
     }
     public String getCompCpicScore() {
         return compCpicScore;
     }

    public void setCompNetEcompensationRate(String compNetEcompensationRate) {
         this.compNetEcompensationRate = compNetEcompensationRate;
     }
     public String getCompNetEcompensationRate() {
         return compNetEcompensationRate;
     }

    public void setCompulsoryAmount(String compulsoryAmount) {
         this.compulsoryAmount = compulsoryAmount;
     }
     public String getCompulsoryAmount() {
         return compulsoryAmount;
     }

    public void setCompulsoryInsuranceConfirmCode(String compulsoryInsuranceConfirmCode) {
         this.compulsoryInsuranceConfirmCode = compulsoryInsuranceConfirmCode;
     }
     public String getCompulsoryInsuranceConfirmCode() {
         return compulsoryInsuranceConfirmCode;
     }

    public void setCompulsoryInsuranceQueryCode(String compulsoryInsuranceQueryCode) {
         this.compulsoryInsuranceQueryCode = compulsoryInsuranceQueryCode;
     }
     public String getCompulsoryInsuranceQueryCode() {
         return compulsoryInsuranceQueryCode;
     }

    public void setCompulsoryPeriod(String compulsoryPeriod) {
         this.compulsoryPeriod = compulsoryPeriod;
     }
     public String getCompulsoryPeriod() {
         return compulsoryPeriod;
     }

    public void setCpicScore(String cpicScore) {
         this.cpicScore = cpicScore;
     }
     public String getCpicScore() {
         return cpicScore;
     }

    public void setDeductionDueProportion(String deductionDueProportion) {
         this.deductionDueProportion = deductionDueProportion;
     }
     public String getDeductionDueProportion() {
         return deductionDueProportion;
     }

    public void setExceedDaysCount(String exceedDaysCount) {
         this.exceedDaysCount = exceedDaysCount;
     }
     public String getExceedDaysCount() {
         return exceedDaysCount;
     }

    public void setFleetTotalEcompensationRate(String fleetTotalEcompensationRate) {
         this.fleetTotalEcompensationRate = fleetTotalEcompensationRate;
     }
     public String getFleetTotalEcompensationRate() {
         return fleetTotalEcompensationRate;
     }

    public void setIndependentPriceRate(String independentPriceRate) {
         this.independentPriceRate = independentPriceRate;
     }
     public String getIndependentPriceRate() {
         return independentPriceRate;
     }

    public void setJqFleetEcompensationRate(String jqFleetEcompensationRate) {
         this.jqFleetEcompensationRate = jqFleetEcompensationRate;
     }
     public String getJqFleetEcompensationRate() {
         return jqFleetEcompensationRate;
     }

    public void setJqbusinessfee(String jqbusinessfee) {
         this.jqbusinessfee = jqbusinessfee;
     }
     public String getJqbusinessfee() {
         return jqbusinessfee;
     }

    public void setJqecompensationRate(String jqecompensationRate) {
         this.jqecompensationRate = jqecompensationRate;
     }
     public String getJqecompensationRate() {
         return jqecompensationRate;
     }

    public void setJqendDate(String jqendDate) {
         this.jqendDate = jqendDate;
     }
     public String getJqendDate() {
         return jqendDate;
     }

    public void setJqperformance(String jqperformance) {
         this.jqperformance = jqperformance;
     }
     public String getJqperformance() {
         return jqperformance;
     }

    public void setJqstartDate(String jqstartDate) {
         this.jqstartDate = jqstartDate;
     }
     public String getJqstartDate() {
         return jqstartDate;
     }

    public void setLateFee(String lateFee) {
         this.lateFee = lateFee;
     }
     public String getLateFee() {
         return lateFee;
     }

    public void setLocalCalTax(String localCalTax) {
         this.localCalTax = localCalTax;
     }
     public String getLocalCalTax() {
         return localCalTax;
     }

    public void setMsbPremium(String msbPremium) {
         this.msbPremium = msbPremium;
     }
     public String getMsbPremium() {
         return msbPremium;
     }

    public void setNcdEcompensationRate(String ncdEcompensationRate) {
         this.ncdEcompensationRate = ncdEcompensationRate;
     }
     public String getNcdEcompensationRate() {
         return ncdEcompensationRate;
     }

    public void setNcdTotalEcompensationRate(String ncdTotalEcompensationRate) {
         this.ncdTotalEcompensationRate = ncdTotalEcompensationRate;
     }
     public String getNcdTotalEcompensationRate() {
         return ncdTotalEcompensationRate;
     }

    public void setNetEcompensationRate(String netEcompensationRate) {
         this.netEcompensationRate = netEcompensationRate;
     }
     public String getNetEcompensationRate() {
         return netEcompensationRate;
     }

    public void setNonClaimDiscountRate(double nonClaimDiscountRate) {
         this.nonClaimDiscountRate = nonClaimDiscountRate;
     }
     public double getNonClaimDiscountRate() {
         return nonClaimDiscountRate;
     }

    public void setPayableAmount(String payableAmount) {
         this.payableAmount = payableAmount;
     }
     public String getPayableAmount() {
         return payableAmount;
     }

    public void setPolicyCostRate(double policyCostRate) {
         this.policyCostRate = policyCostRate;
     }
     public double getPolicyCostRate() {
         return policyCostRate;
     }

    public void setPoudage(String poudage) {
         this.poudage = poudage;
     }
     public String getPoudage() {
         return poudage;
     }

    public void setPremiumCalculationScheme(String premiumCalculationScheme) {
         this.premiumCalculationScheme = premiumCalculationScheme;
     }
     public String getPremiumCalculationScheme() {
         return premiumCalculationScheme;
     }

    public void setReductionAmount(String reductionAmount) {
         this.reductionAmount = reductionAmount;
     }
     public String getReductionAmount() {
         return reductionAmount;
     }

    public void setRegistryNumber(String registryNumber) {
         this.registryNumber = registryNumber;
     }
     public String getRegistryNumber() {
         return registryNumber;
     }

    public void setSecretTaxpayerNo(String secretTaxpayerNo) {
         this.secretTaxpayerNo = secretTaxpayerNo;
     }
     public String getSecretTaxpayerNo() {
         return secretTaxpayerNo;
     }

    public void setSecretTaxpayerRecno(String secretTaxpayerRecno) {
         this.secretTaxpayerRecno = secretTaxpayerRecno;
     }
     public String getSecretTaxpayerRecno() {
         return secretTaxpayerRecno;
     }

    public void setServiceValueAddRate(String serviceValueAddRate) {
         this.serviceValueAddRate = serviceValueAddRate;
     }
     public String getServiceValueAddRate() {
         return serviceValueAddRate;
     }

    public void setStChannelRate(String stChannelRate) {
         this.stChannelRate = stChannelRate;
     }
     public String getStChannelRate() {
         return stChannelRate;
     }

    public void setStNonClaimDiscountRate(String stNonClaimDiscountRate) {
         this.stNonClaimDiscountRate = stNonClaimDiscountRate;
     }
     public String getStNonClaimDiscountRate() {
         return stNonClaimDiscountRate;
     }

    public void setStTaxEndDate(Date stTaxEndDate) {
         this.stTaxEndDate = stTaxEndDate;
     }
     public Date getStTaxEndDate() {
         return stTaxEndDate;
     }

    public void setStTaxStartDate(Date stTaxStartDate) {
         this.stTaxStartDate = stTaxStartDate;
     }
     public Date getStTaxStartDate() {
         return stTaxStartDate;
     }

    public void setStTrafficTransgressRate(String stTrafficTransgressRate) {
         this.stTrafficTransgressRate = stTrafficTransgressRate;
     }
     public String getStTrafficTransgressRate() {
         return stTrafficTransgressRate;
     }

    public void setSyFleetEcompensationRate(String syFleetEcompensationRate) {
         this.syFleetEcompensationRate = syFleetEcompensationRate;
     }
     public String getSyFleetEcompensationRate() {
         return syFleetEcompensationRate;
     }

    public void setSybusinessfee(String sybusinessfee) {
         this.sybusinessfee = sybusinessfee;
     }
     public String getSybusinessfee() {
         return sybusinessfee;
     }

    public void setSyecompensationRate(String syecompensationRate) {
         this.syecompensationRate = syecompensationRate;
     }
     public String getSyecompensationRate() {
         return syecompensationRate;
     }

    public void setSyespcompensationRate(String syespcompensationRate) {
         this.syespcompensationRate = syespcompensationRate;
     }
     public String getSyespcompensationRate() {
         return syespcompensationRate;
     }

    public void setSyperformance(String syperformance) {
         this.syperformance = syperformance;
     }
     public String getSyperformance() {
         return syperformance;
     }

    public void setTax(String tax) {
         this.tax = tax;
     }
     public String getTax() {
         return tax;
     }

    public void setTaxCustomerType(String taxCustomerType) {
         this.taxCustomerType = taxCustomerType;
     }
     public String getTaxCustomerType() {
         return taxCustomerType;
     }

    public void setTaxEndDate(Date taxEndDate) {
         this.taxEndDate = taxEndDate;
     }
     public Date getTaxEndDate() {
         return taxEndDate;
     }

    public void setTaxStartDate(Date taxStartDate) {
         this.taxStartDate = taxStartDate;
     }
     public Date getTaxStartDate() {
         return taxStartDate;
     }

    public void setTaxTimePeriod(String taxTimePeriod) {
         this.taxTimePeriod = taxTimePeriod;
     }
     public String getTaxTimePeriod() {
         return taxTimePeriod;
     }

    public void setTaxType(String taxType) {
         this.taxType = taxType;
     }
     public String getTaxType() {
         return taxType;
     }

    public void setTaxTypeCode(String taxTypeCode) {
         this.taxTypeCode = taxTypeCode;
     }
     public String getTaxTypeCode() {
         return taxTypeCode;
     }

    public void setTaxVehicleType(String taxVehicleType) {
         this.taxVehicleType = taxVehicleType;
     }
     public String getTaxVehicleType() {
         return taxVehicleType;
     }

    public void setTaxpayerName(String taxpayerName) {
         this.taxpayerName = taxpayerName;
     }
     public String getTaxpayerName() {
         return taxpayerName;
     }

    public void setTaxpayerNo(String taxpayerNo) {
         this.taxpayerNo = taxpayerNo;
     }
     public String getTaxpayerNo() {
         return taxpayerNo;
     }

    public void setTaxpayerRecno(String taxpayerRecno) {
         this.taxpayerRecno = taxpayerRecno;
     }
     public String getTaxpayerRecno() {
         return taxpayerRecno;
     }

    public void setTaxpayerSex(String taxpayerSex) {
         this.taxpayerSex = taxpayerSex;
     }
     public String getTaxpayerSex() {
         return taxpayerSex;
     }

    public void setTaxpayerType(String taxpayerType) {
         this.taxpayerType = taxpayerType;
     }
     public String getTaxpayerType() {
         return taxpayerType;
     }

    public void setTotalCpicScore(String totalCpicScore) {
         this.totalCpicScore = totalCpicScore;
     }
     public String getTotalCpicScore() {
         return totalCpicScore;
     }

    public void setTotalEcompensationRate(String totalEcompensationRate) {
         this.totalEcompensationRate = totalEcompensationRate;
     }
     public String getTotalEcompensationRate() {
         return totalEcompensationRate;
     }

    public void setTotalNetEcompensationRate(String totalNetEcompensationRate) {
         this.totalNetEcompensationRate = totalNetEcompensationRate;
     }
     public String getTotalNetEcompensationRate() {
         return totalNetEcompensationRate;
     }

    public void setTotalPolicyCostRate(String totalPolicyCostRate) {
         this.totalPolicyCostRate = totalPolicyCostRate;
     }
     public String getTotalPolicyCostRate() {
         return totalPolicyCostRate;
     }

    public void setTotalPremium(String totalPremium) {
         this.totalPremium = totalPremium;
     }
     public String getTotalPremium() {
         return totalPremium;
     }

    public void setTrafficTransgressRate(int trafficTransgressRate) {
         this.trafficTransgressRate = trafficTransgressRate;
     }
     public int getTrafficTransgressRate() {
         return trafficTransgressRate;
     }

    public void setTrafficpoudage(String trafficpoudage) {
         this.trafficpoudage = trafficpoudage;
     }
     public String getTrafficpoudage() {
         return trafficpoudage;
     }

}