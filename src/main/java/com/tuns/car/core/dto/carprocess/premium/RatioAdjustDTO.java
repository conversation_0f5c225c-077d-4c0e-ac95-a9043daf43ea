package com.tuns.car.core.dto.carprocess.premium;

import com.tuns.car.core.constant.RatioConfigFactorEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 调整系数dto
 * <AUTHOR>
 * @date 2023/05/04
 **/
@Data
public class RatioAdjustDTO {

    @ApiModelProperty(value = "需要调整的推荐系数字段")
    private RatioConfigFactorEnum adjustScore;

    @ApiModelProperty(value = "政策返回的需要调整的推荐系数目标值")
    private String adjustScoreTargetValue;

    /**
     * 因子代码
     */
    @ApiModelProperty(value = "因子代码")
    private RatioConfigFactorEnum factor;
    /**
     * 理想值相对浮动
     */
    @ApiModelProperty(value = "理想值相对浮动")
    private BigDecimal expectAdjustValue;
    /**
     * 可接受区间下限相对浮动
     */
    @ApiModelProperty(value = "可接受区间下限相对浮动")
    private BigDecimal acceptRangeLimitValue;
    /**
     * 可接受区间上限相对浮动
     */
    @ApiModelProperty(value = "可接受区间上限相对浮动")
    private BigDecimal acceptRangeUpperValue;
}
