package com.tuns.car.core.dto.grab;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/19 11:14
 */
@Data
@Accessors(chain = true)
public class InsPlcyCommDTO {

    private String policyId;
    private String ruleCode;
    private String isIncludeTax;
    private String viPolicyName;
    private String policyType;
    private String commType;
    private String policyCode;
    private BigDecimal commRate;
    private BigDecimal commAmt;
}
