package com.tuns.car.core.dto.charging;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tuns.car.core.constant.ChargingAddrTypeEnum;
import com.tuns.car.core.constant.ChargingTypeEnum;
import com.tuns.car.core.constant.ChargingUseYearsEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/04/07 10:42
 */
@Data
public class ChargingPileDTO implements Serializable {
    private static final long serialVersionUID = -4588321729641641544L;
    private Integer id;
    @ApiModelProperty("充电桩型号")
    @NotBlank(message = "充电桩型号不能为空")
    private String chargingModel;
    @ApiModelProperty("充电桩编码")
    @NotBlank(message = "充电桩编码不能为空")
    private String chargingCode;
    @ApiModelProperty("充电桩类型")
    @NotBlank(message = "充电桩类型不能为空 01地面充电桩 02壁挂式充电桩 99其他充电桩")
    private ChargingTypeEnum chargingType;
    @ApiModelProperty("充电桩安装地点类型")
    @NotBlank(message = "充电桩安装地点类型不能为空")
    private ChargingAddrTypeEnum chargingInstallAddrType;
    @ApiModelProperty("安装时间")
    @JsonFormat(pattern = "yyyy-MM-dd",locale = "zh",timezone = "GMT+8")
    private LocalDate chargingInstallDate;
    @ApiModelProperty("充电桩使用年限")
    @NotBlank(message = "充电桩使用年限不能为空")
    private ChargingUseYearsEnum chargingUseYears;
    @ApiModelProperty("充电桩完整地址")
    @NotBlank(message = "充电桩完整地址不能为空")
    private String chargingAddrComplete;
    @ApiModelProperty("充电桩详细地址")
    @NotBlank(message = "充电桩详细地址不能为空")
    private String chargingAddrDetail;
    @ApiModelProperty("充电桩险种列表")
    private List<ChargingKind> chargingKindList;
    @ApiModelProperty("省代码")
    private String province;
    @ApiModelProperty("省名称")
    private String provinceName;
    @ApiModelProperty("市代码")
    private String city;
    @ApiModelProperty("市名称")
    private String cityName;
    @ApiModelProperty("区县代码")
    private String county;
    @ApiModelProperty("区县名称")
    private String countyName;
    @ApiModelProperty("是否勾选 1勾选 0不勾选")
    private String chooseFlag;
    @ApiModelProperty("业务主键id")
    private String chargingPileCarId;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
