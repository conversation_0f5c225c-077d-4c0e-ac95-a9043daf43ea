package com.tuns.car.core.dto.template;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用于解析模版主体内容的DTO
 *
 * <AUTHOR>
 * @since 2023-9-8
 */
@Data
public class RepeatTemplateResolveDTO {

    /**
     * 公司ID
     */
    @ApiModelProperty("公司ID")
    private String companyId;
    /**
     * 模版主体
     */
    @ApiModelProperty("模版主体")
    private String templateContent;
    /**
     * 模版ID
     */
    @ApiModelProperty("模版ID")
    private Integer tempTemplateId;
}
