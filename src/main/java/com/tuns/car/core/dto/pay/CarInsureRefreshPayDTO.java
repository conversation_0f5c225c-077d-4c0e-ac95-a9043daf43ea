package com.tuns.car.core.dto.pay;

import com.tuns.car.core.constant.PayTypeEnum;
import com.tuns.car.core.dto.car.IssueOrderChanInfoDTO;
import com.tuns.car.core.dto.gi.CarSameSaleGiOrderInfoVO;
import com.tuns.car.core.entity.PubPersInfTp;
import com.tuns.car.core.entity.ViInsCarInfTp;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName CarInsureRefreshPayDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/4 16:51
 * @Version 1.0
 */

@Data
@Accessors(chain = true)
public class CarInsureRefreshPayDTO extends IssueOrderChanInfoDTO {

    private ViInsPlcyInfTp viInsPlcyInfTp;

    /**
     * 支付方式
     */
    private PayTypeEnum payType;

    /**
     * 非车订单
     */
    private List<CarSameSaleGiOrderInfoVO> noCarList;
    private ViInsPlcyInfTp insPlcyInfBi;

    private ViInsPlcyInfTp insPlcyInfCi;

    private ViInsCarInfTp carInfTp;

    /**
     * 车主信息
     */
    private List<PubPersInfTp> pubPersInfTps;

    private List<ViInsPlcyInfTp> plcyInfList;

    /**
     * 订单保费（包含非车，交商保费）
     */
    private BigDecimal premium;
}
