package com.tuns.car.core.dto.special;

import com.tuns.car.core.constant.InsEnum;
import com.tuns.car.core.dto.pay.CarInsurePaySignDTO;
import com.tuns.car.core.entity.ViInsPlcyInfTp;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2023/05/16/14:51
 **/
@Data
public class CheckCarCodeQueryDTO {

    private String companyCode;

    private Integer chanDetailId;

    private InsEnum company;

    /**
     * 保司订单号
     */
    private String orderNo;
    private Long policyBatchId;

    private List<ViInsPlcyInfTp> plcyInfList;
}
