package com.tuns.car.core.dto.charging;

import com.tuns.car.core.dto.InsuredAmountDetailVO;
import com.tuns.core.boot.constant.YesNoNumberEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2022-04-12 14:59
 **/

@Data
@Accessors(chain = true)
public class ChargingKind implements Serializable {

    @ApiModelProperty("险种代码")
    private String kindCode;
    @ApiModelProperty("险种保额")
    private BigDecimal amount;
    @ApiModelProperty("保额text")
    private String amountLabel;
    @ApiModelProperty("是否选择 1是 0否")
    private String chooseFlag;
    
    @ApiModelProperty(value = "险种保额集合")
    private List<InsuredAmountDetailVO> list;

    @ApiModelProperty(value = "保额模式 0固定保额 1主险比例保额")
    private String insAmountType;

    @ApiModelProperty(value = "主险保额比例（小数）")
    private BigDecimal mainInsAmountRatio;

    /**
     * 是否勾选共享保额 0否 1是
     */
    @ApiModelProperty(value = "是否勾选共享保额 0否 1是")
    private YesNoNumberEnum isSharedAmount;

    /**
     * 是否展示共享保额 0否 1是
     */
    @ApiModelProperty(value = "是否展示共享保额 0否 1是")
    private YesNoNumberEnum isShowSharedAmount;
}
