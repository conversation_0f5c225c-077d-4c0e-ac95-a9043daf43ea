package com.tuns.car.core.dto.cpic.premium;

/**
 * @ClassName CpicQuoteInsuranceDTO
 * @Description 太平洋原始请求报文dto对象（  ）
 * <AUTHOR>
 * @Date 2022/02/14 09:36:58
 * @Version 1.0
 */

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CpicQuoteInsuranceDTO implements Serializable {

    private BigDecimal amount;
    private String insuranceCode; //险种代码
    private String insuranceType; //险种类型
    private Double premium;
    private String stAmount;
    private String stPremium;
    private String stStandardPremium;
    private Double standardPremium;
    private String nonDeductible;

    private List<CpicSubstituteDrivingDTO> substituteDrivingVos;
    private List<CpicFactorDTO> factorVos;
    private List<CpicVehicleSafetyDetectionDTO> vehicleSafetyDetectionVos;

}
