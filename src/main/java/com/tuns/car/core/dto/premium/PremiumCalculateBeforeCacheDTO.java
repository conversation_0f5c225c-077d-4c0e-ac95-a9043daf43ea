package com.tuns.car.core.dto.premium;

import cn.hutool.json.JSONUtil;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 保费计算前置处理（前置、并行）缓存DTO
 * @date 2024-12-19 08:46:30
 */
@Data
public class PremiumCalculateBeforeCacheDTO {
    /**
     * 进度（处理中、处理完成）
     */
    private Progress progress;
    /**
     * 状态（成功、失败、异常）
     */
    private Status status;
    /**
     * json数据（状态为成功有）
     */
    private String json;
    /**
     * 异常信息（状态为失败或异常有）
     */
    private String errorMsg;

    /**
     * 开始执行初始化
     *
     * @return
     */
    public static PremiumCalculateBeforeCacheDTO start() {
        PremiumCalculateBeforeCacheDTO dto = new PremiumCalculateBeforeCacheDTO();
        dto.setProgress(Progress.PROCESSING);
        dto.setStatus(null);
        dto.setJson(null);
        dto.setErrorMsg(null);
        return dto;
    }

    /**
     * 执行完成
     *
     * @return
     */
    public static PremiumCalculateBeforeCacheDTO completed(Status status, Object result, String errorMsg) {
        PremiumCalculateBeforeCacheDTO dto = new PremiumCalculateBeforeCacheDTO();
        dto.setProgress(Progress.COMPLETED);
        dto.setStatus(status);
        if (Objects.nonNull(result)) {
            dto.setJson(JSONUtil.toJsonStr(result));
        }
        dto.setErrorMsg(errorMsg);
        return dto;
    }

    public enum Progress {
        /**
         * 处理中
         */
        PROCESSING,
        /**
         * 处理完成
         */
        COMPLETED
    }

    public enum Status {
        /**
         * 成功
         */
        SUCCESS,
        /**
         * 失败
         */
        FAIL,
        /**
         * 异常
         */
        ERROR
    }
}
