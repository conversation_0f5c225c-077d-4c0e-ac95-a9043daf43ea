package com.tuns.car.core.dto.carprocess.premium;

import com.tuns.car.core.entity.ViInsKindDetialTp;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/04/28
 **/
public class ServiceTermDTO implements Serializable {
    private static final long serialVersionUID = 7096947788759945476L;
    private String kindCode;

    private String key;

    private Integer value;
    @ApiModelProperty(value = "单位 例：款 、档 & 单元")
    private String unit;

    public ServiceTermDTO() {
    }

    public ServiceTermDTO(ViInsKindDetialTp spViInsKindDetial) {
        this.setKindCode(spViInsKindDetial.getKindCode());
        this.setKey(StringUtils.isNotEmpty(spViInsKindDetial.getValueType()) ? spViInsKindDetial.getValueType() : null);
        this.setValue(spViInsKindDetial.getQuantity());
    }

    public String getKindCode() {
        return kindCode;
    }

    public void setKindCode(String kindCode) {
        this.kindCode = kindCode;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Integer getValue() {
        return value;
    }

    public ServiceTermDTO setValue(Integer value) {
        this.value = value;
        return this;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
