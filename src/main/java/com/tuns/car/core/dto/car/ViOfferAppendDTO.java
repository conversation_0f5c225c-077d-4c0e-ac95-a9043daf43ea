package com.tuns.car.core.dto.car;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ViOfferAppendDTO
 * @Description APP车险报价单补传资料请求数据类
 * <AUTHOR> yi
 * @Date 2020/6/8 19:09
 * @Version 1.0
 */
@Data
public class ViOfferAppendDTO implements Serializable {

    private static final long serialVersionUID = 5475609607404318191L;

    /**
     * 报价批次id
     */
    @NotNull(message = "报价批次id不能为空")
    @ApiModelProperty(value = "报价批次id", required = true)
    private Long policyBatchId;

    /**
     * 附件图片集合
     */
    @Valid
    @NotEmpty(message = "附件图片不能为空")
    @ApiModelProperty(value = "附件图片集合", required = true)
    private List<ViOfferImgDTO> imgList;

    /**
     * 报价单补传或保单补传
     */
    @ApiModelProperty(value = "补传标识不能为空", required = true)
    private String isOrderAndQuotation;

  @Override
  public String toString() {
    return JSON.toJSONString(this);
  }

}
