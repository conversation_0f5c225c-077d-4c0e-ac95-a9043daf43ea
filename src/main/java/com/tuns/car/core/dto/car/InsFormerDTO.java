package com.tuns.car.core.dto.car;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 车险查询原保单信息数据返回转换类
 * Created by <PERSON><PERSON><PERSON> on 2018/7/12.
 */
@Data
public class InsFormerDTO {
    /**
     * 保单号
     */
    private String policyNumber;
    /**
     * 开始日期
     */
    private String insBegin;
    /**
     * 结束日期
     */
    private String insEnd;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date payTime;
    /**
     * 承保时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuredTime;
    /**
     * 保单类型
     */
    private String policyType;
    /**
     * 业务来源
     */
    private String recordSource;
    /**
     * 单证号
     */
    private String documentNumber;
    /**
     * 发票编号
     */
    private String invoiceNumber;
    /**
     * 标志号
     */
    private String ciMarkNumber;
    /**
     * +
     * 报价评分
     */
    private String recordScore;
    /**
     * 审核人名称
     */
    private String policyReviewerName;
    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date policyReviewTime;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date creTm;
    /**
     * 创建人名称
     */
    private String creName;
    /**
     * 创建人部门
     */
    private String creDeptName;
    /**
     * 审核人部门
     */
    private String checkDepartName;

}
