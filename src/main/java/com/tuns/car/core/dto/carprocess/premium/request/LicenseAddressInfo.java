package com.tuns.car.core.dto.carprocess.premium.request;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 行驶证详情
 * @date: 2024/5/6 8:58
 */
@Data
public class LicenseAddressInfo {

    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private String province;
    /**
     * 省code
     */
    @ApiModelProperty(value = "省code")
    private String provinceCode;
    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String city;
    /**
     * 市code
     */
    @ApiModelProperty(value = "市code")
    private String cityCode;
    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    private String county;
    /**
     * 区code
     */
    @ApiModelProperty(value = "区code")
    private String countyCode;
    /**
     * 镇
     */
    @ApiModelProperty(value = "镇")
    private String town;
    /**
     * 详细地址
     */
    @JSONField(name = "detail")
    @ApiModelProperty(value = "详细地址")
    private String detail;


    @Override
    public String toString() {
        return province + city + county + detail;
    }
}
