package com.tuns.car.core.dto.car;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-10-17 9:10
 */
@Data
@ApiModel("证件扫描响应类")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CmmCardScanDTO implements Serializable {
    @ApiModelProperty(value = "业务唯一id", required = true, example = "202004221017031640110002")
    private String businessId;

    @ApiModelProperty(value = "md5", required = true, example = "e10adc3949ba59abbe56e057f20f883e")
    private String md5;

    @ApiModelProperty(value = "业务类型(B银行卡|I身份证|D行驶证|A地址)", required = true, example = "B|I|D|A")
    private String businessType;

    @ApiModelProperty(value = "证件正反面", required = true, example = "face|back")
    private String side;

    @ApiModelProperty(value = "扩展字段json", required = true, example = "{\"cardNum\":\"****************\",\"bankName\":\"招商银行\",\"cardType\":\"\"}")
    private String extendField;

    @ApiModelProperty(value = "绝对路径", required = true)
    private String absolutePath;

    @ApiModelProperty(value = "相对路径", required = true)
    private String relativePath;

    @ApiModelProperty(value = "地址", required = true)
    private AddressScanDTO address;
}
