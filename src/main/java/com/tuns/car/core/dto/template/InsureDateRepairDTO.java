package com.tuns.car.core.dto.template;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 投保时间修复所需信息
 *
 * <AUTHOR>
 * @since 2023-9-8
 */
@Data
@NoArgsConstructor
public class InsureDateRepairDTO {

    /**
     * 等待修复的重复投保消息
     */
    private String waitRepairMessage;
    /**
     * 保险公司ID
     */
    private String companyId;
    /**
     * 投保类型
     */
    private String recordType;

    /**
     * 批次号
     */
    private Long policyBatchId;


    public InsureDateRepairDTO(String waitRepairMessage, String companyId, String recordType, Long policyBatchId) {
        this.waitRepairMessage = waitRepairMessage;
        this.companyId = companyId;
        this.recordType = recordType;
        this.policyBatchId = policyBatchId;
    }
}
