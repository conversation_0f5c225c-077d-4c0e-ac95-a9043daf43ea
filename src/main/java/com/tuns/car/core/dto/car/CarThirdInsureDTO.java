package com.tuns.car.core.dto.car;

import com.tuns.car.core.constant.ViInsuranceInfMapKeyTypeEnum;
import com.tuns.car.core.dto.carprocess.premium.AreaDTO;
import com.tuns.car.core.dto.carprocess.premium.ViQuotationEnterpriseInfoDTO;
import com.tuns.car.core.dto.gi.CarSameSaleGiOrderInfoVO;
import com.tuns.car.core.dto.gi.GiOrderInfoVO;
import com.tuns.car.core.entity.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @ClassName CarThirdInsureDTO
 * @Description 车险投保调用第三方接口参数请求类
 * <AUTHOR> yi
 * @Date 2021/1/28 9:53
 * @Version 1.0
 */
@Data
public class CarThirdInsureDTO extends IssueOrderChanInfoDTO {

    private static final long serialVersionUID = -9200637396911135127L;

    /**
     * 车险公共基础信息数据
     */
    private ViInsPlcyInfTp pubInsPlcy;

    /**
     * 车险交商同保信息数据
     */
    private List<ViInsPlcyInfTp> plcyInfList;

    /**
     * 车险车辆信息数据
     */
    private ViInsCarInfTp carInfTp;

    /**
     * 车险关系人信息数据
     */
    private List<PubPersInfTp> pubPerList;

    /**
     * 车险险种信息数据
     */
    private List<ViInsKindDetialTp> kindList;

    /**
     * 保险公司险种信息数据
     */
    private Map<ViInsuranceInfMapKeyTypeEnum, Map<String, ViInsuranceInf>> viInsuranceInfMap;

    /**
     * 订单数据
     */
    private ViOrderInfo orderInfo;

    /**
     * 非车险
     */
    private GiOrderInfoVO giOrder;
    /**
     * 投保附件
     */
    private List<ViOfferImgDTO> attaches;

    /**
     * 保险公司保费试算原始报文
     */
    private String extendJson;

    /**
     * 车辆关系映射
     */
    private CmmVehicleTypeRelation vehicleTypeRelation;

    /**
     * 地区实体类
     */
    private Map<String,AreaDTO> areaDTO;

    /**
     * 渠道类型
     */
    private String chanType;
    /**
     * 发票信息
     */
    private ViInsPlcyCustomInvoice viInsPlcyCustomInvoice;

    /**
     * 特约信息
     */
    List<ViQuoteSpecialAgreement> specialAgreementDtoList;

    /**
     * 特约配置
     */
    List<ComsChanSpecialAgreement> comsChanSpecialAgreements;

    /**
     * 非车信息
     */
    List<CarSameSaleGiOrderInfoVO> carSameSaleGiOrderInfoList;

    /**
     * OCR
     */
    private CmmCardScanDTO cardScanDTO;

    /**
     * 验车信息
     */
    private CheckVehicleDTO checkVehicleDTO;
    /**
     * 影像资料分类map key 我司代码 value 影像资料
     */
    Map<String, List<ViCompanyImageMappingDTO>> viCompanyImageMappingMap;
    /**
     * 非车订单表的insOrderNo  保险公司提供的订单号
     */
    private String insOrderNo;

    /**
     * 企业车单位信息
     */
    private ViQuotationEnterpriseInfoDTO quotationEnterpriseInfoDTO;
    /**
     * 核保告知
     * @return
     */
    @ApiModelProperty("核保告知")
    private String underwritingInform;
}
