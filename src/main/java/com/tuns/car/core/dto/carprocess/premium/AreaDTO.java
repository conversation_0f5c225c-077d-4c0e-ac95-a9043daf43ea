package com.tuns.car.core.dto.carprocess.premium;

import lombok.Data;

import java.util.Optional;

/**
 * 地区信息 2018/6/22
 *
 * <AUTHOR>
 */
@Data
public class AreaDTO {
    /**
     * 省份id
     */
    private String province;

    private String provinceName;
    /**
     * 城市id
     */
    private String city;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 区县id
     */
    private String county;
    /**
     * 区县名称
     */
    private String countyName;
    /**
     * 绑定车牌
     */
    private String plateNumber;

    public String getCompleteAddr() {
        StringBuilder result = new StringBuilder();
        Optional.ofNullable(provinceName).ifPresent(result::append);
        Optional.ofNullable(cityName).ifPresent(result::append);
        Optional.ofNullable(countyName).ifPresent(result::append);
        return result.toString();
    }
}
