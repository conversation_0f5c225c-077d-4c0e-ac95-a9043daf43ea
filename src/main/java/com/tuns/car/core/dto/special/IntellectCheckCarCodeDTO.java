package com.tuns.car.core.dto.special;

import com.tuns.car.core.constant.InsEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 智能验车响应对象
 * <AUTHOR>
 * @since 2023-12-06 16:51
 **/
@Data
public class IntellectCheckCarCodeDTO {

    @ApiModelProperty(value = "智能验车二维码地址")
    private String checkCarUrl;
    @ApiModelProperty(value = "智能验车二维码原始地址")
    private String checkCarOriginUrl;
    @ApiModelProperty(value = "保险公司代码")
    private String companyCode;
    @ApiModelProperty("出单渠道id")
    private Integer chanDetailId;
    @ApiModelProperty("保险公司")
    private InsEnum company;

}
