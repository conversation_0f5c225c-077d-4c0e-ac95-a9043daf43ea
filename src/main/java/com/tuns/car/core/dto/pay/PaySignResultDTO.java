package com.tuns.car.core.dto.pay;

import com.tuns.car.core.constant.PayTypeEnum;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/6/17
 */
@Data
public class PaySignResultDTO {

    /**
     * 支付连接地址url，key为支付方式
     */
    private Map<PayTypeEnum, PaySignDTO> payUrlMap;

    /**
     * 二维码支付连接地址, key为支付方式
     */
    private Map<PayTypeEnum, PaySignDTO> imagePayUrlMap;

    /**
     * 表单内容，不为空时需配合action表单提交
     */
    private Object form;

    private String paySerialNumber;

    /**
     * 申能需要从支付界面拿到非车日期，其他保司如要用，直接设置
     */
    private String realNoCarDate;
}
