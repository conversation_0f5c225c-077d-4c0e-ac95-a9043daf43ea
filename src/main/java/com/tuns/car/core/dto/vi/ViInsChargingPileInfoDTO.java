package com.tuns.car.core.dto.vi;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tuns.car.core.config.CustomLocalDateToStringSerializer;
import com.tuns.car.core.constant.ViInsContants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.util.List;

/**
 * 车险充电桩信息
 *
 * <AUTHOR>
 * @since 2021/12/21
 */
@ApiModel
public class ViInsChargingPileInfoDTO {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("充电桩型号")
    private String chargingModel;

    @ApiModelProperty("充电桩编码")
    private String chargingCode;

    @ApiModelProperty("业务主键id")
    private Long chargingPileId;

    @ApiModelProperty("充电桩类型")
    private ViInsContants.ChargingType chargingType;

    @ApiModelProperty("充电桩类型(描述)")
    private String chargingTypeDescription;

    @ApiModelProperty("充电桩安装地点类型")
    private ViInsContants.ChargingAddrType chargingInstallAddrType;

    @ApiModelProperty("充电桩安装地点类型(描述)")
    private String chargingInstallAddrTypeDescription;

    @ApiModelProperty("充电桩使用年限")
    private ViInsContants.ChargingUseYears chargingUseYears;

    @ApiModelProperty(value = "充电桩安装时间", required = true)
    @JsonSerialize(using = CustomLocalDateToStringSerializer.class)
    private LocalDate chargingInstallDate;

    @ApiModelProperty("充电桩使用年限(描述)")
    private String chargingUseYearsDescription;

    @ApiModelProperty("充电桩完整地址")
    private String chargingAddrComplete;

    @ApiModelProperty("充电桩详细地址")
    private String chargingAddrDetail;

    @ApiModelProperty("省代码")
    private String province;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市代码")
    private String city;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("区县代码")
    private String county;

    @ApiModelProperty("区县名称")
    private String countyName;

    /**
     * 险种信息
     */
    private List<ChargingKindDTO> kindList;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getChargingModel() {
        return chargingModel;
    }

    public void setChargingModel(String chargingModel) {
        this.chargingModel = chargingModel;
    }

    public String getChargingCode() {
        return chargingCode;
    }

    public void setChargingCode(String chargingCode) {
        this.chargingCode = chargingCode;
    }

    public ViInsContants.ChargingType getChargingType() {
        return chargingType;
    }

    public void setChargingType(ViInsContants.ChargingType chargingType) {
        this.chargingType = chargingType;
    }

    public ViInsContants.ChargingAddrType getChargingInstallAddrType() {
        return chargingInstallAddrType;
    }

    public void setChargingInstallAddrType(ViInsContants.ChargingAddrType chargingInstallAddrType) {
        this.chargingInstallAddrType = chargingInstallAddrType;
    }

    public ViInsContants.ChargingUseYears getChargingUseYears() {
        return chargingUseYears;
    }

    public void setChargingUseYears(ViInsContants.ChargingUseYears chargingUseYears) {
        this.chargingUseYears = chargingUseYears;
    }

    public LocalDate getChargingInstallDate() {
        return chargingInstallDate;
    }

    public void setChargingInstallDate(LocalDate chargingInstallDate) {
        this.chargingInstallDate = chargingInstallDate;
    }

    public String getChargingAddrComplete() {
        return chargingAddrComplete;
    }

    public void setChargingAddrComplete(String chargingAddrComplete) {
        this.chargingAddrComplete = chargingAddrComplete;
    }

    public String getChargingAddrDetail() {
        return chargingAddrDetail;
    }

    public void setChargingAddrDetail(String chargingAddrDetail) {
        this.chargingAddrDetail = chargingAddrDetail;
    }

    public String getChargingTypeDescription() {
        return chargingTypeDescription;
    }

    public void setChargingTypeDescription(String chargingTypeDescription) {
        this.chargingTypeDescription = chargingTypeDescription;
    }

    public String getChargingInstallAddrTypeDescription() {
        return chargingInstallAddrTypeDescription;
    }

    public void setChargingInstallAddrTypeDescription(String chargingInstallAddrTypeDescription) {
        this.chargingInstallAddrTypeDescription = chargingInstallAddrTypeDescription;
    }

    public String getChargingUseYearsDescription() {
        return chargingUseYearsDescription;
    }

    public void setChargingUseYearsDescription(String chargingUseYearsDescription) {
        this.chargingUseYearsDescription = chargingUseYearsDescription;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    public Long getChargingPileId() {
        return chargingPileId;
    }

    public void setChargingPileId(Long chargingPileId) {
        this.chargingPileId = chargingPileId;
    }

    public List<ChargingKindDTO> getKindList() {
        return kindList;
    }

    public void setKindList(List<ChargingKindDTO> kindList) {
        this.kindList = kindList;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
