package com.tuns.car.core.dto.gi;

import com.alibaba.fastjson.JSON;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

public class PaySuccessOrderDetailDTO implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 509549229280030800L;

    /**
     * 订单号--不能为空
     */
    @NotBlank
    private String orderNo;

    /**
     * 保单号--不能为空
     */
    @NotBlank
    private String policyNumber;

    /**
     * 产品编码
     */
    private String productCode;

    public String getOrderNo() {
        return orderNo;
    }

    public PaySuccessOrderDetailDTO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public String getPolicyNumber() {
        return policyNumber;
    }

    public PaySuccessOrderDetailDTO setPolicyNumber(String policyNumber) {
        this.policyNumber = policyNumber;
        return this;
    }

    public String getProductCode() {
        return productCode;
    }

    public PaySuccessOrderDetailDTO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }
    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
