package com.tuns.car.core.dto.picc;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.dto.cpic.session.PartnerSelect;
import lombok.Data;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2022/7/18 17:08
 */
@Data
public class PICCUser {
    public AtomicBoolean success = new AtomicBoolean(false);

    private String jessionId;

    private String lt;

    private PartnerSelect selectVO;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
