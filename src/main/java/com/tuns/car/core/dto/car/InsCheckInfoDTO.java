package com.tuns.car.core.dto.car;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 车险保单审核记录流水查询接收类
 * Created by <PERSON><PERSON><PERSON> on 2019/6/6.
 */
public class InsCheckInfoDTO implements Serializable {

  private String changeContent;
  private String policyContent;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date checkTime;
  private String checkState;
  private String remark;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date creTm;
  private String checkName;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public String getChangeContent() {
        return changeContent;
    }

    public InsCheckInfoDTO setChangeContent(String changeContent) {
        this.changeContent = changeContent;
        return this;
    }

    public String getPolicyContent() {
        return policyContent;
    }

    public InsCheckInfoDTO setPolicyContent(String policyContent) {
        this.policyContent = policyContent;
        return this;
    }

    public Date getCheckTime() {
        return checkTime;
    }

    public InsCheckInfoDTO setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
        return this;
    }

    public String getCheckState() {
        return checkState;
    }

    public InsCheckInfoDTO setCheckState(String checkState) {
        this.checkState = checkState;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public InsCheckInfoDTO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public Date getCreTm() {
        return creTm;
    }

    public InsCheckInfoDTO setCreTm(Date creTm) {
        this.creTm = creTm;
        return this;
    }

    public String getCheckName() {
        return checkName;
    }

    public InsCheckInfoDTO setCheckName(String checkName) {
        this.checkName = checkName;
        return this;
    }
}
