package com.tuns.car.core.dto.car;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 添加非车dto
 * <AUTHOR>
 * @since 2021/11/22
 */
@Data
public class AddNoCarDTO implements Serializable {

    private static final long serialVersionUID = 204960055601752683L;
    /**
     * 非车总金额
     */
    private BigDecimal  totalPremium;


    /**
     * 非车计算后，个险订单信息，用于后续跟个险交互，完善个险保费信息
     */
    private List<GiOrderDetailDTO> giOrderResult;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
