package com.tuns.car.core.dto.charging;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @create 2022-04-12 9:01
 **/

@Data
public class ChargingDetailDTO implements Serializable {
    @ApiModelProperty("充电桩id")
    private Long chargingId;
    @ApiModelProperty("充电桩型号")
    @NotBlank(message = "充电桩型号不能为空")
    private String chargingModel;
    @ApiModelProperty("充电桩编码")
    @NotBlank(message = "充电桩编码不能为空")
    private String chargingCode;
    @ApiModelProperty(value = "险种代码",hidden = true)
    private String kindCode;
    @ApiModelProperty("保额")
    private BigDecimal amount;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
