package com.tuns.car.core.dto.car;

import com.alibaba.fastjson.JSON;
import com.tuns.car.core.constant.CarTypeEnum;
import com.tuns.car.core.constant.PlateNumberMarkEnum;
import com.tuns.car.core.entity.PubPersInfTp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName InsCarBasicInfoVO
 * @Description 车辆基础信息
 * <AUTHOR>
 * @Date 2020/6/17
 * @Version 1.0
 */
@ApiModel(value = "InsCarBasicInfoVO", description = "车辆基础信息")
@Data
public class InsCarBasicInfoDTO implements Serializable {

    private static final long serialVersionUID = 839085492257813647L;

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "保单批次ID", required = true)
    private String serialNumber;

    @ApiModelProperty(value = "批次ID", required = true)
    private String policyBatchId;

    @ApiModelProperty(value = "询价人ID", required = true)
    private String inquirerId;

    @ApiModelProperty(value = "车主", required = true)
    private String carOwner;

    @ApiModelProperty(value = "车主证件号码")
    private String ownerIdentify;

    @ApiModelProperty(value = "车主证件类型")
    private String ownerIdentifyType;

    @ApiModelProperty(value = "车牌号码", required = true)
    private String plateNumber;

    @ApiModelProperty(value = "车架号", required = true)
    private String frameNumber;

    @ApiModelProperty(value = "发动机号码", required = true)
    private String engineNumber;

    @ApiModelProperty(value = "注册日期 yyyy-MM-dd", required = true)
    private String regDate;

    @ApiModelProperty(value = "发证日期 yyyy-MM-dd", required = true)
    private String certDate;

    @ApiModelProperty(value = "过户日期 yyyy-MM-dd")
    private String transferDate;

    @ApiModelProperty(value = "新车标志 0-非新车 1-新车", required = true)
    private String newCarMark;

    @ApiModelProperty(value = "是否上牌 0-未上牌 1-上牌")
    private PlateNumberMarkEnum plateNumberMark;

    @ApiModelProperty(value = "品牌名称", required = true)
    private String modelName;

    @ApiModelProperty(value = "车型代码")
    private String modelCode;

    @ApiModelProperty(value = "核定载客", required = true)
    private Integer seatCount;

    @ApiModelProperty(value = "能源类型", required = true)
    private String fuelType;

    @ApiModelProperty(value = "排量(ML)", required = true)
    private Integer exhaustScale;

    @ApiModelProperty(value = "功率(瓦)", required = true)
    private Integer power;

    @ApiModelProperty(value = "贷款车辆标志 0-非贷款车 1-贷款车", required = true)
    private String loanCarMark;

    @ApiModelProperty(value = "年款 yyyyMM/yyyy", required = true)
    private String marketDate;

    @ApiModelProperty(value = "车款名称")
    private String carName;

    @ApiModelProperty(value = "车辆产地 1-进口 2-国产 3-合资", required = true)
    private String carOrigin;

    @ApiModelProperty(value = "车辆吨位/核定载质量(吨)", required = true)
    private BigDecimal carTonnage;

    @ApiModelProperty(value = "行业车型编码")
    private String prfsModelCode;

    @ApiModelProperty(value = "车系名称")
    private String familyName;

    @ApiModelProperty(value = "公告型号")
    private String noticeType;

    @ApiModelProperty(value = "行政区域代码 省级")
    private String provinceNumber;

    @ApiModelProperty(value = "行政区域代码 市级")
    private String cityNumber;

    @ApiModelProperty(value = "行驶区域")
    private String runAreaCode;

    @ApiModelProperty(value = "使用年限", required = true)
    private Integer useYears;

    @ApiModelProperty(value = "车身颜色")
    private String bodyColor;

    @ApiModelProperty(value = "车型来源公司ID")
    private String carInfoCompany;

    @ApiModelProperty(value = "手机号码")
    private String mobilePhone;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date creTm;

    @ApiModelProperty(value = "创建人")
    private Integer creUser;

    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date mdfTm;

    @ApiModelProperty(value = "更新人", required = true)
    private Integer mdfUser;

    @ApiModelProperty(value = "完整地址")
    private String addressComplete;

    @ApiModelProperty(value = "新车购置价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "减税车型标志")
    private String taxCutsMark;
    
    @ApiModelProperty(value = "使用性质")
    private String usingNature;
    
    @ApiModelProperty(value = "整备质量")
    private Integer wholeWeight;
    
    @ApiModelProperty(value = "轴距")
    private String wheelBase;
    
    @ApiModelProperty(value = "轴数")
    private String shaft;
    
    @ApiModelProperty(value = "生产日期")
    private String manufacturingDate;
    
    @ApiModelProperty(value = "前轮距")
    private String frontWheelBase;
    
    @ApiModelProperty(value = "发动机型号")
    private String engineModel;
    
    @ApiModelProperty(value = "额定载质量")
    private String ratedQuality;
    
    @ApiModelProperty(value = "品牌")
    private String brand;
    
    @ApiModelProperty(value = "后轮距")
    private String rearWheelBase;

    @ApiModelProperty("车辆实际价值")
    private BigDecimal actualPrice;

    @ApiModelProperty("续航里程")
    private String pureRange;

    @ApiModelProperty("车辆种类")
    private CarTypeEnum carTypeCode;

    @ApiModelProperty(value = "关系人详情")
    private List<PubPersInfTp> pubPersInfTpVOList;
    /**
     * 过户标志
     */
    @ApiModelProperty(value = "过户标志")
    private String transferMark;

    @ApiModelProperty(value = "营运性质 0-非营运 1-营运")
    private String operationNature;

    @ApiModelProperty(value = "行驶证车辆")
    private String licenseVehicleType;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
