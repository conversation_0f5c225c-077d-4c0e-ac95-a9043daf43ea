package com.tuns.car.core.dto;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * app自主定价系数详情请求
 * @ClassName RatioRuleAppDetailDTO
 * @Description app自主定价系数详情请求
 * <AUTHOR>
 * @Date 2021/10/20 11:41:16 
 * @Version 1.0
 */
public class RatioRuleAppDetailDTO implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 6432865676995619261L;

    @ApiModelProperty(value = "报价批次id")
    private String policyBatchId;

    public String getPolicyBatchId() {
        return policyBatchId;
    }

    public void setPolicyBatchId(String policyBatchId) {
        this.policyBatchId = policyBatchId;
    }

    @Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}