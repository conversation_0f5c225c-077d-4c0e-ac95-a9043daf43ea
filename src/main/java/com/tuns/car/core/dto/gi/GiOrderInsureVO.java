package com.tuns.car.core.dto.gi;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tuns.core.boot.serializer.BigDecimalTwoScaleSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 个险订单 保险信息
 */
@Data
@Accessors(chain = true)
public class GiOrderInsureVO implements Serializable {

	private String prodName;

    /**
     * 保险起期
     */
    private String startDate;

    /**
     * 保险止期
     */
    private String endDate;

    @JsonSerialize(using = BigDecimalTwoScaleSerializer.class)
    private BigDecimal premium;

    private String proposalNo;


}
