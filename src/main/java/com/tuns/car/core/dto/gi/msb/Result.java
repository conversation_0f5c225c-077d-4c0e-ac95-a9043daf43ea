/**
  * Copyright 2021 json.cn 
  */
package com.tuns.car.core.dto.gi.msb;
import java.util.List;

/**
 * Auto-generated: 2021-11-01 15:12:57
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class Result {

    private boolean accident;
    private boolean aewCar;
    private AgentInputorVo agentInputorVo;
    private CarInfoVo carInfoVo;
    private ClaimPerson claimPerson;
    private List<String> commercialAgreementVo;
    private CompulsoryAggrementVo compulsoryAggrementVo;
    private List<CompulsoryAgreementVos> compulsoryAgreementVos;
    private Drawee drawee;
    private HoldVo holdVo;
    private InsuranceVo insuranceVo;
    private InsureVo insureVo;
    private String isSaic;
    private String isShowCalcu;
    private List<ListVo> list;
    private List<MsbList> msbList;
    private NetworkVo networkVo;
    private boolean outCar;
    private String outerActivityCode;
    private List<PaymentRecordVos> paymentRecordVos;
    private List<PolicyNoTypes> policyNoTypes;
    private String policyStatus;
    private String productVersion;
    private SecondDraweeName secondDraweeName;
    private List<TaxpayerInfoVo> taxpayerInfoVo;
    public void setAccident(boolean accident) {
         this.accident = accident;
     }
     public boolean getAccident() {
         return accident;
     }

    public void setAewCar(boolean aewCar) {
         this.aewCar = aewCar;
     }
     public boolean getAewCar() {
         return aewCar;
     }

    public void setAgentInputorVo(AgentInputorVo agentInputorVo) {
         this.agentInputorVo = agentInputorVo;
     }
     public AgentInputorVo getAgentInputorVo() {
         return agentInputorVo;
     }

    public void setCarInfoVo(CarInfoVo carInfoVo) {
         this.carInfoVo = carInfoVo;
     }
     public CarInfoVo getCarInfoVo() {
         return carInfoVo;
     }

    public void setClaimPerson(ClaimPerson claimPerson) {
         this.claimPerson = claimPerson;
     }
     public ClaimPerson getClaimPerson() {
         return claimPerson;
     }

    public void setCommercialAgreementVo(List<String> commercialAgreementVo) {
         this.commercialAgreementVo = commercialAgreementVo;
     }
     public List<String> getCommercialAgreementVo() {
         return commercialAgreementVo;
     }

    public void setCompulsoryAggrementVo(CompulsoryAggrementVo compulsoryAggrementVo) {
         this.compulsoryAggrementVo = compulsoryAggrementVo;
     }
     public CompulsoryAggrementVo getCompulsoryAggrementVo() {
         return compulsoryAggrementVo;
     }

    public void setCompulsoryAgreementVos(List<CompulsoryAgreementVos> compulsoryAgreementVos) {
         this.compulsoryAgreementVos = compulsoryAgreementVos;
     }
     public List<CompulsoryAgreementVos> getCompulsoryAgreementVos() {
         return compulsoryAgreementVos;
     }

    public void setDrawee(Drawee drawee) {
         this.drawee = drawee;
     }
     public Drawee getDrawee() {
         return drawee;
     }

    public void setHoldVo(HoldVo holdVo) {
         this.holdVo = holdVo;
     }
     public HoldVo getHoldVo() {
         return holdVo;
     }

    public void setInsuranceVo(InsuranceVo insuranceVo) {
         this.insuranceVo = insuranceVo;
     }
     public InsuranceVo getInsuranceVo() {
         return insuranceVo;
     }

    public void setInsureVo(InsureVo insureVo) {
         this.insureVo = insureVo;
     }
     public InsureVo getInsureVo() {
         return insureVo;
     }

    public void setIsSaic(String isSaic) {
         this.isSaic = isSaic;
     }
     public String getIsSaic() {
         return isSaic;
     }

    public void setIsShowCalcu(String isShowCalcu) {
         this.isShowCalcu = isShowCalcu;
     }
     public String getIsShowCalcu() {
         return isShowCalcu;
     }

    public void setList(List<ListVo> list) {
         this.list = list;
     }
     public List<ListVo> getList() {
         return list;
     }

    public void setMsbList(List<MsbList> msbList) {
         this.msbList = msbList;
     }
     public List<MsbList> getMsbList() {
         return msbList;
     }

    public void setNetworkVo(NetworkVo networkVo) {
         this.networkVo = networkVo;
     }
     public NetworkVo getNetworkVo() {
         return networkVo;
     }

    public void setOutCar(boolean outCar) {
         this.outCar = outCar;
     }
     public boolean getOutCar() {
         return outCar;
     }

    public void setOuterActivityCode(String outerActivityCode) {
         this.outerActivityCode = outerActivityCode;
     }
     public String getOuterActivityCode() {
         return outerActivityCode;
     }

    public void setPaymentRecordVos(List<PaymentRecordVos> paymentRecordVos) {
         this.paymentRecordVos = paymentRecordVos;
     }
     public List<PaymentRecordVos> getPaymentRecordVos() {
         return paymentRecordVos;
     }

    public void setPolicyNoTypes(List<PolicyNoTypes> policyNoTypes) {
         this.policyNoTypes = policyNoTypes;
     }
     public List<PolicyNoTypes> getPolicyNoTypes() {
         return policyNoTypes;
     }

    public void setPolicyStatus(String policyStatus) {
         this.policyStatus = policyStatus;
     }
     public String getPolicyStatus() {
         return policyStatus;
     }

    public void setProductVersion(String productVersion) {
         this.productVersion = productVersion;
     }
     public String getProductVersion() {
         return productVersion;
     }

    public void setSecondDraweeName(SecondDraweeName secondDraweeName) {
         this.secondDraweeName = secondDraweeName;
     }
     public SecondDraweeName getSecondDraweeName() {
         return secondDraweeName;
     }

    public void setTaxpayerInfoVo(List<TaxpayerInfoVo> taxpayerInfoVo) {
         this.taxpayerInfoVo = taxpayerInfoVo;
     }
     public List<TaxpayerInfoVo> getTaxpayerInfoVo() {
         return taxpayerInfoVo;
     }

}