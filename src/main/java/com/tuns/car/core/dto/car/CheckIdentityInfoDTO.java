package com.tuns.car.core.dto.car;

import com.tuns.car.core.dto.carprocess.premium.request.PersonInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-07-12 15:29
 **/
@Data
public class CheckIdentityInfoDTO {

    @ApiModelProperty("关系人信息")
    @NotEmpty
    private List<PersonInfo> personInfoList;


    @ApiModelProperty("批次ID")
    @NotBlank
    private String policyBatchId;




}
