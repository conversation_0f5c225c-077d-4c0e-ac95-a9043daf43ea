package com.tuns.car.core.dto.car;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tuns.car.core.constant.TransferMarkTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 车险详情查询车辆信息与公用数据接收类
 * Created by ZhangYi on 2018/10/17.
 */
@ApiModel
@Data
public class InsCarInfDTO {

    private String policyId;//记录id

    /*保单批次ID*/
    private String policyBatchId;

    /* 车主*/
    private String carOwner;

    /*车主性质*/
    private String ownerNature;

    /* 车主证件号码*/
    private String ownerIdentify;

    /* 车主证件类型*/
    private String ownerIdentifyType;

    /* 车牌号码*/
    private String plateNumber;

    /*车架号*/
    private String frameNumber;

    /*发动机号码*/
    private String engineNumber;

    /*注册日期 yyyy-MM-dd*/
    private String regDate;

    /*发证日期 yyyy-MM-dd*/
    private String certDate;

    /*过户标志*/
    private String transferMark;

    /*过户类型*/
    private TransferMarkTypeEnum transferMarkType;

    /*过户日期 yyyy-MM-dd*/
    private String transferDate;

    /*新车标致 0-非新车 1-新车*/
    private String newCarMark;

    /*品牌名称*/
    private String modelName;

    /*车型代码*/
    private String modelCode;

    /*行驶里程 (公里/千米)*/
    private Integer runMiles;

    /*核定载客*/
    private Integer seatCount;

    /*能源类型*/
    private String fuelType;

    /*排量(L)*/
    private Double exhaustScale;

    /*功率(瓦)*/
    private Integer power;

    /*新车购置价*/
    private Double purchasePrice;

    /*车辆实际价*/
    private Double actualPrice;

    /*贷款车辆标志 0-非贷款车 1-贷款车*/
    private String loanCarMark;

    /*整备质量*/
    private Integer wholeWeight;

    /*年款 yyyyMM/yyyy*/
    private String marketDate;

    /*车辆类型描述*/
    private String carStyleNote;

    /*车款名称*/
    private String carName;

    /*车辆产地 1-进口 2-国产 3-合资*/
    private String carOrigin;

    /*车辆吨位/核定载质量(吨)*/
    private Double carTonnage;

    /*行业车型编码*/
    private String prfsModelCode;

    /*是否安装GPS 0-未安装 1-安装*/
    private String gpsMark;

    /*家庭车辆台数*/
    private Integer familyCarCount;

    /*减税车型标志 0-正常 04-减免税 05-减税*/
    private String taxCutsMark;

    /*减税比例 0~1*/
    private Double taxCutProportion;

    /*车系名称*/
    private String familyName;

    /*公告型号*/
    private String noticeType;

    /*所属性质 1-个人 2-机关 3-企业*/
    private String ownershipNature;

    /*营运性质 0-非营运 1-营运*/
    private String operationNature;

    /*使用性质*/
    private String usingNature;

    /*行政区域代码 省级*/
    private String province;

    /*行政区域代码 市级*/
    private String city;

    /*行驶区域*/
    private String runAreaCode;

    /*使用年限*/
    private Integer useYears;

    /*车身颜色*/
    private String bodyColor;

    /*备注*/
    private String note;

    /*车型来源公司ID*/
    private String carInfoCompany;

    /*扩展字段字段（Json字符串-存储保险公司需要而我们不关注的数据）*/
    private String extend;

    /*创建时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date creTm;

    /*创建人*/
    private Integer creUser;

    private Long serialNumber;
    private String recordType;
    private String autoMark;
    private String recordSource;
    private String companyId;
    private String companyName;
    private String provinceNumber;
    private String provinceName;
    private String cityNumber;
    private String cityName;
    private String reviewerId;
    private String reviewerName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reviewTime;
    private String recordStatus;
    private String reviewMsg;
    private String insuredReviewerId;
    private String insuredReviewerName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuredReviewTime;
    private String insuredMsg;
    private String insuredStatus;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;
    private String receiverType;
    private String salesmanId;
    private String inquirerId;
    private String departCode;
    private String channelId;
    private String recordScore;
    private String insuredType;
    private String thisRenewal;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuredTime;
    private String policyReviewerId;
    private String policyReviewerName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date policyReviewTime;
    private String policyReviewMsg;
    private String policyReviewMark;
    private String policyPrintMark;
    private String policyPrinterId;
    private String policyPrinterName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date policyPrintTime;
    private String policyNewMark;
    private String totalRecordScore;
    private String inquirerDepartCode;
    private String insuredSubStatus;
    private String channelName;
    private String salesmanName;
    private String salesmanPhone;
    private String salesmanCard;
    private String personName;
    private String applName;
    private String creName;
    private String departName;
    private String checkDepartName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inquirerTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuredSubmitTime;

    private String realName;
    private String idCardNo;
    private String cellphone;
    private String insuredName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;
    private String payImgUrl;
    private String orderId;
    private String payType;
    private String inquirerMsgType;
    private String inquirerMsg;
    private String taskId;
    private String reverseState;
    private String commRemark;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mdfTm;
    private Object[] mdfColumn;//变更过的字段
    private String status;
    /**
     * 整单预期赔付率
     */
    private BigDecimal totalRatio;

    /**
     * 验车状态  1已验 2免验
     */
    private String checkCarStatus;
    /**
     * 免验车原因 1单保责任险 2按期续保，且未加保损失类险种 3新车 4团车业务 5符合免验规定
     */
    private String exemptionReason;
    /**
     * 验车人
     */
    private String carInspector;
    /**
     * 验车人
     */
    private String inspectionTime;
    /**
     * 验车结果 1 全车完好无损，证车相符，年审合格 9 其他
     */
    private String inspectionResult;

    /**
     * 车辆类型编码
     */
    private String carTypeCode;

    /**
     * 送修代码
     */
    private String repairCode;
    /**
     * 项目代码
     */
    private String projectCode;
    /**
     * 上年度出单渠道类型
     */
    private String lastChannelType;
    /**
     * 是否送修 1是 0否
     */
    private String isRepair;
    /**
     * 自主评分(长安)
     */
    private BigDecimal selfScore;
    /**
     * 承保年限
     */
    private BigDecimal underwritingPeriod;
    /**
     * 交通违法系数
     */
    private BigDecimal trafficViolationCoefficient;
    /**
     * 车辆的评分
     */
    private BigDecimal vehicleRating;
    /**
     * 连续承保期间出险次数
     */
    private BigDecimal numberOfUnderwriting;

    /**
     * 渠道名
     */
    private String linkName;

    /**
     * 渠道工号
     */
    @ApiModelProperty("渠道工号")
    private String linkNo;

    @ApiModelProperty("续航里程(公里)")
    private String pureRange;

    /**
     * 其他能源种类描述
     */
    @ApiModelProperty("其他能源种类描述")
    private String fuelTypeRemark;

    /**
     * 行驶证车辆类型
     */
    @ApiModelProperty("行驶证车辆类型")
    private String licenseVehicleType;

    /**
     * 大家分
     */
    private String allScore;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 当前序号
     */
    private String requestID;

    /**
     * 投保日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date issueTime;
    /**
     * 报价日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date offerTime;

    /**
     * 行驶证地址
     */
    @ApiModelProperty("行驶证地址")
    private String licenseAddress;


}
