package com.tuns.car.core.dto.special;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;

@Data
public class SpecialAgreementDTO implements Serializable {
    private static final long serialVersionUID = -5787021783643835178L;
    /**
     * //约定代码
     */
    private String clauseCode;
    /**
     * //约定名称
     */
    private String clauseName;
    /**
     * //约定序号
     */
    private String clauseOrder;
    /**
     * //约定内容
     */
    private String clauseDetial;
    /**
     * //是否强制
     */
    private String constraint;
    /**
     * //是否可修改
     */
    private String canModified;

    /**
     * 约定类型
     */
    private String specialType;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
