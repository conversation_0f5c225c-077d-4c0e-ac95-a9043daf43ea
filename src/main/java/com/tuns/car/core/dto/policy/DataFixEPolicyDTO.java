package com.tuns.car.core.dto.policy;

import com.tuns.car.core.constant.ViAttachTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "DataFixEPolicyDTO")
public class DataFixEPolicyDTO {

    @ApiModelProperty(value = "下载电子保单的保单批次号ID")
    @NotNull
    private List<String> policyBatchIds;

    @ApiModelProperty(value = "重新下载的类型")
    private ViAttachTypeEnum typeEnum;
}
