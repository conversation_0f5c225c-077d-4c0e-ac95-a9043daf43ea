package com.tuns.car.core.controller.feign;

import com.tuns.car.core.dto.template.*;
import com.tuns.car.core.feign.template.InsureRepeatTemplateFeign;
import com.tuns.car.core.service.template.InsureDateRepairOldService;
import com.tuns.car.core.service.template.impl.InsureRepeatTemplateHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 重复投保模版相关
 *
 * <AUTHOR>
 * @since 2023-9-7
 */
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class InsureRepeatTemplateFeignController implements InsureRepeatTemplateFeign {

    private final InsureRepeatTemplateHandler insureRepeatTemplateHandler;

    private final InsureDateRepairOldService repairOldService;

    @Override
    public List<RepeatProposalMsgDTO> tryInsureDateRepair(InsureDateRepairDTO insureDateRepairDTO) {
        return insureRepeatTemplateHandler.tryInsureDateRepair(insureDateRepairDTO);
    }

    @Override
    public List<RepeatProposalMsgDTO> dateResolver(InsureDateRepairDTO insureDateRepairDTO) {
        return repairOldService.dateResolver(insureDateRepairDTO);
    }

    @Override
    public RepeatTemplateGenerateResult generateTemplate(RepeatTemplateGenerateDTO repeatTemplateGenerateDTO) {
        return insureRepeatTemplateHandler.generateTemplate(repeatTemplateGenerateDTO);
    }

    @Override
    public boolean functionTestOfUseBefore(Integer templateId) {
        return insureRepeatTemplateHandler.functionTestOfUseBefore(templateId);
    }

    @Override
    public boolean functionTestOfRegexUpdate(TemplateFunctionTestDTO functionTestDTO) {
        return insureRepeatTemplateHandler.functionTestOfRegexUpdate(functionTestDTO);
    }

    @Override
    public Map<Integer, String> batchResolveTemplateContent(List<RepeatTemplateResolveDTO> resolveDTOList) {
        return insureRepeatTemplateHandler.batchResolveTemplateContent(resolveDTOList);
    }
}
