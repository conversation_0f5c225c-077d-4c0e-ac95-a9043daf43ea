package com.tuns.car.core.controller.report;

import com.tuns.car.core.vo.ReportSyncResultVO;
import com.tuns.core.boot.annotation.RepeatInvokeLock;
import com.tuns.core.secure.conf.AuthIgnore;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 报表数据同步服务
 *
 * <AUTHOR>
 * @since 2022-11-17
 */
@Api(tags = "报表数据同步")
@RestController
@RequestMapping("/etl/report")
public class ReportDataSyncController {



    /**
     * 报表数据同步
     */
    @AuthIgnore
    @ApiOperation(value = "报表数据同步 仅供job调用", httpMethod = "POST")
    @PostMapping("/reportDataSync")
    @RepeatInvokeLock
    public ReportSyncResultVO reportDataSync() {
        return null;
    }

}
