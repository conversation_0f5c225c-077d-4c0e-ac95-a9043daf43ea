package com.tuns.car.core.service.ocrall;

import com.tuns.car.core.vo.ocrall.CmmOCRScanVO;
import com.tuns.thirdparty.vo.image.CmmCardScanVO;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-04 14:05
 **/
public interface OCRRecognizeAllTextService {

    /**
     * 使用redis计数器累加接口的调用次数
     */
    void updateImageOcrNumber();

    /**
     * 验证判断OCR识别调用次数是否超出设置的最大数值
     */
    void validateOCRtoDayNumber();

    /**
     * 校验身份证与行驶证 中的人名是否一致；校验身份证与购车发票中的人名是否一致
     */
    void checkIdentity(List<CmmCardScanVO> cmmCardScanVOS) throws IOException;

    /**
     * 组装前端所需要的数据格式
     *
     * @param cmmCardScanVOS
     * @return
     */
    CmmOCRScanVO byResult(List<CmmCardScanVO> cmmCardScanVOS);
}
