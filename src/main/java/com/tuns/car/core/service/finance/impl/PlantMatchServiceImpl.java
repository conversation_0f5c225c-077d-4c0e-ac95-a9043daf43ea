package com.tuns.car.core.service.finance.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.plumelog.core.TraceId;
import com.tuns.car.core.constant.CarCoreCtt;
import com.tuns.car.core.constant.ChanConfigFieldEnum;
import com.tuns.car.core.constant.PlateNumberMarkEnum;
import com.tuns.car.core.constant.RedisKeyConstants;
import com.tuns.car.core.constant.finance.NoCarMessageEnum;
import com.tuns.car.core.dto.cache.ViInsuranceBasicDetilCacheDTO;
import com.tuns.car.core.dto.cache.ViInsuranceBasicQueryDTO;
import com.tuns.car.core.dto.car.PremiumCalculateResultDTO;
import com.tuns.car.core.dto.car.RecommendPlanDTO;
import com.tuns.car.core.dto.carprocess.premium.QuoteBaseDTO;
import com.tuns.car.core.dto.finance.PreciseMatchResultDTO;
import com.tuns.car.core.dto.finance.RecommendNoCarDTO;
import com.tuns.car.core.dto.finance.ViQuoteMatchAdjustDetailDTO;
import com.tuns.car.core.entity.*;
import com.tuns.car.core.mapper.ViQuoteInvoiceInfoTpMapper;
import com.tuns.car.core.service.cache.ViInsuranceBasicCacheService;
import com.tuns.car.core.service.carprocess.car.CarInfoService;
import com.tuns.car.core.service.carprocess.car.ConfigureService;
import com.tuns.car.core.service.carprocess.premium.AsyncPremiumProcessor;
import com.tuns.car.core.service.carserver.QuotationService;
import com.tuns.car.core.service.common.CarCoreRedisService;
import com.tuns.car.core.service.companyconfig.ComsChanConfigRatioService;
import com.tuns.car.core.service.companyconfig.SpViInsAdditionClauseService;
import com.tuns.car.core.service.finance.QuotePlantMatchService;
import com.tuns.car.core.service.finance.ViQuoteMatchPlanAdjustDetailService;
import com.tuns.car.core.service.finance.ViQuoteMatchPlanService;
import com.tuns.car.core.service.finance.impl.resolve.KindFactorResolveCollector;
import com.tuns.car.core.service.quotation.ViInsCarInfTpService;
import com.tuns.car.core.service.quotation.ViInsKindDetialTpService;
import com.tuns.car.core.service.quotation.ViInsPlcyInfTpService;
import com.tuns.car.core.util.JacksonJsonUtil;
import com.tuns.car.core.util.PlantIncomeUtil;
import com.tuns.car.core.vo.CheckPlantChangeVO;
import com.tuns.car.core.vo.IncomeVO;
import com.tuns.car.core.vo.NonStandardVO;
import com.tuns.car.core.vo.OfferInfoDetailsVO;
import com.tuns.car.core.vo.premium.*;
import com.tuns.car.spider.dto.chan.ComsChanConfigDTO;
import com.tuns.car.spider.feign.carspider.SpiderChanFeign;
import com.tuns.core.boot.constant.YesNoEnum;
import com.tuns.core.boot.context.BaseContextHandler;
import com.tuns.core.boot.exception.TunsBusinessException;
import com.tuns.core.boot.utils.TunsBeanUtil;
import com.tuns.finance.match.car.constant.FactorTypeEnum;
import com.tuns.finance.match.car.dto.carpolicymatch.CarPolicyMatchDTO;
import com.tuns.finance.match.car.feign.AppCarQuoteMatchFeign;
import com.tuns.finance.match.car.vo.carpolicymatch.QuotePreciseMatchVO;
import com.tuns.personal.core.dto.carsamesale.change.*;
import com.tuns.personal.core.feign.CarSameSaleOrderFeign;
import com.tuns.personal.core.vo.carsamesale.change.CarSameSalePlanChangeRequestVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 政策匹配服务
 *
 * <AUTHOR>
 * @date 2023/04/21
 **/
@Service
@Slf4j
public class PlantMatchServiceImpl implements QuotePlantMatchService {

    @Autowired
    private AppCarQuoteMatchFeign appCarQuoteMatchFeign;

    @Autowired
    private SpiderChanFeign spiderChanFeign;

    @Autowired
    private ComsChanConfigRatioService comsChanConfigRatioService;

    @Autowired
    private ViInsPlcyInfTpService viInsPlcyInfService;

    @Autowired
    private ViInsCarInfTpService viInsCarInfTpService;

    @Autowired
    private ViQuoteMatchPlanService viQuoteMatchPlanService;

    @Autowired
    private ViInsKindDetialTpService viInsKindDetialTpService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ViQuoteMatchPlanAdjustDetailService viQuoteMatchPlanAdjustDetailService;

    @Autowired
    private QuotationService quotationService;

    @Autowired
    private ConfigureService configureService;

    @Autowired
    private PlantMatchReqPackageHelper plantMatchReqPackageHelper;

    @Autowired
    private CheckPlantChangeService checkPlantChangeService;

    @Autowired
    private JacksonJsonUtil jacksonJsonUtil;

    @Autowired
    private QuoteBlurMatchService blurMatchService;

    @Autowired
    private RecommendBuildService recommendBuildService;

    @Autowired
    private SpViInsAdditionClauseService spViInsAdditionClauseService;
    @Autowired
    private CarSameSaleOrderFeign carSameSaleOrderFeign;
    @Autowired
    private ViQuoteInvoiceInfoTpMapper viQuoteInvoiceInfoTpMapper;
    @Autowired
    private CarInfoService carInfoService;
    @Autowired
    private ViInsuranceBasicCacheService viInsuranceBasicCacheService;
    @Resource
    private CarCoreRedisService carCoreRedisService;
    @Resource
    private AsyncPremiumProcessor asyncPremiumProcessor;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public QuotePreciseMatchVO quotePreciseMatch(PremiumCalculateResultDTO premiumCaculateDTO) {
        QuoteBaseDTO ratioInfo = premiumCaculateDTO.getBaseDTO();
        // 智能模式和推荐方案报价 两种场景需要进行政策匹配流程
        // 去除手动模式判断，都需要匹配政策
        /*if (!Arrays.asList("2", "3").contains(ratioInfo.getRatioModel())) {
            return null;
        }*/
        /*
        ComsChanConfigRatio comsChanConfigRatio = comsChanConfigRatioService.queryDetail(premiumCaculateDTO.getChanDetailId());
        // 预估收益配置没开，直接中断流程
        boolean supportEstimatedIncome = Objects.nonNull(comsChanConfigRatio) && SwitchFlagEnum.UP == comsChanConfigRatio.getSwitchEstimatedIncome();
        if (!supportEstimatedIncome) {
            return null;
        }*/
        // 推荐方案配置没开，走精准匹配 先封装政策匹配需要的数据结构
        CarPolicyMatchDTO matchRequest = plantMatchReqPackageHelper.buildMatchReq(Long.valueOf(premiumCaculateDTO.getPolicyBatchId()), true);
        QuotePreciseMatchVO matchResponse = appCarQuoteMatchFeign.quotePreciseMatch(matchRequest);
        // 保存预估收益 (以个险匹配结果为主，车险失败了也要保存个险)
        Boolean giMatchSuccess = Objects.nonNull(matchResponse.getGiMatchSuccess()) && matchResponse.getGiMatchSuccess();
        if (matchResponse.getViMatchSuccess() || giMatchSuccess) {
            viQuoteMatchPlanService.savePreciseResult(matchResponse, Long.valueOf(premiumCaculateDTO.getPolicyBatchId()));
        }
        return matchResponse;
    }

    @Override
    public void quoteBlurMatch(RecommendPlanDTO req) {
        // 查询缓存信息 判断是否开启智能报价 如果开启智能报价 且为手动模式
        String str = carCoreRedisService.getPremiumResultRedisStr(Long.valueOf(req.getSerialNumber()), String.valueOf(req.getPolicyBatchId()));
        if (StringUtils.isNotBlank(str)) {
            CarPremiumCalculateBatchVO batchVO = jacksonJsonUtil.toBean(str, CarPremiumCalculateBatchVO.class);
            if ("2".equals(batchVO.getShouAutoPre())) {
                batchVO.setLoopFlag(CarCoreCtt.YES);
                carCoreRedisService.setPremiumResultRedis(Long.valueOf(req.getSerialNumber()), String.valueOf(req.getPolicyBatchId()), batchVO);
                asyncPremiumProcessor.calculateComplete(req, BaseContextHandler.getUserIdInt(), TraceId.logTraceID.get());
                return;
            }
        }
        blurMatchService.quoteBlurMatch(req);
    }

    @Override
    public List<RecommendPlanResultVO> listRecommend(String serialNumber) {
        List<RecommendPlanResultVO> result = new ArrayList<>();

        String mainKey = CarCoreCtt.REGION_RECOMMEND_RESULT + ":" + serialNumber;

        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(mainKey);

        entries.forEach((rawKey, rawValue) -> {
            final String value = Optional.ofNullable(rawValue)
                    .map(Object::toString)
                    .orElse("");
            RecommendPlanResultVO vo = new RecommendPlanResultVO();
            if (StringUtils.isNotBlank(value)) {
                vo = jacksonJsonUtil.toBean(value, RecommendPlanResultVO.class);
                vo.setFlag(YesNoEnum.YES.getValue());
            }
            vo.setPolicyBatchId(rawKey.toString());
            vo.setFlag(StrUtil.isBlank(value) ? YesNoEnum.NO.getValue() : YesNoEnum.YES.getValue());
            result.add(vo);
        });
        return result;
    }

    @Override
    public RecommendPlanDetailVO recommendPlanDetail(Integer planId) {
        RecommendPlanDetailVO result = new RecommendPlanDetailVO();
        // 查询推荐方案信息
        ViQuoteMatchPlan plan = viQuoteMatchPlanService.getById(planId);
        if (Objects.isNull(plan)) {
            throw new TunsBusinessException("查询不到对应推荐方案");
        }
        result.setPlanId(planId);
        result.setAdjustWay(plan.getAdjustWay());
        // 先沿用报价单详情接口的响应
        OfferInfoDetailsVO offerDetail = quotationService.queryOfferDetail(plan.getPolicyBatchId().toString(), false);
        OfferCarInfoVO carInfo = offerDetail.getCarInfo();
        carInfo.setPlateNumberMark("*".equals(carInfo.getPlateNumber()) || "".equals(carInfo.getPlateNumber()) ? PlateNumberMarkEnum.NO : PlateNumberMarkEnum.YES);
        BeanUtil.copyProperties(offerDetail, result);
        Integer chanDetailId = offerDetail.getOfferInfo().getChanDetailId();
        String policyBatchId = offerDetail.getOfferInfo().getPolicyBatchId();
        String serialNumber = offerDetail.getOfferInfo().getSerialNumber();
        String companyId = offerDetail.getBaseInfo().getCompanyId();
        //查询非车险推荐
        RecommendPlanResultVO planResultVO = getRecommendPlanFromRedis(serialNumber, policyBatchId);
        if (Objects.isNull(planResultVO)) {
            throw new TunsBusinessException("查询不到对应推荐方案，请重新获取");
        }
        RecommendPlanVO recommendPlanVO = planResultVO.getRecommendPlanList().stream().filter(o -> planId.equals(o.getPlanId())).findFirst().orElse(null);
        if (Objects.isNull(recommendPlanVO)) {
            throw new TunsBusinessException("查询不到对应推荐方案，请重新获取");
        }
        result.setRecommendNoCar(recommendPlanVO.getRecommendNoCar());
        // 查询预估收益
        IncomeVO incomeVO = buildIncome(planId);
        result.setIncomeInfo(incomeVO);
        // 然后补充推荐方案相关的信息 这里先查询调整细则
        List<ViQuoteMatchAdjustDetailDTO> adjustDetailList = viQuoteMatchPlanAdjustDetailService.listByPlanId(planId);
        Map<FactorTypeEnum, List<ViQuoteMatchAdjustDetailDTO>> adjustDetailMap = adjustDetailList.stream()
                .collect(Collectors.groupingBy(ViQuoteMatchPlanAdjustDetail::getItemType));
        // 构造推荐险种信息
        Map<String, List<SpViInsAdditionClauseGrade>> additionClause = spViInsAdditionClauseService.queryAdditionClauseMap(companyId, chanDetailId);
        //查询当前保单可以购买的所有险种数据
        ViInsuranceBasicQueryDTO queryDTO = new ViInsuranceBasicQueryDTO();
        queryDTO.setCompanyId(offerDetail.getOfferInfo().getCompanyId());
        queryDTO.setUsingNature(carInfo.getUsingNature());
        queryDTO.setFuelType(carInfo.getFuelType());
        Map<String, ViInsuranceBasicDetilCacheDTO> insuranceMap = viInsuranceBasicCacheService.queryInsuranceAmountByPolicy(queryDTO);
        //初始化险种解析器数据
        KindFactorResolveCollector kindFactorResolveCollector = new KindFactorResolveCollector(insuranceMap);
        List<RecommendKindVO> recommendKindList = recommendBuildService.buildRecommendKind(kindFactorResolveCollector, offerDetail, adjustDetailMap.get(FactorTypeEnum.KIND), additionClause);
        result.setRecommendKindList(recommendKindList);
        // 构造推荐系数信息
        List<RecommendContentVO> recommendScoreList = recommendBuildService.buildRecommendContent(adjustDetailMap.get(FactorTypeEnum.SCORE));
        result.setRecommendRatioList(recommendScoreList);
        // 构造推荐关系人信息
        List<RecommendContentVO> recommendPersonList = recommendBuildService.buildRecommendContent(adjustDetailMap.get(FactorTypeEnum.PERSON));
        if (CollectionUtil.isNotEmpty(recommendPersonList)) {
            result.setPromptMessage("请更换投保人、被保人");
            result.setRecommendPersonList(recommendPersonList);
        }
        // 构造推荐非车提示信息
        String message = buildRecommendNoCarMessage(adjustDetailMap.get(FactorTypeEnum.NO_CAR));
        result.setRecommendNoCarMessage(message);
        // 查询非车购买数量限制
        String noCarBuyCountLimit = queryNoCarBuyCountLimit(chanDetailId);
        result.setNoCarBuyCountLimit(noCarBuyCountLimit);
        // 构造非标信息，前端后续保费试算需要用
        ViInsCarInfTp car = viInsCarInfTpService.getByPolicyBatchId(plan.getPolicyBatchId());
        List<ViInsKindDetialTp> kind = viInsKindDetialTpService.listByPolicyBatchId(plan.getPolicyBatchId());
        List<ViInsPlcyInfTp> plcyTp = viInsPlcyInfService.listByPolicyBatchId(plan.getPolicyBatchId());
        // 创建非车订单
        createNonCarOrder(recommendPlanVO, serialNumber, result);
        NonStandardVO nonStandardVO = configureService.buildNonStandard(null, car, plcyTp, kind);
        nonStandardVO.setGiIsCopy(false);
        nonStandardVO.setGiGroupBizNo(serialNumber);
        if(Objects.nonNull(car.getNegotiatePrice())){
            nonStandardVO.setDamageInsAmount(car.getNegotiatePrice());
        }
        result.setSpecialInfo(nonStandardVO);
        result.setStandardVO(nonStandardVO);
        return result;
    }

    private boolean createNonCarOrder(RecommendPlanVO recommendPlanVO, String serialNumber, RecommendPlanDetailVO result) {
        // 获取redis最后缓存的推荐方案id
        String redisKey = RedisKeyConstants.getNonCarRecordRecommendPlanDetailKey(serialNumber);
        String lastCreatePlanId = stringRedisTemplate.opsForValue().get(redisKey);
        // 如果是同一个方案，则不再创建订单
        if (String.valueOf(result.getPlanId()).equals(lastCreatePlanId)) {
            return true;
        }
        // 清理绑定的非车
        carSameSaleOrderFeign.deleteCarSameSaleOrderBySerialNumber(serialNumber);
        if (CollUtil.isEmpty(result.getRecommendNoCar())) {
            cacheRecommendPlanCreate(result, redisKey);
            return false;
        }
        List<String> createOrderNos = Lists.newArrayListWithCapacity(result.getRecommendNoCar().size());
        // 新增非车订单
        CarSameSalePlanChangeRequestVO carSameSalePlanChangeRequestVO = new CarSameSalePlanChangeRequestVO();
        SalesmanInfoDTO salesmanInfoDTO = new SalesmanInfoDTO();
        salesmanInfoDTO.setSalesmanId(String.valueOf(result.getSalesman().getUserId()));
        carSameSalePlanChangeRequestVO.setSalesmanInfo(salesmanInfoDTO);
        carSameSalePlanChangeRequestVO.setBaseInfo(BeanUtil.copyProperties(result.getBaseInfo(), BaseInfoDTO.class));
        carSameSalePlanChangeRequestVO.setCarInfo(BeanUtil.copyProperties(result.getCarInfo(), CarInfoDTO.class));
        carSameSalePlanChangeRequestVO.setPersonInfos(TunsBeanUtil.copyList(result.getPersonInfos(), PersonInfoDTO.class));
        for (RecommendNoCarDTO prod : result.getRecommendNoCar()) {
            if (CollUtil.isEmpty(prod.getProject())) {
                continue;
            }
            NoCarProjectDetailVO project = prod.getProject().get(0);
            CarSameSaleOrderSimpleDTO carSameSaleOrderSimpleDTO = new CarSameSaleOrderSimpleDTO();
            carSameSaleOrderSimpleDTO.setProjectId(project.getProjectId());
            carSameSaleOrderSimpleDTO.setProdId(String.valueOf(project.getProdId()));
            carSameSaleOrderSimpleDTO.setChannelId(project.getChannelId());
            carSameSaleOrderSimpleDTO.setCompanyId(project.getCompanyId());
            carSameSaleOrderSimpleDTO.setSerialNumber(serialNumber);
            carSameSaleOrderSimpleDTO.setOrderNo(project.getOrderNo());
            carSameSaleOrderSimpleDTO.setSumPrem(project.getPrem());
            carSameSalePlanChangeRequestVO.setCarSameSaleOrderSimpleDTO(carSameSaleOrderSimpleDTO);
            carSameSaleOrderFeign.buyCarSameSalePlan(carSameSalePlanChangeRequestVO);
            createOrderNos.add(project.getOrderNo());
            if (!Boolean.TRUE.equals(recommendPlanVO.getBindOfBuy())) {
                // 如果不是报价购买的非车，则是推荐方案的非车目前非车的收益之后计算第一个，所有订单也只创建第一个
                break;
            }
        }
        cacheRecommendPlanCreate(result, redisKey);
        return CollUtil.isNotEmpty(createOrderNos);
    }

    /**
     * 缓存推荐方案非车创建记录
     *
     * @param result
     * @param redisKey
     */
    private void cacheRecommendPlanCreate(RecommendPlanDetailVO result, String redisKey) {
        stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(result.getPlanId()));
        stringRedisTemplate.expire(redisKey, 1, TimeUnit.DAYS);
    }

    private String queryNoCarBuyCountLimit(Integer chanDetailId) {
        List<ComsChanConfigDTO> chanConfigDTOList = spiderChanFeign.getChanConfig(chanDetailId);
        return chanConfigDTOList.stream()
                .filter(e -> e.getConfigField().equals(ChanConfigFieldEnum.NOCAR_BUY_COUNT_LIMIT))
                .map(ComsChanConfigDTO::getConfigValue)
                .findFirst().orElse("0");
    }

    /**
     * 构造
     *
     * @return
     */
    public IncomeVO buildIncome(Integer planId) {
        IncomeVO incomeVO = new IncomeVO();
        incomeVO.setShowIncome(true);
        PreciseMatchResultDTO preciseMatchResultDTO = viQuoteMatchPlanService.queryByPlanId(planId);
        if (Objects.nonNull(preciseMatchResultDTO)) {
            incomeVO.setTotalCommFee(PlantIncomeUtil.scale2AndRoundUp(preciseMatchResultDTO.getTotalCommFee()));
            incomeVO.setQuoteIncomeList(PlantIncomeUtil.buildQuoteIncomeList(preciseMatchResultDTO.getIncomeList()));
        }
        return incomeVO;
    }

    @Override
    public CheckPlantChangeVO checkPlantChange(Long policyBatchId) {
        return checkPlantChangeService.checkPlantChange(policyBatchId);
    }

    @Override
    public List<RecommendPlanVO> switchRecommend(String serialNumber, String policyBatchId) {
        // 先模糊查询serialNumber下所有批次数据
        RecommendPlanResultVO planVO = getRecommendPlanFromRedis(serialNumber, policyBatchId);
        return Objects.isNull(planVO) ? Collections.emptyList() : planVO.getRecommendPlanList();
    }

    private RecommendPlanResultVO getRecommendPlanFromRedis(String serialNumber, String policyBatchId) {
        final String mainKey = String.format("%s:%s", CarCoreCtt.REGION_RECOMMEND_RESULT, serialNumber);
        log.info("[调试]rediskey：{}", mainKey);
        return Optional.ofNullable(stringRedisTemplate.opsForHash().get(mainKey, policyBatchId))
                .map(this::safeConvertToString)
                .filter(StrUtil::isNotBlank)
                .map(json -> safeParseJson(json, mainKey, policyBatchId))
                .orElseGet(() -> {
                    log.debug("缓存未命中 [key:{}] [field:{}]", mainKey, policyBatchId);
                    return null;
                });
    }

    private String safeConvertToString(Object value) {
        try {
            return value.toString();
        } catch (Exception e) {
            log.warn("缓存值类型转换异常: {}", e.getMessage());
            return null;
        }
    }

    private RecommendPlanResultVO safeParseJson(String json, String mainKey, String field) {
        try {
            return JSONUtil.toBean(json, RecommendPlanResultVO.class, true);
        } catch (Exception e) {
            final String truncatedJson = StrUtil.subPre(json, 200); // 截取前200字符日志
            log.error("反序列化失败 [key:{}][field:{}] \nPartial JSON: {} \nError: {}",
                    mainKey, field, truncatedJson, e.getMessage());
            return null;
        }
    }

    /**
     * 构造 推荐非车提示语
     *
     * @param noCarAdjustList 非车调整细则
     * @return
     */
    private String buildRecommendNoCarMessage(List<ViQuoteMatchAdjustDetailDTO> noCarAdjustList) {
        if (CollectionUtil.isEmpty(noCarAdjustList)) {
            return "投保非车险产品将获得更高收益！";
        }
        return NoCarMessageEnum.resolveMessage(noCarAdjustList);
    }

}
