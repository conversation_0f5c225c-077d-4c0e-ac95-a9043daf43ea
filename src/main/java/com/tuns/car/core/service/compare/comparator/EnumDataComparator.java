package com.tuns.car.core.service.compare.comparator;

import cn.hutool.core.util.ClassUtil;
import com.tuns.car.core.constant.compare.ExtendRuleTypeEnum;
import com.tuns.car.core.constant.compare.InsEnumConverter;
import com.tuns.car.core.dto.compare.CompareContextDTO;
import com.tuns.car.core.service.compare.DataComparator;
import com.tuns.core.boot.exception.TunsBusinessException;
import org.springframework.stereotype.Component;

/**
 * 枚举 对比器
 *
 * <AUTHOR>
 * @since 2022/8/30
 */
@Component
public class EnumDataComparator extends AbstractDataComparator implements DataComparator {

    @Override
    protected Boolean compareValue(CompareContextDTO context) {
        Object insValue = context.getCurrentInsValue();
        boolean isEnum = ClassUtil.isAssignable(InsEnumConverter.class, insValue.getClass());
        if (!isEnum) {
            throw new TunsBusinessException("数据比对失败,出现未知数据类型:{}", insValue.getClass());
        }
        InsEnumConverter converterEnum = (InsEnumConverter) insValue;
        insValue = converterEnum.convert();
        context.setCurrentInsValue(insValue);
        return super.compareValue(context);
    }

    @Override
    public ExtendRuleTypeEnum supportType() {
        return ExtendRuleTypeEnum.T_ENUM;
    }
}
