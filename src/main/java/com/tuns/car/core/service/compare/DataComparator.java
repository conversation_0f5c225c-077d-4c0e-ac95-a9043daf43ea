package com.tuns.car.core.service.compare;

import com.tuns.car.core.constant.compare.ExtendRuleTypeEnum;
import com.tuns.car.core.dto.compare.CompareContextDTO;

/**
 * 数据比较器 比对上下文中的保险公司数据值和我方数据值是否相等
 *
 * <AUTHOR>
 * @since 2022/8/30
 */
public interface DataComparator {

    /**
     * 判断上下文中的当前比较的保险公司值 和 我方系统值是否相等
     *
     * @param context
     */
    Boolean equal(CompareContextDTO context);

    /**
     * 支持的拓展规则类型
     *
     * @return
     */
    ExtendRuleTypeEnum supportType();

    /**
     * 支持的拓展规则子类型
     *
     * @return
     */
    default String supportSonType() {
        return "";
    }

    /**
     * 完整的支持类型
     *
     * @return
     */
    default String getCompleteSupportType() {
        return supportType() + supportSonType();
    }
}
