package com.tuns.car.core.service.common;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tuns.car.core.entity.ViInsuranceAppConfig;

import java.util.List;
import java.util.Map;

/**
 * 车险险种app配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-25 11:27:07
 */
public interface ViInsuranceAppConfigService extends IService<ViInsuranceAppConfig> {

    /**
     * 查询--根据保险公司分组
     *
     * @return
     */
    Map<String, List<ViInsuranceAppConfig>> queryAll();
}

