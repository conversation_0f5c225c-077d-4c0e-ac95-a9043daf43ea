package com.tuns.car.core.feign.template;

import com.tuns.car.core.dto.template.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 重复投保模版相关
 *
 * <AUTHOR>
 * @since 2023-9-7
 */

@FeignClient(name = "tuns-car-core")
@RequestMapping("/feign/carcore/insurerepair")
public interface InsureRepeatTemplateFeign {


    /**
     * 尝试修复投保时间
     *
     * @param insureDateRepairDTO
     * @return
     */
    @PostMapping("/tryInsureDateRepair")
    @ApiOperation(value = "尝试修复投保时间", httpMethod = "POST")
    List<RepeatProposalMsgDTO> tryInsureDateRepair(@RequestBody InsureDateRepairDTO insureDateRepairDTO);

    /**
     * 尝试修复投保时间（old）
     *
     * @param insureDateRepairDTO
     * @return
     */
    @PostMapping("/dateResolver")
    @ApiOperation(value = "尝试修复投保时间（old）", httpMethod = "POST")
    @Deprecated
    List<RepeatProposalMsgDTO> dateResolver(@RequestBody InsureDateRepairDTO insureDateRepairDTO);

    /**
     * 生成一个新的模版
     *
     * @param repeatTemplateGenerateDTO
     * @return
     */
    @PostMapping("/generateTemplate")
    @ApiOperation(value = "生成模版", httpMethod = "POST")
    RepeatTemplateGenerateResult generateTemplate(@RequestBody RepeatTemplateGenerateDTO repeatTemplateGenerateDTO);

    /**
     * 模版审核通过投入使用前进行的一次功能测试
     *
     * @param templateId
     * @return
     */
    @PostMapping("/functionTestOfUseBefore")
    @ApiOperation(value = "模版自检", httpMethod = "POST")
    boolean functionTestOfUseBefore(@RequestParam("templateId") Integer templateId);

    /**
     * 特征值发生变更时，查询所使用到该特征值的模版进行功能测试
     *
     * @param functionTestDTO
     * @return
     */
    @PostMapping("/functionTestOfRegexUpdate")
    @ApiOperation(value = "特征值编辑自检", httpMethod = "POST")
    boolean functionTestOfRegexUpdate(@RequestBody TemplateFunctionTestDTO functionTestDTO);

    /**
     * 批量的模版主体解析
     *
     * @param resolveDTOList
     * @return
     */
    @PostMapping("/batchResolveTemplateContent")
    @ApiOperation(value = "批量模版主体解析", httpMethod = "POST")
    Map<Integer, String> batchResolveTemplateContent(@RequestBody List<RepeatTemplateResolveDTO> resolveDTOList);
}
