package com.tuns.car.core.feign.xxjob;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(name = "tuns-car-core")
@RequestMapping("/feign/xxljob/quote")
public interface ViQuotePolicyXxJobFeign {

    @PostMapping("/premiumCalculateCancel")
    @ApiOperation(value = "报价取消操作", httpMethod = "POST")
    String premiumCalculateCancel();
}
