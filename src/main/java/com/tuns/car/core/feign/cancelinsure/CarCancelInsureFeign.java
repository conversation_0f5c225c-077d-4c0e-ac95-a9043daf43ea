package com.tuns.car.core.feign.cancelinsure;

import com.tuns.car.core.dto.AllowCancelUnderwriteDTO;
import com.tuns.car.core.dto.CancelUnderwriteSubmitDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 提供给web撤销核保使用的feign
 *
 * <AUTHOR>
 * @since 2023-10-26 16:21
 */
@FeignClient(name = "tuns-car-core")
@RequestMapping("/feign/carcore/cancelinsure")
public interface CarCancelInsureFeign {


    @PostMapping("/isAllowCancelUnderwriteByOrder")
    @ApiOperation(value = "当前状态是否允许撤销核保--订单", httpMethod = "POST")
    boolean isAllowCancelUnderwriteByOrder(@RequestBody AllowCancelUnderwriteDTO allowCancelUnderwriteDTO);

    @PostMapping("/isAllowCancelUnderwriteByOffer")
    @ApiOperation(value = "当前状态是否允许撤销核保--报价单", httpMethod = "POST")
    boolean isAllowCancelUnderwriteByOffer(@RequestBody AllowCancelUnderwriteDTO allowCancelUnderwriteDTO);

    @PostMapping("/cancelUnderwrite")
    @ApiOperation(value = "撤销核保", httpMethod = "POST")
    boolean cancelUnderwrite(@RequestBody CancelUnderwriteSubmitDTO dto);
}
