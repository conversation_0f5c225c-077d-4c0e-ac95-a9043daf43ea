package com.tuns.car.core.feign.chan;

import com.tuns.car.core.vo.chan.CompanyChannelPackageVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 渠道查询
 *
 * <AUTHOR>
 * @since 2025-3-2 11:40
 */
@FeignClient(name = "tuns-car-core")
@RequestMapping("/feign/channelQuery")
public interface ChannelQueryFeign {


    /**
     * 查询渠道列表
     *
     * @param policyBatchId
     * @return
     */
    @GetMapping("/listChan")
    List<CompanyChannelPackageVO> listChan(@RequestParam("policyBatchId") Long policyBatchId);
}
