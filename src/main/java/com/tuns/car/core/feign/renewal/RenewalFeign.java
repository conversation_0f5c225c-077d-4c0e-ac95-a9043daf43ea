package com.tuns.car.core.feign.renewal;

import com.tuns.car.core.entity.ViInsCheckInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 组织结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/5
 */
@FeignClient(name = "tuns-car-core")
@Api(value = "ViInsCheckFeign")
@RequestMapping("/feign/renewal")
public interface RenewalFeign {

    @ApiOperation(value = "清除掉新转续映射保司的redis缓存", httpMethod = "GET")
    @GetMapping("/clearRenewalMapCompanyRedisCache")
    boolean clearRenewalMapCompanyRedisCache();


}
