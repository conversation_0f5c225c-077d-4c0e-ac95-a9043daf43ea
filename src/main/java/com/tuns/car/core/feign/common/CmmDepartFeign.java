package com.tuns.car.core.feign.common;

import com.tuns.car.core.entity.CmmDepartInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 组织结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/5
 */
@FeignClient(name = "tuns-car-core")
@Api(value = "CmmDepartFeign")
@RequestMapping("/feign/cmmDepartFeign")
public interface CmmDepartFeign {

    /**
     * 根据组织代码获取组织信息
     *
     * @param departCode 组织代码
     */
    @ApiOperation(value = "根据组织代码获取组织信息", httpMethod = "GET")
    @GetMapping("/getByCode")
    CmmDepartInfo getByCode(@ApiParam(name = "departCode", value = "组织代码", required = true)
                            @RequestParam("departCode") String departCode);

    /**
     * 查询所有组织信息
     */
//    @ApiOperation(value = "查询所有组织信息", httpMethod = "GET")
//    @GetMapping("/queryDepartInfoList")
//    @Deprecated
//    List<CmmDepartInfo> queryDepartInfoList();
}
