package com.tuns.car.core.feign.carcore;

import com.tuns.car.core.dto.car.ViInsPlcyCommissionDTO;
import com.tuns.car.core.dto.car.ViOfferAppendDTO;
import com.tuns.car.core.entity.ViInsPlcyCommission;
import com.tuns.car.core.entity.ViInsPlcyInf;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 业务-保单管理--车险保单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/5
 */
@FeignClient(name = "tuns-car-core")
@Api(value = "PolicyFeign")
@RequestMapping("/feign/carcore/policy")
public interface PolicyFeign {

    /**
     * 根据条件查询车险保单佣金记录列表
     *
     * @param viInsPlcyCommissionDTO 车险保单佣金记录查询请求参数
     */
    @ApiOperation(value = "根据条件查询车险保单佣金记录列表", httpMethod = "POST")
    @PostMapping("/getCommissionList")
    List<ViInsPlcyCommission> getCommissionList(@RequestBody ViInsPlcyCommissionDTO viInsPlcyCommissionDTO);

    /**
     * 根据保单号获取保单基本信息
     *
     * @param policyNumber 保单号
     */
    @ApiOperation(value = "根据保单号获取保单基本信息", httpMethod = "POST")
    @PostMapping("/getPolicyByPolicyNumber")
    ViInsPlcyInf getPolicyByPolicyNumber(@RequestParam(value = "policyNumber") String policyNumber);


    /**
     * 判断车险保单号是否存在
     *
     * @param policyNumber 保单号
     * @return 是否存在
     */
    @ApiOperation(value = "判断车险保单号是否存在", httpMethod = "POST")
    @PostMapping("/policyNumberIsExist")
    Boolean policyNumberIsExist(@RequestParam(value = "policyNumber") String policyNumber);

    /**
     * 补传影像
     * tuns-car-query调用
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "pc补传影像资料", httpMethod = "POST")
    @PostMapping(path = "/pcMakeUpImage")
    Boolean pcMakeUpImage(@Valid @RequestBody ViOfferAppendDTO request);

}
