package com.tuns.car.core.feign.common;

import com.tuns.car.core.entity.ViInsCheckInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 组织结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/5
 */
@FeignClient(name = "tuns-car-core")
@Api(value = "ViInsCheckFeign")
@RequestMapping("/feign/viInsCheck")
public interface ViInsCheckFeign {

    /**
     * 根据保单批次ID获取车险保单审核流水记录
     *
     * @param policyBatchId 保单批次ID
     */
    @ApiOperation(value = "根据保单批次ID获取车险保单审核流水记录", httpMethod = "GET")
    @GetMapping("/getByPolicyBatchId")
    List<ViInsCheckInfo> getByPolicyBatchId(@ApiParam(name = "policyBatchId", value = "保单批次ID", required = true)
                                            @RequestParam("policyBatchId") Long policyBatchId);

}
