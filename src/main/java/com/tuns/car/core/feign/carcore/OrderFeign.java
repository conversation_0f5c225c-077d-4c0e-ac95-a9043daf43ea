package com.tuns.car.core.feign.carcore;

import com.tuns.car.core.dto.car.ViOrderQuoteMatchPlanDTO;
import com.tuns.car.core.vo.ViReversePolicyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 业务-保单管理--车险订单 （仅供远程feign调用，不直接开放给前端使用）
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 13:48
 */
@FeignClient(name = "tuns-car-core")
@Api(value = "OrderFeign", tags = "业务-保单管理--车险订单---生成保单")
@RequestMapping("/feign/carcore/order")
public interface OrderFeign {

    @ApiOperation(value = "查询满足反向匹配的订单批次id集合", httpMethod = "POST")
    @PostMapping("/queryOrderReversePolicyBatchId")
    ViReversePolicyVO queryOrderReversePolicyBatchId(@RequestBody ViOrderQuoteMatchPlanDTO dto);

    @ApiOperation(value = "查询满足反向匹配的订单批次id集合", httpMethod = "POST")
    @PostMapping("/queryOrderReverseMatchLimitPolicyBatchId")
    List<Long> queryOrderReverseMatchLimitPolicyBatchId(@RequestBody ViOrderQuoteMatchPlanDTO dto);

    @ApiOperation(value = "查询指定订单的政策匹配参数", httpMethod = "POST")
    @PostMapping("/queryOrderReversePolicyDetail")
    String queryOrderReversePolicyDetail(@RequestParam(value = "policyBatchId") Long policyBatchId);
}
