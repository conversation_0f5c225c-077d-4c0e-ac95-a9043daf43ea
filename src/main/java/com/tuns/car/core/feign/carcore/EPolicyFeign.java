package com.tuns.car.core.feign.carcore;

import com.tuns.car.core.dto.policy.DataFixEPolicyDTO;
import com.tuns.car.core.dto.policy.ViInsDelayTaskDTO;
import com.tuns.car.core.vo.car.NewViInsEPolicyRecordVO;
import feign.Request;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 电子保单接口服务（仅供远程feign调用，不直接开放给前端使用）
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 13:48
 */
@FeignClient(name = "tuns-car-core")
@Api(value = "EPolicyFeign", tags = "业务-保单管理--车险保单---保单查询：查看电子保单")
@RequestMapping("/feign/carcore/epolicy")
public interface EPolicyFeign {

    /**
     * 查询--电子保单
     *
     * @param policyBatchId 批次id
     * @return
     */
    @GetMapping("/listEPolicy")
    @ApiOperation(value = "查询--电子保单", httpMethod = "GET")
    NewViInsEPolicyRecordVO listEPolicy(@ApiParam(name = "policyBatchId", value = "批次号", required = true) @RequestParam("policyBatchId") String policyBatchId,
                                        Request.Options options);

    /**
     * 下载电子保单延迟队列任务初始化日志
     *
     * @param
     * @return
     */
    @ApiOperation(value = "下载电子保单延迟队列任务初始化日志", httpMethod = "POST")
    @PostMapping("/addDownloadEPolicyDelayTask")
    List<ViInsDelayTaskDTO> addDownloadEPolicyDelayTask(@RequestBody List<ViInsDelayTaskDTO> taskList);


    /**
     * 重新下载某种类型的电子保单
     *
     * @return
     */
    @PostMapping("/dataFixEPolicy")
    @ApiOperation(value = "重新下载某种类型的电子保单", httpMethod = "GET")
    void dataFixEPolicy(@RequestBody DataFixEPolicyDTO dto);
}
