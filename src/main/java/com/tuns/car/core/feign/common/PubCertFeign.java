package com.tuns.car.core.feign.common;

import com.tuns.car.core.entity.PubCertInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 证件信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/19
 */
@FeignClient(name = "tuns-car-core")
@Api(value = "PubCertFeign")
@RequestMapping("/feign/pub/cert")
public interface PubCertFeign {

    /**
     * 根据类型,号码获取证件信息
     *
     * @param type 类型
     * @param number 号码
     * @return
     */
    @ApiOperation(value = "根据类型,号码获取证件信息", httpMethod = "GET")
    @GetMapping("/getByNumberAndType")
    PubCertInfo getByNumberAndType(@ApiParam(name = "type", value = "类型", required = true) @RequestParam("type") String type,
                                     @ApiParam(name = "number", value = "号码", required = true) @RequestParam("number") String number);


}
