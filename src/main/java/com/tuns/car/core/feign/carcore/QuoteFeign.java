package com.tuns.car.core.feign.carcore;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-9-23 15:50
 */
@FeignClient(name = "tuns-car-core")
@Api(value = "QuoteFeign", tags = "业务--报价单管理---车险报价")
@RequestMapping("/feign/carcore/quote")
public interface QuoteFeign {


    /**
     * 报价单刷新
     *
     * @param policyBatchIds
     * @return
     */
    @ApiOperation(value = "报价单状态刷新", httpMethod = "POST")
    @PostMapping("/flushQuote")
    boolean flushQuote(@RequestBody List<String> policyBatchIds);
}
