package com.tuns.car.core.feign.common;

import com.tuns.car.core.vo.TsRenewalViStatusVO;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 车险续保保单数据
 * <AUTHOR>
 * @since 2022-10-24 11:37
 **/
@FeignClient(name = "tuns-car-core")
@Api(value = "RenewalTimeFeign", tags = "车险续保保单")
@RequestMapping("/feign/vi/renewal")
@Deprecated
public interface TsRenewalViStatusFeign {

    /**
     * 查询车险续保数据
     * @param salesmanId 合作伙伴id
     * @return 车险续保数据
     */
    @GetMapping("/queryRenewalData")
    List<TsRenewalViStatusVO> queryRenewalData(@RequestParam("salesmanId") String salesmanId);
}
