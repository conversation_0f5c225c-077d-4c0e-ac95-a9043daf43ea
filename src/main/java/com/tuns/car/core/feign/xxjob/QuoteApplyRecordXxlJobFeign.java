package com.tuns.car.core.feign.xxjob;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2025/1/17 16:27
 */
@FeignClient(name = "tuns-car-core")
@RequestMapping("/feign/xxljob/quoteApplyRecord")
public interface QuoteApplyRecordXxlJobFeign {

    /**
     * 发送投保申请待审核催办消息
     *
     * @return
     */
    @PostMapping("/sendQuoteApplyUnauditedMessage")
    @ApiOperation(value = "【戴艳芳】发送投保申请待审核催办消息", httpMethod = "POST")
    String sendQuoteApplyUnauditedMessage();
}
