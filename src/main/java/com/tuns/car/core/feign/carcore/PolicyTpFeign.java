package com.tuns.car.core.feign.carcore;

import com.tuns.car.core.dto.QueryPlcyInfTpByProposalNoDTO;
import com.tuns.car.core.vo.QueryPlcyInfTpByProposalNoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 业务-保单管理--车险保单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/5
 */
@FeignClient(name = "tuns-car-core")
@Api(value = "PolicyFeign")
@RequestMapping("/feign/carcore/policyTp")
public interface PolicyTpFeign {

    /**
     * 根据投保单号查询报价信息
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "根据投保单号查询报价信息", httpMethod = "POST")
    @PostMapping("/queryPlcyInfTpByProposalNumber")
    QueryPlcyInfTpByProposalNoVO queryPlcyInfTpByProposalNumber(@RequestBody QueryPlcyInfTpByProposalNoDTO dto);
}
