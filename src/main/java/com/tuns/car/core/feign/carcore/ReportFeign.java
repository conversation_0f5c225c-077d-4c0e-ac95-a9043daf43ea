package com.tuns.car.core.feign.carcore;

import com.tuns.car.core.constant.report.CommandTypeEnum;
import com.tuns.car.core.constant.report.ReportSyncTableEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 报表统计服务（仅供远程feign调用，不直接开放给前端使用）
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 13:48
 */
@FeignClient(name = "tuns-car-core")
@Api(value = "ReportFeign", tags = "系统-报表管理-车险报表")
@RequestMapping("/feign/carcore/report")
public interface ReportFeign {

    /**
     * 同步数据
     *
     * @param policyBatchIds 批次id
     * @return
     */
    @PostMapping("/sync")
    @ApiOperation(value = "同步数据到报表", httpMethod = "POST")
    boolean sync(@RequestParam("policyBatchIds") List<Long> policyBatchIds, @RequestParam("table") ReportSyncTableEnum table, @RequestParam("commandType") CommandTypeEnum commandType);
}
