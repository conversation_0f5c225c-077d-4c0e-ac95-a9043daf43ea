package com.tuns.car.core.feign.rulematching;

import com.tuns.car.core.dto.AdditionClauseElementDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * 车险规则匹配
 * @date 2024-4-30 9:08
 */
@FeignClient(name = "tuns-car-core")
@RequestMapping("/feign/carRuleMatching")
public interface CarRuleMatchingFeign<Req> {
    /**
     * 规则匹配
     *
     * @param clauseElementDetails
     * @param req
     * @return
     */
    @PostMapping("/doCarRuleMatching")
    @ApiOperation(value = "规则匹配", httpMethod = "POST")
    boolean doCarRuleMatching(List<AdditionClauseElementDTO> clauseElementDetails, Req req);
}
