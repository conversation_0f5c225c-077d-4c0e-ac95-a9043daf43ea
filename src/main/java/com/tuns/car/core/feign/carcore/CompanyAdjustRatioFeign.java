package com.tuns.car.core.feign.carcore;

import com.tuns.car.core.vo.CompanyAdjustRatioVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 保险公司可调整系数feign
 *
 * <AUTHOR>
 */
@FeignClient(name = "tuns-car-core")
@Api(value = "CompanyAdjustRatioFeign", tags = "保险公司")
@RequestMapping("/feign/carcore/adjust")
public interface CompanyAdjustRatioFeign {


    /**
     * 查询 -- 保险公司支持的调整系数
     *
     * @param companyId 保险公司id
     * @return
     */
    @ApiOperation(value = "查询-保险公司支持的调整系数", httpMethod = "GET")
    @GetMapping("/queryCompanyAdjustRatio")
    List<CompanyAdjustRatioVO> queryByCompanyId(@RequestParam(value = "companyId") String companyId);
}
