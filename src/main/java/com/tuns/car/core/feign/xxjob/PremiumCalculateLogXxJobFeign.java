package com.tuns.car.core.feign.xxjob;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(name = "tuns-car-core")
@RequestMapping("/feign/carcore/premiumcalculatelog")
public interface PremiumCalculateLogXxJobFeign {

    @PostMapping("/batchProcessSaveLog")
    @ApiOperation(value = "批量处理保存日志", httpMethod = "POST")
    Boolean batchProcessLog();
}
