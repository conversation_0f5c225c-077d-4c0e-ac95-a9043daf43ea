package com.tuns.car.core.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 本渠道续保标识 0本渠道续保 1非本渠道续保 2经代续保 3非经代续保
 * @create 2022-08-04 11:42
 **/
@AllArgsConstructor
@Getter
public enum CarThisRenewalEnum {

    LOCAL_CHANNEL_RENEWAL("0","本渠道续保"),
    NO_LOCAL_CHANNEL_RENEWAL("1","非本渠道续保"),
    GENERATION_RENEWAL("2","经代续保"),
    NO_GENERATION_RENEWAL("3","非经代续保");

    private String code;
    private String value;

    private static final Map<String, String> CAR_THIS_RENEWAL_ENUM_MAP = Arrays.stream(CarThisRenewalEnum.values()).collect(Collectors.toMap(CarThisRenewalEnum::getCode, CarThisRenewalEnum::getValue));

    public static String getDesc(String code){
        return CAR_THIS_RENEWAL_ENUM_MAP.get(code);
    }
}
