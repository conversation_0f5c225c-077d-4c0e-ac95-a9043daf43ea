package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @project: tuns-business
 * @description: 车辆类型
 * @author: Gk
 * @time: 2022-4-25 13:08
 */
@Getter
@AllArgsConstructor
public enum CarTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    BELOW_SIX_SEATS("SEAT1", "6座以下", "K", 1, s -> s < 6),

    SIX_TEN_SEATS("SEAT2", "6-10座", "K", 1, s -> s >= 6 && s < 10),

    TEN_TWENTY_SEATS("SEAT3", "10-20座", "K", 1, s -> s >= 10 && s < 20),

    TWENTY_THIRTY_SIX_SEATS("SEAT4", "20-36座", "K", 1, s -> s >= 20 && s < 36),

    THIRTY_SIX_SEATS_ABOVE("SEAT5", "36座以上", "K", 1, s -> s >= 36),

    LESS_THAN_TWO_TONS("LOAD1", "2吨以下", "H", 2, s -> s < 2),

    TWO_FIVE_TONS("LOAD2", "2-5吨", "H", 2, s -> s >= 2 && s < 5),

    FIVE_TEN_TONS("LOAD3", "5-10吨", "H", 2, s -> s >= 5 && s < 10),

    TEN_TONS_ABOVE("LOAD4", "10吨以上", "H", 2, s -> s >= 10),

    LOW_SPEED_TRUCK("LOAD5", "低速载货汽车", "H", 2, s -> s == -Integer.MAX_VALUE),

    ;

    String value;

    String describe;

    String type;

    // 1座位 2吨位
    Integer typeGroup;

    Predicate<Float> querySection;

    private static final Map<String, CarTypeEnum> CAR_TYPE_ENUM_MAP = Arrays.stream(CarTypeEnum.values()).collect(Collectors.toMap(CarTypeEnum::getValue, Function.identity()));

    private static final List<CarTypeEnum> PASSENGER_CAR = Arrays.asList(BELOW_SIX_SEATS, SIX_TEN_SEATS, TEN_TWENTY_SEATS, TWENTY_THIRTY_SIX_SEATS, THIRTY_SIX_SEATS_ABOVE);

    @Override
    public String toString() {
        return this.value;
    }

    @Override
    public String getDescription() {
        return describe;
    }

    public static CarTypeEnum getCarTypeByValue(String value) {
        return CAR_TYPE_ENUM_MAP.get(value);
    }

    public static CarTypeEnum querySeatCountSection(Integer seatCount) {
        return Arrays.stream(CarTypeEnum.values()).filter(e -> e.typeGroup.equals(1)).filter(e -> e.querySection.test(seatCount.floatValue())).findAny().orElse(null);
    }

    public static CarTypeEnum querySeatCountSection(Integer actualValue, Integer typeGroup) {
        return querySeatCountSection(actualValue.floatValue(), typeGroup);
    }

    public static CarTypeEnum querySeatCountSection(Float actualValue, Integer typeGroup) {
        if (Objects.isNull(actualValue) || Objects.isNull(typeGroup)) {
            return null;
        }
        return Arrays.stream(CarTypeEnum.values())
                .filter(e -> e.typeGroup.equals(typeGroup))
                .filter(e -> e.querySection.test(actualValue))
                .findAny().orElse(null);
    }

    public static String getDescriptionByValue(String value) {
        return Optional.ofNullable(getCarTypeByValue(value))
                .map(CarTypeEnum::getDescription)
                .orElse(null);
    }

    /**
     * 是否是客车
     *
     * @param carTypeEnum
     * @return
     */
    public static Boolean isPassengerCar(CarTypeEnum carTypeEnum) {
        return PASSENGER_CAR.contains(carTypeEnum);
    }
}
