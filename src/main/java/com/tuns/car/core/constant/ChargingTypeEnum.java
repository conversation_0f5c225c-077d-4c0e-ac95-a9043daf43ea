package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ChargingTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    /**
     * 地面充电桩
     */
    GROUND_CHARGING_PILE("01", "地面充电桩"),
    /**
     * 壁挂式充电桩
     */
    WALL_MOUNTED_CHARGING_PIL("02", "壁挂式充电桩"),
    /**
     * 其他类充电桩
     */
    OTHER_CHARGING_PILES("99", "其他类充电桩");
    private String value;

    private String description;

    private static Map<String, ChargingTypeEnum> CHARGING_TYPE_MAP = Stream.of(ChargingTypeEnum.values()).collect(Collectors.toMap(ChargingTypeEnum::getValue, Function.identity()));

    public static ChargingTypeEnum getByValue(String value) {
        return CHARGING_TYPE_MAP.get(value);
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    ChargingTypeEnum(String key, String desc) {
        this.value = key;
        this.description = desc;
    }

    @Override
    public String toString() {
        return getValue();
    }
}
