package com.tuns.car.core.constant;

/**
 * ViIssueConstants 2018/6/22
 *
 * <AUTHOR>
 */
@Deprecated
public class ViIssueConstants {

    /**
     * 车险标志
     */
    public final class VehicleInsMark {

        public static final String MARK_VI = "VI";//vi
        public static final String MARK_VIS = "VIS";//vis
    }

    /**
     * 流水号前缀
     */
    public final class SerialPrefix {

        public static final String SHORTPREFIX = "T";//T
        public static final String PREFIX = "TS";//TS
        public static final String LONGPREFIX = "HNTSBX";//HNTSBX
    }

    /**
     * 文章等级
     */
    public final class ArticleLevel {

        public static final String ONE = "01";
        public static final String TWO = "02";

    }

    /**
     * 报价方式
     */
    private final class RecordType {

        public static final String CI = "1";//单交强
        public static final String BI = "2";//单商业
        public static final String BOTH = "3";//交商同保
    }

    /**
     * 交强险默认税率
     */
    public final class TaxRate {

        public static final double DEFAULT = 0.06;
    }

    /**
     * 自动报价标志
     */
    public final class AutoMark {

        public static final String MANUAL = "0";//人工报价
        public static final String AUTO = "1";//自动报价
        public static final String MOBILE_INTEGRATED_QUOTATION = "3";//移动集成出单
    }

    /**
     * 比价状态：0等待比价/1比价中/2比价完成/3比价取消
     */
    public final class CompareStatus {

        public static final String WAIT = "0";
        public static final String DOING = "1";
        public static final String DONE = "2";
        public static final String CANCEL = "3";
    }

    /**
     * 转人工标志
     */
    public final class ToManualmark {

        public static final String YES = "1";//转人工操作
        public static final String NO = "0";//非转人工操作
    }

    /**
     * 投保子状态
     */
    public final class InsuredSubStatus {

        public static final String A1 = "A1";//等待报价
        public static final String A2 = "A2";//等待报价（处理中）
        public static final String A21 = "A2-1";//等待报价（需补传资料）

        public static final String A3 = "A3";//报价成功
        public static final String A4 = "A4";//报价失败（等待补传资料）
        public static final String A5 = "A5";//报价失败（需调整投保方案）
        public static final String A6 = "A6";//报价失败
        public static final String A7 = "A7";//报价取消

        public static final String B1 = "B1";//等待核保
        public static final String B11 = "B1-1";//等待核保（需补传资料）
        public static final String B2 = "B2";//核保通过，已生成订单
        public static final String B3 = "B3";//核保失败（等待补传资料）
        public static final String B4 = "B4";//核保失败（需调整投保方案）
        public static final String B5 = "B5";//核保失败
        public static final String B6 = "B6";//核保取消

        public static final String C1 = "C1";//等待支付
        public static final String C11 = "C1-1";//已支付待确认
        public static final String C19 = "C1-9";//支付成功未承保
        public static final String C2 = "C2";//支付成功已承保
        public static final String C3 = "C3";//支付失败
        public static final String C4 = "C4";//保险公司订单取消
        public static final String C41 = "C4-1";//系统订单取消
        public static final String C42 = "C4-2";//因占单自动取消核保

        public static final String D1 = "D1";//保单自取
        public static final String D2 = "D2";//保单配送
        public static final String D3 = "D3";//订单完成
        public static final String D4 = "D4";//订单完成待评价
        public static final String D5 = "D5";//订单完成已评价
    }

    /**
     * 车险订单操作标志
     */
    public final class ViOrderOperateMark {

        public static final String CANCEL = "C";//取消
        public static final String DELETE = "D";//删除
    }

    /**
     * 车辆产地
     */
    public final class CarOrigin {

        public static final String IMPORT = "1";//进口
        public static final String DOMESTIC = "2";//国产
        public static final String JOINT = "3";//合资
    }

    /**
     * 减税类型代码
     */
    public final class TaxCutsMark {

        public static final String NORMAL = "0";//正常
        public static final String FREE = "04";//减免税
        public static final String CREDITS = "05";//减税
    }

    /**
     * 险种类型 (中华)
     */
    public final class CvrgType {

        public static final String MAIN_RISKS = "0";//主险
        public static final String ADDITIONAL_RISK = "1";//附加险
        public static final String CI = "2";//交强险
    }

    /**
     * 是否内勤
     */
    public final class StaffMark {

        public static final String NO = "0";//否内勤
        public static final String YES = "1";//是内勤
    }

    /**
     * 是否业务员
     */
    public final class SalesmanMark {

        public static final String NO = "0";//否业务员
        public static final String YES = "1";//是业务员
    }

    /**
     * 系统默认审核人
     */
    public final class DefaultReviewer {

        public static final String ID = "100000";//系统ID
        public static final int IDNUMBER = 100000;//系统ID
        public static final String NAME = "系统异常";//系统名称
    }

    /**
     * 错误信息
     */
    public final class IssueErrorMessage {

        public static final String UNKNOWN = "未知错误，建议您联系客服反馈。";
        public static final String REQUEST_THIRDPARTY_ERROR = "访问第三方服务异常";
        public static final String ACTUALVALUE = "获取车辆实际价值失败。";
        public static final String VEHICLENOTFOUND = "未查询到准确车型，请重新选择车型或检查录入的车辆信息；请尽可能选择本公司车型数据。";
        public static final String REINSURANCE = "需转保校验，平台暂不支持，自主打回。";
        public static final String PER_NOT_ALTER = "当前关系人信息【】不允许修改。";
        public static final String NEWCARMARK_FAILD = "判断车辆新旧失败。";
        public static final String UNSUPPORTED_KIND = "不支持自动报价的险种：";
        public static final String VEHICLE_ERROR = "车型选择错误，请确认车型是否与行驶证上描述一致。";
    }

    /**
     * 未上牌默认
     */
    public final class PlateNumberDefault {

        public static final String DEFAULT = "*";//*
    }

    /**
     * 是否外地车标志
     */
    public final class NonlocalCarMark {

        public static final String NO = "0";//非外地车
        public static final String YES = "1";//外地车
    }

    /**
     * 险种信息字典类型
     */
    public final class ViInsuranceInfMapKeyType {

        public static final String SELF = "0";//本公司险种代码
        public static final String TP = "1";//保险公司险种代码
    }

    /**
     * 险种代码
     */
    public final class KindCode {

        public static final String CIBZ = "BZ";//交强险
        public static final String CIBZ_2020 = "200";//交强险
        public static final String CIBT = "BT";//车船税
        public static final String BI_VI_DAMAGE = "A";//机动车损失险
        public static final String BI_TP_LIABILITY = "B";//第三者责任险
        public static final String BI_DRIVER_SEAT_LIABILITY = "D3";//司机座位险
        public static final String BI_PASSENGER_SEAT_LIABILITY = "D4";//乘客座位险
        public static final String BI_ROBBERY_THEFT = "G1";//全车盗抢险
        public static final String BI_ALONE_GLASS_BREAKAGE = "F";//玻璃破碎险
        public static final String BI_SPONTANEOUS_COMBUSTION = "Z";//自燃损失险
        public static final String BI_DESIGNATED_REPAIR_SHOP = "A4";//指定专修厂险
        public static final String BI_ENGINE_WADING = "X1";//发动机涉水险
        public static final String BI_NOT_TP_COULD_FOUND = "A6";//无法找到第三方
        public static final String BI_BODY_SCRATCH = "L";//车身划痕险
        public static final String BI_EQUIPMENT_ADDITIONAL_LOSS = "A04";//新增加设备损失险
        public static final String BI_CAR_KEYS_LOST_DAMAGED = "A02";//机动车钥匙丢失或损坏费用补偿险
        public static final String BI_EXPENSES_INCURRED_DURING_REPAIR_COMPENSATION = "A03";//修理期间费用补偿险
        public static final String BI_ADDL_VI_DAMAGE = "A01";//机动车损失不计免赔险
        public static final String BI_ADDL_TP_LIABILITY = "B01";//第三者责任不计免赔险
        public static final String BI_ADDL_DRIVER_SEAT_LIABILITY = "D301";//司机座位不计免赔险
        public static final String BI_ADDL_PASSENGER_SEAT_LIABILITY = "D401";//乘客座位不计免赔险
        public static final String BI_ADDL_ROBBERY_THEFT = "G101";//全车盗抢不计免赔险
        public static final String BI_ADDL_SPONTANEOUS_COMBUSTION = "Z01";//自燃损失不计免赔险
        public static final String BI_ADDL_ENGINE_WADING = "X101";//发动机涉水不计免赔险
        public static final String BI_ADDL_BODY_SCRATCH = "L01";//车身划痕不计免赔险
        public static final String BI_ADDL_EQUIPMENT_ADDITIONAL_LOSS = "A041";//新增加设备损失险
        public static final String BI_ADDL_TP_LIABILITY_HOLIDAYS_DOUBLE = "B02";//第三者责任保险附加法定节假日限额翻倍险
        public static final String BI_CAR_CARGO_LIABILITY = "D2";//车上货物责任险
        public static final String BI_EMOTIONAL_DAMAGES = "R";//精神损害抚慰金责任险
        public static final String BI_ADDL_CAR_CARGO_LIABILITY = "D201";//车上货物责任不计免赔险
        public static final String BI_ADDL_EMOTIONAL_DAMAGES = "R01";//精神损害不计免赔险

        public static final String BI_PASSENGER_SEAT_LIABILITY_NEW = "K4";// 机动车车上人员责任保险(乘客)
        public static final String BI_DRIVER_SEAT_LIABILITY_NEW = "K3";// 机动车车上人员责任保险(司机)
        public static final String BI_TP_LIABILITY_NEW = "I";// 机动车第三者责任保险
        public static final String BI_VI_DAMAGE_NEW = "H";// 机动车损失保险
        public static final String SPECIAL_VEHICLE_LOSS_EXTENSION_CLAUSE = "E10";//起重、装卸、挖掘车辆损失扩展条款

    }

    /**
     * 指定专修厂险默认费率
     */
    public final class DesignatedRepairShopRateDefault {

        public static final double DOMESTIC = 0.1;//国产
        public static final double IMPORT = 0.15;//进口
    }

    /**
     * 不计免赔险费率
     */
    public final class AddlMarkRateDefault {

        public static final double R0_15 = 0.15;
        public static final double R0_20 = 0.2;
    }

    /**
     * 新车标志间隔月份默认
     */
    public final class NewCarMarkMonthDefault {

        public static final int DEFAULT = 9;//9个月

    }

    /**
     * 附加类型 玻璃险/专修厂险专用
     */
    public final class ValueType {

        public static final String IMPORT = "1";//进口
        public static final String DOMESTIC = "2";//国产
    }

    /**
     * 本渠道续保标志
     */
    public final class ThisRenewal {

        public static final String NO = "0";//非本渠道续保
        public static final String YES = "1";//本渠道续保
    }

    /**
     * 月折旧率
     */
    public final class MHDepreciationRate {

        public static final double K_0_BETWEEN_9 = 0.006;//客车0~9座
    }

    /**
     * 总折旧率
     */
    public final class DepreciationRate {

        public static final double MIN = 0.2;//最低折旧
        public static final double MAX = 0.8;//最高折旧
    }

    /**
     * 减税比例
     */
    public final class TaxCutProportion {

        public static final double NORMAL = 0.0;//正常
        public static final double FREE = 1.0;//全免
        public static final double CREDITS = 0.5;//减免
    }

    /**
     * 关系人性质
     */
    public final class PersonNature {

        public static final String PERSON = "1";//个人
        public static final String OFFICE = "2";//机关
        public static final String ORGANIZATION = "3";//企业/团体
    }

    /**
     * 车辆所属性质
     */
    public final class OwnershipNature {

        public static final String PERSON = "1";//个人
        public static final String OFFICE = "2";//机关/团体
        public static final String ORGANIZATION = "3";//企业
    }

    /**
     * 关系人性别
     */
    public final class PersonSex {

        public static final String MAN = "1";//男
        public static final String WOMAN = "2";//女
    }

    /**
     * 与车辆关系
     */
    public final class CarRelation {

        public static final String OWNER = "1";//机动车所有人
        public static final String USER = "2";//使用人
        public static final String ADMIN = "3";//管理人
    }

    /**
     * 配送方式
     */
    public final class ReceiverType {

        public static final String EXPRESS = "1";//快递配送
        public static final String TAKETHEIR = "2";//门店自提
    }

    /**
     * 发票类型
     */
    public final class InvoiceMark {

        public static final String NO = "0";//无需发票
        public static final String COMMON = "1";//增值税普通发票
        public static final String DEDICATED = "2";//增值税专用发票
        public static final String ELEC = "11";//增值税电子普通发票
    }


    /**
     * 行驶区域
     */
    public final class RunAreaCode {

        public static final String MAINLAND_CHINA = "01";//中国境内（不包含港、澳、台）
        public static final String PROVINCE = "02";//省内
        public static final String BINDING_ITINERARY = "03";//固定路线
        public static final String VENUE = "04";//场内
    }

    /**
     * 保单是否打印
     */
    public final class InsPrintFlag {

        public static final String NO = "0";//未打印
        public static final String YES = "1";//已打印
    }

    /**
     * 商业险最低折扣
     */
    public final class BiMinDiscount {

        public static final double BID3825 = 0.3825;
        public static final double BID4463 = 0.4463;
        public static final double BID5419 = 0.5419;
        public static final double BID6375 = 0.6375;
        public static final double BID7969 = 0.7969;
        public static final double BID81 = 0.81;
        public static final double BID85 = 0.85;
        public static final double BID9563 = 0.9563;
        public static final double BID11156 = 1.1156;
        public static final double BID1275 = 1.275;
    }

    /**
     * 交强险险最低折扣
     */
    public final class CiMinDiscount {

        public static final double CID70 = 0.7;
        public static final double CID80 = 0.8;
        public static final double CID90 = 0.9;
        public static final double CID100 = 1.0;
        public static final double CID110 = 1.1;
        public static final double CID130 = 1.3;
    }

    /**
     * 批单类型
     */
    public final class EndorsementType {
        /* 未批 */
        public static final String DEFAULT = "00";
        /* 商业险加保 */
        public static final String ADD = "01";
        /* 商业险减保 */
        public static final String SUBTRACT = "02";
        /* 商业险退保 */
        public static final String RECALL = "03";
        /* 全单退保 */
        public static final String TYPE04 = "04";
        /* 已批 */
        public static final String TYPE05 = "05";
        /* 全单批改 */
        public static final String TYPE06 = "06";
    }


    /**
     * 冲账类型
     */
    public final class ReverseState {

        public static final String NORUSH = "0";//未冲
        public static final String WASHED = "1";//被冲
        public static final String HEDGE = "2";//对冲
        public static final String HAVERUSH = "3";//已冲
    }


    /**
     * 关键字段
     *
     * @deprecated 即将转移
     */
    public final class NotifyDataNumberType {

        public static final String CARSERIALNUMBER = "car_serial_number";//全流程流水号
        public static final String INSPREMIUMNUMBER = "ins_premium_number";//保费计算识别码
        public static final String POLICYBATCHID = "policy_batch_id";//流程流水号
        public static final String MAINPROPOSALNUMBER = "main_proposal_number";//主投保单号
        public static final String PROPOSALNUMBER = "proposal_number";//投保单号
    }

    /**
     * 询价人备注类型
     */
    public final class InquirerMsgType {

        public static final String NONE = "0";//无
        public static final String APPOINT_ACTUAL_PRICE = "01";//新车购买指定车辆实际价
        public static final String LOAN_CAR = "02";//贷款车特约备注
        public static final String CAR_INFO = "03";//修改车辆信息
        public static final String SPECIAL_TICKET = "04";//专票申请
        public static final String OTHER = "05";//其他备注信息
    }

    /**
     * 车险单证类型
     */
    public final class InsDocType {

        public static final int CI_INS = 1;//交强(保单)
        public static final int CI_SING = 2;//交强(标志)
        public static final int BI_INS = 3;//商业(保单)
        public static final int BI_CARD = 4;//商业(保卡)
        public static final int CI_BILL = 5;//机动车(发票)
        public static final int CI_PD = 6;//交强险(批单)
        public static final int BI_PD = 7;//商业(批单)

    }

    /**
     * 车险附件类型
     */
    public final class InsAttachType {

        public static final String InsureIDCardFront = "01";//被保人身份证正面
        public static final String InsureIDCardBack = "02";//被保人身份证反面
        public static final String RivingLicenseFront = "03";//行驶证正本
        public static final String RivingLicenseBack = "04";//行驶证副本
        public static final String BusinessLicense = "05";//营业执照/组织机构代码证
        public static final String CarLeftFront = "06";//机动车左前
        public static final String CarLeftBack = "07";//机动车左后
        public static final String CarRightFront = "08";//机动车右前
        public static final String CarRightBack = "09";//机动车右后
        public static final String CarFrame = "10";//机动车车架号
        public static final String CarReg = "11";//机动车辆登记证书
        public static final String CarInvoice = "12";//机动车销售统一发票
        public static final String CarQualified = "13";//机动车整车出产合格证
        public static final String DrivingLicence = "14";//驾驶证
        public static final String CarTax = "15";//车船税证明
        public static final String CiInsurance = "16";//交强险电子保单
        public static final String BiInsurance = "17";//商业险电子保单
        public static final String CiInvoice = "18";//交强险电子发票
        public static final String BiInvoice = "19";//商业险电子发票
        public static final String CiLabel = "20";//交强险电子标签
        public static final String CarOwnerFront = "21";//车主身份证正面
        public static final String CarOwnerBack = "22";//车主身份证反面
        public static final String ApplicantFront = "23";//投保人身份证正面
        public static final String ApplicantBack = "24";//投保人身份证反面
        public static final String LastYearInsureInfo = "25";//上年度车险险种信息
        public static final String Other = "99";//其它
    }

    /**
     * 支付二维码刷新状态
     */
    public final class QrFlushType {

        public static final String NOFLUSH = "0";//未刷新
        public static final String REFRESH = "1";//刷新中
        public static final String ALREADY = "2";//已刷新
    }

    /**
     * 支付二维码刷新状态
     */
    public final class CancelType {

        public static final String USERS = "1";//用户自主取消
        public static final String BACK = "2";//后台操作取消
    }

    public final class NewInsuredStatus {

        public static final String PAID_CANCEL = "-4";//取消订单
        public static final String UNDERWRITING_CANCEL = "-3";//核保取消
        public static final String PREMIUMCACULATE_CANCEL = "-2";//报价取消
        public static final String FAILD = "-1";//报价失败
        public static final String WAIT = "0";//报价等待
        public static final String PREMIUMCACULATE = "1";//报价成功
        public static final String UNDERWRITING_AMENDMENTS = "2";//核保打回修改
        public static final String UNDERWRITING_FAILURE = "3";//核保失败
        public static final String UNDERWRITING_WAITING = "4";//等待核保
        public static final String UNDERWRITING_THROUGH = "5";//核保通过
        public static final String WAITING_PAY = "6";//等待支付
        public static final String PAID = "7";//支付成功
        public static final String INSURED_FAILURE = "8";//承保失败
        public static final String INSURED = "9";//已承保
        public static final String RECALL = "10";//已退保
        public static final String PAY_ERROR = "11";//支付失败
        public static final String INS_HELP = "12";//保单自取
        public static final String INS_DIS = "13";//保单配送
        public static final String ORDER_CLOSE = "14";//订单完成
    }

}
