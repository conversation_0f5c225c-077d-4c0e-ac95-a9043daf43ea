package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 使用性质一级枚举
 *
 * <AUTHOR>
 * @since 2023/08/28/12:32
 **/
@Getter
public enum UsingNatureParentEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    K0("K0", "客车"),
    H0("H0", "货车"),
    T1("T1", "特种车一"),
    T2("T2", "特种车二"),
    T3("T3", "特种车三"),
    J0("J0", "家庭自用车"),
    T4("T4", "特种车四"),
    M0("M0", "摩托车");

    private String value;

    private String description;

    private static Map<String, UsingNatureParentEnum> map = Stream.of(UsingNatureParentEnum.values())
            .collect(Collectors.toMap(UsingNatureParentEnum::getValue, Function.identity()));

    UsingNatureParentEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    /**
     * 是否客车
     *
     * @param code
     * @return ture:是 false:否
     */
    public static Boolean isPassenger(String code) {
        UsingNatureParentEnum usingNatureParentEnum = map.get(code);
        return Arrays.asList(K0).contains(usingNatureParentEnum);
    }


    /**
     * 是否货车
     *
     * @param code
     * @return ture:是 false:否
     */
    public static Boolean isTruck(String code) {
        UsingNatureParentEnum usingNatureParentEnum = map.get(code);
        return Arrays.asList(H0).contains(usingNatureParentEnum);
    }

    @Override
    public String toString() {
        return value;
    }
}
