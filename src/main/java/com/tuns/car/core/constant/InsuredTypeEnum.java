package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 我方投保类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum InsuredTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    NEW_INSURANCE("1", "新保"),
    RENEWAL("2", "续保"),
    REINSURANCE("3", "转保"),
    UNKNOWN("0", null),
    ;

    private String value;

    private String description;

    InsuredTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static Map<String, InsuredTypeEnum> map = Stream.of(InsuredTypeEnum.values())
            .collect(Collectors.toMap(InsuredTypeEnum::getValue, Function.identity()));

    /**
     * 根据枚举值获取对应枚举
     *
     * @param value
     * @return
     */
    public static InsuredTypeEnum getByValue(String value) {
        return map.get(value);
    }

    public static String getByDes(String value){
        return map.get(value).getDescription();
    }

    @Override
    public String toString() {
        return value;
    }
}