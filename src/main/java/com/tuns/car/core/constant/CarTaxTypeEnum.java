package com.tuns.car.core.constant;

import cn.hutool.core.convert.ConverterRegistry;
import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.boot.converter.HutoolEnumConverter;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 车船税缴税类型
 */
@Getter
public enum CarTaxTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    FREE("3", "免税"),
    NORMAL("0", "缴税"),
    CREDITS("1", "减税"),
    PAID("2", "完税"),
    REFUSE("4", "拒缴"),
    NONTAXABLE("5", "不征"),
    NULL("Z", "未知");

    private String value;

    private String description;

    CarTaxTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static Map<String, CarTaxTypeEnum> map = Stream.of(CarTaxTypeEnum.values())
            .collect(Collectors.toMap(CarTaxTypeEnum::getValue, Function.identity()));

    public static CarTaxTypeEnum getByValue(String value) {
        return map.get(value);
    }

    static {
        ConverterRegistry.getInstance().putCustom(CarTaxTypeEnum.class, new HutoolEnumConverter<>(CarTaxTypeEnum.class));
    }

    @Override
    public String toString() {
        return value;
    }
}
