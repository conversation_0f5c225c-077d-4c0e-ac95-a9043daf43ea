package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.Getter;

/**
 * 关系人数据类型枚举
 *
 * <AUTHOR>
 * @since 2022/4/29
 */
@Getter
public enum PersonDataTypeEnum implements IEnum<String> {

    /**
     * 车险
     */
    VI("VI");

    PersonDataTypeEnum(String value) {
        this.value = value;
    }

    private String value;

    @Override
    public String toString() {
        return value;
    }
}
