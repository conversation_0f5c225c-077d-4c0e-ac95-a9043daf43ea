package com.tuns.car.core.constant;

import lombok.Getter;

/**
 * 是否不计免赔
 */
@Getter
public enum VIReInsuranceValueSourceTypeEnum {

    /**
     * 用户填入
     */
    USER_INSERT ("1","用户填入"),
    /**
     * 系统默认
     */
    SYSTEM_DEFAULT("2","系统设置"),
    /**
     * 系统解析
     */
    SYSTEM_RESOLVE("3","系统解析"),

    /**
     * 系统解析但无值（未解析到）
     */
    SYSTEM_RESOLVE_WITH_NULL("4","系统解析但无值"),

    /**
     * 用户补填
     */
    USER_REPAIR_INSERT("5","用户补填");

    private String code;
    private String desc;

    VIReInsuranceValueSourceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
