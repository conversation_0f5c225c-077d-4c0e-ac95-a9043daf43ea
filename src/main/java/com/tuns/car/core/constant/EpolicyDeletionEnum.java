package com.tuns.car.core.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-02-05 15:08
 **/
@Getter
public enum EpolicyDeletionEnum {
    EXIST("1", "电子保单存在"),
    DELETION("2", "电子保单缺失"),
    ABSENT("3", "该单该电子保单本就不存在");
    private String value;

    private String description;

    EpolicyDeletionEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
}
