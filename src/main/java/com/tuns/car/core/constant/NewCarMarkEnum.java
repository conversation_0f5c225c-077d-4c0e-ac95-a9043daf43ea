package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 新车标志
 */
@Getter
public enum NewCarMarkEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    OLD("0", "非新车"),
    NEW("1", "新车"),
    ;

    private String value;
    private String description;

    NewCarMarkEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    /**
     * 根据枚举值获取对应枚举
     *
     * @param ownEnum
     * @return
     */
    public static NewCarMarkEnum getByValue(String ownEnum) {
        return Stream.of(NewCarMarkEnum.values())
                .filter(newCarMarkEnum -> newCarMarkEnum.getValue().equals(ownEnum))
                .findFirst()
                .orElse(null);
    }

    @Override
    public String toString() {
        return value;
    }
}
