package com.tuns.car.core.constant;

import lombok.Getter;

/**
 * 投被保人关系枚举
 *
 * <AUTHOR>
 * @since 2022/3/1
 */
@Getter
public enum InsuredRelationTypeEnum {

    /**
     * 本人
     */
    SELF("01", "本人"),
    /**
     * 受益人
     */
    BENE_PER("05", "受益人"),
    /**
     * 被保人
     */
    INSU_PER("06", "被保人"),
    /**
     * 投保人
     */
    APPL_PER("07", "投保人"),
    /**
     * 配偶
     */
    HUBBY("10", "配偶"),
    /**
     * 丈夫
     */
    MATE("11", "丈夫"),
    /**
     * 妻子
     */
    WIFE("12", "妻子"),
    /**
     * 法定
     */
    STATUTORY("14", "法定"),
    /**
     * 儿子
     */
    SON("20", "儿子"),
    /**
     * 女儿子女
     */
    GIRL("30", "女儿子女"),
    /**
     *
     */
    CHILDREN("40", ""),
    /**
     * 兄弟姐妹
     */
    SISTERS("41", "兄弟姐妹"),
    /**
     * 兄弟
     */
    BROTHER("42", "兄弟"),
    /**
     * 妹妹
     */
    SISTER("43", "妹妹"),
    /**
     * 兄妹
     */
    KUANGMEI("44", "兄妹"),
    /**
     * 姐弟
     */
    SIBLINGS("45", "姐弟"),
    /**
     * 叔伯
     */
    UNCLES("46", "叔伯"),
    /**
     * 阿姨
     */
    AUNT("47", "阿姨"),
    /**
     * 父母
     */
    PARENT("50", "父母"),
    /**
     * 父亲
     */
    FATHER("51", "父亲"),
    /**
     * 母亲
     */
    MOTHER("52", "母亲"),
    /**
     * 父子
     */
    FATHER_AND_SON("53", "父子"),
    /**
     * 父女
     */
    FATHER_AND_DAUGHTER("54", "父女"),
    /**
     * 母子
     */
    MOTHER_AND_SON("55", "母子"),
    /**
     * 母女
     */
    MOTHER_AND_DAUGHTER("56", "母女"),
    /**
     * 婆媳
     */
    MOTHER_DAUGHTER("57", "婆媳"),
    /**
     * 继父
     */
    STEPFATHER("58", "继父"),
    /**
     * 祖父母、外祖父母
     */
    GRANDPARENT("60", "祖父母、外祖父母"),
    /**
     * 祖孙、外祖孙
     */
    GRANDPARENT_AND_GRANDCHILD("61", "祖孙、外祖孙"),
    /**
     * 监护人
     */
    GUARDIAN("62", "监护人"),
    /**
     * 被监护人
     */
    PUPILLUS("63", "被监护人"),
    /**
     * 朋友
     */
    FRIEND("64", "朋友"),
    /**
     * 单位
     */
    UNIT("80", "单位"),
    /**
     * 雇佣
     */
    HIRE("81", "雇佣"),
    /**
     * 雇员
     */
    EMPLOYEE("82", "雇员"),
    /**
     * 雇主
     */
    EMPLOYER("83", "雇主"),
    /**
     * 租赁
     */
    LEASE("84", "租赁"),
    /**
     * 未知
     */
    UNKNOWN("98", "未知"),
    /**
     * 其他
     */
    RESTS("99", "其他"),
    /**
     * 抚养
     */
    BRING("100", "抚养"),
    /**
     * 赡养
     */
    SUPPORT("102", "赡养"),
    /**
     * 被保人
     */
    RECOGNIZEE("103", "被保人"),
    /**
     * 投保人
     */
    APPLICANT("104", "投保人"),
    ;

    private String value;

    private String description;

    InsuredRelationTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

}
