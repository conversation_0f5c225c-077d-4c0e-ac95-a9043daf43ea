package com.tuns.car.core.constant.compare;

import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @project: tuns-business
 * @description: 数据比对业务场景
 * @author: Gk
 * @time: 2022-8-25 9:06
 */
@Getter
@AllArgsConstructor
public enum BusinessSceneEnum implements IEnum<String> {
    INSURED("INSURED", "立即投保"),
    PAY("PAY", "立即支付"),
    REFRESH("REFRESH", "刷新状态"),
    ;
    private String value;
    private String desc;

    @Override
    public String toString() {
        return value;
    }
}
