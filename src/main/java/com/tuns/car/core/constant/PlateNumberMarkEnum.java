package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 是否上牌标志
 */
@Getter
public enum PlateNumberMarkEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    NO ( "0","未上牌"),
    YES ( "1","上牌车");

    private String code;
    private String desc;

    PlateNumberMarkEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getValue() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }

    @Override
    public String getSwaggerDesc() {
        return SwaggerDisplayEnum.super.getSwaggerDesc();
    }

    @Override
    public String toString() {
        return code;
    }
}
