package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;

public enum BusiType implements IEnum<String> {
    /**
     * 报价单
     */
    Quotation("1"),
    /**
     * 保单
     */
    policy("2"),
    ;


    public String value;

    @Override
    public String getValue() {
        return value;
    }

    BusiType(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value;
    }
}
