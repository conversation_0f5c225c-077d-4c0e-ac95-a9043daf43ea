package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 车损险限制枚举
 *
 * <AUTHOR>
 * @date 2023/02/24
 **/
@Getter
public enum DamageInsLimitEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    TRANS_CAR("0", "支持过户车"),

    NEW_CAR("1", "支持新车"),

    NOT_LIMIT("2", "不限");

    private String value;

    private String description;

    private static Map<String, DamageInsLimitEnum> map = Arrays.asList(DamageInsLimitEnum.values())
            .stream()
            .collect(Collectors.toMap(DamageInsLimitEnum::getValue, Function.identity()));

    DamageInsLimitEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static DamageInsLimitEnum getByValue(String value) {
        return map.get(value);
    }

    @Override
    public String toString() {
        return value;
    }
}
