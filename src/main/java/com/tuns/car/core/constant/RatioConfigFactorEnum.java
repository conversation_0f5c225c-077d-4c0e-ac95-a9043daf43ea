package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 保险公司系数配置 调整因子枚举
 * 因为可能存在每个保司对因子的命名不一样，所以每个保司单独配置
 *
 * <AUTHOR>
 * @date 2023/04/18
 **/
@Getter
public enum RatioConfigFactorEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    TOTAL_RATIO("totalRatio", "total_ratio", "整单预期赔付率", InsEnum.DJBX, true),

    TOTAL_RATIO2("totalRatio2", "total_ratio2", "签单折扣预期赔付率2", InsEnum.XDCX, true),

    DBIC_TOTAL_RATIO("totalRatio", "total_ratio", "签单折扣预期赔付率", InsEnum.DBIC, true),

    DBIC_TOTAL_RATIO2("totalRatio2", "total_ratio2", "签单折扣下整单两率和", InsEnum.DBIC, true),

    ZKIC_INDEPENDENT_PRICE_RATE("independentPriceRate", "independent_price_rate", "自主定价系数", InsEnum.ZKIC, true),

    ZKIC_TOTAL_SCORE("totalRecordScore", "Total_score", "交商合计太保分", InsEnum.ZKIC, false),

    CAPLI_SELF_SCORE("selfScore", "self_score", "自主评分", InsEnum.CAPLI, false),

    ZCBX_VEHICLE_RATING("vehicleRating", "vehicle_rating", "众诚分", InsEnum.ZCBX, false),

    PAIC_VEHICLE_RATING("vehicleRating", "vehicle_rating", "该车辆的评分", InsEnum.PAIC, false),

    TPIC_VEHICLE_RATING("vehicleRating", "vehicle_rating", "太平分", InsEnum.TPIC, false),

    CICP_COMM_ENJOY_SCORE("commEnjoyScore", "Comm_Enjoy_Score", "商业险客户评分", InsEnum.CICP, false),

    PICC_COMM_SCORE("recordScoreBi", "Comm_score", "商业险人保分", InsEnum.PICC, false),

    CPIC_TOTAL_SCORE("totalRecordScore", "Total_score", "交商合计太保分", InsEnum.CPIC, false),

    CPIC_TOTAL_RATIO("totalRatio", "total_ratio", "交商合计预期赔付率", InsEnum.CPIC, true),
    ;

    private String value;
    private String itemCode;
    private String description;
    private InsEnum insEnum;
    private Boolean isAutoAdjust;

    RatioConfigFactorEnum(String value, String itemCode, String description, InsEnum insEnum, Boolean isAutoAdjust) {
        this.value = value;
        this.itemCode = itemCode;
        this.description = description;
        this.insEnum = insEnum;
        this.isAutoAdjust = isAutoAdjust;
    }

    public static RatioConfigFactorEnum getByValue(String companyId,String value) {
        return Stream.of(RatioConfigFactorEnum.values())
                .filter(recommendRatioTypeEnum -> (recommendRatioTypeEnum.getValue().equals(value) && recommendRatioTypeEnum.getInsEnum().equals(InsEnum.getByCompanyId(companyId))))
                .findFirst()
                .orElse(null);
    }

    public static RatioConfigFactorEnum getByItemCode(String companyId,String itemCode) {
        return Stream.of(RatioConfigFactorEnum.values())
                .filter(recommendRatioTypeEnum -> (recommendRatioTypeEnum.getItemCode().equals(itemCode) && recommendRatioTypeEnum.getInsEnum().equals(InsEnum.getByCompanyId(companyId))))
                .findFirst()
                .orElse(null);
    }

    private static Map<InsEnum, List<RatioConfigFactorEnum>> all = Arrays.stream(RatioConfigFactorEnum.values())
            .collect(Collectors.groupingBy(RatioConfigFactorEnum::getInsEnum));


    public static List<RatioConfigFactorEnum> getByInsEnumAutoAdjust(InsEnum insEnum) {
        return  Optional.ofNullable(all.get(insEnum)).orElse(new ArrayList<>()).stream().filter(r->r.getIsAutoAdjust()).collect(Collectors.toList());
    }

    @Override
    public String toString() {
        return value;
    }
}
