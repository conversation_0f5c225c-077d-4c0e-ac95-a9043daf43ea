package com.tuns.car.core.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/3 16:23
 */
@Getter
@AllArgsConstructor
public enum WalletStatusEnum {
    FREEZING("1","冻结中"),
    RAWABLE("2","可提现"),
    APPLIED("3","已申请"),
    CASH_SUCCESS("4","提现成功"),
    CASH_FAIL("5","提现失败"),
    CASH_BACK("6","提现退回"),
    RUSH_ACCOUNTS("7","冲账"),
    REVOKED("8","已撤销佣金");
    private String value;
    private String desc;
}
