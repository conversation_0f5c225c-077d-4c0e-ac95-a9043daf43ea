package com.tuns.car.core.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 缓存块名称定义
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/02 9:13
 */
@Getter
public enum OrderTypeEnum {
    /**
     * VI:车险
     */
    VI("VI", "车险"),
    /**
     * GI:非车险
     */
    GI("GI", "非车险"),
    /**
     * LI:寿险
     */
    LI("LI", "寿险"),
    /**
     * GR:非车险
     */
    GR("GR", "团险");

    private String code;

    private String desc;

    OrderTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getEnumDesc(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (OrderTypeEnum enumObj : OrderTypeEnum.values()) {
                if (enumObj.code.equals(code)) {
                    return enumObj.desc;
                }
            }
        }
        return null;
    }

}
