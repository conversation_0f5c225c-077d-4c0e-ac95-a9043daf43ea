package com.tuns.car.core.constant;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public enum RiskTypeEnum {
    CI("1", "交强险"),
    VI("2", "商业险"),
    CI_AND_VI("3", "交商同保");

    private String index;

    private String value;

    public String getIndex() {
        return index;
    }

    public RiskTypeEnum setIndex(String index) {
        this.index = index;
        return this;
    }

    public String getValue() {
        return value;
    }

    public RiskTypeEnum setValue(String value) {
        this.value = value;
        return this;
    }

    RiskTypeEnum(String index, String value) {
        this.index = index;
        this.value = value;
    }

    private static Map<String, RiskTypeEnum> all = Arrays.stream(RiskTypeEnum.values())
            .collect(Collectors.toMap(RiskTypeEnum::getIndex, o -> o));

    public static RiskTypeEnum getByIndex(String index) {
        RiskTypeEnum riskTypeEnum = all.get(index);
        return riskTypeEnum;
    }

    public static String getValue(String index) {
        RiskTypeEnum riskTypeEnum = all.get(index);
        return Objects.nonNull(riskTypeEnum) ? riskTypeEnum.getValue() : null;
    }
}
