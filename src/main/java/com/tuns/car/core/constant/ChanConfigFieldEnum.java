package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.car.core.ChanConfigTypeEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/26
 **/
@Getter
public enum ChanConfigFieldEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    FIRST_LEVEL_OPTION("firstLevelOption", "一级选项"),

    SECOND_LEVEL_OPTION("secondLevelOption", "默认二级选项"),

    ENABLE("enable", "启用状态"),

    FIRST_LEVEL_OPTION_BI("firstLevelOptionBi", "一级选项"),

    SECOND_LEVEL_OPTION_BI("secondLevelOptionBi", "默认二级选项"),

    ENABLE_BI("enableBi", "启用状态"),

    FIRST_LEVEL_OPTION_CI("firstLevelOptionCi", "一级选项"),

    SECOND_LEVEL_OPTION_CI("secondLevelOptionCi", "默认二级选项"),

    ENABLE_CI("enableCi", "启用状态"),

    NOCAR_SUPPORT_GRAB("noCarSupportGrab", "未配置的非车支持抓单", ChanConfigTypeEnum.NOCAR_DEFAULT, true),

    DEFAULT_NOCAR_PRODUCT("defaultNoCarProduct", "抓单后展示默认非车产品", ChanConfigTypeEnum.NOCAR_DEFAULT),

    MODIFY_DAMAGE_INS_SUPPORT("modifyDamageInsSupport", "是否支持修改车损险金额", ChanConfigTypeEnum.MODIFY_DAMAGE_INS, true),

    MODIFY_DAMAGE_INS_LIMIT("modifyDamageInsLimit", "修改车损险限制", ChanConfigTypeEnum.MODIFY_DAMAGE_INS),

    MODIFY_DAMAGE_INS_RANGE_LOWER("modifyDamageInsRangeLower", "车损险金额浮动范围下限", ChanConfigTypeEnum.MODIFY_DAMAGE_INS),

    MODIFY_DAMAGE_INS_RANGE_UPPER("modifyDamageInsRangeUpper", "车损险金额浮动范围上限", ChanConfigTypeEnum.MODIFY_DAMAGE_INS),

    SUPPORT_CUSTOM_INVOICE("supportCustomInvoice","是否支持开票"),

    SUPPORT_SEPARATE_TRANSFER("supportSeparateTransfer","是否支持交商分别过户"),

    PAY_TYPE("payType","渠道支付方式",ChanConfigTypeEnum.PAY_TYPE),

    CORPORATE_TRANS("corporateTrans","对公转账信息",ChanConfigTypeEnum.PAY_TYPE),

    NOCAR_BUY_IS_REMIND("noCarBuyIsRemind", "是否提示购买非车"),

    NOCAR_BUY_COUNT_LIMIT("noCarBuyCountLimit", "非车险投保数量配置"),
    REPAIR_CODE("repairCode", "送修代码"),

    ;

    /**
     * 枚举值
     */
    private String value;

    /**
     * 枚举描述
     */
    private String description;

    /**
     * 分类枚举
     */
    private ChanConfigTypeEnum type;


    /**
     * 父配置，当父配置配置true时，同组其他配置也必须传值
     */
    private Boolean parent = Boolean.FALSE;

    ChanConfigFieldEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    ChanConfigFieldEnum(String value, String description, ChanConfigTypeEnum type) {
        this.value = value;
        this.description = description;
        this.type = type;
    }

    ChanConfigFieldEnum(String value, String description, ChanConfigTypeEnum type, Boolean parent) {
        this.value = value;
        this.description = description;
        this.type = type;
        this.parent = parent;
    }

    private static Map<ChanConfigTypeEnum, List<ChanConfigFieldEnum>> groupByType = Arrays.asList(ChanConfigFieldEnum.values())
            .stream()
            .filter(chanConfigFieldEnum -> Objects.nonNull(chanConfigFieldEnum.getType()))
            .collect(Collectors.groupingBy(ChanConfigFieldEnum::getType));

    /**
     * 将枚举项根据type分组
     *
     * @return 分组后的map
     */
    public static Map<ChanConfigTypeEnum, List<ChanConfigFieldEnum>> groupByType() {
        return groupByType;
    }

    @Override
    public String toString() {
        return value;
    }

}
