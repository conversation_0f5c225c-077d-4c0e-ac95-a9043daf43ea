package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 是否新保单
 */
@Getter
public enum PolicyNewMarkEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    /**
     * 旧单
     */
    OLD("0", "批单"),

    /**
     * 新单
     */
    NEW("1", "保单"),
    ;


    private String value;
    private String description;

    PolicyNewMarkEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
