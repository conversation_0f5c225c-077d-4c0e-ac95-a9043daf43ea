package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 险种映射类型
 *
 * <AUTHOR>
 * @since 2022/10/9
 */
@Getter
public enum InsuranceMappingTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    FAMILY("0", "家用车"),

    NEW_ENERGY("1", "新能源"),

    SPECIAL("2", "特种车");

    InsuranceMappingTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    /**
     * 枚举值
     */
    private String value;

    /**
     * 描述
     */
    private String description;

    @Override
    public String toString() {
        return value;
    }
}
