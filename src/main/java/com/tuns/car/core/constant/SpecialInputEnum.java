package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-11-06 10:34
 **/
@AllArgsConstructor
@Getter
public enum SpecialInputEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    SPECIAL_INPUT_1("1", "核定载客修改"),
    SPECIAL_INPUT_10("10", "交商更换代理渠道"),


    ;

    /**
     * 我方数据库枚举值
     */
    private String value;

    /**
     * 描述
     */
    private String description;
    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    private static final Map<String, SpecialInputEnum> ENUM_MAP = Arrays.stream(SpecialInputEnum.values()).collect(Collectors.toMap(SpecialInputEnum::getValue, Function.identity()));

    public static SpecialInputEnum getEnum(String value) {
        return ENUM_MAP.get(value);
    }

    @Override
    public String toString() {
        return value;
    }
}
