package com.tuns.car.core.constant;


import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/15 9:55
 */
public enum ServiceTermsEnum {
    E3("E3", "道路救援服务特约条款"),
    E4("E4", "车辆安全检测特约条款"),
    E5("E5", "代为驾驶服务特约条款"),
    E6("E6", "代为送检服务特约条款");


    private String kindCode;

    private String kindName;

    ServiceTermsEnum(String kindCode, String kindName) {
        this.kindCode = kindCode;
        this.kindName = kindName;
    }

    public static String getKindName(String kindCode) {
        if (StringUtils.isNotEmpty(kindCode)) {
            for (ServiceTermsEnum value : ServiceTermsEnum.values()) {
                if (kindCode.equals(value.kindCode)) {
                    return value.kindName;
                }
            }
        }
        return null;
    }

    public  String getKindCode() {
        return this.kindCode;
    }
}
