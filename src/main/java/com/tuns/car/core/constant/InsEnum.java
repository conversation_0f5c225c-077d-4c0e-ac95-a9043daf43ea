package com.tuns.car.core.constant;

import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

public enum InsEnum {

    /**
     * 太平洋
     */
    CPIC("太平洋保险", "1011", "CPIC"),

    /**
     * 人寿财险
     */
    GPIC("人寿财险", "1009", "GPIC"),
    /**
     * 中华
     */
    CICP("中华联合", "1008", "CICP"),
    /**
     * 大家
     */
    DJBX("大家保险", "1028", "DJBX"),
    /**
     * 申能
     */
    TAIC("申能财险", "1001", "TAIC"),

    /**
     * 平安
     */
    PAIC("平安保险", "1003", "PAIC"),

    /**
     * 长安
     */
    CAPLI("长安保险", "1007", "CAIC"),

    /**
     * 国任
     */
    XDCX("国任保险", "1004", "XDCX"),

    /**
     * 诚泰
     */
    CHAC("诚泰财险", "1006", "CHAC"),

    /**
     * 人保
     */
    PICC("人保保险", "1026", "PICC"),

    /**
     * 华安
     */
    HAIC("华安保险", "1002", "HAIC"),

    /**
     * 紫金
     */
    ZKIC("紫金财险", "1030", "ZKIC"),
    /**
     * 亚太财险
     */
    APAC("亚太财险", "1005", "APAC"),

    /**
     * 众诚保险
     */
    ZCBX("众诚保险", "1021", "ZCBX"),
    /**
     * 太平财险
     */
    TPIC("太平财险", "1027", "TPIC"),
    /**
     * 都邦保险
     */
    DBIC("都邦财险", "1024", "DBIC"),

    /**
     * 渤海财险
     */
    BPIC("渤海财险", "1023", "BPIC"),

    /**
     * 阳光财险
     */
    YGBX("阳光财险", "1015", "YGBX"),

    /**
     * 华泰财险
     */
    HTIC("华泰财险", "1025", "HTIC"),

    /**
     * 众安财险
     */
    ZAIC("众安财险", "1020", "ZAIC");


    private String companyName;

    private String companyId;

    private String companyCode;

    InsEnum(String companyName, String companyId) {
        this.companyName = companyName;
        this.companyId = companyId;
    }

    InsEnum(String companyName, String companyId, String companyCode) {
        this.companyName = companyName;
        this.companyId = companyId;
        this.companyCode = companyCode;
    }

    public static InsEnum getByCompanyId(String companyId) {
        return Stream.of(InsEnum.values())
                .filter(insEnum -> insEnum.getCompanyId().equals(companyId))
                .findFirst()
                .orElse(null);
    }

    public static InsEnum getByCompanyCode(String companyCode) {
        return Stream.of(InsEnum.values())
                .filter(insEnum -> insEnum.getCompanyCode().equals(companyCode))
                .findFirst()
                .orElse(null);
    }

    /**
     * 核心出单所需
     */
    private static final List<InsEnum> CORE_ORDER = Arrays.asList(XDCX, TAIC, DJBX, CPIC, CICP, PICC, HAIC, ZKIC, CAPLI, APAC, TPIC, ZAIC);

    public static boolean coreOrderContain(InsEnum insEnum) {
        return CORE_ORDER.contains(insEnum);
    }

    public String getCompanyName() {
        return companyName;
    }

    public String getCompanyId() {
        return companyId;
    }

    public String getCompanyCode() {
        if (StrUtil.isBlank(companyCode)) {
            return this.name();
        }
        return companyCode;
    }
}
