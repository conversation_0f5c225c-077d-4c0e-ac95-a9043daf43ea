package com.tuns.car.core.constant.report;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/03/27
 **/
@Getter
public enum FailLogProcessStatusEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    WAIT("0","待处理"),

    PROCESSED("1","已处理"),

    NO_NEED("2","无需处理");

    private String value;
    private String description;

    FailLogProcessStatusEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
