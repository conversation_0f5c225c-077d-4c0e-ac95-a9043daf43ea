package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;

/**
 * 业务阶段枚举
 */
public enum BusinessStageEnum implements IEnum<String> {
    /**
     * 报价单
     */
    QUOTATION("1"),
    /**
     * 订单
     */
    ORDER("3"),
    /**
     * 保单
     */
    POLICY("5"),
    ;


    public String value;

    @Override
    public String getValue() {
        return value;
    }

    BusinessStageEnum(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value;
    }
}
