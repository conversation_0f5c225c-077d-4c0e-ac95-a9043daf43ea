package com.tuns.car.core.constant;


import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class CarCoreRedisLockEnum {


    public static final String INSURANCE_OUT_TIME_KEY = CarCoreCtt.getRedisKey(":insurance:out:time");

    /**
     * redis key
     */
    private final String lockName;
    /**
     * 加锁等待时间；-1 不等待；单位秒
     */
    private final long waitTime;
    /**
     * redis失效时间；单位秒
     */
    private final long expireSeconds;

    public String getRedisKey(String key) {
        return StrUtil.format(this.getLockName(), key);
    }
}
