package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023-5-10 14:29
 */
@Getter
public enum PageTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    /**
     * 非标页面
     */
    NON_STANDARD("1", "非标页面"),
    /**
     * 核保确认页面
     */
    UNDERWRITING_CONFIRMATION("2", "核保确认页面"),
    ;
    /**
     * 数据库枚举值
     */
    private final String value;

    /**
     * 描述
     */
    private final String description;

    PageTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
