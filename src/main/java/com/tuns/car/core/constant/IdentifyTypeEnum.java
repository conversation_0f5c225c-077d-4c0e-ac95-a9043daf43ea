package com.tuns.car.core.constant;

import cn.hutool.core.convert.ConverterRegistry;
import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.boot.converter.HutoolEnumConverter;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 证件类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum IdentifyTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    /**
     * 身份证
     */
    ID_CARD("01", "身份证"),
    /**
     * 户口本
     */
    BOOK_REGISTERED("02", "户口本"),
    /**
     * 出生证
     */
    BIRTHCERTIFICATE("03", "出生证"),
    /**
     * 出生日期(新生婴儿)
     */
    BIRTHDATE("04", "出生日期(新生婴儿)"),
    /**
     * 护照
     */
    PASSPORT("05", "护照"),
    /**
     * 军官证
     */
    OFFICERS("06", "军官证"),
    /**
     * 驾驶执照
     */
    DRIVER_LICENSE("07", "驾驶执照"),
    /**
     * 港澳回乡证
     */
    REENTRY_PERMIT("08", "港澳回乡证"),
    /**
     * 组织机构代码
     */
    ORGANIZATION_CODE("09", "组织机构代码"),
    /**
     * 士兵证
     */
    SOLDIERS("10", "士兵证"),
    /**
     * 临时身份证
     */
    TEMPORARY_ID_CARD("11", "临时身份证"),
    /**
     * 警官证
     */
    POLICE_ID_CARD("12", "警官证"),
    /**
     * 学生证
     */
    STUDENT_ID_CARD("13", " 学生证"),
    /**
     * 军官离退休证
     */
    OFFICER_RETIREMENT_CERTIFICATE("14", "军官离退休证"),
    /**
     * 港澳通行证
     */
    HONGKONG_MACAO_PERMIT("15", "港澳通行证"),
    /**
     * 台湾通行证
     */
    TAIWAN_PASS("16", "台湾通行证"),
    /**
     * 旅行证
     */
    TRAVEL_PERMIT("17", "旅行证"),
    /**
     * 外国人永久居留身份证
     */
    ALIENS_PERMANENT_RESIDENT_ID_CARDS("18", "外国人永久居留身份证"),
    /**
     * 统一社会信用代码
     */
    UNIFIED_SOCIAL_CREDIT_CODE("19", "统一社会信用代码"),
    /**
     * 外国护照
     */
    FOREIGN_PASSPORT("20", "外国护照"),
    /**
     * 税务登记证
     */
    TAX_REGISTRATIONI("21", "税务登记证"),
    /**
     * 营业执照
     */
    BUSINESS_LICENSE("22", "营业执照"),

    /**
     * 港澳台居民居住证
     */
    HONGKONG_MACAO_LIVE("23", "港澳台居民居住证"),

    /**
     * 其他
     */
    OTHER("99", "其他");

    private String value;

    private String description;

    private static final Map<String, IdentifyTypeEnum> IDENTIFY_TYPE_MAP = Stream.of(IdentifyTypeEnum.values())
            .collect(Collectors.toMap(IdentifyTypeEnum::getValue, Function.identity()));

    public static IdentifyTypeEnum getByCode(String code) {
        return IDENTIFY_TYPE_MAP.get(code);
    }

    static {
        ConverterRegistry.getInstance().putCustom(IdentifyTypeEnum.class, new HutoolEnumConverter<>(IdentifyTypeEnum.class));
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
