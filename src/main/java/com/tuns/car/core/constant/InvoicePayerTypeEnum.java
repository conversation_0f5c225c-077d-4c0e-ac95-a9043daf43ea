package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 关系人信息
 */
@Getter
public enum InvoicePayerTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    PERSON("1","个人"),

    GENERAL("2","一般纳税人"),

    SMALL_SCALE("3","小规模纳税人"),

    NO_ADD("4","非增值税纳税人"),

    ;



    private String value;

    private String description;

    InvoicePayerTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static InvoicePayerTypeEnum getByValue(String value) {
        if (StringUtils.isNotEmpty(value)) {
            for (InvoicePayerTypeEnum payerTypeEnum : InvoicePayerTypeEnum.values()) {
                if (payerTypeEnum.getValue().equals(value)) {
                    return payerTypeEnum;
                }
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.value;
    }
}
