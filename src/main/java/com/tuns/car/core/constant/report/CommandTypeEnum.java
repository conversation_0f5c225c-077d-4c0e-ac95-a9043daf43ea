package com.tuns.car.core.constant.report;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据同步类型
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Getter
@AllArgsConstructor
public enum CommandTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    UPDATE("UPDATE", "更新"),
    DELETE("DELETE", "删除");
    private String value;
    private String description;

    @Override
    public String toString() {
        return value;
    }
}
