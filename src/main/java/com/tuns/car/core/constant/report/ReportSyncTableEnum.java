package com.tuns.car.core.constant.report;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报表数据同步table
 *
 * <AUTHOR>
 * @since 2022-11-17
 */
@Getter
@AllArgsConstructor
public enum ReportSyncTableEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    /**
     * 保单
     */
    POLICY("POLICY", "保单"),
    /**
     * 报价单
     */
    POLICY_TP("POLICY_TP", "报价单");

    private String value;
    private String description;

    @Override
    public String toString() {
        return value;
    }
}
