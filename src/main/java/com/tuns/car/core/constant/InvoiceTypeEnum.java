package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 布尔值常用枚举
 *
 * <AUTHOR>
 * @since 2022/3/18
 */
@Getter
public enum InvoiceTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    /**
     * 不开票
     */
    NO_INCOICE("0", "不开票"),
    /**
     * 电子发票
     */
    ELECTRONIC("1", "电子发票"),

    /**
     * 专票
     */
    SPECIAL("2", "专票");

    private String value;

    private String description;

    InvoiceTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
