package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ChargingUseYearsEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    /**
     * 两年以内
     */
    TWO_YEARS_AND_LESS("01", "两年以内"),
    /**
     * 2-5（含）年
     */
    TWOORFIVE_INCLUSIVE_YEARS("02", "2-5（含）年"),
    /**
     * 5年以上
     */
    MORE_THAN_FIVE_YEARS("03", "5年以上");

    private String value;

    private String description;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    ChargingUseYearsEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static Map<String, ChargingUseYearsEnum> CHARGING_USE_YEARS_MAP = Stream.of(ChargingUseYearsEnum.values()).collect(Collectors.toMap(ChargingUseYearsEnum::getValue, Function.identity()));

    public static ChargingUseYearsEnum getByValue(String value) {
        return CHARGING_USE_YEARS_MAP.get(value);
    }

    @Override
    public String toString() {
        return getValue();
    }
}
