package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-11-22 9:15
 **/
@AllArgsConstructor
@Getter
public enum TractorTypeEnum implements SwaggerDisplayEnum<String>, IEnum<String> {


    ORDINARY_TRAILER_TRACTOR("1", "普通挂车牵引车"),
    DANGEROUS_GOODS_TRAILER_TRACTOR("2", "危货挂车牵引车"),
    BULK_MATERIAL_TRAILER_TRACTOR("3", "粉粒物料挂车牵引车"),
    ;

    private String value;

    private String description;

    @Override
    public String toString() {
        return value;
    }

    private static final Map<String,TractorTypeEnum> map = Arrays.stream(TractorTypeEnum.values()).collect(Collectors.toMap(TractorTypeEnum::getValue, Function.identity()));

    public static TractorTypeEnum getTractorTypeEnum(String value) {
        return map.get(value);
    }
}
