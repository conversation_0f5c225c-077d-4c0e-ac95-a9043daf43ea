package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 车辆来历凭证类型
 *
 * <AUTHOR>
 * @since 2024-9-2 16:24
 */
@Getter
@AllArgsConstructor
public enum VehicleVhlCertTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    /**
     * 销售发票
     */
    SALES_INVOICE("01", "销售发票"),
    /**
     * 法院调解书
     */
    COURT_MEDIATION("02", "法院调解书"),
    /**
     * 法院裁定书
     */
    COURT_RULING("03", "法院裁定书"),
    /**
     * 法院判决书
     */
    COURT_JUDGMENT("04", "法院判决书"),
    /**
     * 仲裁裁决书
     */
    THE_ARBITRATION_AWARD("05", "仲裁裁决书"),
    /**
     * 相关文书（继承、赠予、协议抵债）
     */
    RELEVANT_DOCUMENT("06", "相关文书（继承、赠予、协议抵债）"),
    /**
     * 批准文件
     */
    APPROVAL_DOCUMENT("07", "批准文件"),
    /**
     * 调拨证明
     */
    TRANSFER_CERTIFICATE("08", "调拨证明"),
    /**
     * 修理发票
     */
    REPAIR_INVOICE("09", "修理发票"),
    ;
    private String value;
    private String description;
}
