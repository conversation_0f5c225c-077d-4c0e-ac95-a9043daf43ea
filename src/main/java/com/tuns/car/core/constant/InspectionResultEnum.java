package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024-11-11 16:37
 */
@Getter
public enum InspectionResultEnum implements IEnum<String>, SwaggerDisplayEnum<String> {


    A("1", "全车完好无损，证车相符，年审合格"),
    B("3", "车辆带伤投保，已上传损伤部位验车照"),
    C("2", "验车不合格"),
    OTHER("9", "其他");

    private String value;

    private String description;

    InspectionResultEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static Map<String, InspectionResultEnum> map = Stream.of(InspectionResultEnum.values())
            .collect(Collectors.toMap(InspectionResultEnum::getValue, Function.identity()));

    public static InspectionResultEnum getValue(String value) {
        return map.get(value);
    }

}
