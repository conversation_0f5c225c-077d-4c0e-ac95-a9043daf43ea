package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 延时任务日志表业务类型枚举
 *
 * <AUTHOR>
 * @date 2022/10/27
 **/
@Getter
public enum DelayTaskLogBusinessTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    REFRESH_ORDER_STATUS("1", "刷新订单状态"),
    DOWNLOAD_ELECTRON_POLICY("2", "下载电子保单");

    private String value;

    private String description;

    DelayTaskLogBusinessTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
