package com.tuns.car.core.constant;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Deprecated
public enum SettingInfoEnum {
    //保险公司相关相关配置
    UNDER_WRITE_COM_CODE("承保机构"),
    CODE_2("代理机构"),
    CODE_3("中介协议"),
    CODE_4("销售渠道"),
    CODE_5("服务代码"),
    CODE_6("业务员"),
    CODE_7("是否实名制"),
    CODE_8("签报号"),
    CODE_9("代理人代码"),
    CODE_10("业务来源"),
    CODE_11("登录机构"),
    CODE_12("验车人"),
    CODE_13("决策单元"),
    CODE_14("验车人代码"),
    CODE_15("决策单元新能源"),
    CODE_16("渠道协议"),
    CODE_17("渠道类型1"),
    CODE_18("渠道类型2"),
    CODE_19("分配方案"),
    CODE_20("终端"),
    ATTRIBUTION_APARTMENT("归属部门"),
    AGENCY_NO("中介名称"),
    SERVICE_CODE("网点服务码"),
    OWNER("归属人"),
    PROJECT_CODE("项目代码"),
    IS_REPAIR("是否送修"),
    REPAIR_CODE("送修代码"),
    RENEWAL_CODE("经代续保"),
    PARTNER_CODE("合作伙伴编码"),
    PARTNER_NAME("合作伙伴名称"),
    PARTNER_1_CODE("合作伙伴编码1"),
    PARTNER_1_NAME("合作伙伴名称1"),
    USER_CASSIFICATION_CODE("服务经理类型"),
    OPERATOR("操作人"),
    SALESMAN("归属业务员"),
    OPERATOR_CODE("经办人代码"),
    STRUCTURES("业务方式"),
    AGREEMENT_NO("合同/协议号"),
    SIGN_REPORD_ID("签报编码"),
    GROUP_NO("团队号"),
    OTHER("其他"),;

    private final String value;// 状态代码

    public String getValue() {
        return value;
    }

    private SettingInfoEnum(String value) {
        this.value = value;
    }

    private static Map<String, SettingInfoEnum> all = Arrays.stream(SettingInfoEnum.values())
            .collect(Collectors.toMap(SettingInfoEnum::getValue, o -> o));


    @Deprecated
    public static SettingInfoEnum getValue(String index) {
        if (Objects.isNull(all.get(index))) {
            return OTHER;
        }
        return all.get(index);
    }

}
