package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 投保状态
 */
@Getter
public enum InsuredStatusEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    PAID_CANCEL("-4", "取消订单", BusinessStageEnum.ORDER),
    UNDERWRITING_CANCEL("-3", "核保取消", BusinessStageEnum.ORDER),
    PREMIUMCACULATE_CANCEL("-2", "报价取消", BusinessStageEnum.QUOTATION),
    FAILD("-1", "报价失败", BusinessStageEnum.QUOTATION),
    WAIT("0", "报价等待", BusinessStageEnum.QUOTATION),
    PREMIUMCACULATE("1", "报价成功", BusinessStageEnum.QUOTATION),
    UNDERWRITING_AMENDMENTS("2", "核保打回修改", BusinessStageEnum.ORDER),
    UNDERWRITING_FAILURE("3", "核保失败", BusinessStageEnum.ORDER),
    UNDERWRITING_WAITING("4", "等待核保", BusinessStageEnum.ORDER),
    UNDERWRITING_THROUGH("5", "核保通过", BusinessStageEnum.ORDER),
    WAITING_PAY("6", "等待支付", BusinessStageEnum.ORDER),
    PAID("7", "支付成功", BusinessStageEnum.ORDER),
    INSURED_FAILURE("8", "承保失败", BusinessStageEnum.ORDER),
    INSURED("9", "已承保", BusinessStageEnum.POLICY),
    RECALL("10", "已退保", BusinessStageEnum.POLICY),
    PAY_ERROR("11", "支付失败", BusinessStageEnum.ORDER),
    INS_HELP("12", "保单自取", BusinessStageEnum.POLICY),
    INS_DIS("13", "保单配送", BusinessStageEnum.POLICY),
    ORDER_CLOSE("14", "订单完成", BusinessStageEnum.POLICY),
    ;

    private String value;
    private String description;
    /**
     * 业务阶段
     */
    private BusinessStageEnum stage;

    InsuredStatusEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    InsuredStatusEnum(String value, String description, BusinessStageEnum busiType) {
        this.value = value;
        this.description = description;
        this.stage = busiType;
    }

    private static Map<String, InsuredStatusEnum> map = Stream.of(InsuredStatusEnum.values())
            .collect(Collectors.toMap(InsuredStatusEnum::getValue, Function.identity()));

    /**
     * 根据我方枚举值获取对应枚举
     *
     * @param value
     * @return
     */
    public static InsuredStatusEnum getByValue(String value) {
        return map.get(value);
    }

    /**
     * 根据我方枚举值获取对应枚举
     *
     * @param value
     * @return
     */
    public static BusinessStageEnum getStageByValue(String value) {
        return Optional.ofNullable(map.get(value))
                .map(InsuredStatusEnum::getStage)
                .orElse(null);
    }

    @Override
    public String toString() {
        return value;
    }
}
