package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 性别枚举
 *
 * <AUTHOR>
 * @since 2022/6/7
 */
@Getter
public enum SupportEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    SUPPORT("1", "支持"),
    NOT_SUPPORT("0", "不支持");

    /**
     * 我方数据库枚举值
     */
    private String value;

    /**
     * 描述
     */
    private String description;

    SupportEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static SupportEnum getByValue(String value) {
        return Stream.of(SupportEnum.values())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
}
