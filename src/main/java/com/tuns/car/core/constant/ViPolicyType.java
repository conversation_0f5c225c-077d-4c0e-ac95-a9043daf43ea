package com.tuns.car.core.constant;

import com.tuns.car.core.constant.ViIssueConstants.KindCode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ViPolicyType
 * @Description: 车险政策类型 数据字典,此字典数据应该和数据库保持一致，需要同步修改
 * <AUTHOR>
 * @date 2019年2月28日
 */
public final class ViPolicyType {
    /** 车辆种类 */
    public static final String VEHICLE_TYPE = "Vehicle_type";
    /** 使用性质 */
    public static final String USING_NATURE = "Using_nature";
    /** 所属性质 */
    public static final String OWNERSHIP_NATURE = "Ownership_nature";
    /** 车牌类型 */
    public static final String CAR_OPTIONS_TYPE = "Car_options_type";
    /** 新旧车标示 */
    public static final String NEW_OLD_TYPE = "New_old_type";
    /** 过户车标示 */
    public static final String TRANSFER_TYPE = "Transfer_type";
    /** 核定座位数 */
    public static final String SEAT_NUMBER = "Seat_number";
    /** 核定载质量 */
    public static final String QUANTITY = "Quantity";
    /** 整备质量 */
    public static final String CURB_WEIGHT = "Curb_weight";
    /** 能源类型 */
    public static final String FUEL_TYPE = "Fuel_type";
    /** 投保类型 */
    public static final String INSURE_TYPE = "Insure_type";
    /** 新转续标识 */
    public static final String NEW_OLD_MARK_POL = "New_old_mark_pol";
    /** 商业险起保日期 */
    public static final String BUSINESS_BEGIN = "Business_begin";
    /** 交强险起保日期 */
    public static final String TRANSPORT_BEGIN = "Transport_begin";
    /** 交强险评分 */
    public static final String TRAFFIC_SCORE = "Traffic_score";
    /** 商业险评分 */
    public static final String COMM_SCORE = "Comm_score";
    /** 交商险合计评分 */
    public static final String TOTAL_SCORE = "Total_score";
    /** 新车购置价 */
    public static final String NEW_CAR_PURCHASE = "New_car_purchase";
    /** 车辆实际价值 */
    public static final String ACTUAL_PRICE = "Actual_price";
    /** 交强险出险次数 */
    public static final String TRAFFIC_APPE_COUNT = "Traffic_appe_count";
    /** 商业险出险次数 */
    public static final String COMM_APPE_COUNT = "Comm_appe_count";
    /** 交商出险总次数 */
    public static final String TOTAL_APPE_COUNT = "Total_appe_count";
    /** 交强险提前起保天数 */
    public static final String TRAFFIC_EARLY_DAY = "Traffic_early_day";
    /** 商业险提前起保天数 */
    public static final String COMM_EARLY_DAY = "Comm_early_day";
    /** 商业险种类 */
    public static final String COMM_TYPE = "Comm_type";
    /** 商业险主险保额 */
    public static final String COMM_MAIN = "Comm_main";
    /** 使用年限 */
    public static final String USING_YEAR = "Using_year";
    /** 商业险保费 */
    public static final String COMM_FEE = "Comm_fee";
    /** 交强险保费 */
    public static final String TRAFFIC_FEE = "Traffic_fee";
    /** 交商合计保费（含车船税） */
    public static final String TOTAL_PREMIUM_WITHTAX = "Total_premium_withTax";
    /** 交商合计保费（不含车船税） */
    public static final String TOTAL_PREMIUM_WITHOUTTAX = "Total_premium_withoutTax";
    /** 商业险首单奖励件数 */
    public static final String COMM_FIRST_AWARD = "Comm_first_award";
    /** 业务来源类型 */
    public static final String BUSINESS_SOURCE = "Business_source";
    /** 贷款车标示 */
    public static final String LOAN_CAR = "Loan_car";
    /** 车船税 */
    public static final String VEHICLE_TAX = "Vehicle_tax";
    /** 投保城市 */
    public static final String POLICY_CITY = "Policy_city";

    /** 附加险条款 */
    public static final String ADDITION_CLAUSE = "addition_clause";
    /** 客户风险评级 */
    public static final String CUSTOMER_RISK_RATING = "customer_risk_rating";
    /** 业务分组 */
    public static final String BUSINESS_GROUP = "business_group";
    /** 保单成本率 */
    public static final String POLICY_COST_RATE = "policy_cost_rate";
    /** 购买驾乘险（个险范畴） */
    public static final String BUY_DRIVERS_KIND = "buy_drivers_kind";
    /** 车辆品牌 */
    public static final String CAR_BRAND = "car_brand";
    /** 私家车车联网分档 */
    public static final String CAR_SPREADING = "car_spreading";
    /** 网约车分级 */
    public static final String WY_CAR_TYPE = "wy_car_type";
    /** 被保人性别 */
    public static final String INSURED_SEX = "insured_sex";
    /** 被保人年龄 */
    public static final String INSURED_AGE = "insured_age";
    /** 整单折扣（NCD*自主定价系数） */
    public static final String ALL_POLICY_RATIO = "all_policy_ratio";
    /** 商业险预期赔付率 */
    public static final String LOSS_RATIO_BI = "loss_ratio_bi";
    /** 交强险预期赔付率 */
    public static final String LOSS_RATIO_CI = "loss_ratio_ci";
    /** 整单预期赔付率 */
    public static final String TOTAL_RATIO = "total_ratio";
    /** NCD系数 */
    public static final String NO_CLAIM_LEVEL = "no_claim_level";
    /** 自主定价系数 */
    public static final String INDEPENDENT_PRICE_RATE = "independent_price_rate";
    /** 交商险合计评分等级 */
    public static final String TOTAL_SCORE_RANK = "total_score_rank";
    /** 商业险评分等级 */
    public static final String COMM_SCORE_RANK = "comm_score_rank";
    /** 交强险评分等级 */
    public static final String TRAFFIC_SCORE_RANK = "traffic_score_rank";
    /** 新的商业险种类 ，只用于查字典 */
    public static final String NEW_COMM_TYPE = "new_comm_type";
    /** 关系人一致性*/
    public static final String RELATION_CONSISTENCE = "relation_consistence";
    

    /**
     * @ClassName: ViPolicyCal
     * @Description: 车险政策计算所需政策条件
     * <AUTHOR>
     * @date 2019年1月3日
     */
    public static class ViPolicyCal {
        // 区间的政策条件
        public final static List<String> rangePolicyList = new ArrayList<String>();
        static {
            rangePolicyList.add(ViPolicyType.SEAT_NUMBER);// 核定座位数
            rangePolicyList.add(ViPolicyType.QUANTITY);// 核定载质量
            rangePolicyList.add(ViPolicyType.CURB_WEIGHT);// 整备质量
            rangePolicyList.add(ViPolicyType.BUSINESS_BEGIN);// 商业险起保日期
            rangePolicyList.add(ViPolicyType.TRANSPORT_BEGIN);// 交强险起保日期
            rangePolicyList.add(ViPolicyType.TRAFFIC_SCORE);// 交强险评分
            rangePolicyList.add(ViPolicyType.COMM_SCORE);// 商业险评分
            rangePolicyList.add(ViPolicyType.TOTAL_SCORE);// 交商险合计评分
            rangePolicyList.add(ViPolicyType.NEW_CAR_PURCHASE);// 新车购置价
            rangePolicyList.add(ViPolicyType.ACTUAL_PRICE);// 车辆实际价值
            rangePolicyList.add(ViPolicyType.TRAFFIC_APPE_COUNT); // 交强险出险次数
            rangePolicyList.add(ViPolicyType.COMM_APPE_COUNT); // 商业险出险次数
            rangePolicyList.add(ViPolicyType.TOTAL_APPE_COUNT); // 交商出险总次数
            rangePolicyList.add(ViPolicyType.TRAFFIC_EARLY_DAY); // 交强险提前起保天数
            rangePolicyList.add(ViPolicyType.COMM_EARLY_DAY); // 商业险提前起保天数
            rangePolicyList.add(ViPolicyType.COMM_MAIN); // 商业险主险保额
            rangePolicyList.add(ViPolicyType.USING_YEAR); // 使用年限
            rangePolicyList.add(ViPolicyType.COMM_FEE); // 商业险保费
            rangePolicyList.add(ViPolicyType.TRAFFIC_FEE); // 交强险保费
            rangePolicyList.add(ViPolicyType.COMM_FIRST_AWARD); // 商业险首单奖励件数
            rangePolicyList.add(ViPolicyType.VEHICLE_TAX); // 车船税
            rangePolicyList.add(ViPolicyType.TOTAL_PREMIUM_WITHTAX); // 交商合计保费（含车船税）
            rangePolicyList.add(ViPolicyType.TOTAL_PREMIUM_WITHOUTTAX); // 交商合计保费（不含车船税）
        }

        // 指定、除外
        public final static List<String> appointPolicyList = new ArrayList<String>();
        static {
            appointPolicyList.add(ViPolicyType.VEHICLE_TYPE);// 车辆种类
            appointPolicyList.add(ViPolicyType.USING_NATURE);// 使用性质
            appointPolicyList.add(ViPolicyType.OWNERSHIP_NATURE);// 所属性质
            appointPolicyList.add(ViPolicyType.CAR_OPTIONS_TYPE);// 车牌类型
            appointPolicyList.add(ViPolicyType.NEW_OLD_TYPE);// 新旧车标识
            appointPolicyList.add(ViPolicyType.TRANSFER_TYPE);// 过户车标识
            appointPolicyList.add(ViPolicyType.FUEL_TYPE);// 能源类型
            appointPolicyList.add(ViPolicyType.INSURE_TYPE);// 投保类型
            appointPolicyList.add(ViPolicyType.NEW_OLD_MARK_POL);// 新转续标识
            appointPolicyList.add(ViPolicyType.COMM_TYPE);// 商业险种类
            appointPolicyList.add(ViPolicyType.BUSINESS_SOURCE);// 业务来源类型
            appointPolicyList.add(ViPolicyType.LOAN_CAR);// 贷款车标识
            appointPolicyList.add(ViPolicyType.POLICY_CITY);// 投保城市

        }

        // 特殊政策
        public final static List<String> specPolicyList = new ArrayList<String>();
        static {
            specPolicyList.add(ViPolicyType.BUSINESS_BEGIN);// 商业险起保日期
            specPolicyList.add(ViPolicyType.TRANSPORT_BEGIN);// 交强险起保日期
            specPolicyList.add(ViPolicyType.TRAFFIC_SCORE);// 交强险评分
            specPolicyList.add(ViPolicyType.COMM_SCORE);// 商业险评分
            specPolicyList.add(ViPolicyType.TRAFFIC_APPE_COUNT); // 交强险出险次数
            specPolicyList.add(ViPolicyType.COMM_APPE_COUNT); // 商业险出险次数
            specPolicyList.add(ViPolicyType.TOTAL_APPE_COUNT); // 交商出险总次数
            specPolicyList.add(ViPolicyType.TRAFFIC_EARLY_DAY); // 交强险提前起保天数
            specPolicyList.add(ViPolicyType.COMM_EARLY_DAY); // 商业险提前起保天数
            specPolicyList.add(ViPolicyType.VEHICLE_TAX); // 车船税
            specPolicyList.add(ViPolicyType.COMM_FIRST_AWARD); // 商业险首单奖励件数
            specPolicyList.add(ViPolicyType.TOTAL_PREMIUM_WITHTAX); // 交商合计保费（含车船税）
            specPolicyList.add(ViPolicyType.TOTAL_PREMIUM_WITHOUTTAX); // 交商合计保费（不含车船税）
            specPolicyList.add(ViPolicyType.COMM_TYPE);// 商业险种类
            specPolicyList.add(ViPolicyType.NEW_COMM_TYPE); // 新 商业险种类

        }

    }

    // 政策条件类型 1指定 2除外 3区间
    public final static Map<String, String> policyTypeMap = new HashMap<>();;
    static {
        policyTypeMap.put(ViPolicyType.VEHICLE_TYPE, "12");
        policyTypeMap.put(ViPolicyType.USING_NATURE, "12");
        policyTypeMap.put(ViPolicyType.OWNERSHIP_NATURE, "12");
        policyTypeMap.put(ViPolicyType.CAR_OPTIONS_TYPE, "12");
        policyTypeMap.put(ViPolicyType.NEW_OLD_TYPE, "12");
        policyTypeMap.put(ViPolicyType.TRANSFER_TYPE, "12");
        policyTypeMap.put(ViPolicyType.SEAT_NUMBER, "3");
        policyTypeMap.put(ViPolicyType.QUANTITY, "3");
        policyTypeMap.put(ViPolicyType.CURB_WEIGHT, "3");
        policyTypeMap.put(ViPolicyType.FUEL_TYPE, "12");
        policyTypeMap.put(ViPolicyType.INSURE_TYPE, "12");
        policyTypeMap.put(ViPolicyType.NEW_OLD_MARK_POL, "12");
        policyTypeMap.put(ViPolicyType.BUSINESS_BEGIN, "3");
        policyTypeMap.put(ViPolicyType.TRANSPORT_BEGIN, "3");
        policyTypeMap.put(ViPolicyType.TRAFFIC_SCORE, "3");
        policyTypeMap.put(ViPolicyType.COMM_SCORE, "3");
        policyTypeMap.put(ViPolicyType.TOTAL_SCORE, "3");
        policyTypeMap.put(ViPolicyType.NEW_CAR_PURCHASE, "3");
        policyTypeMap.put(ViPolicyType.ACTUAL_PRICE, "3");
        policyTypeMap.put(ViPolicyType.TRAFFIC_APPE_COUNT, "3");
        policyTypeMap.put(ViPolicyType.COMM_APPE_COUNT, "3");
        policyTypeMap.put(ViPolicyType.TOTAL_APPE_COUNT, "3");
        policyTypeMap.put(ViPolicyType.TRAFFIC_EARLY_DAY, "3");
        policyTypeMap.put(ViPolicyType.COMM_EARLY_DAY, "3");
        policyTypeMap.put(ViPolicyType.COMM_TYPE, "12");
        policyTypeMap.put(ViPolicyType.USING_YEAR, "3");
        policyTypeMap.put(ViPolicyType.COMM_FEE, "3");
        policyTypeMap.put(ViPolicyType.TRAFFIC_FEE, "3");
        policyTypeMap.put(ViPolicyType.COMM_FIRST_AWARD, "3");
        policyTypeMap.put(ViPolicyType.BUSINESS_SOURCE, "12");
        policyTypeMap.put(ViPolicyType.LOAN_CAR, "12");
        policyTypeMap.put(ViPolicyType.TOTAL_PREMIUM_WITHTAX, "3");
        policyTypeMap.put(ViPolicyType.TOTAL_PREMIUM_WITHOUTTAX, "3");
        policyTypeMap.put(ViPolicyType.VEHICLE_TAX, "3");
        policyTypeMap.put(ViPolicyType.POLICY_CITY, "12");
        policyTypeMap.put(ViPolicyType.COMM_MAIN, "3");

        policyTypeMap.put(ViPolicyType.ADDITION_CLAUSE, "12"); // 附加险条款
        policyTypeMap.put(ViPolicyType.CUSTOMER_RISK_RATING, "3"); // 客户风险评级
        policyTypeMap.put(ViPolicyType.BUSINESS_GROUP, "12"); // 业务分组
        policyTypeMap.put(ViPolicyType.POLICY_COST_RATE, "3"); // 保单成本率
        policyTypeMap.put(ViPolicyType.BUY_DRIVERS_KIND, "12"); // 购买驾乘险（个险范畴）
        policyTypeMap.put(ViPolicyType.CAR_BRAND, "12"); // 车辆品牌
        policyTypeMap.put(ViPolicyType.CAR_SPREADING, "12"); // 私家车车联网分档
        policyTypeMap.put(ViPolicyType.WY_CAR_TYPE, "3"); // 网约车分级
        policyTypeMap.put(ViPolicyType.INSURED_SEX, "12"); // 被保人性别
        policyTypeMap.put(ViPolicyType.INSURED_AGE, "3"); // 被保人年龄
        policyTypeMap.put(ViPolicyType.ALL_POLICY_RATIO, "3"); // 整单折扣（NCD*自主定价系数）
        policyTypeMap.put(ViPolicyType.LOSS_RATIO_BI, "3"); // 商业险预期赔付率
        policyTypeMap.put(ViPolicyType.LOSS_RATIO_CI, "3"); // 交强险预期赔付率
        policyTypeMap.put(ViPolicyType.TOTAL_RATIO, "3"); // 整单预期赔付率
        policyTypeMap.put(ViPolicyType.NO_CLAIM_LEVEL, "3"); // NCD系数
        policyTypeMap.put(ViPolicyType.INDEPENDENT_PRICE_RATE, "3"); // 自主定价系数
        policyTypeMap.put(ViPolicyType.TOTAL_SCORE_RANK, "12"); // 交商险合计评分等级
        policyTypeMap.put(ViPolicyType.COMM_SCORE_RANK, "12"); // 商业险评分等级
        policyTypeMap.put(ViPolicyType.TRAFFIC_SCORE_RANK, "12"); // 交强险评分等级

        policyTypeMap.put(ViPolicyType.NEW_COMM_TYPE, "12");

        // 商业主险保额，需要单独列入
        policyTypeMap.put(KindCode.BI_VI_DAMAGE, "3");// 车损
        policyTypeMap.put(KindCode.BI_TP_LIABILITY, "3");// 三者
        policyTypeMap.put(KindCode.BI_DRIVER_SEAT_LIABILITY, "3");// 机动车车上人员责任保险(司机)
        policyTypeMap.put(KindCode.BI_PASSENGER_SEAT_LIABILITY, "3");// 机动车车上人员责任保险(乘客)
        policyTypeMap.put(KindCode.BI_ROBBERY_THEFT, "3");// 机动车全车盗抢保险
        policyTypeMap.put(KindCode.BI_PASSENGER_SEAT_LIABILITY_NEW, "3"); // 机动车车上人员责任保险(乘客)
        policyTypeMap.put(KindCode.BI_DRIVER_SEAT_LIABILITY_NEW, "3"); // 机动车车上人员责任保险(司机)
        policyTypeMap.put(KindCode.BI_TP_LIABILITY_NEW, "3"); // 机动车第三者责任保险
        policyTypeMap.put(KindCode.BI_VI_DAMAGE_NEW, "3"); // 机动车损失保险

    }
}
