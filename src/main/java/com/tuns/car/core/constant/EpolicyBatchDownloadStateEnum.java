package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName EpolicyBatchDownloadStateEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/5/15 17:32
 * @Version 1.0
 */
@AllArgsConstructor
@Getter
public enum EpolicyBatchDownloadStateEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    PACKAGING("1", "打包中"),

    PACKAGED("2", "打包成功"),

    FAILED("3", "打包失败");

    private String value;

    private String description;

    public static EpolicyBatchDownloadStateEnum getByValue(String value) {
        return Arrays.stream(EpolicyBatchDownloadStateEnum.values()).filter(e->e.getValue().equals(value)).findFirst().orElse(null);
    }

    @Override
    public String toString() {
        return value;
    }
}
