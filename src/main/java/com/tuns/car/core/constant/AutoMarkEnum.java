package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/22 9:57
 */
@Getter
@AllArgsConstructor
public enum AutoMarkEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    MANUAL("0", "人工报价"),
    AUTO("1", "自动报价"),
    MOBILE_INTEGRATED_QUOTATION("3", "移动集成出单"),
    SYSTEM_AUTO("4", "系统自动报价"),
    SYSTEM_INTELLIGENT("5", "系统智能报价");


    private String value;

    private String description;

    public static AutoMarkEnum getByValue(String value) {
        return Stream.of(AutoMarkEnum.values())
                .filter(autoMarkEnum -> autoMarkEnum.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }

    @Override
    public String toString() {
        return value;
    }

}
