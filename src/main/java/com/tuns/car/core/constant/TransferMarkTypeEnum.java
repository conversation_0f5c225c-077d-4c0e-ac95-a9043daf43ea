package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 过户类型
 *
 * <AUTHOR>
 * @since 2023-12-26 16:29
 */
@Getter
@AllArgsConstructor
public enum TransferMarkTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    ONLY_BI("2", "仅商业险过户"),
    ONLY_CI("1", "仅交强险过户"),
    BOTH("3", "交商同时过户");

    private String value;
    private String desc;

    public static TransferMarkTypeEnum matchValue(String recordType) {
        return TRANSFER_MARK_TYPE_ENUM_MAP.get(recordType);
    }

    @Override
    public String toString() {
        return this.value;
    }

    @Override
    public String getDescription() {
        return this.desc;
    }

    private static final Map<String, TransferMarkTypeEnum> TRANSFER_MARK_TYPE_ENUM_MAP = Stream.of(TransferMarkTypeEnum.values()).collect(Collectors.toMap(TransferMarkTypeEnum::getValue, Function.identity()));

    public static final Predicate<TransferMarkTypeEnum> ONLY_CI_PREDICATE = x -> Objects.nonNull(x) && x == ONLY_CI;
    public static final Predicate<TransferMarkTypeEnum> ONLY_BI_PREDICATE = x -> Objects.nonNull(x) && x == ONLY_BI;
    public static final Predicate<TransferMarkTypeEnum> BOTH_PREDICATE = x -> Objects.nonNull(x) && x == BOTH;

    public static boolean tickBiTransferMarkType(String transferMark, TransferMarkTypeEnum transferMarkType) {
        boolean isTransfer = TransferMarkEnum.YES.getValue().equals(transferMark);
        boolean onlyBi = isTransfer && ONLY_BI_PREDICATE.test(transferMarkType);
        boolean both = isTransfer && BOTH_PREDICATE.test(transferMarkType);
        return onlyBi || both;
    }

    public static boolean tickCiTransferMarkType(String transferMark, TransferMarkTypeEnum transferMarkType) {
        boolean isTransfer = TransferMarkEnum.YES.getValue().equals(transferMark);
        boolean onlyCi = isTransfer && ONLY_CI_PREDICATE.test(transferMarkType);
        boolean both = isTransfer && BOTH_PREDICATE.test(transferMarkType);
        return onlyCi || both;
    }
}
