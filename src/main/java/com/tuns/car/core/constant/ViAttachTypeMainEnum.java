package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 影像资料主分类
 *
 * <AUTHOR>
 * @since 2023/12/27
 */
public enum ViAttachTypeMainEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    /**
     * 被保人身份证正面
     */
    INSUREIDCARDFRONT("01", "被保人身份证正面", ImageDataEnum.INSURED),

    /**
     * 被保人身份证反面
     */
    INSUREIDCARDBACK("02", "被保人身份证反面", ImageDataEnum.INSURED),

    /**
     * 行驶证正本
     */
    RIVINGLICENSEFRONT("03", "行驶证正本", ImageDataEnum.VEHICLE_DRIVING),

    /**
     * 行驶证副本
     */
    RIVINGLICENSEBACK("04", "行驶证副本", ImageDataEnum.VEHICLE_DRIVING),

    /**
     * 营业执照/组织机构代码证
     */
    BUSINESSLICENSE("05", "营业执照/组织机构代码证", ImageDataEnum.OTHER),

    /**
     * 车主 营业执照/组织机构代码证
     */
    OWNER_BUSINESSLICENSE("0501", "营业执照/组织机构代码证", ImageDataEnum.OTHER),

    /**
     * 投保人 营业执照/组织机构代码证
     */
    HOLDER_BUSINESSLICENSE("0502", "营业执照/组织机构代码证", ImageDataEnum.OTHER),

    /**
     * 被保人 营业执照/组织机构代码证
     */
    INSURED_BUSINESSLICENSE("0503", "营业执照/组织机构代码证", ImageDataEnum.OTHER),
    /**
     * 车主身份证正面
     */
    CAROWNERFRONT("21", "车主身份证正面", ImageDataEnum.CAR_OWNER_IDENTITY),

    /**
     * 车主身份证反面
     */
    CAROWNERBACK("22", "车主身份证反面", ImageDataEnum.CAR_OWNER_IDENTITY),

    /**
     * 投保人身份证正面
     */
    APPLICANTFRONT("23", "投保人身份证正面", ImageDataEnum.HOLDER),

    /**
     * 投保人身份证反面
     */
    APPLICANTBACK("24", "投保人身份证反面", ImageDataEnum.HOLDER),
    ;


    private String value;

    private String description;

    @Getter
    private ImageDataEnum imageDataEnum;

    ViAttachTypeMainEnum(String value, String description, ImageDataEnum dataEnum) {
        this.value = value;
        this.description = description;
        this.imageDataEnum = dataEnum;
    }

    private static Map<String, ViAttachTypeMainEnum> map = Stream.of(ViAttachTypeMainEnum.values()).collect(Collectors.toMap(ViAttachTypeMainEnum::getValue, Function.identity()));

    public static ViAttachTypeMainEnum getByValue(String value) {
        return map.get(value);
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return value;
    }
}
