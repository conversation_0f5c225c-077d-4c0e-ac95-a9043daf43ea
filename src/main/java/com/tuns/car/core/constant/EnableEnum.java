package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 启用/禁用 枚举
 * <AUTHOR>
 * @date 2022/12/26
 **/
@Getter
public enum EnableEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    DOWN("0", "未启用"),

    UP("1", "启用"),

    STOP("2","停用");

    private String value;

    private String description;

    EnableEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static Map<String, EnableEnum> all = Arrays.stream(EnableEnum.values())
            .collect(Collectors.toMap(EnableEnum::getValue, Function.identity()));

    public static EnableEnum getByValue(String value) {
        return all.get(value);
    }

    @Override
    public String toString() {
        return value;
    }
}
