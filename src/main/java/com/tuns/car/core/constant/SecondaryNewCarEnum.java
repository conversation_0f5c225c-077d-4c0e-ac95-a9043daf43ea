package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024-1-23 10:12
 */
@Getter
public enum SecondaryNewCarEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    CODE_1("1", "去年是转保,今年是续保"),
    CODE_2("2", "去年是新保,今年是续保转次新车"),
    CODE_3("3", "续保（上年是新车，且车龄0~2年内计算）"),
    CODE_4("4", "转保（车龄0~2年内计算）"),
    PICC_NEW_CAR("5", "270 < 生效日期-初登日期 < 635"),
    CODE_6("6", "去年12月买的新车，今年12月续保就是次新车"),
    CODE_99("99", "非次新车"),
    ;

    private String value;

    private String description;

    SecondaryNewCarEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static Map<String, SecondaryNewCarEnum> map = Stream.of(SecondaryNewCarEnum.values())
            .collect(Collectors.toMap(SecondaryNewCarEnum::getValue, Function.identity()));

    /**
     * 根据枚举值获取对应枚举
     *
     * @param value
     * @return
     */
    public static SecondaryNewCarEnum getByValue(String value) {
        return map.get(value);
    }


    @Override
    public String toString() {
        return value;
    }
}
