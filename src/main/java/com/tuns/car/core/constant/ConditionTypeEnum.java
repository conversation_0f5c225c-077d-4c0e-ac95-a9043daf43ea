package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/27 14:43
 */
@Getter
public enum ConditionTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    /**
     * 指定
     */
    POINT("point", "指定"),
    /**
     * 除外
     */
    EXCEPT("except", "除外"),
    /**
     * 区间
     */
    PERIOD("period", "区间"),
    /**
     * 少于
     */
    LESS("less", "少于"),
    /**
     * 大于
     */
    GREATER("greater", "大于"),
    /**
     * 空值
     */
    EMPTY("empty", "为空");

    private String code;
    private String description;

    public String getCode() {
        return code;
    }

    ConditionTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static List<String> pointAndExceptList() {
        List<String> list = new ArrayList<>();
        list.add(ConditionTypeEnum.POINT.code);
        list.add(ConditionTypeEnum.EXCEPT.code);
        return list;
    }

    public static List<String> periodList() {
        List<String> list = new ArrayList<>();
        list.add(ConditionTypeEnum.PERIOD.code);
        list.add(ConditionTypeEnum.LESS.code);
        list.add(ConditionTypeEnum.GREATER.code);
        return list;
    }

    @Override
    public String getValue() {
        return this.code;
    }

    @Override
    public String toString() {
        return code;
    }
}
