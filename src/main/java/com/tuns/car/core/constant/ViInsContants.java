package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 16:58
 */
public interface ViInsContants {

    /**
     * 充电桩安装地址类型
     */
    enum ChargingAddrType implements IEnum<String>, SwaggerDisplayEnum {
        /**
         * 地面停车场
         */
        GROUND_PARKING_LOT("01", "地面停车场"),
        /**
         * 地面停车楼
         */
        GROUND_PARKING_BUILDING("02", "地面停车楼");

        private String value;

        private String description;

        private static Map<String, ChargingAddrType> CHARGING_TYPE_MAP = Stream.of(ChargingAddrType.values()).collect(Collectors.toMap(ChargingAddrType::getValue, Function.identity()));

        public static ChargingAddrType getByValue(String value) {
            return CHARGING_TYPE_MAP.get(value);
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getDescription() {
            return description;
        }

        ChargingAddrType(String key, String description) {
            this.value = key;
            this.description = description;
        }

        @Override
        public String toString() {
            return getValue();
        }
    }

    /**
     * 充电桩类型
     */
    enum ChargingType implements IEnum<String>, SwaggerDisplayEnum {
        /**
         * 地面充电桩
         */
        GROUND_CHARGING_PILE("01", "地面充电桩"),
        /**
         * 壁挂式充电桩
         */
        WALL_MOUNTED_CHARGING_PIL("02", "壁挂式充电桩"),
        /**
         * 壁挂式充电桩
         */
        OTHER_CHARGING_PILES("99", "壁挂式充电桩");
        private String value;

        private String description;

        private static Map<String, ChargingType> CHARGING_TYPE_MAP = Stream.of(ChargingType.values()).collect(Collectors.toMap(ChargingType::getValue, Function.identity()));

        public static ChargingType getByValue(String value) {
            return CHARGING_TYPE_MAP.get(value);
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getDescription() {
            return description;
        }

        ChargingType(String key, String desc) {
            this.value = key;
            this.description = desc;
        }

        @Override
        public String toString() {
            return getValue();
        }
    }

    /**
     * 充电桩使用年限
     */
    enum ChargingUseYears implements IEnum<String>, SwaggerDisplayEnum {
        /**
         * 两年以内
         */
        TWO_YEARS_AND_LESS("01", "两年以内"),
        /**
         * 2-5（含）年
         */
        TWOORFIVE_INCLUSIVE_YEARS("02", "2-5（含）年"),
        /**
         * 5年以上
         */
        MORE_THAN_FIVE_YEARS("03", "5年以上");

        private String value;

        private String description;

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getDescription() {
            return description;
        }

        ChargingUseYears(String value, String description) {
            this.value = value;
            this.description = description;
        }

        private static Map<String, ChargingUseYears> CHARGING_USE_YEARS_MAP = Stream.of(ChargingUseYears.values()).collect(Collectors.toMap(ChargingUseYears::getValue, Function.identity()));

        public static ChargingUseYears getByValue(String value) {
            return CHARGING_USE_YEARS_MAP.get(value);
        }

        @Override
        public String toString() {
            return getValue();
        }
    }

    enum BusiType implements IEnum<String> {
        /**
         * 报价单
         */
        Quotation("1"),
        /**
         * 保单
         */
        policy("2"),
        ;


        public String value;

        @Override
        public String getValue() {
            return value;
        }

        BusiType(String value) {
            this.value = value;
        }

        @Override
        public String toString() {
            return value;
        }
    }

}
