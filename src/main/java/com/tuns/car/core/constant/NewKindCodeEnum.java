package com.tuns.car.core.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 2020版新险种代码
 */
@Getter
public enum NewKindCodeEnum {


    /**
     * 机动车交通事故责任强制保险
     */
    CI_JQ("JQ", "机动车交通事故责任强制保险"),

    /**
     * 车船税
     */
    CI_BT("CS", "车船税"),

    /**
     * 机动车损失保险
     */
    BI_VEHICLE_LOSS("H"),

    /**
     * 机动车第三者责任保险
     */
    BI_TP_LIABILITY("I"),

    /**
     * 机动车车上人员责任保险(司机)
     */
    BI_PERSONNEL_DRIVER("K3"),

    /**
     * 机动车车上人员责任保险(乘客)
     */
    BI_PERSONNEL_PASSENGER("K4"),

    /**
     * 附加车身划痕损失险
     */
    BI_BODY_SCRATCHES("H1"),

    /**
     * 附加新增加设备损失险
     */
    BI_EQUIPMENT("H2"),

    /**
     * 附加修理期间费用补偿险
     */
    BI_REPAIR("H3"),

    /**
     * 附加法定节假日限额翻倍险
     */
    BI_HOLIDAY("I2"),

    /**
     * 绝对免赔额
     */
    BI_EXCESS("H4"),

    /**
     * 道路救援服务特约条款
     */
    BI_ROADSIDE("E3"),

    /**
     * 车辆安全检测特约条款
     */
    BI_SAFETY_MONITORING("E4"),

    /**
     * 代为驾驶服务特约条款
     */
    BI_SUBSTITUTE("E5"),

    /**
     * 代为送检服务特约条款
     */
    BI_INSPECT("E6"),

    /**
     * 附加发动机进水损坏除外特约条款
     */
    BI_ENGINE_WATER("H5"),

    /**
     * 附加车轮单独损失险
     */
    BI_VEHICLE_WHEEL("H6"),

    /**
     * 附加医保外医疗费用责任险(三者)
     */
    BI_MEDICAL_THREE("E27"),

    /**
     * 附加医保外医疗费用责任险(司机)
     */
    BI_MEDICAL_DRIVER("E28"),

    /**
     * 附加医保外医疗费用责任险(乘客)
     */
    BI_MEDICAL_PASSENGER("E29"),

    /**
     * 附加精神损害抚慰金责任险(三者)
     */
    BI_MENTAL_THREE("E24"),

    /**
     * 附加精神损害抚慰金责任险(司机)
     */
    BI_MENTAL_DRIVER("E25"),

    /**
     * 附加精神损害抚慰金责任险(乘客)
     */
    BI_MENTAL_PASSENGER("E26"),

    /**
     * 附加绝对免赔率特约条款(车损)
     */
    BI_EXCESS_DAMAGE("E1"),

    /**
     * 附加绝对免赔率特约条款(三者)
     */
    BI_EXCESS_THREE("E7"),

    /**
     * 附加绝对免赔率特约条款(司机)
     */
    BI_EXCESS_DRIVER("E8"),

    /**
     * 附加绝对免赔率特约条款(乘客)
     */
    BI_EXCESS_PASSENGER("E9"),

    /**
     * 起重、装卸、挖掘车辆损失扩展条款
     */
    SPECIAL_VEHICLE_LOSS_EXTENSION_CLAUSE("E10"),

    /**
     * 附加车上货物责任险
     */
    BI_CAR_GOODS("I3"),

    /**
     * 特种车车上乘客责任险
     */
    SPECIAL_PERSONNEL_PASSENGER("S3"),


    /**
     * 附加外部电网故障损失险
     */
    NEW_ENERGY_EXTERNAL_POWER_FAULT_COVERAGE("E32"),

    /**
     * 附加自用充电桩损失保险
     */
    NEW_ENERGY_PERSONAL_CHARGER_DAMAGE_COVERAGE("E30"),

    /**
     * 附加自用充电桩责任保险
     */
    NEW_ENERGY_PERSONAL_CHARGER_LIABILITY_COVERAGE("E31"),

    /**
     * 盗抢险
     */
    BI_ROBBERY_THEFT("G1"),

    NULL("");

    private final String value;

    private String description;

    NewKindCodeEnum(String value) {
        this.value = value;
    }

    NewKindCodeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    /**
     * 附加医保外
     */
    private static final List<String> ADDITIONAL_MEDICAL_LIST = Stream.of(BI_MEDICAL_THREE, BI_MEDICAL_DRIVER, BI_MEDICAL_PASSENGER)
            .map(NewKindCodeEnum::getValue)
            .collect(Collectors.toList());

    /**
     * 附加服务条款集合
     */
    private static final List<String> ADDITIONAL_TERM_LIST = Stream.of(BI_ROADSIDE, BI_SAFETY_MONITORING, BI_SUBSTITUTE, BI_INSPECT)
            .map(NewKindCodeEnum::getValue)
            .collect(Collectors.toList());

    /**
     * 充电桩险种
     */
    private static final List<String> NEW_ENERGY = Stream.of(NEW_ENERGY_PERSONAL_CHARGER_DAMAGE_COVERAGE, NEW_ENERGY_PERSONAL_CHARGER_LIABILITY_COVERAGE)
            .map(NewKindCodeEnum::getValue)
            .collect(Collectors.toList());

    /**
     * 充电桩险种
     */
    private static final List<String> MAIN_KINDS = Stream.of(BI_VEHICLE_LOSS, BI_TP_LIABILITY, BI_PERSONNEL_DRIVER, BI_PERSONNEL_PASSENGER, BI_ROBBERY_THEFT)
            .map(NewKindCodeEnum::getValue)
            .collect(Collectors.toList());

    /**
     * 乘客险种
     */
    private static final List<String> PASSENGER_KINDS = Stream.of(BI_PERSONNEL_PASSENGER, BI_MEDICAL_PASSENGER, SPECIAL_PERSONNEL_PASSENGER)
            .map(NewKindCodeEnum::getValue)
            .collect(Collectors.toList());

    /**
     * 交强险种
     */
    private static List<String> CI_INSURANCE_LIST = Arrays.asList(NewKindCodeEnum.CI_JQ.getValue(), NewKindCodeEnum.CI_BT.getValue(), "BZ", "BT", "01");

    public static List<String> getAdditionalTermList() {
        return ADDITIONAL_TERM_LIST;
    }

    /**
     * 属于附加医保外险种
     *
     * @param code
     * @return
     */
    public static Boolean isAdditionalServiceMedical(String code) {
        return ADDITIONAL_MEDICAL_LIST.contains(code);
    }

    /**
     * 属于附加服务条款
     *
     * @param code
     * @return
     */
    public static Boolean isAdditionalServiceTerm(String code) {
        return ADDITIONAL_TERM_LIST.contains(code);
    }

    /**
     * 属于主险
     *
     * @param code
     * @return
     */
    public static Boolean isMainKind(String code) {
        return MAIN_KINDS.contains(code);
    }

    /**
     * 是否是充电桩险种
     *
     * @param code
     * @return
     */
    public static Boolean isCharging(String code) {
        return NEW_ENERGY.contains(code);
    }

    /**
     * 是否交强险种
     *
     * @param code
     * @return
     */
    public static Boolean isCIInsurance(String code) {
        return CI_INSURANCE_LIST.contains(code);
    }

    /**
     * 是否跟乘客有关，即总保额需要乘以座位数
     *
     * @param code
     * @return
     */
    public static Boolean followPassenger(String code) {
        return PASSENGER_KINDS.contains(code);
    }

    private static Map<String, NewKindCodeEnum> map = Stream.of(NewKindCodeEnum.values())
            .collect(Collectors.toMap(NewKindCodeEnum::getValue, Function.identity()));

    public static NewKindCodeEnum getByCode(String code) {
        return Optional.ofNullable(map.get(code)).orElse(NULL);
    }
}
