package com.tuns.car.core.constant;

import cn.hutool.core.convert.ConverterRegistry;
import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.boot.converter.HutoolEnumConverter;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 性别枚举
 *
 * <AUTHOR>
 * @since 2022/6/7
 */
@Getter
public enum SexEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    MAN("1", "男"),
    WOMAN("2", "女");

    /**
     * 我方数据库枚举值
     */
    private String value;

    /**
     * 描述
     */
    private String description;

    SexEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static SexEnum getByValue(String value) {
        return Stream.of(SexEnum.values())
                .filter(sexEnum -> sexEnum.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }

    static {
        ConverterRegistry.getInstance().putCustom(SexEnum.class, new HutoolEnumConverter<>(SexEnum.class));
    }

    @Override
    public String toString() {
        return value;
    }
}
