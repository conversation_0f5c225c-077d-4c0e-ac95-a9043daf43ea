package com.tuns.car.core.constant;

import cn.hutool.core.convert.ConverterRegistry;
import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.boot.constant.InsConstants;
import com.tuns.core.boot.converter.HutoolEnumConverter;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName PersonNatureEnum
 * @Description 关系人性质
 * <AUTHOR>
 * @Date 2022/4/26 17:29
 * @Version 1.0
 */

@Getter
@AllArgsConstructor
public enum PersonNatureEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    PERSON("1", "个人"),

    OFFICE("2", "机关"),

    COMPANY("3", "企业"),

    ORGANIZE("4", "事业团体"),

    ;

    private String value;

    private String text;


    @Override
    public String toString() {
        return value;
    }


    @Override
    public String getDescription() {
        return text;
    }

    private static final Predicate<PersonNatureEnum> IS_NATURE = str -> str.equals(PersonNatureEnum.PERSON);

    private static Map<String, PersonNatureEnum> map = Stream.of(PersonNatureEnum.values())
            .collect(Collectors.toMap(PersonNatureEnum::getValue, Function.identity()));

    /**
     * 判断是否是自然人
     *
     * @param personNature
     * @return
     */
    public static String isNature(PersonNatureEnum personNature) {
        return IS_NATURE.test(personNature) ? InsConstants.YNFlag.YES : InsConstants.YNFlag.NO;
    }

    /**
     * 根据 枚举值 获取枚举
     *
     * @param value
     * @return
     */
    public static PersonNatureEnum getByValue(String value) {
        return map.get(value);
    }

    static {
        ConverterRegistry.getInstance().putCustom(PersonNatureEnum.class, new HutoolEnumConverter<>(PersonNatureEnum.class));
    }
}
