package com.tuns.car.core.constant;

import cn.hutool.core.convert.ConverterRegistry;
import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.boot.converter.HutoolEnumConverter;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * ClassName: OwneShipNatureEnum
 * Description:
 * Create Time: 2022/2/10 14:16
 *
 * <AUTHOR>
 */
@Getter
public enum OwnershipNatureEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    PERSONAL("1", "个人"),

    DEPARTMENT("2", "机关"),

    ENTERPRISE("3", "企业"),

    ORGANIZATION("4", "团体"),
    ;

    private String value;
    private String description;

    OwnershipNatureEnum(String value, String description){
        this.value = value;
        this.description = description;
    }

    public static String getDescriptionByValue(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (OwnershipNatureEnum enums : OwnershipNatureEnum.values()) {
                if (enums.value.equals(code)) {
                    return enums.description;
                }
            }
        }
        return null;
    }

    static {
        ConverterRegistry.getInstance().putCustom(OwnershipNatureEnum.class, new HutoolEnumConverter<>(OwnershipNatureEnum.class));
    }

    @Override
    public String toString() {
        return value;
    }
}
