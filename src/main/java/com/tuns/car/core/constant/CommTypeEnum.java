package com.tuns.car.core.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/4 9:36
 */
@Getter
@AllArgsConstructor
public enum CommTypeEnum {

    CI("1","交强1"),
    BI("2","商业1"),
    VI("3","车船税1"),
    SI("4","单件"),
    EX("5","附加"),
    AD("6","调整佣金"),
    CP("7","协作费"),
    CI_2("8","交强二"),
    BI_2("9","商业二"),
    VI_2("10","车船税二");

    private String value;
    private String desc;
}
