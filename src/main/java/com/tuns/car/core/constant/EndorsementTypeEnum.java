package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 车险批单类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/03/07 9:13
 */
@Getter
public enum EndorsementTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    STATUS_00("00", "未批"),
    STATUS_01("01", "商业险加保"),
    STATUS_02("02", "商业险减保"),
    STATUS_03("03", "商业险退保"),
    STATUS_04("04", "全单退保"),
    STATUS_05("05", "已批"),
    STATUS_06("06", "全单批改"),

    STATUS_07("07", "交强险退保"),
    ;

    private String value;

    private String description;

    EndorsementTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static String getEnum(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (EndorsementTypeEnum value : EndorsementTypeEnum.values()) {
                if (value.value.equals(code)) {
                    return value.description;
                }
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return value;
    }
}
