package com.tuns.car.core.constant.compare;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 数据比对是否放开枚举
 * <AUTHOR>
 * @since 2022/9/16
 */
@Getter
public enum CompareResultPassEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    YES("1","手动通过"),

    NO("0","不通过");

    private String value;

    private String description;

    CompareResultPassEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
