package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 渠道-自定义字段枚举定义
 *
 * <AUTHOR>
 * @since 2024-1-22 15:46
 */
@Getter
@AllArgsConstructor
public enum CustomSettingFieldEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    SETTING_CODE_001("SETTING_CODE_001", "承保机构"),
    SETTING_CODE_002("SETTING_CODE_002", "代理机构"),
    SETTING_CODE_003("SETTING_CODE_003", "中介协议"),
    SETTING_CODE_004("SETTING_CODE_004", "销售渠道"),
    SETTING_CODE_005("SETTING_CODE_005", "服务代码"),
    SETTING_CODE_006("SETTING_CODE_006", "业务员"),
    SETTING_CODE_007("SETTING_CODE_007", "是否实名制"),
    SETTING_CODE_008("SETTING_CODE_008", "签报号"),
    SETTING_CODE_009("SETTING_CODE_009", "代理人代码"),
    SETTING_CODE_010("SETTING_CODE_010", "业务来源"),
    SETTING_CODE_011("SETTING_CODE_011", "登录机构"),
    SETTING_CODE_012("SETTING_CODE_012", "验车人"),
    SETTING_CODE_013("SETTING_CODE_013", "决策单元"),
    SETTING_CODE_014("SETTING_CODE_014", "验车人代码"),
    SETTING_CODE_015("SETTING_CODE_015", "决策单元新能源"),
    SETTING_CODE_016("SETTING_CODE_016", "渠道协议"),
    SETTING_CODE_017("SETTING_CODE_017", "渠道类型1"),
    SETTING_CODE_018("SETTING_CODE_018", "渠道类型2"),
    SETTING_CODE_019("SETTING_CODE_019", "分配方案"),
    SETTING_CODE_020("SETTING_CODE_020", "终端"),
    SETTING_CODE_021("SETTING_CODE_021", "归属部门"),
    SETTING_CODE_022("SETTING_CODE_022", "中介名称"),
    SETTING_CODE_023("SETTING_CODE_023", "网点服务码"),
    SETTING_CODE_024("SETTING_CODE_024", "归属人"),
    SETTING_CODE_025("SETTING_CODE_025", "项目代码"),
    SETTING_CODE_026("SETTING_CODE_026", "是否送修"),
    SETTING_CODE_027("SETTING_CODE_027", "送修代码"),
    SETTING_CODE_028("SETTING_CODE_028", "经代续保"),
    SETTING_CODE_029("SETTING_CODE_029", "合作伙伴编码"),
    SETTING_CODE_030("SETTING_CODE_030", "合作伙伴名称"),
    SETTING_CODE_031("SETTING_CODE_031", "合作伙伴编码1"),
    SETTING_CODE_032("SETTING_CODE_032", "合作伙伴名称1"),
    SETTING_CODE_033("SETTING_CODE_033", "服务经理类型"),
    SETTING_CODE_034("SETTING_CODE_034", "操作人"),
    SETTING_CODE_035("SETTING_CODE_035", "归属业务员"),
    SETTING_CODE_036("SETTING_CODE_036", "经办人代码"),
    SETTING_CODE_037("SETTING_CODE_037", "业务方式"),
    SETTING_CODE_038("SETTING_CODE_038", "合同/协议号"),
    SETTING_CODE_039("SETTING_CODE_039", "签报编码"),
    SETTING_CODE_040("SETTING_CODE_040", "团队号"),
    SETTING_CODE_041("SETTING_CODE_041", "其他"),
    SETTING_CODE_043("SETTING_CODE_043", "代理营销员工号");


    /**
     * 自定义字段编码 对应vi_chan_custom_field_setting 中的customFieldCode
     */
    private String value;
    /**
     * 原SettingInfoEnum中的value 仅供参考 已无实际意义
     * 字段真实含义请查看vi_chan_custom_field_config中的custom_field_name
     */
    private String description;

    private static final Map<String, CustomSettingFieldEnum> CUSTOM_SETTING_FIELD_ENUM_MAP = Stream.of(CustomSettingFieldEnum.values()).collect(Collectors.toMap(CustomSettingFieldEnum::getDescription, Function.identity()));

    public static CustomSettingFieldEnum matchValue(String columnName) {
        return CUSTOM_SETTING_FIELD_ENUM_MAP.get(columnName);
    }

    @Override
    public String toString() {
        return value;
    }
}
