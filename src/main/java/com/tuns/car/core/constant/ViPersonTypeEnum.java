package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 关系人信息
 */
@Getter
public enum ViPersonTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    /***
     * 01-车主
     */
    OWNER("01", "车主"),
    /***
     * 02-被保人
     */
    INSURED("02", "被保人"),
    /***
     * 03-投保人
     */
    HOLDER("03", "投保人"),
    /***
     * 04-受益人
     */
    BENEFICIARY("04", "受益人"),
    /***
     * 05-收件人
     */
    DELIVERY("05", "收件人"),
    /***
     * -9-未知
     */
    NULL("-9", "未知");

    private String value;

    private String description;

    ViPersonTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static ViPersonTypeEnum getByValue(String value) {
        if (StringUtils.isNotEmpty(value)) {
            for (ViPersonTypeEnum viPersonTypeEnum : ViPersonTypeEnum.values()) {
                if (viPersonTypeEnum.getValue().equals(value)) {
                    return viPersonTypeEnum;
                }
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.value;
    }
}
