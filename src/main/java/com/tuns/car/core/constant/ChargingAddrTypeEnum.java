package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description
 * @create 2022-04-11 9:53
 **/

public enum ChargingAddrTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    /**
     * 地面停车场
     */
    GROUND_PARKING_LOT("01", "地面停车场"),
    /**
     * 地面停车楼
     */
    GROUND_PARKING_BUILDING("02", "地面停车楼"),
    /**
     * 地下停车场
     */
    GROUND_floor_PARKING_BUILDING("03", "地下停车场"),
    ;

    private String value;

    private String description;

    private static Map<String, ChargingAddrTypeEnum> CHARGING_TYPE_MAP = Stream.of(ChargingAddrTypeEnum.values()).collect(Collectors.toMap(ChargingAddrTypeEnum::getValue, Function.identity()));

    public static ChargingAddrTypeEnum getByValue(String value) {
        return CHARGING_TYPE_MAP.get(value);
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    ChargingAddrTypeEnum(String key, String description) {
        this.value = key;
        this.description = description;
    }

    @Override
    public String toString() {
        return getValue();
    }
}
