package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName OCRTypeEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/22 14:42
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum OCRSideEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    FACE("face","正面"),

    BACK("back","反面"),

    ;

    private String value;

    private String description;

    @Override
    public String toString() {
        return value;
    }

    public static final OCRSideEnum getByValue(String value) {
        return Arrays.stream(OCRSideEnum.values()).filter(e->e.getValue().equals(value)).findFirst().orElse(null);
    }
}
