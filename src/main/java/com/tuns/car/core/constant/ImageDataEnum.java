package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/21 15:53
 */
@Getter
@AllArgsConstructor
public enum ImageDataEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    VEHICLE_DRIVING("01","车辆行驶证照片"),
    CAR_OWNER_IDENTITY("02","车主身份证照片"),
    HOLDER("03","投保人身份证照片"),
    INSURED("04","被保人身份证照片"),
    VEHICLE_CERTIFICATION("05","合格证"),
    CHECK_CAR("67","同步验车照"),
    OTHER("99","其它");


    private String value;

    private String description;

    @Override
    public String getDescription() {
        return null;
    }

    @Override
    public String getSwaggerDesc() {
        return description;
    }

    @Override
    public String getValue() {
        return value;
    }

}
