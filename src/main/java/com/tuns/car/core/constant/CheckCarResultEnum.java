package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 验车结果枚举
 *
 * <AUTHOR>
 * @since 2022/7/6
 */
@Getter
public enum CheckCarResultEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    NORMAL("1", "全车完好无损，证车相符，年审合格"),

    UNQUALIFIED("2", "不合格"),

    DAMAGE("3","车辆带伤投保，已上传损伤部位验车照"),

    OTHER("9", "其他");

    /**
     * 数据库枚举值
     */
    private String value;

    /**
     * 描述
     */
    private String description;

    CheckCarResultEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }

    public static CheckCarResultEnum getByValue(String value) {
        return Stream.of(CheckCarResultEnum.values())
                .filter(checkCarResultEnum -> checkCarResultEnum.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
}
