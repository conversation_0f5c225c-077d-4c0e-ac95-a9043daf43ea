package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 缓存块名称定义
 * 1-正常业务提示 , 2-重复投保 , 3-预核保 , 4-核保
 * <AUTHOR>
 * @version 1.0
 * @date 2022/02/12 9:13
 */
@Getter
public enum MsgTypeEnum implements IEnum, SwaggerDisplayEnum<String> {
    /**
     * 1-正常业务提示
     */
    NORMAL_BUSINESS(   "1", "正常业务提示"),
    /**
     * 2-保费计算时提示消息（重复投保信息）
     */
    PREMIUM_CALCULATE(   "2", "保费计算时提示消息"),
    /**
     * 3-预核保
     */
    PRE_UNDERWRITING(   "3", "预核保"),
    /**
     * 4-核保
     */
    UNDERWRITING(   "4", "核保");

    private String value;

    private String description;

    MsgTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

}
