package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 关系人与主要关系人所属关系
 */
@Getter
public enum MainPersRelationEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    DEFAULT("00", "默认"),
    OWNER("E1", "车主"),
    INSURED("E2", "被保人"),
    HOLDER("E3", "投保人"),
    BENEFICIARY("E4", "受益人"),
    DELIVERY("E5", "收件人");

    /**
     * 我方枚举值
     */
    private String value;

    /**
     * 枚举描述
     */
    private String description;

    MainPersRelationEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
