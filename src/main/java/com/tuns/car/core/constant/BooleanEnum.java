package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 布尔值常用枚举
 *
 * <AUTHOR>
 * @since 2022/3/18
 */
@Getter
public enum BooleanEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    /**
     * 是
     */
    YES("1", "是"),

    /**
     * 否
     */
    NO("0", "否"),

    /**
     * 否
     */
    TOW("2", "其他"),

    ;

    private String value;

    private String description;

    BooleanEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
