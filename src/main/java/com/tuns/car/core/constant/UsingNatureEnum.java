package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 使用性质
 */
@Getter
@AllArgsConstructor
public enum UsingNatureEnum implements IEnum<String> {

    FAMILY("J001", "家庭自用车", UsingNatureParentEnum.J0),
    BUSINESS("K001", "企业用车", UsingNatureParentEnum.K0),
    OFFICE("K002", "事业团体用车", UsingNatureParentEnum.K0),
    RENTAL_LEASE("K003", "出租租赁", UsingNatureParentEnum.K0),
    URBAN_BUS("K004", "城市公交", UsingNatureParentEnum.K0),
    HIGHWAY("K005", "公路客运", UsingNatureParentEnum.K0),
    TOURIST("K006", "旅游客运", UsingNatureParentEnum.K0),
    TAXI("K007", "出租客运", UsingNatureParentEnum.K0),
    ONLINE_TAXI("K008", "网约车", UsingNatureParentEnum.K0),
    COACH("K009", "教练", UsingNatureParentEnum.K0),
    OTHER("K099", "其它客车", UsingNatureParentEnum.K0),
    GENERAL_WAGON("H001", "普通货车", UsingNatureParentEnum.H0),
    DUMPER("H002", "自卸车", UsingNatureParentEnum.H0),
    LOW_SPEED_TRUCK("H003", "低速载货汽车", UsingNatureParentEnum.H0),
    SEMITRAILER_TRACTOR("H004", "半挂牵引车", UsingNatureParentEnum.H0),
    TRAILER("H005", "挂车", UsingNatureParentEnum.H0),
    OTHER_TRUCKS("H099", "其它货车", UsingNatureParentEnum.H0),
    VAN("H098", "厢式货车", UsingNatureParentEnum.H0),
    MEDIUM_VAN("H007", "中型厢式货车", UsingNatureParentEnum.H0),
    MINI_VAN("H009", "微型厢式货车", UsingNatureParentEnum.H0),
    LOW_SPEED_VAN("H010", "低速厢式货车", UsingNatureParentEnum.H0),
    LIGHT_VAN("H008", "轻型厢式货车", UsingNatureParentEnum.H0),
    RATE_TYPE("H011", "仓栅式货车", UsingNatureParentEnum.H0),
    HEAVY_VAN_SEMITRAILER("H006", "重型厢式半挂车", UsingNatureParentEnum.H0),
    RATE_TYPE_H012("H012", "客货两用车", UsingNatureParentEnum.H0),
    RATE_TYPE_H013("H013", "平板货车", UsingNatureParentEnum.H0),
    RATE_TYPE_H014("H014", "罐式货车", UsingNatureParentEnum.H0),
    RATE_TYPE_H015("H015", "重型厢式货车", UsingNatureParentEnum.H0),
    RATE_TYPE_H016("H016", "牵引车", UsingNatureParentEnum.H0),
    OIL_TANKER("T101", "油罐车", UsingNatureParentEnum.T1),
    GAS_TANK_TRUCK("T102", "气罐车", UsingNatureParentEnum.T1),
    TANK_TRUCK("T103", "液罐车", UsingNatureParentEnum.T1),
    SPECIAL_UNIT("T104", "特一挂", UsingNatureParentEnum.T1),
    T1_OTHER("T199", "其它", UsingNatureParentEnum.T1),
    CRANE("T204", "起重机", UsingNatureParentEnum.T2),
    LOADING_TRUCK("T205", "装卸车", UsingNatureParentEnum.T2),
    LIFT("T206", "升降机", UsingNatureParentEnum.T2),
    AGITATOR_TRUCK("T207", "搅拌车", UsingNatureParentEnum.T2),
    EXCAVATOR("T208", "挖掘机", UsingNatureParentEnum.T2),
    CEMENT_PUMP_TRUCK("T209", "水泥泵车", UsingNatureParentEnum.T2),
    BULLDOZER("T210", "推土机", UsingNatureParentEnum.T2),
    REFRIGERATED_CAR("T211", "冷藏车", UsingNatureParentEnum.T2),
    INSULATED_CAR("T212", "保温车", UsingNatureParentEnum.T2),
    ROLLER("T213", "压路车", UsingNatureParentEnum.T2),
    MINING_CAR("T214", "矿山车", UsingNatureParentEnum.T2),
    OIL_FIELD_OPERATION_VEHICLE("T215", "油气田操作用车", UsingNatureParentEnum.T2),
    WATER_PURIFICATION_VEHICLE("T216", "净水车", UsingNatureParentEnum.T2),
    SPECIAL_TWO_TRAILER("T217", "特二挂车", UsingNatureParentEnum.T2),
    T2_OTHER("T299", "其它", UsingNatureParentEnum.T2),
    WRECKER("T201", "清障车", UsingNatureParentEnum.T2),
    SWEEPER("T202", "清扫车", UsingNatureParentEnum.T2),
    CLEANING_VEHICLE("T203", "清洁车", UsingNatureParentEnum.T2),
    CEMENT_MIXER_TRUCK("T218", "水泥搅拌车", UsingNatureParentEnum.T2),
    THE_TV_TRUCK("T301", "电视转播车", UsingNatureParentEnum.T3),
    FIRE_FIGHTING_TRUCK("T302", "消防车", UsingNatureParentEnum.T3),
    HOSPITAL_CAR("T303", "医疗车", UsingNatureParentEnum.T3),
    AMBULANCE("T304", "救护车", UsingNatureParentEnum.T3),
    MONITORING_CAR("T305", "监测车", UsingNatureParentEnum.T3),
    X_RAY_THE_CAR("T307", "X光检查车", UsingNatureParentEnum.T3),
    TELECOMMUNICATION_ENGINEERING_VEHICLE("T308", "电信工程车", UsingNatureParentEnum.T3),
    ELECTRIC_POWER_ENGINEERING_VEHICLE("T309", "电力工程车", UsingNatureParentEnum.T3),
    PROFESSIONAL_TRAILER("T310", "专业拖车", UsingNatureParentEnum.T3),
    CASH_CARRIER("T312", "运钞车", UsingNatureParentEnum.T3),
    POLICE_CAR("T313", "警用车", UsingNatureParentEnum.T3),
    INSTRUMENT_AND_EQUIPMENT_TRAILER("T314", "仪器设备挂车", UsingNatureParentEnum.T3),
    SPECIAL_THREE_TRAILER("T315", "特三挂车", UsingNatureParentEnum.T3),
    T3_OTHER("T399", "其它", UsingNatureParentEnum.T3),
    POST_AND_ELECTRIC_TRAIN("T311", "邮电车", UsingNatureParentEnum.T3),
    RADAR_VEHICLE("T306", "雷达车", UsingNatureParentEnum.T3),
    CONTAINER_TRACTOR("T401", "集装箱拖头", UsingNatureParentEnum.T4),
    SPECIAL_FOUR_TRAILER("T402", "特四挂车", UsingNatureParentEnum.T4);

    private String value;
    private String description;
    private UsingNatureParentEnum parent;

    private static final Map<String, UsingNatureEnum> USING_NATURE_ENUM_MAP = Arrays.stream(UsingNatureEnum.values()).collect(Collectors.toMap(UsingNatureEnum::getValue, Function.identity()));

    private static final List<UsingNatureEnum> TRAILER_CAR_LIST = Arrays.asList(UsingNatureEnum.SPECIAL_UNIT, UsingNatureEnum.SPECIAL_TWO_TRAILER, UsingNatureEnum.SPECIAL_THREE_TRAILER, UsingNatureEnum.SPECIAL_FOUR_TRAILER);

    public static String getUsingNatureDesc(String value) {
        UsingNatureEnum usingNatureEnum = USING_NATURE_ENUM_MAP.get(value);
        if (Objects.nonNull(usingNatureEnum)) {
            return usingNatureEnum.description;
        }
        return null;
    }

    public static UsingNatureEnum getUsingNature(String usingNature) {
        return USING_NATURE_ENUM_MAP.get(usingNature);
    }

    /**
     * 是否挂车
     * @param usingNatureEnum
     * @return
     */
    public static Boolean isTrailerCar(UsingNatureEnum usingNatureEnum) {
        return TRAILER_CAR_LIST.contains(usingNatureEnum);
    }

    @Override
    public String toString() {
        return value;
    }
}
