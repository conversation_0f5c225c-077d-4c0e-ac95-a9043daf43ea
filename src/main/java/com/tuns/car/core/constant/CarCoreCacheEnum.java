package com.tuns.car.core.constant;

import lombok.Getter;

/**
 * 缓存块名称定义
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/02/12 9:13
 */
@Getter
public enum CarCoreCacheEnum {
    /**
     * 缓存块名称：样式案例，有业务值后请删除该值
     */
    R0(CarCoreCtt.APP_NAME + ":demo:", "样式案例，有业务值后请删除该值");

    private String code;

    private String desc;

    CarCoreCacheEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /***
     * <AUTHOR>
     *
     * @description: 根据code获取描述信息
     * @date: 2022/2/12 9:25
     * @param code 编码
     * @return [描述]
     */
    public static String getEnumDesc(String code) {

        for (CarCoreCacheEnum value : CarCoreCacheEnum.values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }

        return "";
    }

}
