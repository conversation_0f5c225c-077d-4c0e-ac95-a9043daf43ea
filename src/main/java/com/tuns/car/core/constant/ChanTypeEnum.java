package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 渠道类型枚举
 *
 * <AUTHOR>
 * @since 2022/3/18
 */
@Getter
public enum ChanTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    /**
     * 核心出单
     */
    CORE("core", "核心出单渠道"),

    /**
     * api出单
     */
    API("api", "api出单渠道");

    private String value;

    private String description;

    ChanTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static ChanTypeEnum getByValue(String value) {
        for (ChanTypeEnum chanTypeEnum : ChanTypeEnum.values()) {
            if (chanTypeEnum.getValue().equals(value)) {
                return chanTypeEnum;
            }
        }
        return null;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }


}
