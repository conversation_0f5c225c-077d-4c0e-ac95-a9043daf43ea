package com.tuns.car.core.constant;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 支付方式
 */
@Getter
public enum PayTypeEnum implements SwaggerDisplayEnum<String>, IEnum<String> {
    CLIENT("client", "客户端"),
    WECHAT("weChat", "直接微信"),
    WECHATONLY("weChatOnly", "仅支持微信打开"),
    ALIPAY("aliPay", "直接支付宝"),
    ALIPAYONLY("aliPayOnly", "仅支持支付宝打开"),
    UNIONPAY("unionPay", "银联"),
    KUAIQIAN("kuaiQian", "快钱"),
    YEEPAY("yeepay", "易宝支付"),
    NONETYPE("noneType", "立即支付"),
    // 整合方式
    OTHER("other", "其它方式"),
    EXTERNALLINK("externalLink","外部链接"),
    CORPORATE_TRANS("corporateTrans","对公转账"),
    ;

    private String value;
    private String description;

    PayTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static Map<String, PayTypeEnum> map = Stream.of(PayTypeEnum.values())
            .collect(Collectors.toMap(PayTypeEnum::getValue, Function.identity()));

    public static PayTypeEnum getByValue(String value) {
        return map.get(value);
    }

    @Override
    public String toString() {
        return value;
    }
}
