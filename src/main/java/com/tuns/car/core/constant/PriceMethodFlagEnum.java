package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报价方式枚举
 *
 * <AUTHOR>
 * @since 2022/3/18
 */
@Getter
@AllArgsConstructor
public enum PriceMethodFlagEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    AUTO("1", "自助报价"),

    MANUAL("2", "人工报价");

    private String value;

    private String description;

    @Override
    public String toString() {
        return value;
    }
}
