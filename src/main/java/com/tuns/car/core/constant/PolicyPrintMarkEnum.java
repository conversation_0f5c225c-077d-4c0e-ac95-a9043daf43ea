package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

@Getter
public enum PolicyPrintMarkEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    YES("1", "是"),
    NO("0", "否");

    private String value;
    private String description;

    PolicyPrintMarkEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
