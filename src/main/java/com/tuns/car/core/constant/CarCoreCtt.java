package com.tuns.car.core.constant;

/***
 * TunsSpiderCtt 名称等项目初步迁移完成后修改为：TunsCarCoreCtt
 * <AUTHOR>
 * @ClassName: TunsSpiderCtt
 * @Description: 系统级常量
 * @date 2021年12月20日
 */
public class CarCoreCtt {

    /***
     * 系统常量:应用名称
     */
    public static final String APP_NAME = "tuns-car-core";

    public static String getRedisKey(String key) {
        return APP_NAME + ":" + key;
    }

    /***
     * Redis缓存块命名-【tuns-spider:premium-calculate】
     * 符合《腾顺保险业务系统缓存命名规范》要求
     ***/
    public static final String REGION_PREMIUM_CALCULATE = APP_NAME + ":premium-calculate";

    /***
     * 保费计算结果
     * Redis缓存块命名-【tuns-spider:premium-calculate】
     * 符合《腾顺保险业务系统缓存命名规范》要求
     ***/
    public static final String REGION_PREMIUM_RESULT = APP_NAME + ":premium-result";

    /***
     * 默认加密解密key（可以暴露出去的秘钥）
     ***/
    public static final String DEFAULT_EXPOSE_KEY = "aes-key-12345678";

    /**
     * 推荐方案结果
     * Redis缓存块命名-【tuns-core:recommend-calculate】
     * 符合《腾顺保险业务系统缓存命名规范》要求
     **/
    public static final String REGION_RECOMMEND_RESULT = APP_NAME + ":recommend-result";

    /**
     * 撤单管理操作次数
     * Redis缓存块命名-【tuns-core:cancel-underwrite】
     * 符合《腾顺保险业务系统缓存命名规范》要求
     **/
    public static final String CANCEL_UNDERWRITE_LIMIT = APP_NAME + ":cancel-underwrite-limit";

    /***
     * 默认值：是
     ***/
    public static final String YES = "Y";
    /***
     * 默认值：否
     ***/
    public static final String NO = "N";

    /**
     * OCR识别操作次数
     * Redis缓存块命名-【tuns-core:image-ocr-identify-limit】
     * 符合《腾顺保险业务系统缓存命名规范》要求
     **/
    public static final String IMAGE_OCR_IDENTIFY_LIMIT = APP_NAME + ":image-ocr-identify-limit";

    public static String buildImageOcrIdentifyLimitKey(Integer userId) {
        return IMAGE_OCR_IDENTIFY_LIMIT + ":" + userId;
    }

    /**
     * 续保自动报价
     */
    public static final String RENEWAL_AUTO_PREMIUM_CALCULATE = APP_NAME + ":renewal:auto-premium-calculate";

    /**
     * 智能报价成功后置处理
     */
    public static final String INTELLECT_PREMIUM_CALCULATE = APP_NAME + ":intellect:premium-success-calculate";

    /**
     * 智能报价成功后置处理
     */
    public static final String INTELLIGENT_QUOTE_PLAN_FINISH_KEY = APP_NAME + ":intellect:premium-calculate-finish:";


}
