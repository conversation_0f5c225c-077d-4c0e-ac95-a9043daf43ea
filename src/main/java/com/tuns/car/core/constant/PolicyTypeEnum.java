package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/2 10:36
 */
@Getter
public enum PolicyTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    CI("1", "交强险"),
    BI("2", "商业险"),
    VI("3", "车船险"),
    ;

    private String value;
    private String description;

    PolicyTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static Map<String, PolicyTypeEnum> map = Stream.of(PolicyTypeEnum.values()).collect(Collectors.toMap(PolicyTypeEnum::getValue, s -> s));

    public static PolicyTypeEnum getByCode(String code) {
        return map.get(code);
    }

    @Override
    public String toString() {
        return value;
    }
}
