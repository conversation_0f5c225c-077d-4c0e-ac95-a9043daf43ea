package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 免验车原因
 */
@Getter
public enum ExemptVerifyCarReasonEnum implements IEnum<String> {
    LIABILITYINS("1", "单保责任险"),
    RENEWALINS("2", "按期续保，且未加保损失类险种"),
    NEWCAR("3", "新车"),
    ORGANIZATION("4", "团车业务"),
    RENEWALINS_TWODAY("5", "符合免验规定"),
    LOWER_CARE("6", "底险车风险"),
    THE_COMPANY_RENEWS_THE_POLICY("7", "本公司续保，脱保2天内，且未加保损失类险种"),
    LOCOMOTIVE_CAR("10", "机关车，团车"),

    OTHER("9", "其他"),
    ;
    private String value;
    private String description;

    private static Map<String, ExemptVerifyCarReasonEnum> map = Stream.of(ExemptVerifyCarReasonEnum.values())
            .collect(Collectors.toMap(ExemptVerifyCarReasonEnum::getValue, Function.identity()));

    public static ExemptVerifyCarReasonEnum getByValue(Integer value) {
        return map.get(String.valueOf(value));
    }

    public static ExemptVerifyCarReasonEnum getByValue(String value) {
        return map.get(value);
    }

    ExemptVerifyCarReasonEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }

}
