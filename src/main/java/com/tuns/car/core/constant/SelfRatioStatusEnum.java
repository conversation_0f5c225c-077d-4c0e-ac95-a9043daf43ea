package com.tuns.car.core.constant;

/**
 * 自主定价系数状态枚举
 * @ClassName SelfRatioStatusEnum
 * @Description 自主定价系数状态枚举 
 * <AUTHOR>
 * @Date 2021/10/20 14:48:16 
 * @Version 1.0
 */
public enum SelfRatioStatusEnum {
    /**
     * 编辑中
     */
    EDIT("1", "编辑中"),
    /**
     * 待审核
     */
    PENDING_AUDIT("2", "待审核"),
    /**
     * 待修改
     */
    PENDING_UPDATE("3", "待修改"),
    /**
     * 已停用
     */
    DISABLE("4", "已停用"),
    /**
     * 待启用，即审核通过
     */
    PENDING_ENABLE("5", "待启用"),
    /**
     * 已启用
     */
    ENABLE("6", "已启用"),
    ;

    private String key;

    private String value;

    private SelfRatioStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    SelfRatioStatusEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
