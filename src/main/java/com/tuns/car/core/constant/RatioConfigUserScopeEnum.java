package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 系数配置试用权限枚举
 * <AUTHOR>
 * @date 2023/04/18
 **/
@Getter
public enum RatioConfigUserScopeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    PART("0","部分用户"),

    ALL("1","所有用户");


    private String value;

    private String description;

    RatioConfigUserScopeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
