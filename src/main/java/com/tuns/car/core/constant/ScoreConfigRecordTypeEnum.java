package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.*;
import java.util.stream.Stream;

/**
 * 附加服务条款等级 配置枚举
 *
 * <AUTHOR>
 * @since 2023/06/06/10:18
 **/
@Getter
public enum ScoreConfigRecordTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    CI("1", "单交强+交商同保"),

    BI("2", "单商业+交商同保"),

    CI_BI("3", "单交强+单商业+交商同保"),
    ONLY_CI("4", "单交强"),
    ONLY_BI("5", "单商业"),
    BOTH_CI_BI("6", "交商同保");

    private String value;

    private String description;

    ScoreConfigRecordTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static String getDescriptionByValue(String value) {
        return Stream.of(ScoreConfigRecordTypeEnum.values())
                .filter(additionClauseGradeEnum -> additionClauseGradeEnum.getValue().equals(value))
                .map(ScoreConfigRecordTypeEnum::getDescription)
                .findFirst()
                .orElse(null);
    }

    private static final Map<ScoreConfigRecordTypeEnum, List<RecordTypeEnum>> map = new HashMap<>();

    static {
        map.put(CI, Arrays.asList(RecordTypeEnum.CI, RecordTypeEnum.BOTH));
        map.put(BI, Arrays.asList(RecordTypeEnum.BI, RecordTypeEnum.BOTH));
        map.put(CI_BI, Arrays.asList(RecordTypeEnum.BI,RecordTypeEnum.CI ,RecordTypeEnum.BOTH));
        map.put(ONLY_CI, Collections.singletonList(RecordTypeEnum.CI));
        map.put(ONLY_BI, Collections.singletonList(RecordTypeEnum.BI));
        map.put(BOTH_CI_BI, Collections.singletonList(RecordTypeEnum.BOTH));
    }

    /**
     * 判断是否包含
     * @param type
     * @param recordType
     * @return
     */
    public static boolean contains(ScoreConfigRecordTypeEnum type,RecordTypeEnum recordType){
        if(map.get(type) == null){
            return false;
        }
        return map.get(type).contains(recordType);
    }


    @Override
    public String toString() {
        return value;
    }
}
