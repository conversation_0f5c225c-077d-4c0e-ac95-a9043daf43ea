package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 我方投保类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum RenewalTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    CONTINUED_FOR_MANY_YEARS("01", "多年续"),
    RENEWAL_OF_THE_POLICY("02", "转保首续"),
    NEW_CAR_FIRST_RENEWAL("03", "新车首续"),
    ;

    private String value;

    private String description;

    RenewalTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static Map<String, RenewalTypeEnum> map = Stream.of(RenewalTypeEnum.values())
            .collect(Collectors.toMap(RenewalTypeEnum::getValue, Function.identity()));

    /**
     * 根据枚举值获取对应枚举
     *
     * @param value
     * @return
     */
    public static RenewalTypeEnum getByValue(String value) {
        return map.get(value);
    }

    public static String getByDes(String value){
        return map.get(value).getDescription();
    }

    @Override
    public String toString() {
        return value;
    }
}