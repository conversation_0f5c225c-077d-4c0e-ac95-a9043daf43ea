package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022/2/9
 */
public enum ViAttachTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    /**
     * 被保人身份证正面
     */
    INSUREIDCARDFRONT("01", "被保人身份证正面", ImageDataEnum.INSURED, 1),

    /**
     * 被保人身份证反面
     */
    INSUREIDCARDBACK("02", "被保人身份证反面", ImageDataEnum.INSURED, 2),

    /**
     * 行驶证正本
     */
    RIVINGLICENSEFRONT("03", "行驶证正本", ImageDataEnum.VEHICLE_DRIVING, 3),

    /**
     * 行驶证副本
     */
    RIVINGLICENSEBACK("04", "行驶证副本", ImageDataEnum.VEHICLE_DRIVING, 4),

    /**
     * 营业执照/组织机构代码证
     */
    BUSINESSLICENSE("05", "营业执照/组织机构代码证", ImageDataEnum.OTHER, 5),

    /**
     * 车主 营业执照/组织机构代码证
     */
    OWNER_BUSINESSLICENSE("0501", "营业执照/组织机构代码证", ImageDataEnum.CAR_OWNER_IDENTITY, 5),

    /**
     * 投保人 营业执照/组织机构代码证
     */
    HOLDER_BUSINESSLICENSE("0502", "营业执照/组织机构代码证", ImageDataEnum.HOLDER, 5),

    /**
     * 被保人 营业执照/组织机构代码证
     */
    INSURED_BUSINESSLICENSE("0503", "营业执照/组织机构代码证", ImageDataEnum.INSURED, 5),

    /**
     * 机动车左前
     */
    CARLEFTFRONT("06", "机动车左前", ImageDataEnum.OTHER, 6),

    /**
     * 机动车左后
     */
    CARLEFTBACK("07", "机动车左后", ImageDataEnum.OTHER, 7),

    /**
     * 机动车右前
     */
    CARRIGHTFRONT("08", "机动车右前", ImageDataEnum.OTHER, 8),

    /**
     * 机动车右后
     */
    CARRIGHTBACK("09", "机动车右后", ImageDataEnum.OTHER, 9),

    /**
     * 机动车车架号
     */
    CARFRAME("10", "机动车车架号", ImageDataEnum.OTHER, 10),

    /**
     * 机动车辆登记证书
     */
    CARREG("11", "机动车辆登记证书", ImageDataEnum.OTHER, 11),

    /**
     * 机动车销售统一发票
     */
    CARINVOICE("12", "机动车销售统一发票", ImageDataEnum.OTHER, 99),

    /**
     * 机动车整车出产合格证
     */
    CARQUALIFIED("13", "机动车整车出产合格证", ImageDataEnum.OTHER, 99),

    /**
     * 驾驶证
     */
    DRIVINGLICENCE("14", "驾驶证", ImageDataEnum.OTHER, 99),

    /**
     * 车船税证明
     */
    CARTAX("15", "车船税证明", ImageDataEnum.OTHER, 99),

    /**
     * 电子保单
     */
    INSURANCE("33", "电子保单", ImageDataEnum.OTHER, 99),

    /**
     * 交强险电子保单
     */
    CIINSURANCE("16", "交强险电子保单", ImageDataEnum.OTHER, 99),

    /**
     * 商业险电子保单
     */
    BIINSURANCE("17", "商业险电子保单", ImageDataEnum.OTHER, 99),

    /**
     * 交强险电子发票
     */
    CIINVOICE("18", "交强险电子发票", ImageDataEnum.OTHER, 99),

    /**
     * 商业险电子发票
     */
    BIINVOICE("19", "商业险电子发票", ImageDataEnum.OTHER, 99),

    /**
     * 交强险电子标签
     */
    CILABEL("20", "保险标志", ImageDataEnum.OTHER, 99),

    /**
     * 车主身份证正面
     */
    CAROWNERFRONT("21", "车主身份证正面", ImageDataEnum.CAR_OWNER_IDENTITY, 21),

    /**
     * 车主身份证反面
     */
    CAROWNERBACK("22", "车主身份证反面", ImageDataEnum.CAR_OWNER_IDENTITY, 22),

    /**
     * 投保人身份证正面
     */
    APPLICANTFRONT("23", "投保人身份证正面", ImageDataEnum.HOLDER, 23),

    /**
     * 投保人身份证反面
     */
    APPLICANTBACK("24", "投保人身份证反面", ImageDataEnum.HOLDER, 24),

    /**
     * 非车险电子保单
     */
    NOCAR_E_POLICY("25", "非车险电子保单", ImageDataEnum.OTHER, 25),

    /**
     * 电子投保单
     */
    ELECTRONICS("26", "电子投保单", ImageDataEnum.OTHER, 99),

    /**
     * 组合险投保单
     */
    COMBINATION_ELECTRONICS("27", "组合险投保单", ImageDataEnum.OTHER, 99),

    /**
     * 交强电子投保单
     */
    ELECTRONICS_CI("28", "交强电子投保单", ImageDataEnum.OTHER, 99),

    /**
     * 商业电子投保单
     */
    ELECTRONICS_BI("29", "商业电子投保单", ImageDataEnum.OTHER, 99),

    /**
     * 免责说明书
     */
    ELECTRONICS_DISCLAIMER("30", "免责说明书", ImageDataEnum.OTHER, 99),

    /**
     * 机动车交通事故责任强制保险费率浮动告知单
     */
    FLOATING_NOTICE("31", "交强险费率浮动告知单", ImageDataEnum.OTHER, 99),

    /**
     * 交商共保投保单--2020新版
     */
    COMMERCIAL_COINSURANCE_APPLICATION("32", "交商共保投保单", ImageDataEnum.OTHER, 99),
    /**
     * 补传资料
     */
    SUPPLEMENT("65", "补传资料", ImageDataEnum.OTHER, 99),

    /**
     * 其它
     */
    UNKONW("66", "未知", ImageDataEnum.OTHER, 99),

    /**
     * 补传资料
     */
    CHECK_CAR("67", "验车影像", ImageDataEnum.OTHER, 99),

    /**
     * 验车资料
     */
    CHECK_CAR_INFO("001", "验车资料", ImageDataEnum.OTHER, 99),
    /**
     * 购车发票
     */
    BUY_CAR_INVOICE("002", "购车发票", ImageDataEnum.OTHER, 99),
    /**
     * 合格证/关单
     */
    CERTIFICATE("003", "合格证/关单", ImageDataEnum.OTHER, 99),
    /**
     * 投保单/批改申请书
     */
    PROPOSAL_POLICY("004", "投保单/批改申请书", ImageDataEnum.OTHER, 99),
    /**
     * 缴税凭证
     */
    PROOF_OF_TAX_PAYMENT("005", "缴税凭证", ImageDataEnum.OTHER, 99),
    /**
     * 已完税/减免税凭证和其他信息
     */
    TAX_PAID("006", "已完税/减免税凭证和其他信息", ImageDataEnum.OTHER, 99),
    /**
     * 已完税/减免税凭证和其他信息
     */
    APPLICATION_FOR_INSURANCE("007", "投保申请书", ImageDataEnum.OTHER, 99),
    /**
     * 车船税证明
     */
    PROOF_OF_VEHICLE_AND_VESSEL_TAX("008", "车船税证明", ImageDataEnum.OTHER, 99),
    /**
     * 上年保单(电子保单)
     */
    PREVIOUS_YEAR_POLICY("009", "上年保单(电子保单)", ImageDataEnum.OTHER, 99),
    /**
     * 单证
     */
    DOCUMENT("010", "单证", ImageDataEnum.OTHER, 99),
    /**
     * 申请书
     */
    APPLICATION_FORM("011", "申请书", ImageDataEnum.OTHER, 99),
    /**
     * 实名缴费证明
     */
    PAYMENT_CERTIFICATE("012", "实名缴费证明", ImageDataEnum.OTHER, 99),
    /**
     * 过户证明
     */
    TRANSFER_CERTIFICATE("013", "过户证明", ImageDataEnum.OTHER, 99),
    /**
     * 异地牌照车辆长期在当地使用证明
     */
    CERTIFICATE_OF_LONG_TERM_LOCAL_USE_OF_OFF_SITE_VEHICLES("014", "异地牌照车辆长期在当地使用证明", ImageDataEnum.OTHER, 99),
    /**
     * 非本人车辆授权代办证明
     */
    CERTIFICATE_OF_AUTHORIZATION_OF_OTHER_THAN_MY_VEHICLE("015", "非本人车辆授权代办证明", ImageDataEnum.OTHER, 99),
    /**
     * 遗失声明
     */
    LOSS_ANNOUNCEMENT("016", "遗失声明", ImageDataEnum.OTHER, 99),
    /**
     * 代办人身份证明原件正/反面照片
     */
    ORIGINAL_IDENTITY_CERTIFICATE_OF_AGENT("017", "代办人身份证明原件正/反面照片", ImageDataEnum.OTHER, 99),
    /**
     * 重要关系人情况说明书
     */
    SIGNIFICANT_PARTIES_FACT_SHEET("018", "重要关系人情况说明书", ImageDataEnum.OTHER, 99),
    /**
     * 机构差异化资料
     */
    ORGANIZATION_DIFFERENTIATION_DATA("019", "机构差异化资料", ImageDataEnum.OTHER, 99),
    /**
     * 车辆其他证件
     */
    OTHER_VEHICLE_DOCUMENTS("020", "车辆其他证件", ImageDataEnum.OTHER, 99),
    /**
     * 充电桩
     */
    CHARGING_STATION_INFORMATION("021", "充电桩", ImageDataEnum.OTHER, 99),
    /**
     * 新能源证明资料
     */
    NEW_ENERGY_CERTIFICATION_MATERIALS("022", "新能源证明资料", ImageDataEnum.OTHER, 99),
    /**
     * 车辆购置证明
     */
    VEHICLE_PURCHASE_CERTIFICATE("023", "车辆购置证明", ImageDataEnum.OTHER, 99),
    /**
     * 二手车销售发票
     */
    USED_CAR_SALES_INVOICE("024", "二手车销售发票", ImageDataEnum.OTHER, 99),
    /**
     * 挂靠证明
     */
    PROOF_OF_AFFILIATION("025", "挂靠证明", ImageDataEnum.OTHER, 99),
    /**
     * 车辆租赁合同
     */
    VEHICLE_LEASE_CONTRACT("026", "车辆租赁合同", ImageDataEnum.OTHER, 99),
    /**
     * 原投保人人证合影
     */
    OTHER_027("027", "原投保人人证合影", ImageDataEnum.OTHER, 99),
    /**
     * 车主人证合影
     */
    OTHER_028("028", "车主人证合影", ImageDataEnum.OTHER, 99),
    /**
     * 跨省投保系数使用证明
     */
    OTHER_029("029", "跨省投保系数使用证明", ImageDataEnum.OTHER, 99),
    /**
     * 跨省投保系数使用证明
     */
    POLICY_ORIGINAL_COPY("030", "保单正本", ImageDataEnum.OTHER, 99),
    /**
     * 授权委托书
     */
    POWER_OF_ATTORNEY("031", "授权委托书", ImageDataEnum.OTHER, 99),
    /**
     * 投保影像
     */
    PROPOSAL_IMAGE("032", "投保影像", ImageDataEnum.OTHER, 99),

    /**
     * 免责事项 - 责任书
     */
    OTHER_079("079", "", ImageDataEnum.OTHER, 99),

    /**
     * 费率浮动告知单
     */
    OTHER_080("080", "", ImageDataEnum.OTHER, 99),

    /**
     * 标的证明-其他
     */
    OTHER_098("098", "标的证明-其他", ImageDataEnum.OTHER, 99),
    /**
     * 其它
     */
    OTHER("99", "其它", ImageDataEnum.OTHER, 99),
    /**
     * 其他
     */
    OTHER_099("099", "其它", ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-新车购车发票
     */
    USAGE_100("100","本地使用证明-新车购车发票",ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-车主身份证
     */
    USAGE_101("101","本地使用证明-车主身份证",ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-车主社保卡
     */
    USAGE_102("102","本地使用证明-车主社保卡",ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-被保险人身份证
     */
    USAGE_103("103","本地使用证明-被保险人身份证",ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-被保险人社保卡
     */
    USAGE_104("104","本地使用证明-被保险人社保卡",ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-标的行驶证
     */
    USAGE_105("105","本地使用证明-标的行驶证",ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-车主港澳台居民居住证
     */
    USAGE_106("106","本地使用证明-车主港澳台居民居住证",ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-被保人港澳台居民居住证
     */
    USAGE_107("107","本地使用证明-被保人港澳台居民居住证",ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-车主营业执照/统一社会信用码
     */
    USAGE_108("108","本地使用证明-车主营业执照/统一社会信用码",ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-电子行驶证
     */
    USAGE_109("109","本地使用证明-电子行驶证",ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-车主房产证
     */
    USAGE_110("110","本地使用证明-车主房产证",ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-被保险人房产证
     */
    USAGE_111("111","本地使用证明-被保险人房产证",ImageDataEnum.OTHER, 99),

    /**
     * 本地使用证明-其他:其他证明,本地验车照和当地监管认可的其他本地使用证明材料等
     */
    USAGE_112("112","本地使用证明-其他:其他证明,本地验车照和当地监管认可的其他本地使用证明材料等",ImageDataEnum.OTHER, 99),

    /**
     * 标的证明-亮尾装置验标照片
     */
    USAGE_113("113","标的证明-亮尾装置验标照片",ImageDataEnum.OTHER, 99),

    /**
     * 投车关系证明-线上资料
     */
    USAGE_114("114","投车关系证明-线上资料",ImageDataEnum.OTHER, 99),
    ;






    private String value;

    private String description;

    @Getter
    private ImageDataEnum imageDataEnum;

    @Getter
    private int sortBy;

    //    ViAttachTypeEnum(String value, String description) {
//        this.value = value;
//        this.description = description;
//    }
//
//    ViAttachTypeEnum(String value, String description, ImageDataEnum dataEnum) {
//        this.value = value;
//        this.description = description;
//        this.imageDataEnum = dataEnum;
//    }
    ViAttachTypeEnum(String value, String description, ImageDataEnum dataEnum, int sortBy) {
        this.value = value;
        this.description = description;
        this.imageDataEnum = dataEnum;
        this.sortBy = sortBy;
    }

    private static Map<String, ViAttachTypeEnum> map = Stream.of(ViAttachTypeEnum.values()).collect(Collectors.toMap(ViAttachTypeEnum::getValue, Function.identity()));

    public static ViAttachTypeEnum getByValue(String value) {
        return map.get(value);
    }

    /**
     * 电子单证类型（非用户投保资料）
     */
    private static Map<ViAttachTypeEnum, Boolean> ELECTRONIC_MAP = Stream.of(CIINSURANCE, BIINSURANCE, NOCAR_E_POLICY, CILABEL,
            BIINVOICE, CIINVOICE, ELECTRONICS, ELECTRONICS_BI, ELECTRONICS_CI, PREVIOUS_YEAR_POLICY
    ).collect(Collectors.toMap(Function.identity(), viAttachTypeEnum -> true));

    /**
     * 是否电子单证
     *
     * @param viAttachTypeEnum
     * @return
     */
    public static boolean isElectronic(ViAttachTypeEnum viAttachTypeEnum) {
        return ELECTRONIC_MAP.containsKey(viAttachTypeEnum);
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return value;
    }
}
