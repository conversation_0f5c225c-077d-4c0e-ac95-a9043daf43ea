package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 营运性质枚举
 */
@Getter
public enum OperationNatureEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    NO_OPERATION("0", "非营运"),

    OPERATION("1", "营运"),

    VCD("2", "危化品运输"),
    ;

    private String value;

    private String description;

    OperationNatureEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static Map<String, OperationNatureEnum> map = Arrays.asList(OperationNatureEnum.values())
            .stream()
            .collect(Collectors.toMap(OperationNatureEnum::getValue, Function.identity()));

    public static OperationNatureEnum getByValue(String value) {
        return map.get(value);
    }

    public static String getDescriptionByValue(String value){
        return Optional.ofNullable(getByValue(value))
                .map(OperationNatureEnum::getDescription)
                .orElse(null);
    }

    @Override
    public String toString() {
        return value;
    }
}
