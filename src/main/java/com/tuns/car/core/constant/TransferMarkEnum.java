package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 是否过户标志
 */
@Getter
public enum TransferMarkEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    NO("0", "未过户"),

    YES("1", "过户");

    private String value;
    private String description;

    TransferMarkEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static TransferMarkEnum getByValue(String thisValue) {
        return Stream.of(TransferMarkEnum.values())
                .filter(transferMarkEnum -> transferMarkEnum.getValue().equals(thisValue))
                .findFirst()
                .orElse(null);
    }

    @Override
    public String toString() {
        return value;
    }

}
