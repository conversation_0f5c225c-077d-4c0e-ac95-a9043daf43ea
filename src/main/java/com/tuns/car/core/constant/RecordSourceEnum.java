package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022/2/8
 */ // 出单业务来源
@Getter
public enum RecordSourceEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    /**
     * app自助
     */
    APP("1","app自助"),
    /**
     * 内勤代出单
     */
    STAFF_PROXY("2","内勤代出单"),
    /**
     * 线下录单
     */
    OFFLINE_INPUT("3","线下录单"),
    /**
     * 抓取数据
     */
    SPIDER("4","同步数据"),
    /**
     * 移动集成出单
     */
    MOBILE_ASSEMBLE("5","移动集成出单");

    private String value;

    private String description;


    private static Map<String, RecordSourceEnum> map = Stream.of(RecordSourceEnum.values())
            .collect(Collectors.toMap(RecordSourceEnum::getValue, Function.identity()));


    public static RecordSourceEnum getByValue(String value) {
        return map.get(value);
    }


    RecordSourceEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
