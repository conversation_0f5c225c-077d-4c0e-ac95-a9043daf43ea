package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/23 17:05
 */
@Getter
@AllArgsConstructor
public enum QuoteTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    ALL("0", "全部"),
    TO_BE_INSURED("1", "待投保"),
    CANCEL("2", "撤单管理"),
    TO_BE_UNDERWRITE("3", "待核保"),
    ;


    private String value;

    private String description;

    @Override
    public String toString() {
        return value;
    }
}
