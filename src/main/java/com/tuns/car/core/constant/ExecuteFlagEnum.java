package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 延时任务日志表业务类型枚举执行状态枚举
 *
 * <AUTHOR>
 * @date 2022/10/27
 **/
@Getter
public enum ExecuteFlagEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    WAIT("0", "待执行"),

    SUCCESS_MIDDLE("1", "执行成功(后续任务继续)"),

    SUCCESS_COMPLETE("2", "执行成功(后续任务不需要执行)"),

    FAIL("3", "执行异常")

    ;

    private String value;

    private String description;

    ExecuteFlagEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
