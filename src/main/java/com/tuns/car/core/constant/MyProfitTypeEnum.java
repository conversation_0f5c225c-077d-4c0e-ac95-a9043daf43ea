package com.tuns.car.core.constant;

/**
 * 收益类型枚举
 */
public enum MyProfitTypeEnum {
    //1常规、2续保、3冲账、4保全、5增员奖、6失效、7收益调整
    ROUTINE("1","常规"),
    RENEWAL("2","续保"),
    WRITE_OFF("3","冲账"),
    PRESERVATION("4","保全"),
    ADDITIONAL_STAFF_AWARD("5","增员奖"),
    INVALID("6","失效"),
    INCOME_ADJUSTMENT("7","收益调整")
    ;

    private String code;
    private String value;

    MyProfitTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getProfitType(String code) {
        for (MyProfitTypeEnum ele : values()) {
            if(ele.getCode().equals(code)) return ele.getValue();
        }
        return null;
    }
}
