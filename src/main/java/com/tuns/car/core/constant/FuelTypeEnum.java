package com.tuns.car.core.constant;

import cn.hutool.core.convert.ConverterRegistry;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.boot.converter.HutoolEnumConverter;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 能源类型代码
 */
@Getter
public enum FuelTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    /**
     * 燃油
     */
    FUELOIL("1", "燃油", false),
    /**
     * 纯电动
     */
    PUREELECTRIC("2", "纯电动", true),
    /**
     * 燃料电池
     */
    FUELCELL("3", "燃料电池", true),
    /**
     * 插电式混合动力
     */
    PLUGINHYBRID("4", "插电式混合动力", true),
    /**
     * 其它混合动力
     */
    OTHERHYBRID("5", "其它混合动力", false),
    /**
     * 两用燃料
     */
    DUALFUEL("6", "两用燃料", false),
    /**
     * 混合
     */
    MIXTURE("8", "混合", false),
    /**
     * 柴油
     */
    DIESEL("7", "柴油", false),
    /**
     * 天然气
     */
    NATURAL_GAS("9", "天然气", false),
    /**
     * 液化石油气
     */
    LIQUEFIED_PGAS("10", "液化石油气", false),
    /**
     * 混合动力
     */
    HYBRID_POWER("11", "混合动力", false),

    OTHER("99", "其它", false);

    private String value;

    private String description;

    /**
     * 是否新能源
     */
    private Boolean isNewEnergy;

    FuelTypeEnum(String code, String description, Boolean isNewEnergy) {
        this.value = code;
        this.description = description;
        this.isNewEnergy = isNewEnergy;
    }

    private static final Map<String, FuelTypeEnum> FuelTypeEnumMap = Arrays.stream(FuelTypeEnum.values())
            .collect(Collectors.toMap(FuelTypeEnum::getValue, Function.identity()));

    public static String getDesc(String value) {
        return Optional.ofNullable(FuelTypeEnumMap.get(value))
                .map(FuelTypeEnum::getDescription)
                .orElse(null);
    }

    public static FuelTypeEnum getByValue(String value) {
        return FuelTypeEnumMap.get(value);
    }

    private static final Map<String, String> FuelTypeEnumMapDes = Arrays.stream(FuelTypeEnum.values()).collect(Collectors.toMap(FuelTypeEnum::getDescription, FuelTypeEnum::getValue));

    public static String getCode(String description) {
        return FuelTypeEnumMapDes.get(description);
    }


    static {
        ConverterRegistry.getInstance().putCustom(FuelTypeEnum.class, new HutoolEnumConverter<>(FuelTypeEnum.class));
    }


    /**
     * 是否是新能源车
     */
    public static boolean isNewEnergy(String value) {
        FuelTypeEnum fuelType = FuelTypeEnum.getByValue(value);
        if (Objects.isNull(fuelType)) {
            return false;
        }
        return fuelType.getIsNewEnergy();
    }


    @Override
    public String toString() {
        return value;
    }

}
