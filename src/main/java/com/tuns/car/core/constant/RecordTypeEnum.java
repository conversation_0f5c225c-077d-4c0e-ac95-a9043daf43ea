package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * ClassName: RecordTypeEnum
 * Description:
 * Create Time: 2022/2/10 15:35
 *
 * <AUTHOR>
 */
@Getter
public enum RecordTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    /***
     * 1-单交强
     */
    CI("1", "单交强"),
    /***
     * 2-单商业
     */
    BI("2", "单商业"),
    /***
     * 3-交商同保
     */
    BOTH("3", "交商同保");

    private String value;

    private String description;

    private static Map<String, RecordTypeEnum> map = Stream.of(RecordTypeEnum.values())
            .collect(Collectors.toMap(RecordTypeEnum::getValue, Function.identity()));

    RecordTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static RecordTypeEnum getByValue(String code) {
        return map.get(code);
    }

    @Override
    public String toString() {
        return value;
    }
}
