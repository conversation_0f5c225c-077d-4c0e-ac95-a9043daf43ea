package com.tuns.car.core.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 13:51
 */
public enum CommonKeyEnum {
    /**
     * 订单状态
     */
    STATUS("status","订单状态"),
    /**
     * 商业险保单号
     */
    COMMPLYNO("commPlyNo","商业险保单号"),
    /**
     * 交强险保单号
     */
    COMPPLYNO("compPlyNo","交强险保单号"),
    /**
     * 交强险保单号
     */
    PROPOSALCINO("proposalCiNo","交强险投保单号"),
    /**
     * 商业险投保单号
     */
    PROPOSALBINO("proposalBiNo","商业险投保单号"),
    /**
     * 签单时间/承保时间
     */
    INSUREDTIME("insuredTime","签单时间/承保时间"),

    /**
     * 审核意见
     */
    INSUREDMSG("insuredMsg","审核意见");

    private String key;

    private String description;

    public String key(){
        return this.key;
    }

    public String description(){
        return this.description;
    }

    CommonKeyEnum(String key, String description) {
        this.key = key;
        this.description = description;
    }

    public String getKey() {
        return key;
    }

    public String getDescription() {
        return description;
    }
}
