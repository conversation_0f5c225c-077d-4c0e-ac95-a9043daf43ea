package com.tuns.car.core.constant;

import cn.hutool.core.convert.ConverterRegistry;
import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.boot.converter.HutoolEnumConverter;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 验车状态枚举
 *
 * <AUTHOR>
 * @since 2022/4/19
 */
@Getter
public enum CheckCarStatusEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    /**
     * 需验车
     */
    NEED("0", "需验车"),

    /**
     * 已验车
     */
    VERIFIED("1", "已验车"),

    /**
     * 免验
     */
    EXEMPTION("2", "免验"),

    /**
     * 未验车
     */
    NO("3", "未验车"),

    /**
     * 待补充
     */
    WAIT("4", "待补充"),
    /**
     * 已验车、已验证
     */
    CAR_VERIFIED("5", "已验车、已验证"),
    /**
     * 已验证
     */
    CHECKED("6", "已验证"),

    /**
     * 补验车
     */
    ADD("8", "补验车"),

    /**
     * 送单验车
     */
    DELIVERY_CAR("9", "送单验车"),
    /**
     * 客户自主验车
     */
    SELF_HELP_CHECK("10", "客户自主验车");


    private String value;

    private String description;

    CheckCarStatusEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    private static Map<String, CheckCarStatusEnum> map = Stream.of(CheckCarStatusEnum.values())
            .collect(Collectors.toMap(CheckCarStatusEnum::getValue, Function.identity()));

    public static CheckCarStatusEnum getByValue(String value) {
        return map.get(value);
    }

    static {
        ConverterRegistry.getInstance().putCustom(CheckCarStatusEnum.class, new HutoolEnumConverter<>(CheckCarStatusEnum.class));
    }

    @Override
    public String toString() {
        return value;
    }
}
