package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 投保状态枚举
 *
 * <AUTHOR>
 * @since 2022/1/13
 */
public enum InsuredSubStatusEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    /**
     * 等待报价
     */
    A1("A1", "等待报价", InsuredStatusEnum.WAIT),
    /**
     * 等待报价处理中
     */
    A2("A2", "等待报价处理中", InsuredStatusEnum.WAIT),
    /**
     * 等待报价需补传资料
     */
    A21("A2-1", "等待报价需补传资料", InsuredStatusEnum.WAIT),
    /**
     * 报价成功
     */
    A3("A3", "报价成功", InsuredStatusEnum.PREMIUMCACULATE),
    /**
     * 报价失败等待补传资料
     */
    A4("A4", "报价失败等待补传资料", InsuredStatusEnum.FAILD),
    /**
     * 报价失败需调整投保方案
     */
    A5("A5", "报价失败需调整投保方案", InsuredStatusEnum.FAILD),
    /**
     * 报价失败
     */
    A6("A6", "报价失败", InsuredStatusEnum.FAILD),
    /**
     * 报价取消
     */
    A7("A7", "报价取消", InsuredStatusEnum.PREMIUMCACULATE_CANCEL),
    /**
     * 等待核保
     */
    B1("B1", "等待核保", InsuredStatusEnum.UNDERWRITING_WAITING),
    /**
     * 等待核保需补传资料
     */
    B11("B1-1", "等待核保需补传资料", InsuredStatusEnum.UNDERWRITING_FAILURE),
    /**
     * 核保通过已生成订单
     */
    B2("B2", "核保通过已生成订单", InsuredStatusEnum.UNDERWRITING_THROUGH),
    /**
     * 核保失败等待补传资料
     */
    B3("B3", "核保失败等待补传资料", InsuredStatusEnum.UNDERWRITING_FAILURE),
    /**
     * 核保失败需调整投保方案
     */
    B4("B4", "核保失败需调整投保方案", InsuredStatusEnum.UNDERWRITING_FAILURE),
    /**
     * 核保失败
     */
    B5("B5", "核保失败", InsuredStatusEnum.UNDERWRITING_FAILURE),
    /**
     * 核保取消
     */
    B6("B6", "核保取消", InsuredStatusEnum.UNDERWRITING_CANCEL),
    /**
     * 等待支付
     */
    C1("C1", "等待支付", InsuredStatusEnum.WAITING_PAY),
    /**
     * 已支付待确认
     */
    C11("C1-1", "已支付待确认", InsuredStatusEnum.WAITING_PAY),
    /**
     * 支付成功未承保
     */
    C19("C1-9", "支付成功未承保", InsuredStatusEnum.PAID),
    /**
     * 支付成功已承保
     */
    C2("C2", "支付成功已承保", InsuredStatusEnum.INSURED),
    /**
     * 支付失败
     */
    C3("C3", "支付失败", InsuredStatusEnum.PAY_ERROR),
    /**
     * 系统订单取消
     */
    C4("C4", "保险公司取消", InsuredStatusEnum.PAID_CANCEL),
    /**
     * 保险公司订单取消
     */
    C4_1("C4-1", "起保时间小于当前时间", InsuredStatusEnum.PAID_CANCEL),
    /**
     * 占单取消
     */
    C4_2("C4-2", "因占单自动取消核保", InsuredStatusEnum.UNDERWRITING_CANCEL),
    /**
     * 用户撤销核保，订单取消
     */
    C4_3("C4-3", "用户撤销核保，订单取消", InsuredStatusEnum.UNDERWRITING_CANCEL),
    /**
     * 订单取消-长时间未操作
     */
    C4_4("C4-4", "订单取消-长时间未操作", InsuredStatusEnum.PAID_CANCEL),

    /**
     * 订单取消-保险公司取消
     */
    C4_5("C4-5", "订单取消-保险公司取消", InsuredStatusEnum.PAID_CANCEL),
    /**
     * 保单自取
     */
    D1("D1", "保单自取", InsuredStatusEnum.INS_HELP),
    /**
     * 保单配送
     */
    D2("D2", "保单配送", InsuredStatusEnum.INS_DIS),
    /**
     * 订单完成
     */
    D3("D3", "订单完成", InsuredStatusEnum.ORDER_CLOSE),
    /**
     * 订单完成待评价
     */
    D4("D4", "订单完成待评价", InsuredStatusEnum.ORDER_CLOSE),
    /**
     * 订单完成已评价
     */
    D5("D5", "订单完成已评价", InsuredStatusEnum.ORDER_CLOSE);

    private String value;

    private String description;

    private InsuredStatusEnum parentStatus;

    private static Map<String, InsuredSubStatusEnum> map = Stream.of(InsuredSubStatusEnum.values()).collect(Collectors.toMap(InsuredSubStatusEnum::getValue, s -> s));

    private static Map<InsuredStatusEnum, List<InsuredSubStatusEnum>> parentStatusMap = Stream.of(InsuredSubStatusEnum.values()).collect(Collectors.groupingBy(InsuredSubStatusEnum::getParentStatus));

    InsuredSubStatusEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    InsuredSubStatusEnum(String value, String description, InsuredStatusEnum parentStatus) {
        this.value = value;
        this.description = description;
        this.parentStatus = parentStatus;
    }

    public static InsuredSubStatusEnum getByCode(String code) {
        InsuredSubStatusEnum insuredSubStatusEnum = map.get(code);
        return insuredSubStatusEnum;
    }

    /**
     * 根据状态code获取对应描述
     * @param code
     * @return
     */
    public static String getDescriptionByCode(String code) {
        return Optional.ofNullable(getByCode(code)).map(InsuredSubStatusEnum::getDescription).orElse("");
    }

    /**
     * 根据父状态获取子状态集合
     * @param insuredStatus
     * @return
     */
    public static List<InsuredSubStatusEnum> getByParentStatus(InsuredStatusEnum insuredStatus) {
        return parentStatusMap.get(insuredStatus);
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public InsuredStatusEnum getParentStatus() {
        return parentStatus;
    }

    @Override
    public String toString() {
        return value;
    }
}
