package com.tuns.car.core.constant;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023-5-10 14:26
 */
@Getter
public enum InputTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {
    /**
     * 固定值
     */
    FIXED_VALUE("1", "固定值"),
    /**
     * 输入框
     */
    EDIT_TEXT("2", "输入框"),
    ;
    /**
     * 数据库枚举值
     */
    private final String value;

    /**
     * 描述
     */
    private final String description;

    InputTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }
}
