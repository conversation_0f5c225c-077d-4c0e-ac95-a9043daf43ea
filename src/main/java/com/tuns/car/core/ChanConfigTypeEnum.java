package com.tuns.car.core;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.tuns.core.swagger.constants.SwaggerDisplayEnum;
import lombok.Getter;

/**
 * 渠道配置类型枚举  （即分组code）
 *
 * <AUTHOR>
 * @date 2022/12/26
 **/
@Getter
public enum ChanConfigTypeEnum implements IEnum<String>, SwaggerDisplayEnum<String> {

    NOCAR_DEFAULT("noCarDefault", "默认非车产品"),

    PAY_TYPE("payType","支付相关"),

    MODIFY_DAMAGE_INS("modifyDamageIns", "车损险修改");



    /**
     * 枚举值
     */
    private String value;

    /**
     * 枚举描述
     */
    private String description;

    ChanConfigTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String toString() {
        return value;
    }

}
