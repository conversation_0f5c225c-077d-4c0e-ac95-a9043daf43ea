<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.tuns.core</groupId>
        <artifactId>tuns-core-parent</artifactId>
        <version>2.0.2-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.tuns.car.core</groupId>
    <artifactId>tuns-car-core-api</artifactId>
    <version>${package.environment}-SNAPSHOT</version>
    <packaging>jar</packaging>

    <dependencies>
        <!-- 微服务框架-核心-基础api包 -->
        <dependency>
            <groupId>com.tuns.core</groupId>
            <artifactId>tuns-core-api</artifactId>
        </dependency>
        <!-- 启用远程调用 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- lombok 实体类简化 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!-- 微服务框架-核心-接口文档包 -->
        <dependency>
            <groupId>com.tuns.core</groupId>
            <artifactId>tuns-core-swagger</artifactId>
        </dependency>
        <!-- 参数校验 自动去除首尾空格 -->
        <dependency>
            <groupId>com.supalle</groupId>
            <artifactId>auto-trim</artifactId>
            <version>1.0.0</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>